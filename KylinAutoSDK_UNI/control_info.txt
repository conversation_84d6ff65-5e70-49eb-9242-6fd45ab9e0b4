{"name": "ukui-panel_sni_QToolButton_qing", "type": "push button", "coords": {"x": 1541, "y": 1040, "width": 32, "height": 32}, "datamap": {"Name": "ukui-panel_sni_QToolButton_qing", "ID": -1, "ProcessID": 2565, "Rolename": "push button", "Description": "This is a button of SNI plugin", "Index_in_parent": 9, "ChildrenCount": 0, "ProcessName": "ukui-panel", "Coords": {"x": 1541, "y": 1040, "width": 32, "height": 32}, "Text": "Not available: ", "Actions": ["Press", "SetFocus"], "States": ["enabled", "focusable", "sensitive", "showing", "visible"], "ParentPath": [1, 0, 2, 0, 0, 0, 0, 9], "ParentCount": 8, "Key": "Nukui-panel-sni-QToolButton-qing-DThis<PERSON>buttonofSNIplugin-P10200009", "RecordPosition": [1558, 1061], "WindowRoleName": "frame", "WindowChildCount": 1, "WindowName": "ukui-panel_mainwindow_QFrame_panel", "capture_status": "success"}, "description": "This is a button of SNI plugin", "states": ["enabled", "focusable", "sensitive", "showing", "visible"], "actions": ["Press", "SetFocus"], "capture_status": "success"}