#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
hellobig程序专用检测器
专门针对hellobig程序进行控件检测和分析

可能的问题:
1. hellobig程序可能不支持AT-SPI
2. 控件可能使用了自定义实现
3. 坐标系统可能有特殊处理
4. 权限问题
"""

import sys
import os
import subprocess
import pyatspi
import time


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def check_hellobig_process():
    """检查hellobig进程是否在运行"""
    print("🔍 检查hellobig进程...")
    
    try:
        # 检查进程
        result = subprocess.run(['pgrep', '-f', 'hello'], capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"✅ 找到相关进程: {pids}")
            
            # 获取进程详情
            for pid in pids:
                if pid.strip():
                    try:
                        cmd_result = subprocess.run(['ps', '-p', pid.strip(), '-o', 'pid,ppid,cmd'], 
                                                  capture_output=True, text=True)
                        print(f"   {cmd_result.stdout.strip()}")
                    except Exception:
                        pass
        else:
            print("❌ 未找到hellobig相关进程")
            print("   请确保hellobig程序正在运行")
            return False
            
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return False
    
    return True


def check_window_manager():
    """检查窗口管理器信息"""
    print("\n🪟 检查窗口管理器...")
    
    try:
        # 使用xwininfo获取窗口信息
        result = subprocess.run(['xwininfo', '-tree', '-root'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            hellobig_windows = []
            
            for line in lines:
                if 'hello' in line.lower() or 'big' in line.lower():
                    hellobig_windows.append(line.strip())
            
            if hellobig_windows:
                print("✅ 找到hellobig相关窗口:")
                for window in hellobig_windows:
                    print(f"   {window}")
                return True
            else:
                print("❌ 未在X11窗口列表中找到hellobig")
                
    except Exception as e:
        print(f"❌ 检查窗口管理器失败: {e}")
    
    return False


def get_window_at_position(x, y):
    """获取指定位置的窗口信息"""
    print(f"\n🎯 获取位置 ({x}, {y}) 的窗口信息...")
    
    try:
        # 使用xwininfo获取鼠标位置的窗口
        result = subprocess.run(['xwininfo', '-frame'], input='\n', text=True, capture_output=True)
        if result.returncode == 0:
            print("✅ 窗口信息:")
            for line in result.stdout.split('\n'):
                if any(keyword in line.lower() for keyword in ['window id', 'class', 'name']):
                    print(f"   {line.strip()}")
        
        # 尝试使用xdotool获取窗口ID
        result2 = subprocess.run(['xdotool', 'getmouselocation', '--shell'], 
                                capture_output=True, text=True)
        if result2.returncode == 0:
            for line in result2.stdout.split('\n'):
                if 'WINDOW=' in line:
                    window_id = line.split('=')[1]
                    print(f"   窗口ID: {window_id}")
                    
                    # 获取窗口名称
                    name_result = subprocess.run(['xdotool', 'getwindowname', window_id],
                                               capture_output=True, text=True)
                    if name_result.returncode == 0:
                        print(f"   窗口名称: {name_result.stdout.strip()}")
                    break
                    
    except Exception as e:
        print(f"❌ 获取窗口信息失败: {e}")


def detailed_atspi_scan():
    """详细的AT-SPI扫描"""
    print("\n🔬 详细AT-SPI扫描...")
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        print(f"📱 桌面应用数量: {desktop.childCount}")
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            print(f"\n📦 应用 {i}: {app_name}")
            print(f"   子窗口数量: {app.childCount}")
            
            # 检查是否是hellobig相关
            if any(keyword in app_name.lower() for keyword in ['hello', 'big', 'test']):
                print(f"   🎯 疑似目标应用!")
                
                # 详细检查这个应用
                scan_application_details(app, app_name)
            
            # 即使不是目标应用，也简单检查一下
            try:
                for j in range(min(app.childCount, 2)):
                    window = app.getChildAtIndex(j)
                    window_name = window.name or f"窗口{j}"
                    
                    if any(keyword in window_name.lower() for keyword in ['hello', 'big', 'test']):
                        print(f"   🎯 疑似目标窗口: {window_name}")
                        scan_application_details(app, f"{app_name}-{window_name}")
                        
            except Exception:
                pass
                
    except Exception as e:
        print(f"❌ AT-SPI扫描失败: {e}")


def scan_application_details(app, app_name):
    """扫描应用的详细信息"""
    print(f"\n🔍 详细扫描应用: {app_name}")
    
    try:
        for i in range(app.childCount):
            window = app.getChildAtIndex(i)
            window_name = window.name or f"窗口{i}"
            
            print(f"  📖 窗口 {i}: {window_name}")
            
            # 获取窗口位置
            try:
                if hasattr(window, 'queryComponent'):
                    component = window.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        print(f"     位置: ({extents.x}, {extents.y}) 尺寸: {extents.width}×{extents.height}")
                        
                        # 扫描窗口内的控件
                        scan_window_controls(window, extents, depth=1, max_depth=3)
            except Exception as e:
                print(f"     ❌ 获取窗口信息失败: {e}")
                
    except Exception as e:
        print(f"❌ 扫描应用详情失败: {e}")


def scan_window_controls(element, parent_extents, depth=0, max_depth=3):
    """扫描窗口内的控件"""
    if depth > max_depth:
        return
    
    indent = "  " * (depth + 2)
    
    try:
        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            
            name = child.name or "N/A"
            role = child.getRoleName() if hasattr(child, 'getRoleName') else 'unknown'
            
            print(f"{indent}🔸 {name} ({role})")
            
            # 获取控件位置
            try:
                if hasattr(child, 'queryComponent'):
                    component = child.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        
                        # 检查位置是否合理
                        if (extents.x >= 0 and extents.y >= 0 and 
                            extents.width > 0 and extents.height > 0):
                            print(f"{indent}   位置: ({extents.x}, {extents.y}) 尺寸: {extents.width}×{extents.height}")
                            
                            # 如果是有意义的控件，记录下来
                            if role in ['button', 'label', 'text', 'entry'] or name != "N/A":
                                print(f"{indent}   ✅ 有意义的控件")
                        else:
                            print(f"{indent}   ⚠️  位置异常: ({extents.x}, {extents.y}) {extents.width}×{extents.height}")
            except Exception:
                print(f"{indent}   ❌ 无法获取位置")
            
            # 递归扫描子控件
            if child.childCount > 0 and depth < max_depth:
                scan_window_controls(child, parent_extents, depth + 1, max_depth)
                
    except Exception as e:
        print(f"{indent}❌ 扫描控件失败: {e}")


def test_specific_position():
    """测试特定位置的控件检测"""
    print("\n🎯 测试特定位置的控件检测")
    
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is None:
        print("❌ 无法获取鼠标位置")
        return
    
    print(f"🖱️  当前鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 使用多种方法检测
    methods = [
        ("AT-SPI直接查询", test_atspi_direct),
        ("坐标遍历匹配", test_coordinate_matching),
        ("焦点元素检测", test_focus_detection)
    ]
    
    for method_name, method_func in methods:
        print(f"\n🧪 方法: {method_name}")
        try:
            result = method_func(mouse_x, mouse_y)
            if result:
                print(f"   ✅ 检测成功")
            else:
                print(f"   ❌ 检测失败")
        except Exception as e:
            print(f"   ❌ 方法异常: {e}")


def test_atspi_direct(x, y):
    """AT-SPI直接查询方法"""
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        # 尝试使用AT-SPI的坐标查询功能
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            
            try:
                # 使用componentAtDesktopCoords方法
                if hasattr(app, 'queryComponent'):
                    component = app.queryComponent()
                    if component and hasattr(component, 'getAccessibleAtPoint'):
                        element = component.getAccessibleAtPoint(x, y, pyatspi.DESKTOP_COORDS)
                        if element:
                            print(f"     找到元素: {element.name} ({element.getRoleName()})")
                            return element
            except Exception:
                continue
                
    except Exception as e:
        print(f"     AT-SPI直接查询异常: {e}")
    
    return None


def test_coordinate_matching(x, y):
    """坐标遍历匹配方法"""
    found_elements = []
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        def check_element_recursive(element, depth=0):
            if depth > 4:  # 限制深度
                return
            
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        
                        # 检查坐标是否在范围内
                        if (extents.x <= x < extents.x + extents.width and
                            extents.y <= y < extents.y + extents.height):
                            
                            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                            name = element.name or 'N/A'
                            
                            found_elements.append({
                                'element': element,
                                'name': name,
                                'role': role,
                                'extents': extents,
                                'depth': depth
                            })
                            
                            print(f"     匹配: {name} ({role}) 深度:{depth}")
                
                # 递归检查子元素
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    check_element_recursive(child, depth + 1)
                    
            except Exception:
                pass
        
        # 检查所有应用
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            check_element_recursive(app)
        
        if found_elements:
            # 选择最深层的元素
            best = max(found_elements, key=lambda x: x['depth'])
            print(f"     最佳匹配: {best['name']} ({best['role']})")
            return best['element']
            
    except Exception as e:
        print(f"     坐标匹配异常: {e}")
    
    return None


def test_focus_detection(x, y):
    """焦点元素检测方法"""
    try:
        # 首先尝试获取当前焦点元素
        desktop = pyatspi.Registry.getDesktop(0)
        
        # 查找有焦点的元素
        def find_focused_element(element):
            try:
                state = element.getState()
                if state.contains(pyatspi.STATE_FOCUSED):
                    print(f"     焦点元素: {element.name} ({element.getRoleName()})")
                    return element
                
                # 递归查找
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    result = find_focused_element(child)
                    if result:
                        return result
            except Exception:
                pass
            return None
        
        # 在所有应用中查找焦点元素
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            focused = find_focused_element(app)
            if focused:
                return focused
                
    except Exception as e:
        print(f"     焦点检测异常: {e}")
    
    return None


def main():
    """主函数"""
    print("🔍 hellobig程序控件检测诊断工具")
    print("=" * 80)
    
    # 环境检查
    if not sys.platform.startswith('linux'):
        print("❌ 此程序仅支持Linux系统")
        sys.exit(1)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    # 步骤1: 检查hellobig进程
    if not check_hellobig_process():
        print("\n💡 请确保:")
        print("1. hellobig程序正在运行")
        print("2. hellobig窗口可见且未被最小化")
        print("3. 将鼠标移动到hellobig窗口内的控件上")
        return
    
    # 步骤2: 检查窗口管理器
    check_window_manager()
    
    # 步骤3: 获取鼠标位置窗口信息
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        get_window_at_position(mouse_x, mouse_y)
    
    # 步骤4: 详细AT-SPI扫描
    detailed_atspi_scan()
    
    # 步骤5: 测试特定位置检测
    test_specific_position()
    
    print("\n🎉 诊断完成!")
    print("\n💡 如果仍然无法检测到hellobig控件，可能的原因:")
    print("1. hellobig程序不支持AT-SPI (辅助功能接口)")
    print("2. hellobig使用了自定义的GUI框架，不提供标准的辅助功能信息")
    print("3. 需要特殊的权限或配置才能访问hellobig的控件")
    print("4. hellobig程序可能需要特定的启动参数才能启用辅助功能")


if __name__ == "__main__":
    main()