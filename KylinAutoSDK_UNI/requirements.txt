# KylinAutoSDK_UNI 项目依赖

# GUI自动化相关
PyGObject>=3.42.0
python-xlib>=0.31
pynput>=1.7.6

# 图像处理
opencv-python>=4.5.0

# 系统交互
pyatspi>=2.38.0  # 注意：这个包通常通过系统包管理器安装

# PyQt5（用于demo程序）
PyQt5>=5.15.0

# 注意事项：
# 1. pyatspi 通常需要通过系统包管理器安装：
#    Ubuntu/Debian: sudo apt-get install python3-pyatspi
#    Fedora: sudo dnf install python3-pyatspi
#
# 2. 部分依赖可能需要系统级别的库支持：
#    - libwnck3: sudo apt-get install libwnck-3-0
#    - gir1.2-wnck-3.0: sudo apt-get install gir1.2-wnck-3.0
#
# 3. 在麒麟系统上，部分包可能已经预装 