#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
专门分析hellobig应用的树形控件结构
"""

import sys
import os
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def analyze_hellobig_tree():
    """分析hellobig应用的树形控件"""
    try:
        import pyatspi
        
        print("🔍 搜索hellobig应用...")
        
        # 获取所有应用
        desktop = pyatspi.Registry.getDesktop(0)
        hellobig_app = None
        
        print("📋 当前运行的应用程序:")
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                app_name = app.name if app.name else f"<未命名应用{i}>"
                print(f"   {i}: {app_name}")
                
                if app.name and 'hellobig' in app.name.lower():
                    hellobig_app = app
                    print(f"   ✅ 找到目标应用: {app_name}")
                    
            except Exception as e:
                print(f"   {i}: <获取失败: {e}>")
        
        if not hellobig_app:
            print("❌ 未找到hellobig应用，请确保应用正在运行")
            return
        
        print(f"\n📊 分析 {hellobig_app.name} 的完整控件结构:")
        print("=" * 80)
        
        def print_element_detailed(element, depth=0, max_depth=8, index=""):
            """详细打印元素信息"""
            if depth > max_depth:
                return
            
            indent = "  " * depth
            try:
                # 基本信息
                name = getattr(element, 'name', '') or ''
                role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                
                # 坐标信息
                coords_str = ""
                area = 0
                try:
                    if hasattr(element, 'queryComponent'):
                        component = element.queryComponent()
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        coords_str = f" [{extents.x},{extents.y} {extents.width}×{extents.height}]"
                        area = extents.width * extents.height
                except:
                    coords_str = " [坐标获取失败]"
                
                # 状态信息
                states = []
                try:
                    if hasattr(element, 'getState'):
                        state_set = element.getState()
                        if state_set.contains(pyatspi.STATE_VISIBLE):
                            states.append("VIS")
                        if state_set.contains(pyatspi.STATE_ENABLED):
                            states.append("EN")
                        if state_set.contains(pyatspi.STATE_FOCUSED):
                            states.append("FOC")
                        if state_set.contains(pyatspi.STATE_SELECTED):
                            states.append("SEL")
                        if state_set.contains(pyatspi.STATE_EXPANDED):
                            states.append("EXP")
                        if state_set.contains(pyatspi.STATE_COLLAPSED):
                            states.append("COL")
                except:
                    pass
                
                state_str = f" |{','.join(states)}|" if states else ""
                
                # 打印控件信息
                icon = "🌳" if "tree" in role.lower() else ("📁" if "item" in role.lower() else "📄")
                print(f"{indent}{index}{icon} {role}: '{name}'{coords_str}{state_str}")
                
                # 如果是树形相关控件，特别标注
                if "tree" in role.lower() or "item" in role.lower():
                    print(f"{indent}   ⭐ 【树形控件】层次: {depth}, 面积: {area}")
                
                # 递归处理子控件
                try:
                    child_count = element.childCount if hasattr(element, 'childCount') else 0
                    if child_count > 0:
                        print(f"{indent}   📂 子控件数量: {child_count}")
                        for i in range(min(child_count, 50)):  # 限制最多显示50个子控件
                            try:
                                child = element.getChildAtIndex(i)
                                if child:
                                    child_index = f"[{i}] "
                                    print_element_detailed(child, depth + 1, max_depth, child_index)
                            except Exception as e:
                                print(f"{indent}    ❌ 子控件{i}: {e}")
                except Exception as e:
                    print(f"{indent}   ❌ 获取子控件失败: {e}")
                    
            except Exception as e:
                print(f"{indent}❌ 元素{index}信息获取失败: {e}")
        
        # 开始详细分析
        print_element_detailed(hellobig_app)
        
        print("\n" + "=" * 80)
        print("🎯 树形控件分析完成!")
        
        # 查找所有树形相关控件
        print("\n🌳 所有树形相关控件汇总:")
        print("-" * 60)
        
        def find_tree_controls(element, path="", depth=0):
            """查找所有树形控件"""
            if depth > 10:
                return []
            
            tree_controls = []
            try:
                role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                name = getattr(element, 'name', '') or ''
                
                if "tree" in role.lower() or "item" in role.lower():
                    # 获取坐标
                    coords = None
                    try:
                        if hasattr(element, 'queryComponent'):
                            component = element.queryComponent()
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            coords = (extents.x, extents.y, extents.width, extents.height)
                    except:
                        pass
                    
                    tree_controls.append({
                        'role': role,
                        'name': name,
                        'path': path,
                        'depth': depth,
                        'coords': coords,
                        'element': element
                    })
                
                # 递归查找子控件
                try:
                    child_count = element.childCount if hasattr(element, 'childCount') else 0
                    for i in range(child_count):
                        try:
                            child = element.getChildAtIndex(i)
                            if child:
                                child_path = f"{path} > [{i}]{role}"
                                child_trees = find_tree_controls(child, child_path, depth + 1)
                                tree_controls.extend(child_trees)
                        except:
                            continue
                except:
                    pass
                    
            except:
                pass
            
            return tree_controls
        
        tree_controls = find_tree_controls(hellobig_app)
        
        for i, control in enumerate(tree_controls):
            print(f"{i+1}. {control['role']}: '{control['name']}'")
            print(f"   路径: {control['path']}")
            print(f"   层次: {control['depth']}")
            if control['coords']:
                x, y, w, h = control['coords']
                print(f"   坐标: ({x}, {y}) 尺寸: {w}×{h}")
            print()
        
        print(f"📊 共找到 {len(tree_controls)} 个树形相关控件")
        
        return tree_controls
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_tree_node_detection():
    """测试树形节点检测"""
    print("\n🧪 测试树形节点检测准确性:")
    print("=" * 60)
    
    tree_controls = analyze_hellobig_tree()
    
    if not tree_controls:
        print("❌ 未找到树形控件，无法进行检测测试")
        return
    
    # 找到有坐标信息的树形控件
    valid_controls = [c for c in tree_controls if c['coords']]
    
    if not valid_controls:
        print("❌ 未找到有效坐标的树形控件")
        return
    
    print(f"🎯 准备测试 {len(valid_controls)} 个有坐标的树形控件:")
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        for i, control in enumerate(valid_controls):
            x, y, w, h = control['coords']
            center_x = x + w // 2
            center_y = y + h // 2
            
            print(f"\n--- 测试控件 {i+1}/{len(valid_controls)} ---")
            print(f"预期: {control['role']} '{control['name']}'")
            print(f"坐标: 中心点({center_x}, {center_y})")
            
            # 使用UNI检测
            try:
                detected, info = uni.kdk_getElement_Uni(center_x, center_y, quick=False, highlight=False)
                
                if detected:
                    detected_role = detected.get('Rolename', 'N/A')
                    detected_name = detected.get('Name', 'N/A')
                    print(f"检测到: {detected_role} '{detected_name}'")
                    
                    # 比较结果
                    if detected_role.lower() == control['role'].lower() and detected_name == control['name']:
                        print("✅ 检测结果完全匹配!")
                    elif detected_role.lower() == control['role'].lower():
                        print("⚠️  角色匹配，但名称不同")
                    elif detected_name == control['name']:
                        print("⚠️  名称匹配，但角色不同")
                    else:
                        print("❌ 检测结果不匹配")
                        print(f"   可能原因: 坐标偏移或控件层次选择错误")
                else:
                    print(f"❌ 未检测到控件: {info}")
                    
            except Exception as e:
                print(f"❌ 检测出错: {e}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🌳 Hellobig应用树形控件深度分析")
    print("=" * 80)
    
    # 分析应用结构
    analyze_hellobig_tree()
    
    # 测试检测准确性
    test_tree_node_detection()
    
    print("\n" + "=" * 80)
    print("🎉 分析完成!")
    print("\n💡 查看结果:")
    print("   1. 应用的完整控件层次结构")
    print("   2. 所有树形相关控件的详细信息")
    print("   3. UNI检测与实际树形控件的匹配度测试")
    print("   4. 重点关注 tree item 控件的层次和坐标")
    
    return 0

if __name__ == "__main__":
    try:
        import pyatspi
    except ImportError:
        print("❌ 需要安装 python3-pyatspi")
        print("   sudo apt-get install python3-pyatspi")
        sys.exit(1)
    
    sys.exit(main())