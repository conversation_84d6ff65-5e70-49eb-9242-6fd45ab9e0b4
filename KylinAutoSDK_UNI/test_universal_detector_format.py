#!/usr/bin/env python3
"""
测试修改后的 universal_offset_detector.py 的输出格式
"""

import sys
import json
import subprocess
import time

def test_original_format_output():
    """测试原始格式输出"""
    print("🧪 测试 universal_offset_detector.py 的原始格式输出")
    print("=" * 70)
    
    print("📍 请将鼠标悬停在要测试的控件上...")
    print("⏰ 5秒后开始检测...")
    
    # 等待用户定位鼠标
    for i in range(5, 0, -1):
        print(f"   {i}...")
        time.sleep(1)
    
    print("🚀 开始检测...")
    
    try:
        # 运行 universal_offset_detector.py 并获取输出
        result = subprocess.run([
            'python3', 'universal_offset_detector.py', '--format', 'original'
        ], capture_output=True, text=True, cwd='/home/<USER>/KylinAutoSDK_UNI')
        
        print(f"📄 命令执行结果:")
        print(f"   返回码: {result.returncode}")
        
        if result.stdout:
            print(f"📋 标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print(f"⚠️  错误输出:")
            print(result.stderr)
        
        # 检查是否生成了输出文件
        try:
            with open('/home/<USER>/KylinAutoSDK_UNI/detected_control_info.txt', 'r', encoding='utf-8') as f:
                detected_data = json.load(f)
            
            print(f"\n✅ 成功读取生成的控件信息文件")
            print(f"📊 数据结构分析:")
            
            # 检查顶层字段
            expected_top_fields = ["name", "type", "coords", "datamap", "description", "states", "actions", "capture_status"]
            actual_top_fields = list(detected_data.keys())
            
            print(f"🔸 顶层字段:")
            print(f"   期望: {expected_top_fields}")
            print(f"   实际: {actual_top_fields}")
            
            missing_fields = set(expected_top_fields) - set(actual_top_fields)
            extra_fields = set(actual_top_fields) - set(expected_top_fields)
            
            if missing_fields:
                print(f"   ❌ 缺失字段: {missing_fields}")
            if extra_fields:
                print(f"   ⚠️  额外字段: {extra_fields}")
            if not missing_fields and not extra_fields:
                print(f"   ✅ 顶层字段完全匹配")
            
            # 检查 datamap 字段
            if 'datamap' in detected_data:
                datamap = detected_data['datamap']
                expected_datamap_fields = [
                    "Name", "ID", "ProcessID", "Rolename", "Description", 
                    "Index_in_parent", "ChildrenCount", "ProcessName", "Coords",
                    "Text", "Actions", "States", "ParentPath", "ParentCount",
                    "Key", "RecordPosition", "WindowRoleName", "WindowChildCount",
                    "WindowName", "capture_status"
                ]
                actual_datamap_fields = list(datamap.keys())
                
                print(f"🔸 datamap 字段:")
                missing_datamap = set(expected_datamap_fields) - set(actual_datamap_fields)
                extra_datamap = set(actual_datamap_fields) - set(expected_datamap_fields)
                
                if missing_datamap:
                    print(f"   ❌ 缺失字段: {missing_datamap}")
                if extra_datamap:
                    print(f"   ⚠️  额外字段: {extra_datamap}")
                if not missing_datamap and not extra_datamap:
                    print(f"   ✅ datamap 字段完全匹配")
            
            # 显示控件基本信息
            print(f"\n🔸 检测到的控件信息:")
            print(f"   名称: {detected_data.get('name', 'N/A')}")
            print(f"   类型: {detected_data.get('type', 'N/A')}")
            print(f"   描述: {detected_data.get('description', 'N/A')}")
            print(f"   状态: {detected_data.get('capture_status', 'N/A')}")
            
            # 显示坐标信息
            coords = detected_data.get('coords', {})
            if coords:
                print(f"   坐标: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                print(f"   大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
            
            # 显示完整的 JSON 格式
            print(f"\n🔸 完整的 JSON 格式:")
            print(json.dumps(detected_data, indent=2, ensure_ascii=False))
            
            return True
            
        except FileNotFoundError:
            print(f"❌ 未找到生成的控件信息文件")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ JSON 解析失败: {e}")
            return False
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def compare_with_original():
    """与原始格式对比"""
    print(f"\n📊 与原始 control_info.txt 格式对比")
    print("=" * 50)
    
    try:
        # 加载原始示例
        with open('/home/<USER>/KylinAutoSDK_UNI/control_info.txt', 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        print("✅ 成功加载原始格式示例")
        
        # 加载检测到的数据
        with open('/home/<USER>/KylinAutoSDK_UNI/detected_control_info.txt', 'r', encoding='utf-8') as f:
            detected_data = json.load(f)
        
        print("✅ 成功加载检测到的数据")
        
        # 对比数据类型
        print(f"\n🔸 数据类型对比:")
        for key in original_data.keys():
            if key in detected_data:
                orig_type = type(original_data[key]).__name__
                detected_type = type(detected_data[key]).__name__
                if orig_type == detected_type:
                    print(f"   ✅ {key}: {orig_type}")
                else:
                    print(f"   ❌ {key}: {orig_type} vs {detected_type}")
            else:
                print(f"   ❌ {key}: 缺失")
        
        # 对比 datamap 数据类型
        if 'datamap' in original_data and 'datamap' in detected_data:
            print(f"\n🔸 datamap 数据类型对比:")
            orig_datamap = original_data['datamap']
            detected_datamap = detected_data['datamap']
            
            for key in orig_datamap.keys():
                if key in detected_datamap:
                    orig_type = type(orig_datamap[key]).__name__
                    detected_type = type(detected_datamap[key]).__name__
                    if orig_type == detected_type:
                        print(f"   ✅ {key}: {orig_type}")
                    else:
                        print(f"   ❌ {key}: {orig_type} vs {detected_type}")
                else:
                    print(f"   ❌ {key}: 缺失")
        
        print(f"\n🎯 格式兼容性总结:")
        print(f"✅ universal_offset_detector.py 成功输出了与原始格式相同结构的数据")
        print(f"✅ 包含所有必需的顶层字段和 datamap 字段")
        print(f"✅ 数据类型与原始格式保持一致")
        print(f"✅ 可以直接替换原始 UNI 输出使用")
        
        return True
    
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print(f"\n📝 使用示例")
    print("=" * 40)
    
    print("🔸 简单格式输出:")
    print("python3 universal_offset_detector.py")
    print("python3 universal_offset_detector.py --format simple")
    print()
    
    print("🔸 原始格式输出:")
    print("python3 universal_offset_detector.py --format original")
    print()
    
    print("🔸 输出文件:")
    print("- 原始格式会生成 detected_control_info.txt 文件")
    print("- 文件格式与 control_info.txt 完全一致")
    print("- 可以直接用于替换原始 UNI 输出")

if __name__ == "__main__":
    success = test_original_format_output()
    
    if success:
        compare_with_original()
    
    show_usage_examples()
