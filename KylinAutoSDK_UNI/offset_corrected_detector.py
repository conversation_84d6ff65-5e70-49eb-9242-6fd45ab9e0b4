#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标偏移修正版控件检测器
基于已知的AT-SPI坐标偏差问题进行修正

解决方案:
1. 自动检测多种可能的坐标偏移
2. 实时验证偏移的准确性
3. 提供多种坐标修正策略
4. 确保高亮位置与实际控件匹配
"""

import sys
import os
import subprocess
import pyatspi
import time
import signal


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


class OffsetCorrectedDetector:
    """坐标偏移修正版检测器"""
    
    def __init__(self, target_app_name=None):
        """初始化检测器"""
        # 动态检测窗口偏移
        self.window_offset = self._detect_window_offset()
        
        self.known_offsets = [
            (0, 0, "无偏移"),
            (0, 30, "顶部面板偏移+30"),
            (0, -30, "顶部面板偏移-30"),
            (0, 24, "标准面板偏移+24"),
            (0, -24, "标准面板偏移-24"),
            (0, 40, "厚面板偏移+40"),
            (0, -40, "厚面板偏移-40"),
            (0, 27, "GNOME面板偏移+27"),
            (0, -27, "GNOME面板偏移-27"),
            (8, 30, "窗口装饰偏移(8,30)"),
            (-8, -30, "窗口装饰偏移(-8,-30)")
        ]
        
        # 如果检测到窗口偏移，添加到已知偏移列表
        if self.window_offset != (0, 0):
            self.known_offsets.insert(0, (self.window_offset[0], self.window_offset[1], f"检测到的窗口偏移{self.window_offset}"))
        
        self.verified_offset = None
        self.all_elements_cache = None
        self.target_app_name = target_app_name
        
        print("🔧 坐标偏移修正版检测器已初始化")
        print(f"🪟 检测到窗口偏移: {self.window_offset}")
        print(f"📐 预设 {len(self.known_offsets)} 种偏移模式")
        if target_app_name:
            print(f"🎯 目标应用: {target_app_name}")
    
    def _detect_window_offset_by_mouse(self, mouse_x, mouse_y):
        """根据鼠标位置动态检测窗口偏移"""
        try:
            import subprocess
            
            print(f"🖱️  根据鼠标位置({mouse_x}, {mouse_y})检测窗口偏移...")
            
            # 方法1: 使用xdotool直接获取鼠标下的窗口
            try:
                result = subprocess.run(['xdotool', 'getmouselocation', '--shell'], 
                                      capture_output=True, text=True, env={'DISPLAY': ':0'})
                if result.returncode == 0:
                    window_id = None
                    for line in result.stdout.strip().split('\n'):
                        if line.startswith('WINDOW='):
                            window_id = line.split('=')[1]
                            break
                    
                    if window_id and window_id != '0':
                        print(f"   找到鼠标下的窗口ID: {window_id}")
                        
                        # 获取这个窗口的几何信息
                        geo_result = subprocess.run(['xdotool', 'getwindowgeometry', '--shell', window_id], 
                                                  capture_output=True, text=True, env={'DISPLAY': ':0'})
                        if geo_result.returncode == 0:
                            x = y = 0
                            for line in geo_result.stdout.strip().split('\n'):
                                if line.startswith('X='):
                                    x = int(line.split('=')[1])
                                elif line.startswith('Y='):
                                    y = int(line.split('=')[1])
                            
                            if x != 0 or y != 0:
                                print(f"   窗口位置: ({x}, {y})")
                                return (x, y)
                        
                        # 如果上面的方法失败，尝试获取窗口名称来验证
                        name_result = subprocess.run(['xdotool', 'getwindowname', window_id], 
                                                   capture_output=True, text=True, env={'DISPLAY': ':0'})
                        if name_result.returncode == 0:
                            window_name = name_result.stdout.strip()
                            print(f"   窗口名称: {window_name}")
            except Exception as e:
                print(f"   xdotool方法失败: {e}")
            
            # 方法2: 通过AT-SPI查找鼠标位置对应的窗口
            try:
                desktop = pyatspi.Registry.getDesktop(0)
                target_windows = []  # 收集所有可能的目标窗口
                
                for i in range(desktop.childCount):
                    app = desktop.getChildAtIndex(i)
                    app_name = app.name or "unknown"
                    
                    for j in range(app.childCount):
                        window = app.getChildAtIndex(j)
                        if hasattr(window, 'queryComponent'):
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                
                                # 检查鼠标是否在这个窗口内
                                if (extents.x <= mouse_x < extents.x + extents.width and
                                    extents.y <= mouse_y < extents.y + extents.height):
                                    
                                    window_name = window.name or "unknown"
                                    
                                    target_windows.append({
                                        'app': app,
                                        'window': window,
                                        'app_name': app_name,
                                        'window_name': window_name,
                                        'extents': extents
                                    })
                
                # 优先选择hellobig或AT-SPI测试相关的窗口
                for tw in target_windows:
                    if ('hellobig' in tw['app_name'].lower() or 
                        'AT-SPI' in tw['window_name'] or 
                        '测试' in tw['window_name']):
                        
                        print(f"   通过AT-SPI找到目标窗口: {tw['app_name']} -> {tw['window_name']}")
                        print(f"   AT-SPI报告的窗口位置: ({tw['extents'].x}, {tw['extents'].y})")
                        
                        # 如果AT-SPI报告的窗口位置不是(0,0)，直接使用
                        if tw['extents'].x != 0 or tw['extents'].y != 0:
                            return (tw['extents'].x, tw['extents'].y)
                        
                        # 如果AT-SPI报告(0,0)，说明需要查找真实窗口位置
                        # 这种情况下，我们通过已知控件反推窗口偏移
                        return self._calculate_offset_from_known_controls(mouse_x, mouse_y, tw['window'])
                
                # 如果没有找到目标窗口，使用第一个找到的窗口
                if target_windows:
                    tw = target_windows[0]
                    print(f"   通过AT-SPI找到窗口: {tw['app_name']} -> {tw['window_name']}")
                    print(f"   AT-SPI报告的窗口位置: ({tw['extents'].x}, {tw['extents'].y})")
                    
                    if tw['extents'].x != 0 or tw['extents'].y != 0:
                        return (tw['extents'].x, tw['extents'].y)
                    
                    return self._calculate_offset_from_known_controls(mouse_x, mouse_y, tw['window'])
            except Exception as e:
                print(f"   AT-SPI方法失败: {e}")
            
        except Exception as e:
            print(f"❌ 窗口偏移检测失败: {e}")
        
        return (0, 0)
    
    def _calculate_offset_from_known_controls(self, mouse_x, mouse_y, window):
        """通过已知控件位置反推窗口偏移"""
        try:
            print("   🔍 通过已知控件反推窗口偏移...")
            
            # 获取窗口内的所有控件
            controls = []
            self._collect_elements_recursive(window, controls, max_depth=3)
            
            # 查找具有明确坐标和尺寸的控件
            for ctrl in controls:
                if (ctrl['name'] and ctrl['name'] != 'N/A' and 
                    ctrl['role'] not in ['filler', 'unknown', 'application', 'frame'] and
                    ctrl['extents'].width > 0 and ctrl['extents'].height > 0 and
                    ctrl['extents'].width < 500 and ctrl['extents'].height < 200):  # 排除大型容器
                    
                    # 计算控件中心点
                    center_x = ctrl['extents'].x + ctrl['extents'].width // 2
                    center_y = ctrl['extents'].y + ctrl['extents'].height // 2
                    
                    # 如果鼠标在这个控件附近（100像素范围内）
                    if (abs(mouse_x - center_x) < 100 and abs(mouse_y - center_y) < 100):
                        # 反推窗口偏移
                        offset_x = mouse_x - center_x
                        offset_y = mouse_y - center_y
                        
                        print(f"   找到参考控件: {ctrl['name']} ({ctrl['role']})")
                        print(f"   控件AT-SPI坐标: ({ctrl['extents'].x}, {ctrl['extents'].y})")
                        print(f"   控件中心: ({center_x}, {center_y})")
                        print(f"   推算窗口偏移: ({offset_x}, {offset_y})")
                        
                        return (offset_x, offset_y)
        
        except Exception as e:
            print(f"   反推窗口偏移失败: {e}")
        
        return (0, 0)
    
    def _detect_window_offset(self):
        """初始化时的窗口偏移检测（返回默认值，实际检测在使用时进行）"""
        return (0, 0)
    
    def refresh_elements_cache(self):
        """刷新元素缓存"""
        print("🔄 刷新AT-SPI元素缓存...")
        
        self.all_elements_cache = []
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                # 如果指定了目标应用名称，只收集该应用的元素
                if self.target_app_name:
                    if self.target_app_name.lower() not in app_name.lower():
                        continue
                
                app_elements = []
                self._collect_elements_recursive(app, app_elements, max_depth=5)
                
                if app_elements:
                    print(f"   📱 {app_name}: {len(app_elements)} 个元素")
                    self.all_elements_cache.extend(app_elements)
            
            print(f"✅ 缓存完成，总共 {len(self.all_elements_cache)} 个元素")
            
        except Exception as e:
            print(f"❌ 刷新缓存失败: {e}")
    
    def _collect_elements_recursive(self, element, element_list, depth=0, max_depth=5):
        """递归收集元素"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 只收集有效的元素
            if extents and extents.width > 0 and extents.height > 0:
                element_list.append({
                    'element': element,
                    'name': name,
                    'role': role,
                    'extents': extents,
                    'depth': depth
                })
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_elements_recursive(child, element_list, depth + 1, max_depth)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def detect_offset_automatically(self, mouse_x, mouse_y):
        """自动检测坐标偏移"""
        print(f"🎯 自动检测坐标偏移，鼠标位置: ({mouse_x}, {mouse_y})")
        
        # 动态检测窗口偏移
        detected_offset = self._detect_window_offset_by_mouse(mouse_x, mouse_y)
        if detected_offset != (0, 0):
            self.window_offset = detected_offset
            print(f"🪟 动态检测到窗口偏移: {self.window_offset}")
        else:
            print(f"🪟 使用默认窗口偏移: {self.window_offset}")
        
        if not self.all_elements_cache:
            self.refresh_elements_cache()
        
        offset_results = []
        
        # 首先应用窗口偏移，将鼠标坐标转换为窗口内相对坐标
        window_relative_x = mouse_x - self.window_offset[0]
        window_relative_y = mouse_y - self.window_offset[1]
        print(f"🔄 窗口内相对坐标: ({window_relative_x}, {window_relative_y})")
        
        # 测试每种偏移
        for x_offset, y_offset, desc in self.known_offsets:
            corrected_x = window_relative_x + x_offset
            corrected_y = window_relative_y + y_offset
            
            matches = []
            for elem in self.all_elements_cache:
                ext = elem['extents']
                
                if (ext.x <= corrected_x < ext.x + ext.width and
                    ext.y <= corrected_y < ext.y + ext.height):
                    matches.append(elem)
            
            if matches:
                # 选择最小面积的匹配（最具体的控件）
                best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
                
                offset_results.append({
                    'offset': (x_offset, y_offset),
                    'desc': desc,
                    'match': best_match,
                    'match_count': len(matches)
                })
                
                print(f"   ✅ {desc}: 找到 {len(matches)} 个匹配，最佳: {best_match['name']} ({best_match['role']})")
        
        if offset_results:
            # 改进的偏移选择策略：
            # 1. 优先选择匹配到具体控件的偏移（非 "N/A" 和 "filler"）
            # 2. 其次选择匹配数量最少的偏移
            
            # 过滤掉匹配到通用容器控件的结果
            filtered_results = []
            for result in offset_results:
                match = result['match']
                if (match['name'] not in ['N/A', ''] and 
                    match['role'] not in ['filler', 'unknown', 'application']):
                    filtered_results.append(result)
            
            # 如果有具体控件匹配，从中选择最精确的
            if filtered_results:
                best_offset_result = min(filtered_results, key=lambda x: x['match_count'])
            else:
                # 如果没有具体控件匹配，选择匹配数量最少的
                best_offset_result = min(offset_results, key=lambda x: x['match_count'])
            
            self.verified_offset = best_offset_result['offset']
            
            print(f"\n🏆 推荐偏移: {best_offset_result['desc']} {best_offset_result['offset']}")
            print(f"   匹配控件: {best_offset_result['match']['name']} ({best_offset_result['match']['role']})")
            
            return best_offset_result
        else:
            print("❌ 未找到合适的偏移")
            return None
    
    def get_control_at_point_corrected(self, x, y, auto_detect_offset=True):
        """获取修正坐标后的控件"""
        print(f"🔍 检测坐标 ({x}, {y}) 的控件（含偏移修正）")
        
        # 首先应用窗口偏移，将屏幕坐标转换为窗口内相对坐标
        window_relative_x = x - self.window_offset[0]
        window_relative_y = y - self.window_offset[1]
        
        # 如果还没有验证过的偏移，先自动检测
        if auto_detect_offset and not self.verified_offset:
            offset_result = self.detect_offset_automatically(x, y)
            if offset_result:
                # 直接返回自动检测的结果
                match = offset_result['match']
                return {
                    'success': True,
                    'control_info': self._build_control_info(match),
                    'position': (match['extents'].x, match['extents'].y, 
                               match['extents'].width, match['extents'].height),
                    'used_offset': offset_result['offset'],
                    'offset_desc': offset_result['desc']
                }
            else:
                return {
                    'success': False,
                    'message': "无法自动检测到合适的坐标偏移",
                    'used_offset': None
                }
        
        # 使用已验证的偏移
        if self.verified_offset:
            offset_x, offset_y = self.verified_offset
        else:
            offset_x, offset_y = 0, 0
        
        corrected_x = window_relative_x + offset_x
        corrected_y = window_relative_y + offset_y
        
        print(f"   窗口相对坐标: ({window_relative_x}, {window_relative_y})")
        print(f"   修正后坐标: ({corrected_x}, {corrected_y}) (偏移: {offset_x}, {offset_y})")
        
        if not self.all_elements_cache:
            self.refresh_elements_cache()
        
        # 查找匹配的控件
        matches = []
        for elem in self.all_elements_cache:
            ext = elem['extents']
            
            if (ext.x <= corrected_x < ext.x + ext.width and
                ext.y <= corrected_y < ext.y + ext.height):
                matches.append(elem)
        
        if matches:
            # 选择最佳匹配
            best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
            
            return {
                'success': True,
                'control_info': self._build_control_info(best_match),
                'position': (best_match['extents'].x, best_match['extents'].y,
                           best_match['extents'].width, best_match['extents'].height),
                'used_offset': (offset_x, offset_y)
            }
        else:
            return {
                'success': False,
                'message': f"在修正坐标 ({corrected_x}, {corrected_y}) 处未找到控件",
                'used_offset': (offset_x, offset_y)
            }
    
    def _build_control_info(self, elem):
        """构建控件信息"""
        try:
            # 获取状态
            states = []
            try:
                state = elem['element'].getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass
            
            # 获取动作
            actions = []
            try:
                if hasattr(elem['element'], 'queryAction'):
                    action = elem['element'].queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass
            
            return {
                'name': elem['name'],
                'role': elem['role'],
                'states': states,
                'actions': actions,
                'depth': elem['depth']
            }
        except Exception:
            return {
                'name': elem['name'],
                'role': elem['role'],
                'states': [],
                'actions': [],
                'depth': elem['depth']
            }
    
    def highlight_control_corrected(self, x, y, duration=2, color='red', border_width=2):
        """高亮修正后的控件"""
        result = self.get_control_at_point_corrected(x, y)
        
        if not result['success']:
            return {
                'success': False,
                'message': f"高亮失败: {result['message']}",
                'highlighted': False
            }
        
        # 获取AT-SPI坐标
        atspi_x, atspi_y, width, height = result['position']
        used_offset = result.get('used_offset', (0, 0))
        
        # 计算高亮坐标（需要反向应用偏移，并添加窗口偏移）
        highlight_x = atspi_x - used_offset[0] + self.window_offset[0]
        highlight_y = atspi_y - used_offset[1] + self.window_offset[1]
        
        print(f"✨ 高亮控件:")
        print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y}) {width}×{height}")
        print(f"   使用偏移: {used_offset}")
        print(f"   高亮坐标: ({highlight_x}, {highlight_y}) {width}×{height}")
        
        try:
            from ultimate_highlight import ultimate_highlight
            highlight_success = ultimate_highlight(
                highlight_x, highlight_y, width, height,
                duration=duration, color=color, border_width=border_width
            )
            
            return {
                'success': True,
                'message': f"控件高亮{'成功' if highlight_success else '失败'}",
                'highlighted': highlight_success,
                'control_position': (highlight_x, highlight_y, width, height),
                'atspi_position': (atspi_x, atspi_y, width, height),
                'used_offset': used_offset,
                'control_info': result['control_info']
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"高亮异常: {e}",
                'highlighted': False
            }
    
    def print_control_info_corrected(self, x, y):
        """打印修正后的控件信息"""
        print(f"\n🔍 检测坐标 ({x}, {y}) 处的控件 (含偏移修正):")
        print("=" * 60)
        
        result = self.get_control_at_point_corrected(x, y)
        
        if not result['success']:
            print(f"❌ {result['message']}")
            return
        
        info = result['control_info']
        atspi_pos = result['position']
        used_offset = result.get('used_offset', (0, 0))
        
        print(f"📋 控件信息:")
        print(f"   名称: {info['name']}")
        print(f"   角色: {info['role']}")
        print(f"   层级深度: {info['depth']}")
        
        print(f"\n📐 坐标信息:")
        print(f"   AT-SPI坐标: ({atspi_pos[0]}, {atspi_pos[1]}) {atspi_pos[2]}×{atspi_pos[3]}")
        print(f"   使用偏移: {used_offset}")
        print(f"   实际显示坐标: ({atspi_pos[0] - used_offset[0]}, {atspi_pos[1] - used_offset[1]})")
        
        if result.get('offset_desc'):
            print(f"   偏移描述: {result['offset_desc']}")
        
        if info.get('states'):
            print(f"\n🔄 状态: {', '.join(info['states'][:5])}")
        
        if info.get('actions'):
            print(f"⚡ 动作: {', '.join(info['actions'][:3])}")
        
        print("=" * 60)


class AutoOffsetDetector:
    """自动偏移检测器"""
    
    def __init__(self):
        """初始化自动检测器"""
        self.detector = OffsetCorrectedDetector(target_app_name="hellobig")
        self.last_mouse_pos = (0, 0)
        self.last_detection_time = 0
        self.detection_cooldown = 2.0
        self.is_running = True
        
        print("🎯 自动偏移检测器已启动")
        print("=" * 60)
        print("📖 功能说明:")
        print("   ✅ 自动检测AT-SPI坐标偏移")
        print("   ✅ 实时修正控件位置")
        print("   ✅ 精确的高亮显示")
        print("   ✅ 详细的偏移信息")
        print("=" * 60)
        print("🖱️  移动鼠标到hellobig程序的控件上...")
        print("🛑 按 Ctrl+C 退出程序")
        print("=" * 60)
    
    def should_detect_at_position(self, x, y):
        """判断是否应该在此位置进行检测"""
        current_time = time.time()
        
        if current_time - self.last_detection_time < self.detection_cooldown:
            return False
        
        last_x, last_y = self.last_mouse_pos
        distance = ((x - last_x) ** 2 + (y - last_y) ** 2) ** 0.5
        
        return distance >= 15  # 移动超过15像素才检测
    
    def detect_and_highlight_with_offset(self, x, y):
        """检测并高亮（含偏移修正）"""
        print(f"\n🔍 检测鼠标位置: ({x}, {y})")
        print("-" * 40)
        
        try:
            # 打印详细信息
            self.detector.print_control_info_corrected(x, y)
            
            # 执行高亮
            print(f"\n✨ 执行高亮...")
            result = self.detector.highlight_control_corrected(x, y, duration=2, color='green', border_width=2)
            
            if result['success']:
                if result['highlighted']:
                    print(f"✅ 高亮成功!")
                    print(f"   偏移: {result['used_offset']}")
                    
                    # 验证偏移准确性
                    atspi_pos = result['atspi_position']
                    highlight_pos = result['control_position']
                    
                    if (atspi_pos[0] - result['used_offset'][0] == highlight_pos[0] and
                        atspi_pos[1] - result['used_offset'][1] == highlight_pos[1]):
                        print(f"   🎯 坐标修正验证: ✅ 完全匹配")
                    else:
                        print(f"   ⚠️  坐标修正验证: 可能存在细微差异")
                else:
                    print(f"❌ 高亮失败: {result['message']}")
            else:
                print(f"❌ 检测失败: {result['message']}")
                
        except Exception as e:
            print(f"❌ 检测异常: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 40)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 接收到信号 {signum}，正在退出...")
        self.is_running = False
    
    def run(self):
        """运行检测器"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            print("🚀 开始监控鼠标位置...")
            
            while self.is_running:
                current_pos = get_mouse_position()
                
                if current_pos == (None, None):
                    time.sleep(0.5)
                    continue
                
                x, y = current_pos
                
                if self.should_detect_at_position(x, y):
                    self.detect_and_highlight_with_offset(x, y)
                    self.last_mouse_pos = (x, y)
                    self.last_detection_time = time.time()
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 接收到中断信号，正在退出...")
        except Exception as e:
            print(f"\n❌ 运行异常: {e}")
        finally:
            self.is_running = False
            print("👋 检测器已停止")


def main():
    """主函数"""
    print("🔧 坐标偏移修正版控件检测器")
    print("=" * 60)
    print("专门解决accerciser能看到控件但坐标不准确的问题")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        detector = AutoOffsetDetector()
        detector.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()