#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标偏移诊断和修复工具
分析并修复高亮边框与实际控件位置的偏移问题
"""

import sys
import os
import subprocess
import time
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return 1000, 500
    except:
        return 1000, 500

def get_desktop_info():
    """获取桌面环境信息"""
    try:
        # 检查桌面环境
        desktop_env = os.environ.get('XDG_CURRENT_DESKTOP', 'Unknown')
        
        # 检查显示协议
        wayland_display = os.environ.get('WAYLAND_DISPLAY', '')
        x11_display = os.environ.get('DISPLAY', '')
        
        # 检查窗口管理器
        try:
            wm_result = subprocess.run(['wmctrl', '-m'], capture_output=True, text=True)
            wm_info = wm_result.stdout if wm_result.returncode == 0 else "Unknown"
        except:
            wm_info = "Unknown"
        
        # 检查面板信息（可能导致坐标偏移）
        try:
            panel_result = subprocess.run(['xprop', '-root', '_NET_WORKAREA'], 
                                        capture_output=True, text=True)
            workarea = panel_result.stdout if panel_result.returncode == 0 else "Unknown"
        except:
            workarea = "Unknown"
            
        return {
            'desktop_env': desktop_env,
            'wayland_display': wayland_display,
            'x11_display': x11_display,
            'window_manager': wm_info,
            'workarea': workarea
        }
    except Exception as e:
        return {'error': str(e)}

def diagnose_coordinate_offset():
    """诊断坐标偏移问题"""
    print("🔍 坐标偏移诊断工具")
    print("=" * 70)
    
    # 1. 环境信息诊断
    print("📋 步骤1: 环境信息诊断")
    desktop_info = get_desktop_info()
    for key, value in desktop_info.items():
        print(f"   {key}: {value}")
    print()
    
    # 2. 鼠标位置获取
    print("📋 步骤2: 当前鼠标位置")
    mouse_x, mouse_y = get_mouse_position()
    print(f"   鼠标坐标: ({mouse_x}, {mouse_y})")
    print()
    
    # 3. UNI控件检测
    print("📋 步骤3: UNI控件检测分析")
    try:
        from UNI_new import UNI
        uni = UNI()
        
        # 先进行不带高亮的检测
        print("   正在检测控件...")
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        
        if control_data:
            print("   ✅ 检测到控件")
            
            # 分析不同的坐标数据
            coords = control_data.get('Coords', {})
            rel_coords = control_data.get('RelativeCoords', {})
            
            print(f"   控件名称: {control_data.get('Name', 'N/A')}")
            print(f"   控件类型: {control_data.get('Rolename', 'N/A')}")
            print(f"   窗口名称: {control_data.get('WindowName', 'N/A')}")
            print()
            
            print("🎯 坐标数据分析:")
            if coords:
                print(f"   屏幕坐标 (Coords): x={coords.get('x')}, y={coords.get('y')}, w={coords.get('width')}, h={coords.get('height')}")
            if rel_coords:
                print(f"   相对坐标 (RelativeCoords): x={rel_coords.get('x')}, y={rel_coords.get('y')}, w={rel_coords.get('width')}, h={rel_coords.get('height')}")
            
            # AT-SPI原始坐标
            atspi_element = control_data.get('_atspi_element')
            if atspi_element:
                try:
                    import pyatspi
                    extents = atspi_element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    print(f"   AT-SPI坐标: x={extents.x}, y={extents.y}, w={extents.width}, h={extents.height}")
                except Exception as e:
                    print(f"   AT-SPI坐标获取失败: {e}")
            print()
            
            # 4. 偏移分析
            print("📋 步骤4: 偏移原因分析")
            
            # 检查是否有面板偏移
            panel_offset = check_panel_offset()
            if panel_offset:
                print(f"   🔍 检测到面板偏移: {panel_offset}")
            
            # 检查标题栏偏移
            titlebar_offset = check_titlebar_offset(desktop_info)
            if titlebar_offset:
                print(f"   🔍 检测到标题栏偏移: {titlebar_offset}")
            
            # 检查缩放因子
            scale_factor = check_scale_factor()
            if scale_factor != 1.0:
                print(f"   🔍 检测到显示缩放: {scale_factor}")
            
            print()
            
            # 5. 修复建议
            print("📋 步骤5: 修复建议")
            suggested_coords = suggest_coordinate_fix(control_data, panel_offset, titlebar_offset, scale_factor)
            
            if suggested_coords:
                print("   💡 建议的修正坐标:")
                print(f"      x={suggested_coords['x']}, y={suggested_coords['y']}")
                print(f"      w={suggested_coords['width']}, h={suggested_coords['height']}")
                
                # 6. 测试修正效果
                print()
                print("📋 步骤6: 测试修正效果")
                print("   将显示修正后的高亮效果，请观察是否准确对齐...")
                
                from ultimate_highlight import ultimate_highlight
                success = ultimate_highlight(
                    x=suggested_coords['x'],
                    y=suggested_coords['y'], 
                    width=suggested_coords['width'],
                    height=suggested_coords['height'],
                    duration=5,
                    color='blue',
                    border_width=2
                )
                
                if success:
                    print("   ✅ 修正后高亮显示成功")
                    print("   请观察蓝色边框是否准确对齐控件")
                    time.sleep(5)
                    
                    # 对比测试：显示原始坐标
                    print()
                    print("   现在显示原始坐标的高亮效果进行对比...")
                    if coords:
                        ultimate_highlight(
                            x=coords.get('x', 0),
                            y=coords.get('y', 0),
                            width=coords.get('width', 100),
                            height=coords.get('height', 50),
                            duration=3,
                            color='red',
                            border_width=2
                        )
                        print("   红色边框为原始坐标，蓝色边框为修正坐标")
                        print("   请比较哪个更准确对齐控件")
                else:
                    print("   ❌ 修正后高亮显示失败")
            else:
                print("   ⚠️ 无法生成修正建议，需要更多信息")
                
        else:
            print("   ❌ 未检测到控件")
            print(f"   信息: {info}")
            
    except Exception as e:
        print(f"   ❌ 检测失败: {e}")
    
    print()
    print("=" * 70)
    print("🎉 坐标偏移诊断完成")

def check_panel_offset():
    """检查面板导致的偏移"""
    try:
        # 在UKUI环境下，顶部面板通常会导致坐标偏移
        result = subprocess.run(['xprop', '-root', '_NET_WORKAREA'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and '_NET_WORKAREA' in result.stdout:
            # 解析工作区域
            workarea_line = result.stdout.strip()
            if '=' in workarea_line:
                coords_part = workarea_line.split('=')[1].strip()
                coords = [int(x.strip()) for x in coords_part.split(',')]
                if len(coords) >= 4:
                    # coords = [x, y, width, height]
                    # 如果y > 0，说明有顶部面板
                    panel_height = coords[1]
                    if panel_height > 0:
                        return {'top_panel_height': panel_height}
    except:
        pass
    return None

def check_titlebar_offset(desktop_info):
    """检查窗口标题栏偏移"""
    try:
        desktop_env = desktop_info.get('desktop_env', '').lower()
        
        # 不同桌面环境的典型标题栏高度
        titlebar_heights = {
            'ukui': 30,      # UKUI典型标题栏高度
            'gnome': 38,     # GNOME典型标题栏高度
            'kde': 32,       # KDE典型标题栏高度
            'xfce': 28,      # XFCE典型标题栏高度
        }
        
        for env in titlebar_heights:
            if env in desktop_env:
                return {'titlebar_height': titlebar_heights[env]}
                
    except:
        pass
    return None

def check_scale_factor():
    """检查显示缩放因子"""
    try:
        # 检查GDK缩放
        gdk_scale = os.environ.get('GDK_SCALE', '1')
        
        # 检查Qt缩放
        qt_scale = os.environ.get('QT_SCALE_FACTOR', '1')
        
        # 检查Xft DPI
        try:
            result = subprocess.run(['xrdb', '-query'], capture_output=True, text=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'Xft.dpi' in line:
                        dpi = float(line.split(':')[1].strip())
                        scale = dpi / 96.0  # 96 DPI是标准
                        return scale
        except:
            pass
            
        return float(gdk_scale) or float(qt_scale) or 1.0
        
    except:
        return 1.0

def suggest_coordinate_fix(control_data, panel_offset, titlebar_offset, scale_factor):
    """建议坐标修正方案"""
    try:
        coords = control_data.get('Coords', {})
        if not coords:
            return None
            
        x = coords.get('x', 0)
        y = coords.get('y', 0) 
        width = coords.get('width', 100)
        height = coords.get('height', 50)
        
        # 应用修正
        corrected_x = x
        corrected_y = y
        
        # 面板偏移修正
        if panel_offset and 'top_panel_height' in panel_offset:
            corrected_y -= panel_offset['top_panel_height']
            print(f"   🔧 应用面板偏移修正: y -= {panel_offset['top_panel_height']}")
        
        # 标题栏偏移修正
        if titlebar_offset and 'titlebar_height' in titlebar_offset:
            corrected_y -= titlebar_offset['titlebar_height']
            print(f"   🔧 应用标题栏偏移修正: y -= {titlebar_offset['titlebar_height']}")
        
        # 缩放因子修正
        if scale_factor != 1.0:
            corrected_x = int(corrected_x / scale_factor)
            corrected_y = int(corrected_y / scale_factor)
            width = int(width / scale_factor)
            height = int(height / scale_factor)
            print(f"   🔧 应用缩放因子修正: scale = {scale_factor}")
        
        return {
            'x': corrected_x,
            'y': corrected_y,
            'width': width,
            'height': height
        }
        
    except Exception as e:
        print(f"   ❌ 修正建议生成失败: {e}")
        return None

if __name__ == "__main__":
    print()
    print("🎯 UNI控件高亮坐标偏移诊断工具")
    print()
    print("📖 使用说明:")
    print("   1. 将鼠标移动到要检测的控件上")
    print("   2. 运行此工具进行坐标偏移分析")
    print("   3. 工具将显示诊断结果和修正建议")
    print("   4. 观察修正后的高亮效果是否准确对齐")
    print()
    
    input("按回车键开始诊断...")
    print()
    
    diagnose_coordinate_offset()