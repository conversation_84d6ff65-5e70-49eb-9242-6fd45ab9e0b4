#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
使用正确的窗口偏移测试hellobig控件
基于分析得出的偏移量: (564, 155)
"""

import sys
import os
import subprocess
import time
import re
from offset_corrected_detector import OffsetCorrectedDetector


class FinalCorrectTester:
    """使用最终正确偏移的测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.detector = OffsetCorrectedDetector(target_app_name="hellobig")
        
        # 基于分析得出的正确偏移
        self.window_offset = self.get_accurate_window_offset()
        
        # 基于AT-SPI探索的控件位置（这些位置是正确的）
        self.test_controls = [
            {
                'name': '普通按钮',
                'atspi_position': (20, 20),
                'size': (100, 30),
                'type': 'push button'
            },
            {
                'name': '切换按钮',
                'atspi_position': (140, 20),
                'size': (100, 30),
                'type': 'check box'
            },
            {
                'name': '复选框',
                'atspi_position': (20, 71),
                'size': (58, 22),
                'type': 'check box'
            },
            {
                'name': '单选按钮1',
                'atspi_position': (160, 71),
                'size': (54, 22),
                'type': 'radio button'
            },
            {
                'name': '单选按钮2',
                'atspi_position': (280, 71),
                'size': (54, 22),
                'type': 'radio button'
            },
            {
                'name': '用户名输入框',
                'atspi_position': (110, 110),
                'size': (200, 25),
                'type': 'text'
            },
            {
                'name': '密码输入框',
                'atspi_position': (420, 110),
                'size': (200, 25),
                'type': 'password text'
            },
            {
                'name': '音量滑块',
                'atspi_position': (110, 160),
                'size': (200, 20),
                'type': 'slider'
            },
            {
                'name': '数字输入框',
                'atspi_position': (330, 160),
                'size': (80, 25),
                'type': 'spin button'
            },
            {
                'name': '城市下拉框',
                'atspi_position': (110, 210),
                'size': (150, 25),
                'type': 'combo box'
            }
        ]
        
        print("🎯 最终正确偏移测试器")
        print("=" * 60)
        if self.window_offset:
            print(f"🪟 使用窗口偏移: {self.window_offset}")
            print(f"📋 准备测试 {len(self.test_controls)} 个控件")
        else:
            print("❌ 无法获取窗口偏移")
    
    def get_accurate_window_offset(self):
        """获取准确的窗口偏移"""
        try:
            print("🔍 获取准确的窗口偏移...")
            
            # 获取窗口列表
            result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ wlcctrl --list 失败")
                return None
            
            # 查找hellobig窗口ID
            window_id = None
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                    if i > 0:
                        prev_line = lines[i-1]
                        if 'toplevel' in prev_line:
                            window_id = prev_line.split('"')[1]
                            break
            
            if not window_id:
                print("❌ 未找到hellobig窗口")
                return None
            
            # 获取窗口几何信息
            result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ 获取窗口几何信息失败")
                return None
            
            # 解析几何信息
            for line in result.stdout.strip().split('\n'):
                if 'geometry:' in line:
                    match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)', line)
                    if match:
                        x, y = int(match.group(1)), int(match.group(2))
                        print(f"   窗口偏移: ({x}, {y})")
                        return (x, y)
            
            return None
            
        except Exception as e:
            print(f"❌ 获取窗口偏移失败: {e}")
            return None
    
    def test_control_with_correct_offset(self, control_info):
        """使用正确偏移测试控件"""
        atspi_x, atspi_y = control_info['atspi_position']
        width, height = control_info['size']
        
        # 计算AT-SPI中心点
        atspi_center_x = atspi_x + width // 2
        atspi_center_y = atspi_y + height // 2
        
        # 计算真实屏幕位置
        if self.window_offset:
            screen_center_x = self.window_offset[0] + atspi_center_x
            screen_center_y = self.window_offset[1] + atspi_center_y
        else:
            screen_center_x = atspi_center_x
            screen_center_y = atspi_center_y
        
        print(f"\n🎯 测试控件: {control_info['name']}")
        print(f"   类型: {control_info['type']}")
        print(f"   AT-SPI位置: ({atspi_x}, {atspi_y}) 尺寸: {width}×{height}")
        print(f"   AT-SPI中心: ({atspi_center_x}, {atspi_center_y})")
        print(f"   真实屏幕中心: ({screen_center_x}, {screen_center_y})")
        print("-" * 40)
        
        # 使用真实屏幕坐标进行检测
        result = self.detector.get_control_at_point_corrected(screen_center_x, screen_center_y)
        
        if result['success']:
            detected_info = result['control_info']
            print(f"✅ 检测成功!")
            print(f"   检测到名称: {detected_info['name']}")
            print(f"   检测到类型: {detected_info['role']}")
            
            # 验证是否匹配
            name_match = control_info['name'] in detected_info['name'] or detected_info['name'] in control_info['name']
            type_match = control_info['type'] == detected_info['role']
            
            if name_match and type_match:
                print(f"   🎉 完全匹配!")
                return True
            elif name_match:
                print(f"   ⚠️  名称匹配，但类型不匹配 (期望: {control_info['type']}, 实际: {detected_info['role']})")
                return True
            elif type_match:
                print(f"   ⚠️  类型匹配，但名称不匹配 (期望: {control_info['name']}, 实际: {detected_info['name']})")
                return True
            else:
                print(f"   ❌ 不匹配 (期望: {control_info['name']}/{control_info['type']}, 实际: {detected_info['name']}/{detected_info['role']})")
                return False
        else:
            print(f"❌ 检测失败: {result['message']}")
            return False
    
    def test_control_with_highlight(self, control_info):
        """测试控件并高亮显示"""
        atspi_x, atspi_y = control_info['atspi_position']
        width, height = control_info['size']
        
        # 计算AT-SPI中心点
        atspi_center_x = atspi_x + width // 2
        atspi_center_y = atspi_y + height // 2
        
        # 计算真实屏幕位置
        if self.window_offset:
            screen_center_x = self.window_offset[0] + atspi_center_x
            screen_center_y = self.window_offset[1] + atspi_center_y
        else:
            screen_center_x = atspi_center_x
            screen_center_y = atspi_center_y
        
        print(f"\n✨ 高亮测试控件: {control_info['name']}")
        print(f"   真实屏幕中心: ({screen_center_x}, {screen_center_y})")
        
        # 执行高亮
        result = self.detector.highlight_control_corrected(screen_center_x, screen_center_y, duration=2, color='blue', border_width=3)
        
        if result['success'] and result['highlighted']:
            print(f"✅ 高亮成功!")
            detected_info = result['control_info']
            print(f"   检测到: {detected_info['name']} ({detected_info['role']})")
            return True
        else:
            print(f"❌ 高亮失败: {result['message']}")
            return False
    
    def test_all_controls(self):
        """测试所有控件"""
        if not self.window_offset:
            print("❌ 无法进行测试，未获取到窗口偏移")
            return 0, 0
        
        print("🚀 开始测试所有控件（使用正确窗口偏移）...")
        
        success_count = 0
        total_count = len(self.test_controls)
        
        for i, control in enumerate(self.test_controls):
            print(f"\n📍 测试 {i+1}/{total_count}")
            
            if self.test_control_with_correct_offset(control):
                success_count += 1
        
        print(f"\n📊 测试结果统计:")
        print("=" * 40)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        return success_count, total_count
    
    def test_with_highlights(self):
        """测试并高亮显示所有控件"""
        if not self.window_offset:
            print("❌ 无法进行测试，未获取到窗口偏移")
            return 0, 0
        
        print("\n✨ 开始高亮测试（使用正确窗口偏移）...")
        
        success_count = 0
        total_count = len(self.test_controls)
        
        for i, control in enumerate(self.test_controls):
            print(f"\n🎯 高亮测试 {i+1}/{total_count}")
            
            if self.test_control_with_highlight(control):
                success_count += 1
            
            # 等待一下，让用户看到高亮效果
            time.sleep(1.5)
        
        print(f"\n📊 高亮测试结果:")
        print("=" * 40)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        return success_count, total_count
    
    def interactive_test(self):
        """交互式测试"""
        print("\n🎮 交互式测试模式（最终正确偏移）")
        print("=" * 40)
        print("选择测试模式:")
        print("1. 仅检测测试")
        print("2. 高亮测试")
        print("3. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-3): ").strip()
                
                if choice == '1':
                    self.test_all_controls()
                elif choice == '2':
                    self.test_with_highlights()
                elif choice == '3':
                    print("👋 退出测试")
                    break
                else:
                    print("❌ 无效选择，请输入 1-3")
                    
            except KeyboardInterrupt:
                print("\n👋 退出测试")
                break
            except Exception as e:
                print(f"❌ 测试异常: {e}")


def main():
    """主函数"""
    print("🎯 最终正确偏移的hellobig控件测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        tester = FinalCorrectTester()
        tester.interactive_test()
        
    except Exception as e:
        print(f"❌ 测试器启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
