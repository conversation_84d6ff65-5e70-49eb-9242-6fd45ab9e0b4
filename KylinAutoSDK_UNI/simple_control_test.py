#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
简单的控件检测测试脚本
用于基本功能测试和演示

使用方法:
1. 运行脚本: python3 simple_control_test.py
2. 按照提示输入坐标或选择测试模式
"""

import sys
import time
import subprocess
from at_spi_control_detector import ATSPIControlDetector, print_control_info_at_point, highlight_control_at_point


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None


def test_basic_detection():
    """测试基本的控件检测功能"""
    print("🔍 基本控件检测测试")
    print("=" * 50)
    
    # 获取鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        print(f"当前鼠标位置: ({mouse_x}, {mouse_y})")
        test_x, test_y = mouse_x, mouse_y
    else:
        print("无法获取鼠标位置，使用默认坐标")
        test_x, test_y = 500, 300
    
    # 创建检测器
    detector = ATSPIControlDetector()
    
    # 测试1: 基本检测
    print(f"\n📍 测试坐标: ({test_x}, {test_y})")
    result = detector.get_control_at_point(test_x, test_y)
    
    if result['success']:
        print("✅ 控件检测成功！")
        print(f"   位置: {result['position']}")
        
        # 测试2: 高亮控件
        print("\n✨ 测试高亮功能...")
        highlight_result = detector.highlight_control_at_point(test_x, test_y, duration=2, color='red')
        if highlight_result['success']:
            print("✅ 高亮测试成功！")
        else:
            print(f"❌ 高亮测试失败: {highlight_result['message']}")
    else:
        print(f"❌ 控件检测失败: {result['message']}")
    
    # 测试3: 详细信息
    print(f"\n📋 获取详细信息...")
    detector.print_control_info(test_x, test_y)


def test_multiple_positions():
    """测试多个位置的控件检测"""
    print("🎯 多位置控件检测测试")
    print("=" * 50)
    
    # 获取屏幕中心附近的几个测试点
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        # 以当前鼠标位置为中心的测试点
        test_positions = [
            (mouse_x, mouse_y),
            (mouse_x - 50, mouse_y),
            (mouse_x + 50, mouse_y),
            (mouse_x, mouse_y - 50),
            (mouse_x, mouse_y + 50)
        ]
    else:
        # 默认测试点
        test_positions = [
            (500, 300),
            (600, 300),
            (700, 300),
            (500, 400),
            (600, 400)
        ]
    
    detector = ATSPIControlDetector()
    colors = ['red', 'blue', 'green', 'yellow', 'purple']
    
    for i, (x, y) in enumerate(test_positions):
        color = colors[i % len(colors)]
        print(f"\n📍 测试点 {i+1}: ({x}, {y})")
        
        result = detector.get_control_at_point(x, y)
        if result['success']:
            print(f"✅ 检测成功，高亮颜色: {color}")
            detector.highlight_control_at_point(x, y, duration=1, color=color)
        else:
            print(f"❌ 检测失败: {result['message']}")
        
        time.sleep(0.5)


def interactive_test():
    """交互式测试"""
    print("🎮 交互式控件检测测试")
    print("=" * 50)
    
    detector = ATSPIControlDetector()
    
    while True:
        print("\n选择操作:")
        print("1. 检测当前鼠标位置的控件")
        print("2. 输入坐标进行检测")
        print("3. 高亮最后检测到的控件")
        print("4. 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == '1':
            mouse_x, mouse_y = get_mouse_position()
            if mouse_x is not None:
                print(f"\n🔍 检测鼠标位置: ({mouse_x}, {mouse_y})")
                detector.print_control_info(mouse_x, mouse_y)
                
                highlight = input("\n是否高亮此控件? (y/n): ").strip().lower()
                if highlight == 'y':
                    detector.highlight_control_at_point(mouse_x, mouse_y, duration=2)
            else:
                print("❌ 无法获取鼠标位置")
        
        elif choice == '2':
            try:
                x = int(input("请输入X坐标: "))
                y = int(input("请输入Y坐标: "))
                
                print(f"\n🔍 检测坐标: ({x}, {y})")
                detector.print_control_info(x, y)
                
                highlight = input("\n是否高亮此控件? (y/n): ").strip().lower()
                if highlight == 'y':
                    color = input("请输入颜色 (red/blue/green/yellow/purple): ").strip().lower()
                    if color not in ['red', 'blue', 'green', 'yellow', 'purple']:
                        color = 'red'
                    detector.highlight_control_at_point(x, y, duration=2, color=color)
                    
            except ValueError:
                print("❌ 请输入有效的数字")
        
        elif choice == '3':
            print("\n🔄 高亮最后检测到的控件...")
            result = detector.highlight_last_detected_control(duration=2)
            print(f"结果: {result['message']}")
        
        elif choice == '4':
            print("👋 退出测试")
            break
        
        else:
            print("❌ 无效选项，请重新输入")


def main():
    """主函数"""
    print("🎯 AT-SPI控件检测测试程序")
    print("=" * 60)
    
    # 检查环境
    if not sys.platform.startswith('linux'):
        print("❌ 此程序仅支持Linux系统")
        sys.exit(1)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量，请在图形界面环境中运行")
        sys.exit(1)
    
    while True:
        print("\n选择测试模式:")
        print("1. 基本功能测试")
        print("2. 多位置检测测试")
        print("3. 交互式测试")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            test_basic_detection()
        elif choice == '2':
            test_multiple_positions()
        elif choice == '3':
            interactive_test()
        elif choice == '4':
            print("👋 程序结束")
            break
        else:
            print("❌ 无效选项，请重新输入")


if __name__ == "__main__":
    import os
    main()