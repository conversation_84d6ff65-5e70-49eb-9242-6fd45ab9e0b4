#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
hellobig应用控件探索器
遍历并分析hellobig应用中的所有控件
"""

import sys
import os
import pyatspi
import time


class HellobigControlExplorer:
    """hellobig控件探索器"""
    
    def __init__(self):
        """初始化探索器"""
        self.all_controls = []
        self.app_found = False
        # 更精确的目标应用名称匹配
        self.target_app_names = ['hellobig', 'HelloBig', 'HELLOBIG']
        self.target_window_names = ['hellobig', 'HelloBig', 'MainWindow', 'Widget']

        print("🔍 hellobig控件探索器")
        print("=" * 60)
    
    def find_hellobig_app(self):
        """查找hellobig应用"""
        print("🔍 搜索hellobig应用...")
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                print(f"   检查应用: {app_name}")

                # 检查是否是目标应用（排除Qt Creator）
                if 'qtcreator' not in app_name.lower() and 'qt creator' not in app_name.lower():
                    for target_name in self.target_app_names:
                        if target_name.lower() in app_name.lower():
                            print(f"✅ 找到目标应用: {app_name}")
                            self.app_found = True
                            return app

                # 也检查窗口名称（排除Qt Creator相关窗口）
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        window_name = window.name or ""

                        # 排除Qt Creator窗口
                        if ('qt creator' not in window_name.lower() and
                            'qtcreator' not in window_name.lower() and
                            '.ui @' not in window_name.lower()):

                            for target_name in self.target_window_names:
                                if target_name.lower() in window_name.lower():
                                    print(f"✅ 通过窗口名称找到目标应用: {app_name} -> {window_name}")
                                    self.app_found = True
                                    return app
                    except Exception:
                        continue
            
            print("❌ 未找到hellobig应用")
            print("📋 当前运行的应用列表:")
            
            # 重新遍历显示所有应用
            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)
                    app_name = app.name or f"应用{i}"
                    print(f"   {i+1}. {app_name}")
                    
                    # 显示窗口
                    for j in range(min(app.childCount, 3)):  # 最多显示3个窗口
                        try:
                            window = app.getChildAtIndex(j)
                            window_name = window.name or "无名窗口"
                            print(f"      └─ 窗口: {window_name}")
                        except Exception:
                            continue
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            print(f"❌ 搜索应用失败: {e}")
            return None
    
    def explore_controls_recursive(self, element, depth=0, max_depth=6, parent_path=""):
        """递归探索控件"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标信息
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 获取状态信息
            states = []
            try:
                state = element.getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass
            
            # 获取动作信息
            actions = []
            try:
                if hasattr(element, 'queryAction'):
                    action = element.queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass
            
            # 获取文本信息
            text_content = ""
            try:
                if hasattr(element, 'queryText'):
                    text = element.queryText()
                    if text:
                        text_content = text.getText(0, -1)[:50]  # 最多50字符
            except Exception:
                pass
            
            # 构建路径
            current_path = f"{parent_path}/{name}" if parent_path else name
            
            # 记录控件信息
            control_info = {
                'element': element,
                'name': name,
                'role': role,
                'depth': depth,
                'path': current_path,
                'extents': extents,
                'states': states,
                'actions': actions,
                'text_content': text_content,
                'child_count': element.childCount if hasattr(element, 'childCount') else 0
            }
            
            self.all_controls.append(control_info)
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self.explore_controls_recursive(child, depth + 1, max_depth, current_path)
            except Exception:
                pass
                
        except Exception as e:
            print(f"   探索控件时出错 (深度{depth}): {e}")
    
    def print_control_summary(self):
        """打印控件摘要"""
        print(f"\n📊 控件统计摘要:")
        print("=" * 60)
        print(f"总控件数量: {len(self.all_controls)}")
        
        # 按角色分类统计
        role_counts = {}
        for ctrl in self.all_controls:
            role = ctrl['role']
            role_counts[role] = role_counts.get(role, 0) + 1
        
        print(f"\n📋 按角色分类:")
        for role, count in sorted(role_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   {role}: {count}")
        
        # 按深度分类统计
        depth_counts = {}
        for ctrl in self.all_controls:
            depth = ctrl['depth']
            depth_counts[depth] = depth_counts.get(depth, 0) + 1
        
        print(f"\n📏 按深度分类:")
        for depth in sorted(depth_counts.keys()):
            print(f"   深度 {depth}: {depth_counts[depth]} 个控件")
    
    def print_detailed_controls(self, max_controls=20):
        """打印详细控件信息"""
        print(f"\n📋 详细控件信息 (显示前{max_controls}个):")
        print("=" * 80)
        
        for i, ctrl in enumerate(self.all_controls[:max_controls]):
            print(f"\n{i+1}. 控件信息:")
            print(f"   名称: {ctrl['name']}")
            print(f"   角色: {ctrl['role']}")
            print(f"   深度: {ctrl['depth']}")
            print(f"   路径: {ctrl['path']}")
            print(f"   子控件数: {ctrl['child_count']}")
            
            if ctrl['extents']:
                ext = ctrl['extents']
                print(f"   坐标: ({ext.x}, {ext.y}) 尺寸: {ext.width}×{ext.height}")
            else:
                print(f"   坐标: 无坐标信息")
            
            if ctrl['states']:
                print(f"   状态: {', '.join(ctrl['states'][:3])}")
            
            if ctrl['actions']:
                print(f"   动作: {', '.join(ctrl['actions'])}")
            
            if ctrl['text_content']:
                print(f"   文本: {ctrl['text_content']}")
            
            print("-" * 40)
    
    def find_interactive_controls(self):
        """查找可交互的控件"""
        print(f"\n🎯 可交互控件分析:")
        print("=" * 60)
        
        interactive_roles = ['push button', 'button', 'text', 'entry', 'combo box', 
                           'check box', 'radio button', 'menu item', 'list item',
                           'tree item', 'tab', 'slider', 'spin button']
        
        interactive_controls = []
        for ctrl in self.all_controls:
            if (ctrl['role'] in interactive_roles or 
                any(action in ['click', 'press', 'activate'] for action in ctrl['actions']) or
                'focusable' in ctrl['states']):
                interactive_controls.append(ctrl)
        
        print(f"找到 {len(interactive_controls)} 个可交互控件:")
        
        for i, ctrl in enumerate(interactive_controls):
            print(f"\n{i+1}. {ctrl['name']} ({ctrl['role']})")
            if ctrl['extents']:
                ext = ctrl['extents']
                print(f"   位置: ({ext.x}, {ext.y}) 尺寸: {ext.width}×{ext.height}")
            if ctrl['actions']:
                print(f"   动作: {', '.join(ctrl['actions'])}")
            if 'focusable' in ctrl['states']:
                print(f"   ✅ 可获得焦点")
        
        return interactive_controls
    
    def run_exploration(self):
        """运行探索"""
        print("🚀 开始探索hellobig应用控件...")
        
        # 查找应用
        app = self.find_hellobig_app()
        if not app:
            print("❌ 无法找到hellobig应用，请确保应用正在运行")
            return False
        
        print(f"✅ 开始探索应用: {app.name}")
        
        # 探索所有控件
        print("🔍 递归探索所有控件...")
        self.explore_controls_recursive(app)
        
        # 打印结果
        self.print_control_summary()
        self.print_detailed_controls()
        
        # 分析可交互控件
        interactive_controls = self.find_interactive_controls()
        
        print(f"\n✅ 探索完成!")
        print(f"   总控件数: {len(self.all_controls)}")
        print(f"   可交互控件数: {len(interactive_controls)}")
        
        return True


def main():
    """主函数"""
    print("🔍 hellobig应用控件探索器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        explorer = HellobigControlExplorer()
        success = explorer.run_exploration()
        
        if not success:
            print("\n💡 提示:")
            print("   1. 确保hellobig应用正在运行")
            print("   2. 确保应用窗口可见")
            print("   3. 尝试重新启动应用")
            
    except Exception as e:
        print(f"❌ 探索失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
