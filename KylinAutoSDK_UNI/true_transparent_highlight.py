#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
真正透明的高亮 - 只绘制边框线条，完全无背景
"""

import subprocess
import os

def true_transparent_highlight(x, y, width, height, 
                              duration=2,
                              color='red', 
                              border_width=2):
    """
    创建真正透明的高亮 - 只有边框线条，无任何背景
    
    使用四个独立的线条窗口来构成边框，避免任何背景填充
    """
    
    # 创建四条边框线的脚本
    x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    # 连接到X11
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取颜色
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    border_w = {border_width}
    
    # 创建四个边框窗口（上、下、左、右）
    windows = []
    
    # 上边框
    top_win = root.create_window(
        {x}, {y}, {width}, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,  # 边框颜色
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(top_win)
    
    # 下边框
    bottom_win = root.create_window(
        {x}, {y + height - border_w}, {width}, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(bottom_win)
    
    # 左边框
    left_win = root.create_window(
        {x}, {y}, border_w, {height},
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(left_win)
    
    # 右边框
    right_win = root.create_window(
        {x + width - border_w}, {y}, border_w, {height},
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(right_win)
    
    # 设置所有窗口属性
    for win in windows:
        win.set_wm_name("Border Line")
        win.set_wm_class("border", "BorderLine")
        
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_WINDOW_TYPE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
            )
            win.change_property(
                disp.intern_atom("_NET_WM_STATE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
                 disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
                 disp.intern_atom("_NET_WM_STATE_ABOVE")]
            )
        except:
            pass
    
    # 显示所有边框
    for win in windows:
        win.map()
    disp.sync()
    
    # 等待指定时间
    time.sleep({duration})
    
    # 清理所有窗口
    for win in windows:
        win.unmap()
        win.destroy()
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    exit(1)
'''
    
    try:
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def minimal_border_highlight(x, y, width, height, 
                            duration=2,
                            color='red', 
                            border_width=1):
    """
    最小化边框高亮 - 使用绘制方法创建真正的线条
    """
    
    draw_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取颜色
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 创建一个大的透明窗口用于绘制
    canvas_win = root.create_window(
        {x - 5}, {y - 5}, {width + 10}, {height + 10},
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,  # 完全透明背景
        override_redirect=True,
        colormap=X.CopyFromParent,
        event_mask=X.ExposureMask
    )
    
    # 设置窗口属性
    canvas_win.set_wm_name("Canvas")
    try:
        canvas_win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
    except:
        pass
    
    # 创建绘图上下文
    gc = canvas_win.create_gc(
        foreground=color_pixel,
        background=0,
        line_width={border_width}
    )
    
    canvas_win.map()
    disp.sync()
    
    # 绘制矩形边框（相对于画布坐标）
    border_x = 5
    border_y = 5
    
    # 绘制四条边
    canvas_win.rectangle(gc, border_x, border_y, {width}, {height})
    
    disp.sync()
    
    time.sleep({duration})
    
    canvas_win.unmap()
    canvas_win.destroy()
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    exit(1)
'''
    
    try:
        result = subprocess.run(['python3', '-c', draw_script], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def test_true_transparent():
    """测试真正透明的高亮效果"""
    print("🎯 真正透明高亮测试")
    print("=" * 60)
    
    # 获取鼠标位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    test_x = mouse_x - 100
    test_y = mouse_y - 75
    test_w = 200
    test_h = 150
    
    print(f"测试位置: ({test_x}, {test_y}) {test_w}×{test_h}")
    print()
    
    print("✅ 真正透明高亮特点:")
    print("   • 完全无背景，只有边框线条")
    print("   • 中间区域100%透明")
    print("   • 不遮挡任何内容")
    print("   • 完全无窗口装饰")
    print()
    
    # 测试1: 四个独立边框窗口
    print("🔍 测试1: 四个独立边框窗口方法")
    print("   • 使用四个独立的线条窗口")
    print("   • 中间完全没有窗口覆盖")
    success1 = true_transparent_highlight(test_x, test_y, test_w, test_h,
                                        duration=3, color='red', border_width=2)
    if success1:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    # 测试2: 绘制方法
    print("🔍 测试2: 画布绘制方法")
    print("   • 在透明画布上绘制边框")
    print("   • 只有边框线条可见")
    success2 = minimal_border_highlight(test_x, test_y, test_w, test_h,
                                      duration=3, color='blue', border_width=1)
    if success2:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    print("=" * 60)
    print("🎊 真正透明测试完成！")
    print("\n💡 如果这次还是有黑色背景:")
    print("   • 可能是系统的合成器问题")
    print("   • 或者需要启用窗口透明效果")
    print("   • 第一种方法应该是最干净的")

if __name__ == "__main__":
    test_true_transparent()