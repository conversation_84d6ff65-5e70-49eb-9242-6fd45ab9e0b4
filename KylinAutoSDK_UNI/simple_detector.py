#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
简化版控件检测器
移除所有复杂逻辑，使用最基本的AT-SPI检测

目标: 找出为什么无法检测到hellobig程序的控件
"""

import sys
import os
import subprocess
import pyatspi
import time


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def simple_atspi_scan(target_x, target_y):
    """最简单的AT-SPI扫描"""
    print(f"🔍 简单扫描坐标 ({target_x}, {target_y})")
    print("=" * 50)
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        print(f"📱 桌面应用数量: {desktop.childCount}")
        
        all_elements = []
        
        # 遍历所有应用
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                print(f"\n📦 应用 {i}: {app_name} (子窗口: {app.childCount})")
                
                # 收集这个应用的所有元素
                app_elements = []
                collect_all_elements(app, app_elements, max_depth=4)
                
                print(f"   总共找到 {len(app_elements)} 个元素")
                
                # 检查哪些元素包含目标坐标
                matching_elements = []
                for element_info in app_elements:
                    if element_info['extents']:
                        ext = element_info['extents']
                        if (ext.x <= target_x < ext.x + ext.width and
                            ext.y <= target_y < ext.y + ext.height):
                            matching_elements.append(element_info)
                
                if matching_elements:
                    print(f"   🎯 匹配坐标的元素: {len(matching_elements)} 个")
                    for j, elem in enumerate(matching_elements[:5]):  # 只显示前5个
                        ext = elem['extents']
                        print(f"     {j+1}. {elem['name']} ({elem['role']}) - "
                              f"({ext.x}, {ext.y}) {ext.width}×{ext.height}")
                    
                    all_elements.extend(matching_elements)
                
            except Exception as e:
                print(f"   ❌ 处理应用 {i} 失败: {e}")
                continue
        
        print(f"\n📊 总结:")
        print(f"   总匹配元素: {len(all_elements)} 个")
        
        if all_elements:
            # 选择最小的元素
            best = min(all_elements, key=lambda x: x['extents'].width * x['extents'].height)
            print(f"   🏆 最佳匹配: {best['name']} ({best['role']})")
            
            # 尝试高亮
            try_highlight(best['extents'])
            
            return best
        else:
            print("   ❌ 未找到任何匹配元素")
            
            # 诊断信息
            print("\n🔧 诊断信息:")
            print("   可能的原因:")
            print("   1. 目标程序不支持AT-SPI")
            print("   2. 坐标计算有误差")
            print("   3. 元素被遮挡或不可见")
            print("   4. 需要特殊权限")
            
    except Exception as e:
        print(f"❌ 扫描失败: {e}")
        import traceback
        traceback.print_exc()
    
    return None


def collect_all_elements(element, element_list, depth=0, max_depth=4):
    """收集所有元素信息"""
    if depth > max_depth:
        return
    
    try:
        # 获取基本信息
        name = element.name or "N/A"
        role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
        
        # 获取位置信息
        extents = None
        try:
            if hasattr(element, 'queryComponent'):
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
        except Exception:
            pass
        
        # 添加到列表
        element_list.append({
            'element': element,
            'name': name,
            'role': role,
            'extents': extents,
            'depth': depth
        })
        
        # 递归处理子元素
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                collect_all_elements(child, element_list, depth + 1, max_depth)
        except Exception:
            pass
            
    except Exception:
        pass


def try_highlight(extents):
    """尝试高亮元素"""
    if not extents:
        return
    
    print(f"\n✨ 尝试高亮: ({extents.x}, {extents.y}) {extents.width}×{extents.height}")
    
    try:
        from ultimate_highlight import ultimate_highlight
        result = ultimate_highlight(extents.x, extents.y, extents.width, extents.height, 
                                  duration=3, color='red', border_width=3)
        print(f"   高亮结果: {'成功' if result else '失败'}")
    except Exception as e:
        print(f"   高亮异常: {e}")


def monitor_mouse_detection():
    """监控鼠标位置并实时检测"""
    print("🖱️  鼠标监控模式")
    print("按Ctrl+C退出")
    print("=" * 50)
    
    last_pos = None
    last_detection_time = 0
    
    try:
        while True:
            current_pos = get_mouse_position()
            
            if current_pos != (None, None) and current_pos != last_pos:
                current_time = time.time()
                
                # 冷却时间2秒
                if current_time - last_detection_time > 2.0:
                    x, y = current_pos
                    print(f"\n🎯 检测位置: ({x}, {y})")
                    
                    result = simple_atspi_scan(x, y)
                    
                    last_pos = current_pos
                    last_detection_time = current_time
            
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n🛑 监控结束")


def show_all_visible_elements():
    """显示所有可见元素"""
    print("👁️  显示所有可见元素")
    print("=" * 50)
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        all_visible = []
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_elements = []
            collect_all_elements(app, app_elements, max_depth=3)
            
            # 筛选可见元素
            for elem in app_elements:
                if elem['extents'] and elem['extents'].width > 0 and elem['extents'].height > 0:
                    # 检查是否可见
                    try:
                        state = elem['element'].getState()
                        is_visible = any(state.contains(s) for s in [pyatspi.STATE_SHOWING, pyatspi.STATE_VISIBLE])
                        
                        if is_visible:
                            all_visible.append(elem)
                    except Exception:
                        # 如果无法获取状态，就假设可见
                        all_visible.append(elem)
        
        print(f"📊 找到 {len(all_visible)} 个可见元素")
        
        # 按面积排序
        all_visible.sort(key=lambda x: x['extents'].width * x['extents'].height)
        
        # 显示前20个
        for i, elem in enumerate(all_visible[:20]):
            ext = elem['extents']
            print(f"{i+1:2d}. {elem['name'][:20]:20s} ({elem['role'][:10]:10s}) "
                  f"({ext.x:4d}, {ext.y:4d}) {ext.width:3d}×{ext.height:3d}")
        
        if len(all_visible) > 20:
            print(f"... 还有 {len(all_visible) - 20} 个元素")
            
    except Exception as e:
        print(f"❌ 显示元素失败: {e}")


def test_specific_apps():
    """测试特定应用的检测"""
    print("🧪 测试特定应用")
    print("=" * 50)
    
    target_apps = ['hello', 'big', 'test', 'demo', 'qt', 'gtk']
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = (app.name or "").lower()
            
            # 检查是否是目标应用
            is_target = any(target in app_name for target in target_apps)
            
            if is_target or not app_name:  # 也检查无名应用
                print(f"\n🎯 检查应用: {app.name or '无名应用'}")
                
                app_elements = []
                collect_all_elements(app, app_elements, max_depth=4)
                
                print(f"   元素总数: {len(app_elements)}")
                
                # 统计元素类型
                role_counts = {}
                for elem in app_elements:
                    role = elem['role']
                    role_counts[role] = role_counts.get(role, 0) + 1
                
                print("   元素类型分布:")
                for role, count in sorted(role_counts.items()):
                    print(f"     {role}: {count}")
                
                # 显示有名称的元素
                named_elements = [e for e in app_elements if e['name'] != "N/A"]
                if named_elements:
                    print("   有名称的元素:")
                    for elem in named_elements[:5]:
                        ext = elem['extents']
                        if ext:
                            print(f"     {elem['name']} ({elem['role']}) - "
                                  f"({ext.x}, {ext.y}) {ext.width}×{ext.height}")
                        else:
                            print(f"     {elem['name']} ({elem['role']}) - 无位置信息")
                            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    """主函数"""
    print("🔍 简化版控件检测器")
    print("=" * 50)
    
    # 环境检查
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    while True:
        print("\n选择测试模式:")
        print("1. 检测当前鼠标位置")
        print("2. 鼠标监控模式")
        print("3. 显示所有可见元素")
        print("4. 测试特定应用")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            mouse_x, mouse_y = get_mouse_position()
            if mouse_x is not None:
                simple_atspi_scan(mouse_x, mouse_y)
            else:
                print("❌ 无法获取鼠标位置")
                
        elif choice == '2':
            monitor_mouse_detection()
            
        elif choice == '3':
            show_all_visible_elements()
            
        elif choice == '4':
            test_specific_apps()
            
        elif choice == '5':
            print("👋 退出")
            break
            
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()