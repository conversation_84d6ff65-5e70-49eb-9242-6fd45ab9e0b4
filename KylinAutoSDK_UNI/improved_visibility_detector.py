#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
改进的可见性检测器
使用多种方法验证窗口是否真的可见
"""

import sys
import os
import subprocess
import pyatspi
import re


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def is_window_actually_visible(window_info, mouse_x, mouse_y):
    """综合判断窗口是否真的可见"""
    window_title = window_info['title']
    window_pos = window_info['position']
    window_size = window_info['size']
    
    print(f"   🔍 验证窗口可见性: {window_title}")
    print(f"      位置: {window_pos}, 尺寸: {window_size}")
    
    # 检查1: 异常尺寸检测
    width, height = window_size
    if width >= 2560 or height >= 1600:  # 接近或等于全屏尺寸
        print(f"      ❌ 窗口尺寸异常 ({width}×{height})，可能是最小化窗口的错误报告")
        return False
    
    # 检查2: 异常位置检测
    x, y = window_pos
    if x == 0 and y == 0 and (width >= 1920 or height >= 1080):
        print(f"      ❌ 窗口在(0,0)且尺寸很大，可能是最小化窗口")
        return False
    
    # 检查3: 通过AT-SPI验证窗口状态
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or ""
            
            # 查找匹配的应用
            if window_titles_match(window_title, "", app_name):
                for j in range(app.childCount):
                    try:
                        atspi_window = app.getChildAtIndex(j)
                        window_name = atspi_window.name or ""
                        
                        if window_titles_match(window_title, window_name, app_name):
                            # 检查AT-SPI窗口状态
                            try:
                                if hasattr(atspi_window, 'getState'):
                                    state = atspi_window.getState()
                                    
                                    # 检查是否最小化（使用正确的常量）
                                    if hasattr(pyatspi, 'STATE_ICONIFIED') and state.contains(pyatspi.STATE_ICONIFIED):
                                        print(f"      ❌ AT-SPI检测到窗口已最小化")
                                        return False
                                    
                                    # 检查是否可见
                                    if hasattr(pyatspi, 'STATE_VISIBLE') and not state.contains(pyatspi.STATE_VISIBLE):
                                        print(f"      ❌ AT-SPI检测到窗口不可见")
                                        return False
                                    
                                    # 检查是否显示中
                                    if hasattr(pyatspi, 'STATE_SHOWING') and not state.contains(pyatspi.STATE_SHOWING):
                                        print(f"      ❌ AT-SPI检测到窗口未显示")
                                        return False
                                
                                # 检查AT-SPI几何信息是否合理
                                if hasattr(atspi_window, 'queryComponent'):
                                    component = atspi_window.queryComponent()
                                    if component:
                                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                        
                                        # 如果AT-SPI报告的尺寸与wlcctrl差异太大，可能有问题
                                        atspi_width, atspi_height = extents.width, extents.height
                                        wlc_width, wlc_height = window_size
                                        
                                        width_diff = abs(atspi_width - wlc_width)
                                        height_diff = abs(atspi_height - wlc_height)
                                        
                                        if width_diff > 100 or height_diff > 100:
                                            print(f"      ⚠️  AT-SPI尺寸 ({atspi_width}×{atspi_height}) 与wlcctrl尺寸差异过大")
                                            # 但不直接返回False，因为这可能是正常的
                                        
                                        print(f"      ✅ AT-SPI验证通过: {window_name}")
                                        return True
                                
                            except Exception as e:
                                print(f"      ⚠️  AT-SPI状态检查异常: {e}")
                                # 继续其他检查
                                
                    except Exception:
                        continue
                break
    except Exception as e:
        print(f"      ⚠️  AT-SPI验证失败: {e}")
    
    # 检查4: 实用性检查 - 窗口是否在合理的位置和尺寸范围内
    if (x < -width or y < -height or  # 完全在屏幕外
        x > 3000 or y > 2000 or       # 远超常见屏幕范围
        width <= 0 or height <= 0 or  # 无效尺寸
        width > 2560 or height > 1600): # 超大尺寸
        print(f"      ❌ 窗口位置或尺寸不合理")
        return False
    
    # 检查5: 如果是特定的问题应用，使用特殊逻辑
    if 'hellobig' in window_title.lower() or 'AT-SPI测试' in window_title:
        # 对于hellobig，如果报告全屏或接近全屏，很可能是最小化的
        if width >= 2000 or height >= 1200:
            print(f"      ❌ hellobig窗口报告异常大尺寸，可能已最小化")
            return False
        
        # 如果位置在(0,0)且尺寸很大，也可能是最小化的
        if x == 0 and y == 0 and (width > 1500 or height > 1000):
            print(f"      ❌ hellobig窗口在原点且尺寸大，可能已最小化")
            return False
    
    print(f"      ✅ 窗口通过可见性验证")
    return True


def window_titles_match(real_title, atspi_title, app_name):
    """检查窗口标题是否匹配"""
    if not real_title:
        return False
    
    # 应用名称匹配
    if app_name and any(keyword in app_name.lower() for keyword in real_title.lower().split() if len(keyword) > 3):
        return True
    
    # 标题匹配
    if atspi_title and (real_title.lower() in atspi_title.lower() or atspi_title.lower() in real_title.lower()):
        return True
    
    # 特殊匹配
    if 'hellobig' in real_title.lower() and 'hellobig' in app_name.lower():
        return True
    
    return False


def get_visible_windows_at_mouse(mouse_x, mouse_y):
    """获取鼠标位置下真正可见的窗口"""
    print(f"🔍 获取鼠标位置 ({mouse_x}, {mouse_y}) 下的可见窗口")
    
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            return []
        
        visible_windows = []
        lines = result.stdout.strip().split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                # 获取几何信息
                geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                          capture_output=True, text=True)
                if geo_result.returncode == 0:
                    for geo_line in geo_result.stdout.strip().split('\n'):
                        if 'geometry:' in geo_line:
                            match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                            if match:
                                x, y, width, height = map(int, match.groups())
                                
                                # 检查鼠标是否在窗口范围内
                                if (x <= mouse_x < x + width and y <= mouse_y < y + height):
                                    window_info = {
                                        'window_id': window_id,
                                        'title': window_title,
                                        'position': (x, y),
                                        'size': (width, height),
                                        'area': width * height
                                    }
                                    
                                    # 验证窗口是否真的可见
                                    if is_window_actually_visible(window_info, mouse_x, mouse_y):
                                        visible_windows.append(window_info)
                                    else:
                                        print(f"   ❌ 窗口 {window_title} 未通过可见性验证")
                                
                                break
                
                i += 2
            else:
                i += 1
        
        return visible_windows
        
    except Exception as e:
        print(f"❌ 获取可见窗口失败: {e}")
        return []


def main():
    """主函数"""
    print("🔍 改进的窗口可见性检测器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    mouse_x, mouse_y = get_mouse_position()
    if not mouse_x:
        print("❌ 无法获取鼠标位置")
        return
    
    print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 获取真正可见的窗口
    visible_windows = get_visible_windows_at_mouse(mouse_x, mouse_y)
    
    print(f"\n📋 鼠标下的真正可见窗口:")
    print("=" * 40)
    
    if not visible_windows:
        print("❌ 未找到可见窗口")
    else:
        print(f"✅ 找到 {len(visible_windows)} 个可见窗口:")
        for i, window in enumerate(visible_windows):
            print(f"   {i+1}. {window['title']}")
            print(f"      位置: {window['position']}")
            print(f"      尺寸: {window['size'][0]}×{window['size'][1]}")
            print(f"      面积: {window['area']}")
            
            # 特别标记hellobig窗口
            if 'hellobig' in window['title'].lower() or 'AT-SPI测试' in window['title']:
                print(f"      🎯 这是hellobig窗口，已通过可见性验证")
    
    print(f"\n💡 测试结果:")
    print("=" * 30)
    if any('hellobig' in w['title'].lower() or 'AT-SPI测试' in w['title'] for w in visible_windows):
        print("⚠️  hellobig窗口被检测为可见")
        print("   如果实际上它是最小化的，说明检测逻辑仍需改进")
    else:
        print("✅ hellobig窗口未被检测到")
        print("   如果它确实是最小化的，这是正确的结果")


if __name__ == "__main__":
    main()
