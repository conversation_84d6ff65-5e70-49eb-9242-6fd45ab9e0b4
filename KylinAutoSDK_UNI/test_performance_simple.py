#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
简化的性能测试脚本
仅测试核心功能性能
"""

import sys
import os
import time
import subprocess

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return 500, 300
    except:
        return 500, 300

def test_original_performance():
    """测试原版性能"""
    print("🔍 测试原版 UNI_new 性能")
    print("=" * 40)
    
    try:
        from UNI_new import UNI
        
        mouse_x, mouse_y = get_mouse_position()
        print(f"测试坐标: ({mouse_x}, {mouse_y})")
        
        uni = UNI()
        
        # 测试3次
        times = []
        for i in range(3):
            start_time = time.time()
            control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
            elapsed = time.time() - start_time
            times.append(elapsed)
            
            status = "✅" if control_data else "❌"
            name = control_data.get('Name', 'N/A') if control_data else 'N/A'
            print(f"  测试{i+1}: {status} {elapsed:.2f}s - {name}")
        
        avg_time = sum(times) / len(times)
        print(f"\n📊 原版平均时间: {avg_time:.2f}s")
        return avg_time
        
    except Exception as e:
        print(f"❌ 原版测试失败: {e}")
        return None

def test_optimized_performance():
    """测试优化版性能"""
    print("\n⚡ 测试优化版 UNI_Optimized 性能")
    print("=" * 40)
    
    try:
        # 先尝试导入优化版本所需的依赖
        try:
            from uni_sdk.utils.helpers import get_windows_by_wlcctrl, get_element_extents
            print("✅ 成功导入依赖模块")
        except ImportError as e:
            print(f"❌ 缺少依赖模块: {e}")
            print("优化版本需要完整的uni_sdk环境")
            return None
        
        from UNI_optimized import UNI_Optimized
        
        mouse_x, mouse_y = get_mouse_position()
        print(f"测试坐标: ({mouse_x}, {mouse_y})")
        
        uni = UNI_Optimized()
        
        # 测试3次
        times = []
        for i in range(3):
            start_time = time.time()
            control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=True, highlight=False)
            elapsed = time.time() - start_time
            times.append(elapsed)
            
            status = "✅" if control_data else "❌"
            name = control_data.get('Name', 'N/A') if control_data else 'N/A'
            print(f"  测试{i+1}: {status} {elapsed:.2f}s - {name}")
        
        avg_time = sum(times) / len(times)
        print(f"\n📊 优化版平均时间: {avg_time:.2f}s")
        return avg_time
        
    except Exception as e:
        print(f"❌ 优化版测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("控件识别性能简化测试")
    print("=" * 50)
    print("🎯 目标: 验证优化效果并达到1秒以内")
    print()
    
    # 测试原版
    original_time = test_original_performance()
    
    # 测试优化版
    optimized_time = test_optimized_performance()
    
    # 对比结果
    print("\n🏆 性能对比")
    print("=" * 30)
    
    if original_time and optimized_time:
        improvement = (original_time - optimized_time) / original_time * 100
        speedup = original_time / optimized_time
        
        print(f"原版平均时间: {original_time:.2f}s")
        print(f"优化版平均时间: {optimized_time:.2f}s")
        print(f"性能提升: {improvement:.1f}%")
        print(f"速度提升: {speedup:.1f}x")
        
        if optimized_time <= 1.0:
            print("✅ 目标达成！优化版速度已在1秒以内")
        else:
            print(f"❌ 目标未达成，还需优化 {optimized_time - 1.0:.2f}s")
    
    elif original_time:
        print(f"原版平均时间: {original_time:.2f}s")
        print("优化版测试失败，无法进行对比")
    
    else:
        print("测试失败，无法获得性能数据")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())