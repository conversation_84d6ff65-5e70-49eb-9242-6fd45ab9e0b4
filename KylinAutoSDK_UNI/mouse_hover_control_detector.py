#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
自动鼠标控件检测器
自动检测并高亮鼠标悬停处的控件，无需交互

使用方法:
1. 运行脚本: python3 mouse_hover_control_detector.py
2. 移动鼠标到想要检测的控件上
3. 脚本会自动检测、高亮并打印控件信息
4. 按 Ctrl+C 退出

功能:
- 自动检测鼠标位置的控件
- 自动高亮显示控件
- 自动打印详细控件信息
- 避免重复检测同一位置
"""

import sys
import time
import os
import signal

# 导入我们的控件检测器
from at_spi_control_detector import ATSPIControlDetector


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        import subprocess
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None


class AutoMouseControlDetector:
    """自动鼠标控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.detector = ATSPIControlDetector()
        self.last_mouse_pos = (0, 0)
        self.last_detection_time = 0
        self.detection_cooldown = 1.0  # 检测冷却时间（秒）
        self.position_threshold = 10   # 位置变化阈值（像素）
        self.is_running = True
        
        print("🎯 自动鼠标控件检测器已启动")
        print("=" * 60)
        print("📖 功能说明:")
        print("   ✅ 自动检测鼠标位置的控件")
        print("   ✅ 自动高亮显示控件")
        print("   ✅ 自动打印详细控件信息")
        print("   ✅ 避免重复检测同一位置")
        print("=" * 60)
        print("🖱️  移动鼠标到想要检测的控件上...")
        print("🛑 按 Ctrl+C 退出程序")
        print("=" * 60)
    
    def should_detect_at_position(self, x, y):
        """判断是否应该在此位置进行检测"""
        current_time = time.time()
        
        # 检查时间冷却
        if current_time - self.last_detection_time < self.detection_cooldown:
            return False
        
        # 检查位置变化
        last_x, last_y = self.last_mouse_pos
        distance = ((x - last_x) ** 2 + (y - last_y) ** 2) ** 0.5
        
        if distance < self.position_threshold:
            return False
        
        return True
    
    def detect_and_highlight_control(self, x, y):
        """检测并高亮指定位置的控件"""
        print(f"\n🔍 检测坐标 ({x}, {y}) 处的控件...")
        
        try:
            # 首先检测控件
            result = self.detector.get_control_at_point(x, y, include_details=True)
            
            if result['success']:
                pos = result['position']
                print(f"✅ 检测成功！位置: ({pos[0]}, {pos[1]}) 尺寸: {pos[2]}×{pos[3]}")
                
                # 高亮控件
                highlight_result = self.detector.highlight_control_at_point(
                    x, y, duration=2, color='red', border_width=2
                )
                
                if highlight_result['highlighted']:
                    print("✨ 控件高亮成功")
                else:
                    print("⚠️  控件检测成功但高亮失败")
                
                # 打印详细信息
                print("\n📋 控件详细信息:")
                print("-" * 40)
                self.detector.print_control_info(x, y)
                print("-" * 40)
                
            else:
                print(f"❌ 未找到控件: {result['message']}")
                
        except Exception as e:
            print(f"❌ 检测异常: {e}")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 接收到信号 {signum}，正在退出...")
        self.is_running = False
    
    def run(self):
        """运行检测器"""
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            print("🚀 开始监控鼠标位置...")
            
            while self.is_running:
                # 获取当前鼠标位置
                current_pos = get_mouse_position()
                
                if current_pos == (None, None):
                    print("⚠️  无法获取鼠标位置，等待中...")
                    time.sleep(1)
                    continue
                
                x, y = current_pos
                
                # 检查是否应该检测
                if self.should_detect_at_position(x, y):
                    self.detect_and_highlight_control(x, y)
                    self.last_mouse_pos = (x, y)
                    self.last_detection_time = time.time()
                
                # 短暂休眠避免CPU占用过高
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 接收到中断信号，正在退出...")
        except Exception as e:
            print(f"\n❌ 运行异常: {e}")
        finally:
            self.is_running = False
            print("👋 检测器已停止")


def main():
    """主函数"""
    print("🎯 自动鼠标控件检测器")
    print("=" * 60)
    
    # 检查运行环境
    if not sys.platform.startswith('linux'):
        print("❌ 错误: 此程序仅支持Linux系统")
        sys.exit(1)
    
    if os.getenv('DISPLAY') is None:
        print("❌ 错误: 未检测到DISPLAY环境变量")
        print("请在图形界面环境中运行此程序")
        sys.exit(1)
    
    try:
        detector = AutoMouseControlDetector()
        detector.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()