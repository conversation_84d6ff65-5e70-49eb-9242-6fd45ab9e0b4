# Wayland 兼容性指南

## 概述

KylinAutoSDK_UNI 现已支持 Wayland 环境！我们已经解决了之前在 Wayland 环境下出现的段错误问题，并保持了与 X11 环境的完全兼容性。

## 问题解决

### 原始问题
在 Wayland 环境中运行 `UNI_new.py` 时出现以下错误：
```
(UNI_new.py:3771046): Gdk-WARNING **: Failed to read portal settings
(UNI_new.py:3771046): Wnck-WARNING **: libwnck is designed to work in X11 only, no valid display found
段错误（核心已转储）
```

### 解决方案
我们实现了智能环境检测和条件导入机制：

1. **环境检测**: 自动检测当前运行的显示服务器类型（X11/Wayland）
2. **条件导入**: 只在 X11 环境下导入 X11 专用库（Wnck、Xlib）
3. **替代实现**: 在 Wayland 环境下使用 AT-SPI 提供替代功能
4. **优雅降级**: 确保核心功能在两种环境下都能正常工作

## 技术实现

### 1. 环境检测
新增了环境检测函数：
- `detect_display_server()`: 检测显示服务器类型
- `is_wayland_environment()`: 检查是否为 Wayland 环境
- `is_x11_environment()`: 检查是否为 X11 环境

### 2. 条件导入
修改了导入逻辑，避免在 Wayland 环境下导入 X11 专用库：
```python
# 只在 X11 环境下导入
if is_x11_environment():
    import gi
    gi.require_version('Wnck', '3.0')
    from gi.repository import Wnck
    from Xlib import display, X
```

### 3. 替代实现
为 Wayland 环境提供了基于 AT-SPI 的替代实现：
- 窗口管理使用 AT-SPI 而非 Wnck
- 保持 API 兼容性，用户代码无需修改

## 使用方式

### 在 Wayland 环境中
```python
from UNI_new import UNI

# 初始化时会显示环境信息
uni = UNI()
# 输出: UNI SDK initialized in Wayland mode. Some X11-specific features may be limited.

# 正常使用所有 API
data, info = uni.kdk_getElement_Uni(100, 200)
```

### 在 X11 环境中
```python
from UNI_new import UNI

# 初始化时会显示环境信息
uni = UNI()
# 输出: UNI SDK initialized in X11 mode. Full functionality available.

# 正常使用所有 API，包括完整的窗口管理功能
data, info = uni.kdk_getElement_Uni(100, 200)
```

## 功能对比

| 功能 | X11 环境 | Wayland 环境 | 说明 |
|------|----------|--------------|------|
| 控件获取 | ✅ 完全支持 | ✅ 完全支持 | 基于 AT-SPI，两环境一致 |
| 窗口管理 | ✅ 完全支持 | ⚠️ 部分支持 | Wayland 下使用 AT-SPI 替代 |
| 截图功能 | ✅ 完全支持 | ✅ 完全支持 | 使用系统截图命令 |
| 控件验证 | ✅ 完全支持 | ✅ 完全支持 | 基于 AT-SPI，两环境一致 |
| 数据处理 | ✅ 完全支持 | ✅ 完全支持 | 纯 Python 实现，无依赖 |

## 测试验证

我们提供了完整的测试脚本来验证兼容性：

```bash
# 运行兼容性测试
python3 test_wayland_compatibility.py
```

测试内容包括：
- 环境检测功能
- UNI 类导入
- 对象初始化
- 基本功能验证

## 注意事项

### 1. 功能限制
在 Wayland 环境下，以下功能可能受限：
- 精确的窗口堆栈获取
- 某些高级窗口管理操作

### 2. 性能影响
- Wayland 环境下主要依赖 AT-SPI，性能与 X11 环境基本一致
- 环境检测只在初始化时执行一次，运行时无额外开销

### 3. 兼容性
- 保持完全的 API 兼容性
- 现有代码无需修改即可在两种环境下运行
- 生成的数据格式完全一致

## 故障排除

### 1. 如果仍然出现导入错误
```bash
# 检查 AT-SPI 是否正确安装
python3 -c "import pyatspi; print('AT-SPI 可用')"

# 检查环境变量
echo $XDG_SESSION_TYPE
echo $WAYLAND_DISPLAY
echo $DISPLAY
```

### 2. 如果功能异常
```bash
# 运行测试脚本诊断
python3 test_wayland_compatibility.py

# 检查系统服务
systemctl --user status at-spi-dbus-bus
```

## 开发建议

### 1. 新功能开发
- 优先使用 AT-SPI API，确保跨环境兼容
- 避免直接使用 X11 专用功能
- 在添加新功能时考虑 Wayland 兼容性

### 2. 测试
- 在两种环境下都进行测试
- 使用提供的测试脚本验证兼容性
- 关注性能差异

## 总结

通过这次更新，KylinAutoSDK_UNI 现在可以：
- ✅ 在 Wayland 环境下正常运行，不再出现段错误
- ✅ 保持与 X11 环境的完全兼容性
- ✅ 提供一致的 API 接口
- ✅ 自动适应运行环境
- ✅ 保持良好的性能表现

用户可以放心在任何环境下使用 UNI SDK，无需担心兼容性问题。
