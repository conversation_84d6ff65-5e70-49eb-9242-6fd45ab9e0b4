#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
通用控件检测器
支持检测任何桌面应用的控件，自动适配不同应用的坐标偏移
"""

import sys
import os
import subprocess
import pyatspi
import re


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_window_at_mouse_position(mouse_x, mouse_y):
    """获取鼠标位置下的窗口信息"""
    try:
        # 使用xdotool获取鼠标下的窗口ID
        result = subprocess.run(['xdotool', 'getmouselocation', '--shell'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            window_id = None
            for line in lines:
                if line.startswith('WINDOW='):
                    window_id = line.split('=')[1]
                    break
            
            if window_id and window_id != '0':
                # 获取窗口几何信息
                result = subprocess.run(['xdotool', 'getwindowgeometry', window_id], 
                                      capture_output=True, text=True, env={'DISPLAY': ':0'})
                if result.returncode == 0:
                    # 解析几何信息
                    for line in result.stdout.strip().split('\n'):
                        if 'Position:' in line:
                            pos_part = line.split('Position:')[1].strip()
                            coords = pos_part.split(',')
                            if len(coords) == 2:
                                x = int(coords[0].strip())
                                y = int(coords[1].strip().split()[0])
                                
                                # 获取窗口标题
                                title_result = subprocess.run(['xdotool', 'getwindowname', window_id], 
                                                            capture_output=True, text=True, env={'DISPLAY': ':0'})
                                window_title = title_result.stdout.strip() if title_result.returncode == 0 else "Unknown"
                                
                                return {
                                    'window_id': window_id,
                                    'position': (x, y),
                                    'title': window_title
                                }
    except Exception as e:
        print(f"⚠️  xdotool获取窗口信息失败: {e}")
    
    # 如果xdotool失败，尝试使用wlcctrl (Wayland)
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode == 0:
            print("🔍 检测到Wayland环境，使用wlcctrl...")
            # 这里可以添加Wayland窗口检测逻辑
            # 暂时返回None，让系统使用AT-SPI直接检测
    except Exception:
        pass
    
    return None


class UniversalControlDetector:
    """通用控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.all_controls = []
        self.window_offsets = {}  # 缓存不同窗口的偏移
        
        # 具体控件类型
        self.specific_control_types = {
            'push button', 'button', 'toggle button',
            'check box', 'radio button', 
            'text', 'password text', 'entry',
            'combo box', 'list', 'list item',
            'slider', 'spin button', 'scroll bar',
            'menu item', 'tab', 'tree item',
            'label', 'image', 'progress bar',
            'table cell', 'column header', 'row header'
        }
        
        print("🌍 通用控件检测器")
        print("=" * 60)
        print("✅ 支持检测任何桌面应用的控件")
        print("🔄 自动适配不同应用的坐标系统")
        print("=" * 60)
    
    def refresh_all_controls(self):
        """刷新所有应用的控件缓存"""
        self.all_controls = []
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                # 跳过系统应用
                if app_name.lower() in ['at-spi-bus-launcher', 'gnome-shell', 'ibus-daemon']:
                    continue
                
                app_controls = []
                self._collect_controls_recursive(app, app_controls, app_name, max_depth=6)
                
                if app_controls:
                    print(f"   📱 {app_name}: {len(app_controls)} 个控件")
                    self.all_controls.extend(app_controls)
            
            print(f"✅ 总共收集到 {len(self.all_controls)} 个控件")
            
        except Exception as e:
            print(f"❌ 刷新缓存失败: {e}")
    
    def _collect_controls_recursive(self, element, control_list, app_name, depth=0, max_depth=6):
        """递归收集控件"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 收集所有有坐标的控件
            if extents and extents.width > 0 and extents.height > 0:
                control_list.append({
                    'element': element,
                    'name': name,
                    'role': role,
                    'extents': extents,
                    'depth': depth,
                    'app_name': app_name
                })
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_controls_recursive(child, control_list, app_name, depth + 1, max_depth)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def find_control_at_position_direct(self, mouse_x, mouse_y):
        """直接在AT-SPI坐标系中查找控件（无偏移）"""
        if not self.all_controls:
            print("🔄 刷新控件缓存...")
            self.refresh_all_controls()
        
        # 直接使用鼠标坐标在AT-SPI中查找
        matches = []
        for ctrl in self.all_controls:
            ext = ctrl['extents']
            
            # 检查点是否在控件范围内
            if (ext.x <= mouse_x < ext.x + ext.width and
                ext.y <= mouse_y < ext.y + ext.height):
                matches.append(ctrl)
        
        return matches
    
    def find_control_with_window_offset(self, mouse_x, mouse_y, window_info):
        """使用窗口偏移查找控件"""
        if not window_info:
            return []
        
        # 计算可能的偏移
        window_x, window_y = window_info['position']
        
        # 尝试不同的偏移策略
        offset_strategies = [
            (window_x, window_y),  # 直接使用窗口位置
            (window_x, window_y - 30),  # 减去标题栏高度
            (window_x - 8, window_y - 30),  # 减去边框和标题栏
            (window_x + 8, window_y + 30),  # 加上边框
        ]
        
        best_matches = []
        
        for offset_x, offset_y in offset_strategies:
            atspi_x = mouse_x - offset_x
            atspi_y = mouse_y - offset_y
            
            matches = []
            for ctrl in self.all_controls:
                ext = ctrl['extents']
                
                if (ext.x <= atspi_x < ext.x + ext.width and
                    ext.y <= atspi_y < ext.y + ext.height):
                    matches.append({
                        'control': ctrl,
                        'offset': (offset_x, offset_y),
                        'atspi_coords': (atspi_x, atspi_y)
                    })
            
            if matches:
                best_matches.extend(matches)
        
        return best_matches
    
    def select_best_control(self, matches):
        """从匹配的控件中选择最佳的"""
        if not matches:
            return None
        
        # 如果是直接匹配（无偏移）
        if isinstance(matches[0], dict) and 'element' in matches[0]:
            # 优先选择具体控件类型
            specific_matches = [m for m in matches if m['role'] in self.specific_control_types]
            
            if specific_matches:
                # 选择面积最小的具体控件
                best_match = min(specific_matches, key=lambda x: x['extents'].width * x['extents'].height)
            else:
                # 选择面积最小的控件
                best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
            
            return {
                'control': best_match,
                'offset': None,
                'method': 'direct'
            }
        
        # 如果是带偏移的匹配
        else:
            # 优先选择具体控件类型
            specific_matches = [m for m in matches if m['control']['role'] in self.specific_control_types]
            
            if specific_matches:
                best_match = min(specific_matches, key=lambda x: x['control']['extents'].width * x['control']['extents'].height)
            else:
                best_match = min(matches, key=lambda x: x['control']['extents'].width * x['control']['extents'].height)
            
            return {
                'control': best_match['control'],
                'offset': best_match['offset'],
                'method': 'offset'
            }
    
    def get_control_info(self, ctrl):
        """获取控件详细信息"""
        try:
            # 获取状态
            states = []
            try:
                state = ctrl['element'].getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass
            
            # 获取动作
            actions = []
            try:
                if hasattr(ctrl['element'], 'queryAction'):
                    action = ctrl['element'].queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass
            
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'app_name': ctrl.get('app_name', 'Unknown'),
                'states': states,
                'actions': actions,
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
        except Exception:
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'app_name': ctrl.get('app_name', 'Unknown'),
                'states': [],
                'actions': [],
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
    
    def highlight_control(self, ctrl, offset=None, duration=3, color='lime', border_width=3):
        """高亮控件"""
        if not ctrl:
            return False
        
        ext = ctrl['extents']
        
        # 计算屏幕坐标
        if offset:
            screen_x = ext.x + offset[0]
            screen_y = ext.y + offset[1]
        else:
            # 直接使用AT-SPI坐标作为屏幕坐标
            screen_x = ext.x
            screen_y = ext.y
        
        width = ext.width
        height = ext.height
        
        try:
            from ultimate_highlight import ultimate_highlight
            highlight_success = ultimate_highlight(
                screen_x, screen_y, width, height,
                duration=duration, color=color, border_width=border_width
            )
            
            return highlight_success
                
        except Exception as e:
            print(f"❌ 高亮异常: {e}")
            return False
    
    def analyze_control_type(self, info):
        """分析控件类型"""
        role = info['role']
        
        type_descriptions = {
            'push button': "🔘 按钮 - 可点击执行操作",
            'button': "🔘 按钮 - 可点击执行操作",
            'check box': "☑️  复选框 - 可切换选中状态",
            'radio button': "🔘 单选按钮 - 与其他单选按钮互斥",
            'text': "📝 文本框 - 可输入文字",
            'password text': "🔒 密码框 - 输入内容隐藏",
            'entry': "📝 输入框 - 可输入文字",
            'slider': "🎚️  滑块 - 可拖动调整数值",
            'spin button': "🔢 数字框 - 可输入或调整数字",
            'combo box': "📋 下拉框 - 可选择选项",
            'list': "📋 列表 - 包含多个选项",
            'list item': "📄 列表项 - 可选择的项目",
            'menu item': "🍽️  菜单项 - 可点击的菜单选项",
            'tab': "📑 标签页 - 可切换的页面",
            'label': "🏷️  标签 - 显示文本信息",
            'image': "🖼️  图像 - 显示图片",
            'table cell': "📊 表格单元格 - 表格中的数据",
            'window': "🪟 窗口 - 应用程序窗口",
            'frame': "🖼️  框架 - 容器控件"
        }
        
        return type_descriptions.get(role, f"🔍 {role} 控件")
    
    def detect_control_at_mouse(self):
        """检测鼠标位置的控件"""
        # 获取鼠标位置
        mouse_pos = get_mouse_position()
        if mouse_pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return False
        
        mouse_x, mouse_y = mouse_pos
        
        print(f"\n🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        print("-" * 60)
        
        # 获取窗口信息
        window_info = get_window_at_mouse_position(mouse_x, mouse_y)
        if window_info:
            print(f"🪟 窗口信息: {window_info['title']}")
            print(f"   窗口位置: {window_info['position']}")
        else:
            print("⚠️  无法获取窗口信息，使用直接检测模式")
        
        # 尝试直接检测
        print(f"\n🔍 方法1: 直接AT-SPI检测...")
        direct_matches = self.find_control_at_position_direct(mouse_x, mouse_y)
        
        # 尝试窗口偏移检测
        offset_matches = []
        if window_info:
            print(f"🔍 方法2: 窗口偏移检测...")
            offset_matches = self.find_control_with_window_offset(mouse_x, mouse_y, window_info)
        
        # 合并结果
        all_matches = []
        if direct_matches:
            all_matches.extend(direct_matches)
        if offset_matches:
            all_matches.extend([m['control'] for m in offset_matches])
        
        if not all_matches:
            print("❌ 未找到任何控件")
            print("💡 提示:")
            print("   1. 确保鼠标在应用程序窗口内")
            print("   2. 确保目标控件支持AT-SPI")
            print("   3. 尝试移动鼠标到其他控件上")
            return False
        
        # 选择最佳控件
        if direct_matches:
            best_result = self.select_best_control(direct_matches)
        else:
            best_result = self.select_best_control(offset_matches)
        
        ctrl = best_result['control']
        offset = best_result['offset']
        method = best_result['method']
        
        # 获取控件信息
        info = self.get_control_info(ctrl)
        
        print(f"\n✅ 检测成功! (使用{method}方法)")
        print("=" * 60)
        print(f"📱 应用程序: {info['app_name']}")
        print(f"📋 控件名称: {info['name']}")
        print(f"🏷️  控件类型: {info['role']}")
        print(f"📍 AT-SPI位置: {info['position']}")
        print(f"📏 尺寸: {info['size']}")
        print(f"📊 深度: {info['depth']}")
        
        if offset:
            screen_x = info['position'][0] + offset[0]
            screen_y = info['position'][1] + offset[1]
            print(f"🔄 使用偏移: {offset}")
            print(f"📍 屏幕位置: ({screen_x}, {screen_y})")
        
        if info['states']:
            print(f"🔧 状态: {', '.join(info['states'][:3])}")
        
        if info['actions']:
            print(f"⚡ 动作: {', '.join(info['actions'])}")
        
        # 控件类型说明
        type_description = self.analyze_control_type(info)
        print(f"\n💡 {type_description}")
        
        # 执行高亮
        print(f"\n✨ 执行高亮...")
        highlight_success = self.highlight_control(ctrl, offset, duration=3, color='yellow', border_width=3)
        
        if highlight_success:
            print(f"✅ 高亮成功! 请观察控件是否被准确高亮")
        else:
            print(f"❌ 高亮失败")
        
        return True


def main():
    """主函数"""
    print("🌍 通用控件检测器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        detector = UniversalControlDetector()
        
        print(f"\n🚀 开始检测当前鼠标位置的控件...")
        success = detector.detect_control_at_mouse()
        
        if success:
            print(f"\n🎉 检测完成!")
        else:
            print(f"\n❌ 检测失败")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
