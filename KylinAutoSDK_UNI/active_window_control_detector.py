#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
基于激活窗口的控件检测器
只在当前激活窗口中查找和高亮控件
"""

import sys
import os
import subprocess
import pyatspi
import re
import time


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_active_window():
    """获取当前激活窗口"""
    print("🔍 获取当前激活窗口...")
    
    try:
        result = subprocess.run(['wlcctrl', '--getactivewindow'], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"   ❌ 无法获取激活窗口: {result.stderr}")
            return None
        
        active_window_id = result.stdout.strip()
        
        # 处理可能的前缀
        if active_window_id.startswith('uuid : '):
            active_window_id = active_window_id[7:]
        elif active_window_id.startswith('uuid:'):
            active_window_id = active_window_id[5:]
        
        print(f"   激活窗口ID: {active_window_id}")
        
        # 获取窗口标题 - 从窗口列表中查找（最可靠的方法）
        window_title = "Unknown"

        try:
            list_result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
            if list_result.returncode == 0:
                lines = list_result.stdout.strip().split('\n')
                i = 0
                while i < len(lines):
                    line = lines[i]
                    if 'toplevel' in line and active_window_id in line:
                        if i + 1 < len(lines):
                            title_line = lines[i + 1].strip()
                            if title_line.startswith('title: '):
                                window_title = title_line[7:]  # 移除 "title: " 前缀
                                print(f"   激活窗口标题: {window_title}")
                                break
                    i += 1

                if window_title == "Unknown":
                    print(f"   ❌ 在窗口列表中未找到窗口ID: {active_window_id}")
            else:
                print(f"   ❌ 获取窗口列表失败: {list_result.stderr.strip()}")
        except Exception as e:
            print(f"   ❌ 获取窗口标题异常: {e}")

        if window_title == "Unknown":
            print(f"   ⚠️  使用窗口ID作为标题")
            window_title = active_window_id
        
        # 获取窗口几何信息
        geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', active_window_id], 
                                  capture_output=True, text=True)
        
        window_info = {
            'id': active_window_id,
            'title': window_title
        }
        
        if geo_result.returncode == 0:
            for geo_line in geo_result.stdout.strip().split('\n'):
                if 'geometry:' in geo_line:
                    match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                    if match:
                        x, y, width, height = map(int, match.groups())
                        window_info.update({
                            'position': (x, y),
                            'size': (width, height)
                        })
                        print(f"   窗口位置: ({x}, {y}) 尺寸: {width}×{height}")
                        break
        
        return window_info
        
    except Exception as e:
        print(f"   ❌ 获取激活窗口失败: {e}")
        return None


def find_atspi_window_for_active_window(active_window_info):
    """为激活窗口找到对应的AT-SPI窗口"""
    print(f"\n🔍 查找激活窗口的AT-SPI对应窗口...")
    
    window_title = active_window_info['title']
    print(f"   目标窗口: {window_title}")
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            # 跳过系统应用
            if app_name.lower() in ['at-spi-bus-launcher', 'gnome-shell', 'ibus-daemon']:
                continue
            
            print(f"   检查应用: {app_name}")
            
            for j in range(app.childCount):
                try:
                    window = app.getChildAtIndex(j)
                    window_name = window.name or ""
                    
                    print(f"     窗口: {window_name}")
                    
                    # 匹配策略
                    if window_titles_match(window_title, window_name, app_name):
                        print(f"   ✅ 找到匹配的AT-SPI窗口: {window_name}")
                        
                        # 获取AT-SPI窗口坐标
                        if hasattr(window, 'queryComponent'):
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                
                                return {
                                    'app': app,
                                    'app_name': app_name,
                                    'window': window,
                                    'window_name': window_name,
                                    'atspi_position': (extents.x, extents.y),
                                    'atspi_size': (extents.width, extents.height)
                                }
                except Exception:
                    continue
        
        print(f"   ❌ 未找到匹配的AT-SPI窗口")
        return None
        
    except Exception as e:
        print(f"❌ 查找AT-SPI窗口失败: {e}")
        return None


def window_titles_match(real_title, atspi_title, app_name):
    """检查窗口标题是否匹配"""
    if not real_title or not atspi_title:
        return False

    print(f"       匹配检查: '{real_title}' vs '{atspi_title}' (应用: {app_name})")

    # 排除明显不匹配的情况
    # 如果真实窗口是hellobig应用，不应该匹配到Qt Creator
    if ('AT-SPI测试界面' in real_title and 'Qt Creator' in atspi_title):
        print(f"       ❌ 排除Qt Creator窗口")
        return False

    # 如果真实窗口是hellobig应用，不应该匹配到其他IDE窗口
    if ('AT-SPI测试界面' in real_title and any(ide in atspi_title for ide in ['Qt Creator', 'Visual Studio', 'Eclipse'])):
        print(f"       ❌ 排除IDE窗口")
        return False

    # 精确匹配：hellobig应用的特殊匹配
    if 'AT-SPI测试界面' in real_title and 'Qt控件集合' in real_title:
        # 寻找hellobig应用本身的窗口
        if ('测试主窗口' in atspi_title or
            'AT-SPI测试' in atspi_title or
            ('hellobig' in app_name.lower() and 'Qt Creator' not in atspi_title)):
            print(f"       ✅ hellobig应用匹配")
            return True
        else:
            print(f"       ❌ 不是hellobig应用窗口")
            return False

    # 直接匹配
    if (real_title.lower() in atspi_title.lower() or
        atspi_title.lower() in real_title.lower()):
        print(f"       ✅ 直接匹配")
        return True

    # 关键词匹配（排除短词）
    real_keywords = [word for word in real_title.lower().split() if len(word) > 3]
    if any(keyword in atspi_title.lower() for keyword in real_keywords):
        print(f"       ✅ 关键词匹配")
        return True

    # 终端特殊匹配
    if ('terminal' in real_title.lower() or '终端' in real_title):
        if ('terminal' in atspi_title.lower() or 'Terminal' in atspi_title):
            print(f"       ✅ 终端匹配")
            return True

    print(f"       ❌ 无匹配")
    return False


def find_control_at_position(atspi_window, target_x, target_y):
    """在AT-SPI窗口中查找指定位置的控件"""
    print(f"🎯 在AT-SPI坐标 ({target_x}, {target_y}) 查找控件...")
    
    def search_controls(element, depth=0):
        if depth > 10:  # 防止递归过深
            return None
        
        try:
            if hasattr(element, 'queryComponent'):
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                    
                    # 检查点是否在控件范围内
                    if (extents.x <= target_x < extents.x + extents.width and
                        extents.y <= target_y < extents.y + extents.height):
                        
                        role_name = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                        element_name = element.name or f"unnamed_{role_name}"
                        
                        print(f"   {'  ' * depth}找到控件: {element_name} ({role_name})")
                        print(f"   {'  ' * depth}位置: ({extents.x}, {extents.y}) 尺寸: {extents.width}×{extents.height}")
                        
                        # 优先选择具体的控件类型
                        specific_roles = {
                            'push button', 'button', 'toggle button',
                            'check box', 'radio button', 
                            'text', 'password text', 'entry',
                            'combo box', 'list', 'list item',
                            'slider', 'spin button', 'scroll bar',
                            'menu item', 'tab', 'tree item',
                            'label', 'image', 'progress bar'
                        }
                        
                        best_match = {
                            'element': element,
                            'name': element_name,
                            'role': role_name,
                            'position': (extents.x, extents.y),
                            'size': (extents.width, extents.height),
                            'is_specific': role_name.lower() in specific_roles
                        }
                        
                        # 继续搜索子元素，寻找更具体的控件
                        for i in range(element.childCount):
                            try:
                                child = element.getChildAtIndex(i)
                                child_result = search_controls(child, depth + 1)
                                
                                if (child_result and child_result['is_specific'] and 
                                    not best_match['is_specific']):
                                    best_match = child_result
                            except Exception:
                                continue
                        
                        return best_match
            
            # 如果当前元素不包含目标点，搜索子元素
            for i in range(element.childCount):
                try:
                    child = element.getChildAtIndex(i)
                    result = search_controls(child, depth + 1)
                    if result:
                        return result
                except Exception:
                    continue
            
            return None
            
        except Exception:
            return None
    
    return search_controls(atspi_window)


def highlight_control(control_info, offset):
    """高亮控件"""
    print(f"\n✨ 高亮控件...")
    
    # 计算屏幕坐标
    atspi_x, atspi_y = control_info['position']
    width, height = control_info['size']
    offset_x, offset_y = offset
    
    screen_x = atspi_x + offset_x
    screen_y = atspi_y + offset_y
    
    print(f"   控件: {control_info['name']} ({control_info['role']})")
    print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y})")
    print(f"   偏移量: ({offset_x}, {offset_y})")
    print(f"   屏幕坐标: ({screen_x}, {screen_y})")
    print(f"   尺寸: {width}×{height}")
    
    # 执行高亮
    try:
        highlight_cmd = [
            'python3', '-c', f'''
import tkinter as tk
import time

root = tk.Tk()
root.withdraw()
root.attributes("-topmost", True)

# 创建高亮窗口
highlight = tk.Toplevel(root)
highlight.geometry(f"{width}x{height}+{screen_x}+{screen_y}")
highlight.configure(bg="red")
highlight.attributes("-alpha", 0.3)
highlight.attributes("-topmost", True)
highlight.overrideredirect(True)

print("高亮显示3秒...")
root.after(3000, root.quit)
root.mainloop()
'''
        ]
        
        subprocess.run(highlight_cmd, env={'DISPLAY': ':0'})
        print(f"   ✅ 高亮完成")
        
    except Exception as e:
        print(f"   ❌ 高亮失败: {e}")


def main():
    """主函数"""
    print("🎯 基于激活窗口的控件检测器")
    print("=" * 60)
    print("🔍 只在当前激活窗口中查找和高亮控件")
    print("✨ 避免检测最小化或隐藏窗口的问题")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        # 步骤1: 获取鼠标位置
        mouse_x, mouse_y = get_mouse_position()
        if not mouse_x:
            print("❌ 无法获取鼠标位置")
            return
        
        print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        
        # 步骤2: 获取当前激活窗口
        active_window = get_active_window()
        if not active_window:
            print("❌ 无法获取激活窗口")
            return
        
        # 步骤3: 检查鼠标是否在激活窗口内
        if 'position' in active_window and 'size' in active_window:
            win_x, win_y = active_window['position']
            win_width, win_height = active_window['size']
            
            if not (win_x <= mouse_x < win_x + win_width and win_y <= mouse_y < win_y + win_height):
                print(f"❌ 鼠标不在激活窗口内")
                print(f"   窗口范围: ({win_x}, {win_y}) 到 ({win_x + win_width}, {win_y + win_height})")
                print(f"   鼠标位置: ({mouse_x}, {mouse_y})")
                return
            
            print(f"✅ 鼠标在激活窗口内")
        
        # 步骤4: 查找对应的AT-SPI窗口
        atspi_window_info = find_atspi_window_for_active_window(active_window)
        if not atspi_window_info:
            print("❌ 未找到对应的AT-SPI窗口")
            return
        
        # 步骤5: 计算偏移
        real_x, real_y = active_window['position']
        real_width, real_height = active_window['size']
        atspi_x, atspi_y = atspi_window_info['atspi_position']
        atspi_width, atspi_height = atspi_window_info['atspi_size']
        
        offset_x = real_x - atspi_x
        offset_y = real_y - atspi_y
        
        # 考虑标题栏高度
        height_diff = real_height - atspi_height
        if height_diff > 0:
            corrected_offset_y = offset_y + height_diff
            calculated_offset = (offset_x, corrected_offset_y)
            print(f"\n📐 偏移计算:")
            print(f"   真实窗口: ({real_x}, {real_y}) {real_width}×{real_height}")
            print(f"   AT-SPI窗口: ({atspi_x}, {atspi_y}) {atspi_width}×{atspi_height}")
            print(f"   标题栏高度: {height_diff}像素")
            print(f"   最终偏移: {calculated_offset}")
        else:
            calculated_offset = (offset_x, offset_y)
            print(f"\n📐 偏移计算: {calculated_offset}")
        
        # 步骤6: 转换鼠标坐标到AT-SPI坐标系
        atspi_mouse_x = mouse_x - calculated_offset[0]
        atspi_mouse_y = mouse_y - calculated_offset[1]
        
        print(f"\n🎯 控件检测:")
        print(f"   鼠标屏幕坐标: ({mouse_x}, {mouse_y})")
        print(f"   转换为AT-SPI坐标: ({atspi_mouse_x}, {atspi_mouse_y})")
        
        # 步骤7: 查找控件
        control = find_control_at_position(atspi_window_info['window'], atspi_mouse_x, atspi_mouse_y)
        
        if not control:
            print("❌ 未找到控件")
            return
        
        print(f"✅ 找到控件: {control['name']} ({control['role']})")
        
        # 步骤8: 高亮控件
        highlight_control(control, calculated_offset)
        
        print(f"\n🎉 检测完成！")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
