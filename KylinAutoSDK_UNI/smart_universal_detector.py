#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
智能通用控件检测器
保留hellobig的精确检测，同时支持其他应用的通用检测
"""

import sys
import os
import subprocess
import pyatspi
import re


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def detect_application_at_mouse(mouse_x, mouse_y):
    """检测鼠标位置下的应用程序"""
    try:
        # 尝试使用xdotool获取窗口信息
        result = subprocess.run(['xdotool', 'getmouselocation', '--shell'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            window_id = None
            for line in lines:
                if line.startswith('WINDOW='):
                    window_id = line.split('=')[1]
                    break
            
            if window_id and window_id != '0':
                # 获取窗口标题
                title_result = subprocess.run(['xdotool', 'getwindowname', window_id], 
                                            capture_output=True, text=True, env={'DISPLAY': ':0'})
                window_title = title_result.stdout.strip() if title_result.returncode == 0 else ""
                
                # 检测是否是hellobig应用
                if ('hellobig' in window_title.lower() or 
                    'AT-SPI测试界面' in window_title or 
                    'Qt控件集合' in window_title):
                    return 'hellobig'
                
                return 'other'
    except Exception:
        pass
    
    # 如果xdotool失败，通过AT-SPI检测
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or ""
            
            if 'hellobig' in app_name.lower():
                # 检查鼠标是否在hellobig应用的窗口范围内
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        if hasattr(window, 'queryComponent'):
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                if (extents.x <= mouse_x < extents.x + extents.width and
                                    extents.y <= mouse_y < extents.y + extents.height):
                                    return 'hellobig'
                    except Exception:
                        continue
    except Exception:
        pass
    
    return 'other'


class SmartUniversalDetector:
    """智能通用控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        # hellobig的精确偏移（已验证）
        self.hellobig_offset = (567, 192)
        
        # 具体控件类型
        self.specific_control_types = {
            'push button', 'button', 'toggle button',
            'check box', 'radio button', 
            'text', 'password text', 'entry',
            'combo box', 'list', 'list item',
            'slider', 'spin button', 'scroll bar',
            'menu item', 'tab', 'tree item',
            'label', 'image', 'progress bar'
        }
        
        self.all_controls = []
        
        print("🧠 智能通用控件检测器")
        print("=" * 60)
        print("🎯 hellobig应用: 使用精确偏移 (567, 192)")
        print("🌍 其他应用: 使用智能检测算法")
        print("=" * 60)
    
    def refresh_controls(self, target_app=None):
        """刷新控件缓存"""
        self.all_controls = []
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                # 如果指定了目标应用，只收集该应用
                if target_app == 'hellobig' and 'hellobig' not in app_name.lower():
                    continue
                elif target_app == 'other' and 'hellobig' in app_name.lower():
                    continue
                
                # 跳过系统应用
                if app_name.lower() in ['at-spi-bus-launcher', 'gnome-shell', 'ibus-daemon']:
                    continue
                
                app_controls = []
                self._collect_controls_recursive(app, app_controls, app_name, max_depth=6)
                
                if app_controls:
                    self.all_controls.extend(app_controls)
            
        except Exception as e:
            print(f"❌ 刷新缓存失败: {e}")
    
    def _collect_controls_recursive(self, element, control_list, app_name, depth=0, max_depth=6):
        """递归收集控件"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 收集所有有坐标的控件
            if extents and extents.width > 0 and extents.height > 0:
                control_list.append({
                    'element': element,
                    'name': name,
                    'role': role,
                    'extents': extents,
                    'depth': depth,
                    'app_name': app_name
                })
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_controls_recursive(child, control_list, app_name, depth + 1, max_depth)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def find_control_hellobig_mode(self, mouse_x, mouse_y):
        """hellobig模式：使用精确偏移"""
        print(f"🎯 使用hellobig精确模式")
        
        # 转换为AT-SPI坐标
        atspi_x = mouse_x - self.hellobig_offset[0]
        atspi_y = mouse_y - self.hellobig_offset[1]
        
        print(f"   鼠标坐标: ({mouse_x}, {mouse_y})")
        print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y})")
        print(f"   使用偏移: {self.hellobig_offset}")
        
        # 查找匹配的控件
        matches = []
        for ctrl in self.all_controls:
            ext = ctrl['extents']
            
            if (ext.x <= atspi_x < ext.x + ext.width and
                ext.y <= atspi_y < ext.y + ext.height):
                matches.append(ctrl)
        
        if not matches:
            return None
        
        print(f"   找到 {len(matches)} 个匹配控件")
        
        # 选择最具体的控件
        specific_matches = [m for m in matches if m['role'] in self.specific_control_types]
        
        if specific_matches:
            best_match = min(specific_matches, key=lambda x: x['extents'].width * x['extents'].height)
            print(f"   ✅ 选择最具体控件: {best_match['name']} ({best_match['role']})")
            return {
                'control': best_match,
                'offset': self.hellobig_offset,
                'method': 'hellobig_precise'
            }
        else:
            best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
            print(f"   ⚠️  选择最小控件: {best_match['name']} ({best_match['role']})")
            return {
                'control': best_match,
                'offset': self.hellobig_offset,
                'method': 'hellobig_fallback'
            }
    
    def find_control_universal_mode(self, mouse_x, mouse_y):
        """通用模式：智能检测其他应用"""
        print(f"🌍 使用通用检测模式")
        
        # 方法1：直接AT-SPI检测（适用于大多数GTK应用）
        print(f"   尝试直接AT-SPI检测...")
        direct_matches = []
        for ctrl in self.all_controls:
            ext = ctrl['extents']
            
            if (ext.x <= mouse_x < ext.x + ext.width and
                ext.y <= mouse_y < ext.y + ext.height):
                direct_matches.append(ctrl)
        
        if direct_matches:
            print(f"   直接检测找到 {len(direct_matches)} 个控件")
            
            # 选择最具体的控件
            specific_matches = [m for m in direct_matches if m['role'] in self.specific_control_types]
            
            if specific_matches:
                best_match = min(specific_matches, key=lambda x: x['extents'].width * x['extents'].height)
                return {
                    'control': best_match,
                    'offset': None,
                    'method': 'direct_atspi'
                }
        
        # 方法2：尝试常见的窗口偏移（适用于Qt等应用）
        print(f"   尝试常见窗口偏移...")
        common_offsets = [
            (0, -30),    # 标题栏高度
            (-8, -38),   # 边框+标题栏
            (8, 30),     # 反向偏移
            (0, 0)       # 无偏移
        ]
        
        for offset_x, offset_y in common_offsets:
            atspi_x = mouse_x - offset_x
            atspi_y = mouse_y - offset_y
            
            matches = []
            for ctrl in self.all_controls:
                ext = ctrl['extents']
                
                if (ext.x <= atspi_x < ext.x + ext.width and
                    ext.y <= atspi_y < ext.y + ext.height):
                    matches.append(ctrl)
            
            if matches:
                specific_matches = [m for m in matches if m['role'] in self.specific_control_types]
                
                if specific_matches:
                    best_match = min(specific_matches, key=lambda x: x['extents'].width * x['extents'].height)
                    print(f"   偏移 ({offset_x}, {offset_y}) 找到控件: {best_match['name']}")
                    return {
                        'control': best_match,
                        'offset': (offset_x, offset_y),
                        'method': 'common_offset'
                    }
        
        # 如果都失败了，返回直接检测的最佳结果
        if direct_matches:
            best_match = min(direct_matches, key=lambda x: x['extents'].width * x['extents'].height)
            return {
                'control': best_match,
                'offset': None,
                'method': 'direct_fallback'
            }
        
        return None
    
    def get_control_info(self, ctrl):
        """获取控件详细信息"""
        try:
            # 获取状态
            states = []
            try:
                state = ctrl['element'].getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass
            
            # 获取动作
            actions = []
            try:
                if hasattr(ctrl['element'], 'queryAction'):
                    action = ctrl['element'].queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass
            
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'app_name': ctrl.get('app_name', 'Unknown'),
                'states': states,
                'actions': actions,
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
        except Exception:
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'app_name': ctrl.get('app_name', 'Unknown'),
                'states': [],
                'actions': [],
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
    
    def highlight_control(self, ctrl, offset=None, duration=3, color='lime', border_width=3):
        """高亮控件"""
        if not ctrl:
            return False
        
        ext = ctrl['extents']
        
        # 计算屏幕坐标
        if offset:
            screen_x = ext.x + offset[0]
            screen_y = ext.y + offset[1]
        else:
            screen_x = ext.x
            screen_y = ext.y
        
        width = ext.width
        height = ext.height
        
        try:
            from ultimate_highlight import ultimate_highlight
            return ultimate_highlight(
                screen_x, screen_y, width, height,
                duration=duration, color=color, border_width=border_width
            )
        except Exception as e:
            print(f"❌ 高亮异常: {e}")
            return False
    
    def detect_control_at_mouse(self):
        """检测鼠标位置的控件"""
        # 获取鼠标位置
        mouse_pos = get_mouse_position()
        if mouse_pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return False
        
        mouse_x, mouse_y = mouse_pos
        
        print(f"\n🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        print("-" * 60)
        
        # 检测应用类型
        app_type = detect_application_at_mouse(mouse_x, mouse_y)
        print(f"📱 检测到应用类型: {app_type}")
        
        # 刷新控件缓存
        print(f"🔄 刷新控件缓存...")
        self.refresh_controls(app_type)
        print(f"   收集到 {len(self.all_controls)} 个控件")
        
        if not self.all_controls:
            print("❌ 未找到任何控件")
            return False
        
        # 根据应用类型选择检测方法
        if app_type == 'hellobig':
            result = self.find_control_hellobig_mode(mouse_x, mouse_y)
        else:
            result = self.find_control_universal_mode(mouse_x, mouse_y)
        
        if not result:
            print("❌ 未找到匹配的控件")
            return False
        
        ctrl = result['control']
        offset = result['offset']
        method = result['method']
        
        # 获取控件信息
        info = self.get_control_info(ctrl)
        
        print(f"\n✅ 检测成功! (方法: {method})")
        print("=" * 60)
        print(f"📱 应用程序: {info['app_name']}")
        print(f"📋 控件名称: {info['name']}")
        print(f"🏷️  控件类型: {info['role']}")
        print(f"📍 AT-SPI位置: {info['position']}")
        print(f"📏 尺寸: {info['size']}")
        
        if offset:
            screen_x = info['position'][0] + offset[0]
            screen_y = info['position'][1] + offset[1]
            print(f"🔄 使用偏移: {offset}")
            print(f"📍 屏幕位置: ({screen_x}, {screen_y})")
        
        if info['states']:
            print(f"🔧 状态: {', '.join(info['states'][:3])}")
        
        if info['actions']:
            print(f"⚡ 动作: {', '.join(info['actions'])}")
        
        # 执行高亮
        print(f"\n✨ 执行高亮...")
        highlight_success = self.highlight_control(ctrl, offset, duration=3, color='orange', border_width=3)
        
        if highlight_success:
            print(f"✅ 高亮成功!")
        else:
            print(f"❌ 高亮失败")
        
        return True


def main():
    """主函数"""
    print("🧠 智能通用控件检测器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        detector = SmartUniversalDetector()
        
        print(f"\n🚀 开始检测当前鼠标位置的控件...")
        success = detector.detect_control_at_mouse()
        
        if success:
            print(f"\n🎉 检测完成!")
        else:
            print(f"\n❌ 检测失败")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
