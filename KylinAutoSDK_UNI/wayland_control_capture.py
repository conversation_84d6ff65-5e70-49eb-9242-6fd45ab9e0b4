#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
Wayland环境下的控件捕获工具
替代X11的全局监听功能，提供类似的控件捕获和高亮体验
"""

import sys
import os
import time
import threading
import signal
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')

from universal_offset_detector import UniversalOffsetDetector, get_mouse_position


class WaylandControlCapture:
    """Wayland环境下的控件捕获器"""
    
    def __init__(self):
        """初始化捕获器"""
        self.detector = UniversalOffsetDetector()
        self.running = False
        self.capture_thread = None
        self.last_position = (None, None)
        self.last_control_id = None
        self.capture_mode = False  # 是否处于捕获模式
        
        print("🌊 Wayland控件捕获工具")
        print("=" * 60)
        print("🎯 替代X11全局监听，实现控件捕获和高亮")
        print("💡 使用说明：")
        print("   1. 启动后进入监控模式")
        print("   2. 将鼠标悬停在目标控件上")
        print("   3. 按 Enter 键捕获当前控件")
        print("   4. 按 Ctrl+C 退出程序")
        print("=" * 60)
    
    def start_capture_mode(self):
        """启动捕获模式"""
        if self.running:
            print("⚠️  捕获模式已在运行中")
            return
        
        self.running = True
        
        # 启动监控线程
        self.capture_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.capture_thread.start()
        
        # 启动用户输入处理线程
        input_thread = threading.Thread(target=self._input_handler, daemon=True)
        input_thread.start()
        
        print("🚀 捕获模式已启动")
        print("📍 将鼠标悬停在目标控件上，然后按 Enter 键捕获")
        print()
    
    def stop_capture_mode(self):
        """停止捕获模式"""
        self.running = False
        if self.capture_thread:
            self.capture_thread.join(timeout=1)
        print("🛑 捕获模式已停止")
    
    def _monitor_loop(self):
        """监控循环 - 实时显示鼠标下的控件信息"""
        while self.running:
            try:
                # 获取当前鼠标位置
                mouse_pos = get_mouse_position()
                if mouse_pos == (None, None):
                    time.sleep(0.2)
                    continue
                
                mouse_x, mouse_y = mouse_pos
                
                # 检查位置是否变化
                if (mouse_x, mouse_y) != self.last_position:
                    self.last_position = (mouse_x, mouse_y)
                    self._show_current_control(mouse_x, mouse_y)
                
                time.sleep(0.3)  # 300ms间隔，平衡响应性和性能
                
            except Exception as e:
                print(f"❌ 监控循环错误: {e}")
                time.sleep(1)
    
    def _show_current_control(self, mouse_x, mouse_y):
        """显示当前鼠标下的控件信息"""
        try:
            # 计算窗口偏移
            offset, atspi_window_info = self.detector.calculate_window_offset(mouse_x, mouse_y)
            if not offset or not atspi_window_info:
                return
            
            # 收集应用控件
            self.detector.collect_app_controls(atspi_window_info)
            if not self.detector.current_app_controls:
                return
            
            # 查找控件
            ctrl = self.detector.find_control_with_offset(mouse_x, mouse_y, offset)
            if not ctrl:
                return
            
            # 生成控件ID，避免重复显示
            ctrl_id = f"{ctrl['name']}_{ctrl['extents'].x}_{ctrl['extents'].y}"
            if ctrl_id == self.last_control_id:
                return
            
            self.last_control_id = ctrl_id
            
            # 显示控件信息
            info = self.detector.get_control_info(ctrl)
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # 清屏并显示当前状态
            os.system('clear')
            print("🌊 Wayland控件捕获工具 - 实时监控")
            print("=" * 60)
            print(f"⏰ 时间: {timestamp}")
            print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
            print()
            print("🎯 当前检测到的控件:")
            print(f"   📱 应用程序: {atspi_window_info['app_name']}")
            print(f"   🪟 窗口名称: {atspi_window_info['window_name']}")
            print(f"   📋 控件名称: {info['name']}")
            print(f"   🏷️  控件类型: {info['role']}")
            print(f"   📍 AT-SPI位置: {info['position']}")
            print(f"   📏 尺寸: {info['size']}")
            print(f"   🔄 计算偏移: {offset}")
            
            if info['states']:
                print(f"   🔧 状态: {', '.join(info['states'][:3])}")
            
            if info['actions']:
                print(f"   ⚡ 动作: {', '.join(info['actions'])}")
            
            print()
            print("💡 操作提示:")
            print("   - 按 Enter 键捕获当前控件的完整信息")
            print("   - 按 Ctrl+C 退出程序")
            print("   - 移动鼠标到其他控件继续检测")
            
            # 显示轻量级高亮（短时间）
            self.detector.highlight_control(ctrl, offset, 
                                          duration=0.8, 
                                          color='yellow', border_width=1)
            
        except Exception as e:
            print(f"❌ 显示控件信息错误: {e}")
    
    def _input_handler(self):
        """处理用户输入"""
        while self.running:
            try:
                user_input = input().strip()
                
                if user_input == "":  # Enter键
                    self._capture_current_control()
                elif user_input.lower() in ['q', 'quit', 'exit']:
                    self.stop_capture_mode()
                    break
                    
            except (EOFError, KeyboardInterrupt):
                break
            except Exception as e:
                print(f"❌ 输入处理错误: {e}")
    
    def _capture_current_control(self):
        """捕获当前控件的完整信息"""
        try:
            mouse_pos = get_mouse_position()
            if mouse_pos == (None, None):
                print("❌ 无法获取鼠标位置")
                return
            
            mouse_x, mouse_y = mouse_pos
            
            print(f"\n🎯 正在捕获位置 ({mouse_x}, {mouse_y}) 的控件...")
            
            # 使用检测器捕获完整信息
            success = self.detector.detect_control_at_mouse(output_format='original')
            
            if success:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"captured_control_{timestamp}.json"
                
                print(f"✅ 控件信息已捕获!")
                print(f"📄 详细信息已保存到: {filename}")
                print(f"💾 同时保存到: detected_control_info.txt")
                
                # 显示强烈高亮确认捕获
                offset, atspi_window_info = self.detector.calculate_window_offset(mouse_x, mouse_y)
                if offset and atspi_window_info:
                    self.detector.collect_app_controls(atspi_window_info)
                    ctrl = self.detector.find_control_with_offset(mouse_x, mouse_y, offset)
                    if ctrl:
                        self.detector.highlight_control(ctrl, offset, 
                                                      duration=3, 
                                                      color='lime', border_width=4)
            else:
                print("❌ 控件捕获失败")
            
            print("\n按 Enter 继续监控，或 Ctrl+C 退出...")
            
        except Exception as e:
            print(f"❌ 捕获控件错误: {e}")


def main():
    """主函数"""
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    # 创建捕获器
    capture = WaylandControlCapture()
    
    # 设置信号处理
    def signal_handler(sig, frame):
        print(f"\n🛑 收到信号 {sig}，正在退出...")
        capture.stop_capture_mode()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动捕获模式
        capture.start_capture_mode()
        
        # 保持主线程运行
        while capture.running:
            time.sleep(1)
            
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        capture.stop_capture_mode()


if __name__ == "__main__":
    main()
