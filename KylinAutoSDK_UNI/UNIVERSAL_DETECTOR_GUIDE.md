# universal_offset_detector.py 原始格式输出指南

## 概述

`universal_offset_detector.py` 脚本现在已经完全支持输出与您的 `control_info.txt` 文件相同格式的控件信息。通过添加 `--format original` 参数，您可以获得与原始 UNI 输出完全一致的 JSON 数据。

## 🎯 验证结果

### ✅ 格式完全兼容
- **顶层字段完全匹配**: `['actions', 'capture_status', 'coords', 'datamap', 'description', 'name', 'states', 'type']`
- **datamap 字段完全匹配**: 包含所有 20 个必需字段
- **数据类型完全一致**: 所有字段的数据类型与原始格式保持一致

### ✅ 功能验证
- **鼠标位置自动检测**: 自动获取当前鼠标位置
- **窗口偏移自动计算**: 智能计算 Wayland 环境下的坐标偏移
- **控件高亮显示**: 成功检测后自动高亮目标控件
- **文件自动保存**: 生成 `detected_control_info.txt` 文件

## 🚀 使用方法

### 1. 简单格式输出（默认）
```bash
python3 universal_offset_detector.py
```
或
```bash
python3 universal_offset_detector.py --format simple
```

**输出特点:**
- 易读的控件信息显示
- 适合调试和查看
- 包含应用程序、窗口名称、控件详情等

### 2. 原始格式输出（推荐用于数据处理）
```bash
python3 universal_offset_detector.py --format original
```

**输出特点:**
- 与 `control_info.txt` 完全相同的 JSON 格式
- 自动生成 `detected_control_info.txt` 文件
- 可以直接替换原始 UNI 输出使用

## 📊 原始格式输出示例

```json
{
  "name": "进度条",
  "type": "progress bar",
  "coords": {
    "x": 1026,
    "y": 408,
    "width": 200,
    "height": 25
  },
  "datamap": {
    "Name": "进度条",
    "ID": -1,
    "ProcessID": 1521566,
    "Rolename": "progress bar",
    "Description": "显示当前进度",
    "Index_in_parent": 12,
    "ChildrenCount": 0,
    "ProcessName": "hellobig",
    "Coords": {
      "x": 1026,
      "y": 408,
      "width": 200,
      "height": 25
    },
    "Text": "Not available: ",
    "Actions": ["Increase", "Decrease"],
    "States": ["enabled", "sensitive", "showing", "visible", "read only"],
    "ParentPath": [0, 0, 12],
    "ParentCount": 3,
    "Key": "N进度条-D显示当前进度-P0012",
    "RecordPosition": [1169, 415],
    "WindowRoleName": "frame",
    "WindowChildCount": 1,
    "WindowName": "AT-SPI测试主窗口",
    "capture_status": "success"
  },
  "description": "显示当前进度",
  "states": ["enabled", "sensitive", "showing", "visible", "read only"],
  "actions": ["Increase", "Decrease"],
  "capture_status": "success"
}
```

## 📋 数据字段说明

### 顶层字段
- `name`: 控件名称
- `type`: 控件类型/角色
- `coords`: 控件坐标信息 `{x, y, width, height}`
- `datamap`: 详细信息对象（包含所有原始字段）
- `description`: 控件描述
- `states`: 控件状态列表
- `actions`: 可用动作列表
- `capture_status`: 捕获状态（"success"）

### datamap 详细字段
- `Name`: 控件名称
- `ID`: 元素ID
- `ProcessID`: 进程ID
- `Rolename`: 控件角色
- `Description`: 描述
- `Index_in_parent`: 在父元素中的索引
- `ChildrenCount`: 子元素数量
- `ProcessName`: 进程名称
- `Coords`: 坐标信息
- `Text`: 文本内容
- `Actions`: 动作列表
- `States`: 状态列表
- `ParentPath`: 父路径列表
- `ParentCount`: 父路径数量
- `Key`: 唯一标识符
- `RecordPosition`: 记录的鼠标位置 `[x, y]`
- `WindowRoleName`: 窗口角色
- `WindowChildCount`: 窗口子控件数量
- `WindowName`: 窗口名称
- `capture_status`: 捕获状态

## 🔧 实际使用场景

### 场景1: 替换原始 UNI 输出
```bash
# 将鼠标悬停在目标控件上
python3 universal_offset_detector.py --format original

# 使用生成的 detected_control_info.txt 文件
# 该文件格式与原始 control_info.txt 完全一致
```

### 场景2: 批量控件信息收集
```bash
# 脚本化使用
for position in positions:
    # 移动鼠标到指定位置
    # 运行检测
    python3 universal_offset_detector.py --format original
    # 处理生成的 JSON 数据
```

### 场景3: 与现有代码集成
```python
import json
import subprocess

# 运行检测
result = subprocess.run([
    'python3', 'universal_offset_detector.py', '--format', 'original'
], capture_output=True, text=True)

# 读取生成的数据
with open('detected_control_info.txt', 'r', encoding='utf-8') as f:
    control_data = json.load(f)

# 使用数据（格式与原始 UNI 输出完全一致）
print(f"控件名称: {control_data['name']}")
print(f"控件类型: {control_data['type']}")
print(f"进程名称: {control_data['datamap']['ProcessName']}")
```

## 🎉 优势特点

### ✅ 完全兼容
- 与原始 `control_info.txt` 格式 100% 一致
- 包含所有必需字段和数据类型
- 可以直接替换原始 UNI 输出

### ✅ 自动化程度高
- 自动获取鼠标位置
- 自动计算窗口偏移
- 自动选择最合适的控件
- 自动保存结果文件

### ✅ Wayland 环境优化
- 专为 Wayland 环境设计
- 智能窗口检测和偏移计算
- 支持复杂的窗口层叠场景

### ✅ 用户友好
- 详细的执行过程输出
- 清晰的错误提示
- 支持控件高亮显示
- 灵活的输出格式选择

## 📝 总结

现在您的 `universal_offset_detector.py` 脚本已经完全支持输出与原始 UNI 相同格式的控件信息。无论是用于替换现有的控件检测逻辑，还是集成到新的自动化流程中，都能提供完全一致的数据格式，确保无缝兼容。
