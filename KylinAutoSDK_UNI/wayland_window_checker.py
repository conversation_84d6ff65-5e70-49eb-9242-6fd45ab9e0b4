#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
Wayland环境窗口状态检查器
专门针对Wayland环境的窗口可见性检测
"""

import sys
import os
import subprocess
import re
import json


def check_wayland_environment():
    """检查Wayland环境"""
    print("🔍 检查Wayland环境...")
    
    wayland_display = os.getenv('WAYLAND_DISPLAY')
    xdg_session_type = os.getenv('XDG_SESSION_TYPE')
    
    print(f"   WAYLAND_DISPLAY: {wayland_display}")
    print(f"   XDG_SESSION_TYPE: {xdg_session_type}")
    
    is_wayland = wayland_display or xdg_session_type == 'wayland'
    
    if is_wayland:
        print("✅ 确认Wayland环境")
    else:
        print("⚠️  可能不是Wayland环境")
    
    return is_wayland


def check_wayland_compositor():
    """检查Wayland合成器类型"""
    print("\n🔍 检查Wayland合成器...")
    
    # 检查常见的Wayland合成器
    compositors = [
        ('mutter', '<PERSON><PERSON><PERSON><PERSON> Mutter'),
        ('kwin_wayland', 'K<PERSON> KWin'),
        ('sway', 'Sway'),
        ('weston', 'Weston'),
        ('wayfire', 'Wayfire'),
        ('river', 'River'),
        ('hyprland', 'Hyprland')
    ]
    
    running_compositor = None
    
    for process_name, display_name in compositors:
        try:
            result = subprocess.run(['pgrep', process_name], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ 检测到合成器: {display_name}")
                running_compositor = process_name
                break
        except Exception:
            continue
    
    if not running_compositor:
        print("❌ 未检测到已知的Wayland合成器")
    
    return running_compositor


def check_wlcctrl_detailed():
    """详细检查wlcctrl的功能"""
    print("\n🔍 详细检查wlcctrl功能...")
    
    try:
        # 检查wlcctrl版本
        result = subprocess.run(['wlcctrl', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   版本: {result.stdout.strip()}")
        
        # 获取所有窗口的详细信息
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            print("   ❌ wlcctrl --list 失败")
            return []
        
        print(f"   原始输出:")
        lines = result.stdout.strip().split('\n')
        for i, line in enumerate(lines[:10]):  # 显示前10行
            print(f"     {i+1}: {line}")
        
        # 解析窗口信息
        windows = []
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                # 获取详细几何信息
                geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                          capture_output=True, text=True)
                
                window_info = {
                    'id': window_id,
                    'title': window_title,
                    'geometry_raw': geo_result.stdout if geo_result.returncode == 0 else None
                }
                
                if geo_result.returncode == 0:
                    # 解析几何信息
                    geo_lines = geo_result.stdout.strip().split('\n')
                    for geo_line in geo_lines:
                        if 'geometry:' in geo_line:
                            match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                            if match:
                                x, y, width, height = map(int, match.groups())
                                window_info.update({
                                    'x': x, 'y': y, 'width': width, 'height': height,
                                    'position': (x, y), 'size': (width, height)
                                })
                        elif 'minimized:' in geo_line.lower():
                            window_info['minimized_info'] = geo_line.strip()
                        elif 'visible:' in geo_line.lower():
                            window_info['visible_info'] = geo_line.strip()
                        elif 'active:' in geo_line.lower():
                            window_info['active_info'] = geo_line.strip()
                
                windows.append(window_info)
                i += 2
            else:
                i += 1
        
        print(f"\n   解析结果: 找到 {len(windows)} 个窗口")
        return windows
        
    except Exception as e:
        print(f"   ❌ wlcctrl检查失败: {e}")
        return []


def analyze_hellobig_window_state(windows):
    """分析hellobig窗口状态"""
    print(f"\n🎯 分析hellobig窗口状态...")
    
    hellobig_windows = []
    
    for window in windows:
        title = window['title']
        if ('AT-SPI测试' in title or 'hellobig' in title.lower() or 
            'Qt控件集合' in title):
            hellobig_windows.append(window)
    
    if not hellobig_windows:
        print("❌ 未找到hellobig相关窗口")
        print("💡 这强烈表明hellobig窗口已最小化或关闭")
        return False, "not_found"
    
    print(f"✅ 找到 {len(hellobig_windows)} 个hellobig相关窗口:")
    
    all_visible = True
    analysis_results = []
    
    for window in hellobig_windows:
        print(f"\n   窗口: {window['title']}")
        print(f"   ID: {window['id']}")
        
        if 'position' in window:
            print(f"   位置: {window['position']}")
            print(f"   尺寸: {window['size']}")
            
            # 分析几何信息
            x, y, width, height = window['x'], window['y'], window['width'], window['height']
            
            # 检查异常几何信息
            if width >= 2500 or height >= 1500:
                print(f"   ⚠️  异常大尺寸，可能是最小化窗口")
                all_visible = False
                analysis_results.append("large_size")
            elif x == 0 and y == 0 and (width > 1500 or height > 1000):
                print(f"   ⚠️  在原点且尺寸大，可能是最小化窗口")
                all_visible = False
                analysis_results.append("origin_large")
            else:
                print(f"   ✅ 几何信息正常")
                analysis_results.append("normal_geometry")
        
        # 检查其他状态信息
        if 'minimized_info' in window:
            print(f"   最小化信息: {window['minimized_info']}")
            if 'true' in window['minimized_info'].lower():
                print(f"   🔴 wlcctrl确认窗口已最小化")
                all_visible = False
                analysis_results.append("minimized_true")
        
        if 'visible_info' in window:
            print(f"   可见性信息: {window['visible_info']}")
            if 'false' in window['visible_info'].lower():
                print(f"   🔴 wlcctrl确认窗口不可见")
                all_visible = False
                analysis_results.append("visible_false")
        
        if 'active_info' in window:
            print(f"   活动状态: {window['active_info']}")
        
        if 'geometry_raw' in window and window['geometry_raw']:
            print(f"   原始几何信息:")
            for line in window['geometry_raw'].split('\n'):
                if line.strip():
                    print(f"     {line.strip()}")
    
    # 综合分析
    print(f"\n📊 综合分析:")
    print(f"   分析结果: {analysis_results}")
    
    if not all_visible:
        print(f"🔴 结论: hellobig窗口不可见")
        print(f"   原因: 检测到最小化或隐藏状态")
        return False, "not_visible"
    else:
        print(f"🟢 结论: hellobig窗口可见")
        print(f"   原因: 所有检查都显示窗口正常")
        return True, "visible"


def check_active_window():
    """检查当前活动窗口"""
    print(f"\n🔍 检查当前活动窗口...")
    
    try:
        result = subprocess.run(['wlcctrl', '--getactivewindow'], capture_output=True, text=True)
        if result.returncode == 0:
            active_window_id = result.stdout.strip()
            print(f"   活动窗口ID: {active_window_id}")
            
            # 获取活动窗口的标题
            result = subprocess.run(['wlcctrl', '--getwindowtitle', active_window_id], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                active_title = result.stdout.strip()
                print(f"   活动窗口标题: {active_title}")
                
                if ('AT-SPI测试' in active_title or 'hellobig' in active_title.lower()):
                    print(f"   ✅ hellobig是当前活动窗口")
                    return True, active_window_id, active_title
                else:
                    print(f"   ❌ hellobig不是当前活动窗口")
                    return False, active_window_id, active_title
        else:
            print(f"   ❌ 无法获取活动窗口: {result.stderr}")
            return None, None, None
    
    except Exception as e:
        print(f"   ❌ 检查活动窗口失败: {e}")
        return None, None, None


def main():
    """主函数"""
    print("🔍 Wayland环境窗口状态检查器")
    print("=" * 60)
    print("🎯 专门针对Wayland环境的窗口可见性检测")
    print("💡 解决AT-SPI状态信息不准确的问题")
    print("=" * 60)
    
    # 检查Wayland环境
    is_wayland = check_wayland_environment()
    
    if not is_wayland:
        print("\n⚠️  警告: 可能不在Wayland环境中")
    
    # 检查合成器
    compositor = check_wayland_compositor()
    
    # 检查wlcctrl功能
    windows = check_wlcctrl_detailed()
    
    if not windows:
        print("\n❌ 无法获取窗口信息")
        return
    
    # 检查活动窗口
    is_active, active_id, active_title = check_active_window()
    
    # 分析hellobig窗口状态
    is_visible, reason = analyze_hellobig_window_state(windows)
    
    print(f"\n💡 最终结论:")
    print("=" * 30)
    
    if is_visible:
        print("✅ hellobig窗口可见")
        print("   AT-SPI检测应该是准确的")
    else:
        print("❌ hellobig窗口不可见/已最小化")
        print("   这解释了为什么AT-SPI状态不准确")
        print("   应该拒绝在虚无位置绘制控件")
    
    print(f"\n🔧 集成建议:")
    print("=" * 30)
    print("可以将wlcctrl的窗口存在性检查集成到")
    print("universal_offset_detector.py中:")
    print("1. 使用wlcctrl --list检查窗口是否存在")
    print("2. 检查窗口几何信息是否异常")
    print("3. 如果窗口不存在或几何异常，拒绝检测")


if __name__ == "__main__":
    main()
