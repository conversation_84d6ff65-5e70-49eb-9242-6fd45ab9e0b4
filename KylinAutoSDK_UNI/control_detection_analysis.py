#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
控件识别范围分析工具
分析为什么只能识别pushbutton，而无法识别其他控件类型
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>in<PERSON>utoSDK_UNI/src')

def analyze_control_detection_scope():
    """分析控件识别范围限制"""
    print("🔍 控件识别范围分析")
    print("=" * 70)
    
    # 1. 分析当前的控件类型限制
    print("📋 1. 当前控件类型限制分析")
    print("-" * 40)
    
    print("🚫 发现的限制问题:")
    print("   在 search_control_ultra_fast() 函数中:")
    print("   priority_roles = {'push button', 'button', 'radio button', 'check box'}")
    print("   → 只搜索4种控件类型")
    print()
    
    print("   在 search_control_optimized() 函数中:")
    print("   interactive_roles = {")
    print("       'push button', 'button', 'radio button', 'check box',")
    print("       'toggle button', 'menu item', 'combo box', 'text'")
    print("   }")
    print("   → 只搜索8种控件类型")
    print()
    
    # 2. hellobig应用控件调查
    print("📋 2. hellobig应用实际控件调查")
    print("-" * 40)
    
    try:
        from UNI_new import UNI
        import subprocess
        
        # 获取鼠标位置
        mouse_x, mouse_y = get_mouse_position()
        print(f"测试位置: ({mouse_x}, {mouse_y})")
        
        uni = UNI()
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        
        if control_data:
            atspi_element = control_data.get('_atspi_element')
            if atspi_element:
                print("✅ 找到控件，开始深度扫描应用中的所有控件类型...")
                all_control_types = scan_all_control_types(atspi_element)
                
                print(f"\n🎯 hellobig应用中发现的所有控件类型:")
                for role, count in sorted(all_control_types.items()):
                    status = "✅" if is_role_supported(role) else "❌"
                    print(f"   {status} {role}: {count}个")
                
                unsupported = {role: count for role, count in all_control_types.items() 
                             if not is_role_supported(role)}
                
                if unsupported:
                    print(f"\n⚠️ 未支持的控件类型 ({len(unsupported)}种):")
                    for role, count in sorted(unsupported.items()):
                        print(f"   • {role}: {count}个")
            else:
                print("❌ 无法获取AT-SPI元素")
        else:
            print("❌ 未检测到控件")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
    
    print()
    
    # 3. 解决方案建议
    print("📋 3. 解决方案建议")
    print("-" * 40)
    
    print("💡 问题根源:")
    print("   UNI_new.py中的搜索函数人为限制了控件类型范围")
    print("   这是为了提高性能，但牺牲了识别范围")
    print()
    
    print("🔧 建议的修复方案:")
    print("   方案A: 扩展支持的控件类型列表")
    print("   方案B: 添加一个'完全模式'，搜索所有控件类型")
    print("   方案C: 根据应用类型动态调整搜索范围")
    print()
    
    print("🎯 推荐方案: 方案A + 方案B")
    print("   • 扩展默认支持的控件类型")
    print("   • 添加complete_search参数支持完全搜索")

def get_mouse_position():
    """获取鼠标位置"""
    try:
        import subprocess
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except:
        pass
    return 1000, 500

def scan_all_control_types(start_element):
    """递归扫描所有控件类型"""
    control_types = {}
    
    def scan_recursive(element, depth=0):
        if depth > 10:  # 防止无限递归
            return
            
        try:
            role = element.getRoleName()
            if role:
                control_types[role] = control_types.get(role, 0) + 1
            
            # 递归扫描子元素
            for i in range(element.childCount):
                try:
                    child = element.getChildAtIndex(i)
                    scan_recursive(child, depth + 1)
                except:
                    continue
                    
        except Exception:
            pass
    
    # 从应用根元素开始扫描
    try:
        # 找到应用根元素
        app_element = start_element
        while app_element and hasattr(app_element, 'parent') and app_element.parent:
            try:
                parent = app_element.parent
                if parent.getRoleName() == 'application':
                    app_element = parent
                    break
                app_element = parent
            except:
                break
        
        scan_recursive(app_element)
    except Exception as e:
        print(f"扫描失败: {e}")
    
    return control_types

def is_role_supported(role):
    """检查控件类型是否被当前系统支持"""
    # 当前系统支持的控件类型
    supported_roles = {
        'push button', 'button', 'radio button', 'check box',
        'toggle button', 'menu item', 'combo box', 'text'
    }
    return role.lower() in supported_roles

def create_enhanced_search_functions():
    """创建增强的搜索函数"""
    print("\n🔧 创建增强的控件搜索函数")
    print("=" * 70)
    
    # 扩展的控件类型列表
    extended_roles = {
        # 基础交互控件
        'push button', 'button', 'radio button', 'check box', 'toggle button',
        
        # 文本控件
        'text', 'label', 'static text', 'paragraph', 'heading',
        'password text', 'terminal',
        
        # 输入控件
        'combo box', 'list box', 'text box', 'spin button', 'entry',
        'editable text', 'password field',
        
        # 选择控件
        'menu item', 'menu', 'list', 'list item', 'tree', 'tree item',
        'table', 'table cell', 'table row', 'table column header',
        
        # 容器控件
        'panel', 'pane', 'tab list', 'tab', 'scroll bar', 'slider',
        'progress bar', 'group box', 'frame', 'filler',
        
        # 图形控件
        'image', 'icon', 'canvas', 'drawing area',
        
        # 窗口控件
        'window', 'dialog', 'tool bar', 'menu bar', 'status bar',
        
        # 其他控件
        'separator', 'link', 'document', 'embedded object',
        'page tab', 'calendar', 'date editor', 'color chooser'
    }
    
    enhanced_code = f'''
def search_control_enhanced(app_element, target_x, target_y, target_app, complete_search=False):
    """
    增强的控件搜索算法
    
    Args:
        complete_search: True = 搜索所有控件类型, False = 仅搜索常用类型
    """
    from collections import deque
    from uni_sdk.utils.helpers import get_element_extents
    
    # 根据模式选择搜索范围
    if complete_search:
        # 完全搜索模式：搜索所有可能的控件类型
        target_roles = {{{', '.join(f"'{role}'" for role in sorted(extended_roles))}}}
        max_depth = 8
        max_checks = 200
        print("🔍 使用完全搜索模式 - 搜索所有控件类型")
    else:
        # 快速搜索模式：仅搜索常用交互控件
        target_roles = {{
            'push button', 'button', 'radio button', 'check box', 'toggle button',
            'text', 'label', 'combo box', 'list', 'menu item', 'entry',
            'editable text', 'text box'
        }}
        max_depth = 4
        max_checks = 50
        print("⚡ 使用快速搜索模式 - 搜索常用控件类型")
    
    search_queue = deque([(app_element, 0)])
    checked_count = 0
    found_controls = []
    
    while search_queue and checked_count < max_checks:
        element, depth = search_queue.popleft()
        checked_count += 1
        
        if depth > max_depth:
            continue
            
        try:
            role = element.getRoleName().lower()
            
            # 检查是否是目标控件类型
            if role in target_roles:
                extents = get_element_extents(element)
                if extents and is_coordinate_in_control(target_x, target_y, extents, target_app):
                    control_data = build_control_data(element, extents, target_app)
                    if control_data:
                        score = calculate_control_priority(control_data, target_x, target_y, target_app)
                        found_controls.append((control_data, score))
            
            # 添加子元素到搜索队列
            container_roles = {{'frame', 'filler', 'panel', 'pane', 'window', 'dialog'}}
            if role in container_roles or depth < 2:
                child_limit = 10 if complete_search else 5
                for i in range(min(element.childCount, child_limit)):
                    try:
                        child = element.getChildAtIndex(i)
                        search_queue.append((child, depth + 1))
                    except:
                        continue
                        
        except Exception:
            continue
    
    # 返回最佳匹配
    if found_controls:
        found_controls.sort(key=lambda x: x[1], reverse=True)
        return found_controls[0][0]
    
    return None
'''
    
    # 保存增强的搜索函数
    with open('/home/<USER>/KylinAutoSDK_UNI/enhanced_search.py', 'w', encoding='utf-8') as f:
        f.write(f'''#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
增强的控件搜索函数
解决控件识别范围限制问题
"""

{enhanced_code}

# 支持的控件类型列表
EXTENDED_CONTROL_TYPES = {{{', '.join(f"'{role}'" for role in sorted(extended_roles))}}}

def get_supported_control_types():
    """获取支持的控件类型列表"""
    return EXTENDED_CONTROL_TYPES.copy()
''')
    
    print("✅ 创建了增强的搜索函数: enhanced_search.py")
    print(f"   支持控件类型: {len(extended_roles)}种")
    print("   模式选择: complete_search=True/False")

if __name__ == "__main__":
    print()
    print("🎯 UNI控件识别范围分析工具")
    print()
    print("📖 分析目标:")
    print("   1. 找出为什么只能识别pushbutton类型")
    print("   2. 分析hellobig应用中的所有控件类型")
    print("   3. 提供扩展控件识别范围的解决方案")
    print()
    
    input("按回车键开始分析...")
    print()
    
    analyze_control_detection_scope()
    create_enhanced_search_functions()
    
    print()
    print("🎉 分析完成！")
    print()
    print("💡 下一步建议:")
    print("   1. 查看分析结果中未支持的控件类型")
    print("   2. 运行增强版搜索函数测试")
    print("   3. 根据需要集成到UNI_new.py中")