#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
通用AT-SPI坐标转换验证测试
测试真正的通用接口，无硬编码应用适配
"""

import sys
import os
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_universal_coordinate_conversion():
    """测试通用坐标转换"""
    print("🧪 通用AT-SPI坐标转换验证")
    print("=" * 60)
    
    try:
        from universal_coordinate_converter import UniversalATSPICoordinateConverter
        
        # 创建通用转换器
        converter = UniversalATSPICoordinateConverter(titlebar_height=40)
        
        # 测试任意窗口的坐标转换（这里以hellobig为例，但方法是通用的）
        window_pattern = "AT-SPI测试界面"  # 可以是任意窗口标题的一部分
        
        print(f"🎯 测试窗口: {window_pattern}")
        
        # 获取窗口信息
        window_info = converter.get_window_by_title_pattern(window_pattern)
        if not window_info:
            print("❌ 未找到目标窗口")
            return False
        
        print(f"✅ 找到窗口: {window_info['title']}")
        print(f"   位置: ({window_info['x']}, {window_info['y']})")
        print(f"   大小: {window_info['width']} × {window_info['height']}")
        
        # 测试已知的AT-SPI坐标（基于之前的分析）
        test_controls = [
            (20, 20, 100, 30, "预期的第一个按钮"),
            (20, 70, 100, 22, "预期的第二个控件"),
            (20, 115, 100, 22, "预期的第三个控件"),
        ]
        
        success_count = 0
        
        for atspi_x, atspi_y, width, height, description in test_controls:
            print(f"\n📍 测试 {description}")
            print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y})")
            
            # 使用通用转换器进行坐标转换
            desktop_x, desktop_y, win_info = converter.convert_atspi_to_desktop_coords(
                atspi_x, atspi_y, window_pattern
            )
            
            if win_info:
                print(f"   转换后桌面坐标: ({desktop_x}, {desktop_y})")
                print(f"   计算过程:")
                print(f"     X = {win_info['x']} + {atspi_x} = {desktop_x}")
                print(f"     Y = {win_info['y']} + {atspi_y} + 40 = {desktop_y}")
                
                # 进行高亮验证
                success = converter.highlight_control_universal(
                    atspi_x, atspi_y, width, height, window_pattern, 2
                )
                
                if success:
                    success_count += 1
                    print(f"   ✅ 高亮验证成功")
                else:
                    print(f"   ❌ 高亮验证失败")
            else:
                print(f"   ❌ 坐标转换失败")
            
            time.sleep(1.5)  # 等待观察
        
        print(f"\n📊 测试结果:")
        print(f"   总测试数: {len(test_controls)}")
        print(f"   成功数: {success_count}")
        print(f"   成功率: {success_count/len(test_controls)*100:.1f}%")
        
        return success_count >= len(test_controls) // 2
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_atspi_detection():
    """结合真实的AT-SPI控件检测测试坐标转换"""
    print("\n🔬 真实AT-SPI控件检测 + 通用坐标转换测试")
    print("=" * 60)
    
    try:
        from UNI_new import UNI
        from universal_coordinate_converter import get_universal_converter
        
        uni = UNI()
        converter = get_universal_converter()
        
        # 在hellobig窗口内的一个已知位置进行控件检测
        window_pattern = "AT-SPI测试界面"
        window_info = converter.get_window_by_title_pattern(window_pattern)
        
        if not window_info:
            print("❌ 未找到目标窗口")
            return False
        
        # 计算一个窗口内的测试位置
        test_x = window_info['x'] + 70  # 相对位置70
        test_y = window_info['y'] + 80  # 相对位置80
        
        print(f"🎯 测试位置: ({test_x}, {test_y})")
        print(f"   (相对于窗口: (70, 80))")
        
        # 使用UNI进行控件检测（无硬编码）
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(test_x, test_y, quick=False, highlight=False)
        elapsed = time.time() - start_time
        
        if control_data and isinstance(control_data, dict):
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            
            print(f"✅ UNI检测成功 ({elapsed:.2f}s): {name} ({role})")
            
            if coords:
                atspi_x = coords.get('x', 0)
                atspi_y = coords.get('y', 0)
                width = coords.get('width', 0)
                height = coords.get('height', 0)
                
                print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y}) 大小: {width}×{height}")
                
                # 使用通用转换器进行坐标修正
                desktop_x, desktop_y, _ = converter.convert_atspi_to_desktop_coords(
                    atspi_x, atspi_y, window_pattern
                )
                
                print(f"   修正后桌面坐标: ({desktop_x}, {desktop_y})")
                print(f"   与测试位置偏差: X={desktop_x - test_x}, Y={desktop_y - test_y}")
                
                # 显示修正后的高亮
                success = converter.highlight_control_universal(
                    atspi_x, atspi_y, width, height, window_pattern, 3
                )
                
                if success:
                    print(f"   ✅ 修正后高亮显示成功")
                    print(f"   🎉 这应该准确对准实际控件了！")
                    return True
                else:
                    print(f"   ❌ 高亮显示失败")
            else:
                print(f"   ⚠️ 无坐标信息")
        else:
            print(f"❌ UNI检测失败 ({elapsed:.2f}s): {info}")
        
        return False
        
    except Exception as e:
        print(f"❌ 真实检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("通用AT-SPI坐标转换验证测试")
    print("=" * 80)
    print("🎯 目标: 验证通用接口解决坐标偏移问题")
    print("✨ 特点: 无硬编码，支持任意应用")
    print("🔧 修复: 考虑窗口标题栏偏移")
    print()
    
    # 测试1: 通用坐标转换
    test1_success = test_universal_coordinate_conversion()
    
    # 测试2: 结合真实AT-SPI检测
    test2_success = test_with_real_atspi_detection()
    
    print("\n" + "=" * 80)
    print("📊 最终测试结果:")
    print(f"   通用坐标转换: {'✅ 成功' if test1_success else '❌ 失败'}")
    print(f"   真实检测验证: {'✅ 成功' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 通用坐标转换修复验证成功！")
        print("✅ 实现了真正的通用AT-SPI接口")
        print("✅ 解决了窗口标题栏偏移问题")
        print("✅ 无需硬编码特定应用适配")
        print("✅ 红色边框应该准确对准实际控件")
        
        print("\n💡 关键改进:")
        print("   - 移除了硬编码的应用映射")
        print("   - 使用通用窗口标题匹配")
        print("   - 考虑标题栏偏移的坐标转换")
        print("   - 支持任意应用的控件识别")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
        if not test1_success:
            print("   - 通用坐标转换需要优化")
        if not test2_success:
            print("   - 与UNI系统集成需要改进")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())