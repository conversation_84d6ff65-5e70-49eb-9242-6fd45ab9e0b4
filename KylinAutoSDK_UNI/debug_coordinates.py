#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标系统调试工具
分析AT-SPI坐标与实际窗口坐标的差异
"""

import sys
import os
import time
import subprocess

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def get_window_info():
    """获取窗口信息"""
    try:
        from uni_sdk.utils.helpers import get_windows_by_wlcctrl
        
        windows = get_windows_by_wlcctrl()
        
        print("📋 当前桌面窗口列表:")
        for i, (title, geometry) in enumerate(windows.items()):
            print(f"  {i+1}. {title}")
            print(f"     位置: ({geometry['x']}, {geometry['y']})")
            print(f"     大小: {geometry['width']} × {geometry['height']}")
            print()
        
        return windows
        
    except Exception as e:
        print(f"❌ 获取窗口信息失败: {e}")
        return {}

def analyze_atspi_coordinates():
    """分析AT-SPI坐标系统"""
    print("🔍 分析AT-SPI坐标系统")
    print("=" * 50)
    
    try:
        import pyatspi
        
        desktop = pyatspi.Registry.getDesktop(0)
        print(f"AT-SPI桌面应用数量: {desktop.childCount}")
        
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                print(f"\n应用 {i+1}: {app.name}")
                
                if 'hellobig' in app.name.lower():
                    print("  🎯 找到hellobig应用，分析其控件坐标...")
                    analyze_app_controls(app)
                    
            except Exception as e:
                print(f"  ❌ 分析应用失败: {e}")
        
    except Exception as e:
        print(f"❌ AT-SPI分析失败: {e}")

def analyze_app_controls(app):
    """分析应用控件的坐标"""
    try:
        from uni_sdk.utils.helpers import get_element_extents
        
        print(f"    应用子控件数量: {app.childCount}")
        
        for i in range(min(app.childCount, 5)):  # 只分析前5个控件
            try:
                element = app.getChildAtIndex(i)
                role = element.getRoleName()
                name = getattr(element, 'name', '') or 'N/A'
                
                # 获取AT-SPI坐标
                extents = get_element_extents(element)
                
                if extents:
                    x, y, width, height = extents
                    print(f"    控件 {i+1}: {name} ({role})")
                    print(f"      AT-SPI坐标: ({x}, {y})")
                    print(f"      AT-SPI大小: {width} × {height}")
                    
                    # 尝试获取相对坐标
                    try:
                        rel_extents = element.getExtents(pyatspi.DESKTOP_COORDS)
                        print(f"      桌面坐标: ({rel_extents.x}, {rel_extents.y})")
                        print(f"      桌面大小: {rel_extents.width} × {rel_extents.height}")
                    except:
                        print("      无法获取桌面坐标")
                
            except Exception as e:
                print(f"    ❌ 分析控件 {i+1} 失败: {e}")
        
    except Exception as e:
        print(f"  ❌ 分析应用控件失败: {e}")

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n🧪 测试坐标转换")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        # 获取当前鼠标位置作为测试点
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            print("❌ 无法获取鼠标位置")
            return
        
        print(f"测试坐标: ({mouse_x}, {mouse_y})")
        print(f"🖱️ 实际鼠标位置: ({mouse_x}, {mouse_y})")
        
        uni = UNI()
        
        # 进行控件识别但不高亮
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            rel_coords = control_data.get('RelativeCoords', {})
            
            print(f"✅ 找到控件: {name} ({role})")
            
            if coords:
                atspi_x = coords.get('x', 0)
                atspi_y = coords.get('y', 0)
                atspi_w = coords.get('width', 0)
                atspi_h = coords.get('height', 0)
                
                print(f"📊 坐标对比分析:")
                print(f"  鼠标实际位置: ({mouse_x}, {mouse_y})")
                print(f"  AT-SPI控件位置: ({atspi_x}, {atspi_y})")
                print(f"  AT-SPI控件大小: {atspi_w} × {atspi_h}")
                
                # 计算偏差
                offset_x = atspi_x - mouse_x
                offset_y = atspi_y - mouse_y
                
                print(f"  坐标偏差: X={offset_x}, Y={offset_y}")
                
                if rel_coords:
                    rel_x = rel_coords.get('x', 0)
                    rel_y = rel_coords.get('y', 0)
                    print(f"  相对坐标: ({rel_x}, {rel_y})")
                
                # 分析偏差模式
                if abs(offset_x) > 50 or abs(offset_y) > 50:
                    print("  ⚠️ 发现较大坐标偏差!")
                    
                    if abs(offset_y) > abs(offset_x):
                        print("    主要是Y轴偏差，可能是标题栏/菜单栏影响")
                    else:
                        print("    主要是X轴偏差，可能是边框影响")
                
                # 测试不同的高亮位置
                print(f"\n🎨 测试不同高亮策略:")
                
                # 策略1: 使用AT-SPI原始坐标
                print("  策略1: AT-SPI原始坐标")
                test_highlight(atspi_x, atspi_y, atspi_w, atspi_h, "blue", "AT-SPI原始")
                
                # 策略2: 使用鼠标位置
                print("  策略2: 鼠标位置")
                test_highlight(mouse_x-25, mouse_y-12, 50, 25, "green", "鼠标位置")
                
                # 策略3: 坐标修正
                corrected_x = atspi_x
                corrected_y = atspi_y - 40  # 常见的标题栏偏移
                print("  策略3: Y轴修正(-40)")
                test_highlight(corrected_x, corrected_y, atspi_w, atspi_h, "red", "Y轴修正")
                
        else:
            print(f"❌ 未找到控件: {info}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_highlight(x, y, width, height, color, strategy_name):
    """测试高亮显示"""
    try:
        from ultimate_highlight import ultimate_highlight
        
        print(f"    {strategy_name}: 位置({x}, {y}) 大小{width}×{height}")
        
        success = ultimate_highlight(
            x=x, y=y, width=width, height=height,
            duration=2, color=color, border_width=2
        )
        
        if success:
            print(f"    ✅ {color}色高亮显示成功")
        else:
            print(f"    ❌ {color}色高亮显示失败")
        
        time.sleep(2.5)  # 等待高亮消失
        
    except Exception as e:
        print(f"    ❌ 高亮测试失败: {e}")

def interactive_coordinate_debug():
    """交互式坐标调试"""
    print("\n🖱️ 交互式坐标调试")
    print("=" * 50)
    print("请移动鼠标到不同控件上，观察坐标差异")
    print("每次移动后等待3秒自动测试，或按Ctrl+C结束")
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        last_pos = None
        test_count = 0
        
        while test_count < 5:  # 最多测试5次
            current_pos = get_mouse_position()
            
            if current_pos != last_pos and current_pos[0] is not None:
                mouse_x, mouse_y = current_pos
                test_count += 1
                
                print(f"\n📍 测试 {test_count}/5: 鼠标位置 ({mouse_x}, {mouse_y})")
                
                try:
                    control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
                    
                    if control_data:
                        name = control_data.get('Name', 'N/A')
                        role = control_data.get('Rolename', 'N/A')
                        coords = control_data.get('Coords', {})
                        
                        if coords:
                            atspi_x = coords.get('x', 0)
                            atspi_y = coords.get('y', 0)
                            
                            print(f"  控件: {name} ({role})")
                            print(f"  鼠标: ({mouse_x}, {mouse_y})")
                            print(f"  AT-SPI: ({atspi_x}, {atspi_y})")
                            print(f"  偏差: X={atspi_x-mouse_x}, Y={atspi_y-mouse_y}")
                            
                            # 显示修正后的高亮
                            corrected_y = atspi_y - 40
                            test_highlight(atspi_x, corrected_y, coords.get('width', 50), coords.get('height', 25), "red", "修正高亮")
                        
                    else:
                        print(f"  ❌ 未找到控件")
                    
                except Exception as e:
                    print(f"  ❌ 测试失败: {e}")
                
                last_pos = current_pos
            
            time.sleep(1)
    
    except KeyboardInterrupt:
        print("\n用户中断调试")
    except Exception as e:
        print(f"❌ 交互调试失败: {e}")

def main():
    """主函数"""
    print("坐标系统调试工具")
    print("=" * 60)
    print("🎯 目标: 分析AT-SPI坐标与实际窗口坐标的差异")
    print()
    
    # 1. 获取窗口信息
    print("📋 步骤1: 获取桌面窗口信息")
    windows = get_window_info()
    
    # 2. 分析AT-SPI坐标
    print("\n🔍 步骤2: 分析AT-SPI坐标系统")
    analyze_atspi_coordinates()
    
    # 3. 测试坐标转换
    print("\n🧪 步骤3: 测试坐标转换")
    test_coordinate_conversion()
    
    # 4. 交互式调试
    print("\n🖱️ 步骤4: 交互式坐标调试")
    interactive_coordinate_debug()
    
    print("\n" + "=" * 60)
    print("🎉 坐标调试完成!")
    print("💡 基于调试结果可以确定坐标修正策略")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())