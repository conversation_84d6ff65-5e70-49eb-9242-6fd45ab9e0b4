#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
诊断高亮不稳定问题
对比直接高亮 vs 控件识别后高亮的差异
"""

import sys
import os
import subprocess
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def test_direct_highlight():
    """测试1: 直接高亮（像 test_highlight_only.py）"""
    print("🎯 测试1: 直接高亮（稳定方式）")
    print("=" * 50)
    
    try:
        from ultimate_highlight import ultimate_highlight
        
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        # 直接在鼠标位置高亮
        test_x = mouse_x - 50
        test_y = mouse_y - 25
        test_w = 100
        test_h = 50
        
        print(f"📍 鼠标位置: ({mouse_x}, {mouse_y})")
        print(f"🎨 高亮区域: ({test_x}, {test_y}) 尺寸: {test_w}×{test_h}")
        print("🎯 直接调用 ultimate_highlight...")
        
        success = ultimate_highlight(
            x=test_x, y=test_y, width=test_w, height=test_h,
            duration=3, color='blue', border_width=2
        )
        
        if success:
            print("✅ 直接高亮成功 - 应该看到蓝色边框")
        else:
            print("❌ 直接高亮失败")
            
        time.sleep(4)
        
    except Exception as e:
        print(f"❌ 直接高亮测试失败: {e}")

def test_control_detection_highlight():
    """测试2: 控件识别后高亮（像 test_enhanced_control_detection.py）"""
    print("\n🎯 测试2: 控件识别后高亮（不稳定方式）")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        print(f"📍 鼠标位置: ({mouse_x}, {mouse_y})")
        print("🔍 通过控件识别进行高亮...")
        
        uni = UNI()
        
        # 使用控件识别的高亮方式
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            print(f"✅ 找到控件: {name} ({role})")
            print("🎨 应该看到红色高亮边框")
            
            # 显示坐标转换信息
            coords = control_data.get('Coords', {})
            if coords:
                print(f"📏 控件坐标: ({coords.get('x')}, {coords.get('y')}) 尺寸: {coords.get('width')}×{coords.get('height')}")
                
                # 计算坐标偏差
                detected_x = coords.get('x', 0)
                detected_y = coords.get('y', 0)
                offset_x = detected_x - mouse_x
                offset_y = detected_y - mouse_y
                
                print(f"📐 坐标偏差: X偏移 {offset_x}, Y偏移 {offset_y}")
                
                if abs(offset_x) > 100 or abs(offset_y) > 100:
                    print("⚠️ 坐标偏差较大，可能导致高亮位置错误")
                    
        else:
            print(f"❌ 未找到控件: {info}")
            
        time.sleep(4)
        
    except Exception as e:
        print(f"❌ 控件识别高亮测试失败: {e}")

def test_coordinate_transformation_steps():
    """测试3: 分步测试坐标转换过程"""
    print("\n🎯 测试3: 坐标转换过程分析")
    print("=" * 50)
    
    try:
        from UNI_new import UNI, highlight_found_control
        
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        print(f"📍 目标坐标: ({mouse_x}, {mouse_y})")
        
        uni = UNI()
        
        # 只进行控件识别，不高亮
        print("🔍 步骤1: 控件识别（无高亮）")
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            print(f"   ✅ 找到控件: {name} ({role})")
            
            # 分析坐标信息
            coords = control_data.get('Coords', {})
            relative_coords = control_data.get('RelativeCoords', {})
            
            print(f"\n📊 坐标信息分析:")
            print(f"   原始鼠标坐标: ({mouse_x}, {mouse_y})")
            
            if coords:
                print(f"   Coords (绝对): ({coords.get('x')}, {coords.get('y')}) {coords.get('width')}×{coords.get('height')}")
                
            if relative_coords:
                print(f"   RelativeCoords: ({relative_coords.get('x')}, {relative_coords.get('y')}) {relative_coords.get('width')}×{relative_coords.get('height')}")
            
            # 手动调用高亮函数
            print(f"\n🎨 步骤2: 手动调用高亮函数")
            try:
                success = highlight_found_control(control_data, duration=3, color='green', thickness=2)
                if success:
                    print("   ✅ 高亮函数调用成功 - 应该看到绿色边框")
                else:
                    print("   ❌ 高亮函数调用失败")
            except Exception as e:
                print(f"   ❌ 高亮函数异常: {e}")
                
        else:
            print(f"   ❌ 未找到控件: {info}")
            
        time.sleep(4)
        
    except Exception as e:
        print(f"❌ 坐标转换测试失败: {e}")

def test_multiple_attempts():
    """测试4: 多次尝试测试稳定性"""
    print("\n🎯 测试4: 多次尝试测试稳定性")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        print(f"📍 固定测试坐标: ({mouse_x}, {mouse_y})")
        print("🔄 进行5次连续测试，观察稳定性...")
        
        uni = UNI()
        success_count = 0
        
        for i in range(5):
            print(f"\n--- 第 {i+1} 次尝试 ---")
            
            try:
                control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    print(f"   ✅ 找到控件: {name} ({role})")
                    success_count += 1
                    
                    # 检查坐标一致性
                    coords = control_data.get('Coords', {})
                    if coords:
                        detected_x = coords.get('x', 0)
                        detected_y = coords.get('y', 0)
                        print(f"   📏 检测坐标: ({detected_x}, {detected_y})")
                        
                else:
                    print(f"   ❌ 未找到控件: {info}")
                    
                time.sleep(2)  # 短暂等待
                
            except Exception as e:
                print(f"   ❌ 尝试失败: {e}")
        
        print(f"\n📊 稳定性测试结果:")
        print(f"   成功次数: {success_count}/5")
        print(f"   成功率: {success_count/5*100:.1f}%")
        
        if success_count < 5:
            print("⚠️ 检测到不稳定性，这可能是高亮不稳定的原因")
        else:
            print("✅ 控件识别稳定，问题可能在坐标转换环节")
            
    except Exception as e:
        print(f"❌ 稳定性测试失败: {e}")

def main():
    """主函数"""
    print("诊断高亮不稳定问题")
    print("=" * 60)
    print("🎯 目标: 找出为什么控件识别后的高亮不如直接高亮稳定")
    print("💡 对比: 直接高亮 vs 控件识别高亮")
    print()
    
    # 获取鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        print(f"🖱️ 当前鼠标位置: ({mouse_x}, {mouse_y})")
        print("📍 请保持鼠标位置不动，进行对比测试")
    else:
        print("⚠️ 无法获取鼠标位置，使用默认位置")
    print()
    
    # 依次进行各种测试
    test_direct_highlight()
    test_control_detection_highlight()
    test_coordinate_transformation_steps()
    test_multiple_attempts()
    
    print("\n" + "=" * 60)
    print("🎉 诊断测试完成!")
    print()
    print("📊 问题分析:")
    print("   1. 直接高亮 - 使用精确坐标，稳定可靠")
    print("   2. 控件识别高亮 - 经过复杂坐标转换，可能有误差")
    print("   3. 坐标转换链条长，每一步都可能引入误差")
    print("   4. AT-SPI坐标 → 窗口匹配 → 坐标修正 → 高亮显示")
    print()
    print("💡 解决方案建议:")
    print("   - 简化坐标转换流程")
    print("   - 增加坐标验证步骤")
    print("   - 提供坐标转换的调试信息")
    print("   - 考虑直接使用检测坐标进行高亮")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
