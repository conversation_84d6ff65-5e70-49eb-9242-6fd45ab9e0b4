#!/usr/bin/env python3
# 按需显示版Xlib系统级高亮 - 只在Ctrl键按下时显示高亮，点击后清除

import sys
import time
import threading
import signal
import json
import os
import queue
from threading import Lock

# 从环境变量获取 scripts 根目录，并直接用于模块导入
gat_common_base = os.environ.get("GAT_COMMON_PATH")
if not gat_common_base:
    print("[ERROR] GAT_COMMON_PATH 未设置", file=sys.stderr)
    sys.exit(1)
if os.path.isdir(gat_common_base):
    sys.path.insert(0, gat_common_base)
    print(f"[INFO] Added '{gat_common_base}' to sys.path from GAT_COMMON_PATH env var.", file=sys.stderr)
else:
    print(f"[ERROR] scripts 根目录 '{gat_common_base}' 不存在. Exiting.", file=sys.stderr)
    sys.exit(1)

# 尝试导入UNI模块
try:
    from UNI import UNI
    UNI_AVAILABLE = True
    safe_print = lambda message, output_file=sys.stderr: print(message, file=output_file, flush=True)
    safe_print("[INFO] UNI模块已导入")
except ImportError:
    UNI_AVAILABLE = False
    safe_print = lambda message, output_file=sys.stderr: print(message, file=output_file, flush=True)
    safe_print("[WARNING] UNI模块导入失败，将使用模拟数据")

# 设置无缓冲输出
os.environ['PYTHONUNBUFFERED'] = '1'

# 自定义打印函数，确保所有输出都是无缓冲的
def safe_safe_print(message):
    print(message, file=sys.stderr, flush=True)

try:
    # 导入threaded模块会自动初始化X11的线程支持
    from Xlib import threaded
    from Xlib import X, display, Xatom
    safe_print("[INFO] Xlib库已安装，并已初始化线程支持")
except ImportError:
    safe_print("[ERROR] Xlib库未安装，请使用 pip install python-xlib 安装")
    sys.exit(1)

try:
    from pynput import keyboard
    PYNPUT_AVAILABLE = True
    safe_print("[INFO] pynput库已安装")
except ImportError:
    PYNPUT_AVAILABLE = False
    safe_print("[WARNING] pynput库未安装，将使用X11键盘事件")
    safe_print("[INFO] 可以使用 pip install pynput 安装")

class OnDemandHighlighter:
    def __init__(self):
        safe_print("[INIT] 初始化按需显示版Xlib高亮系统")

        # 创建X11操作的互斥锁
        self.x11_lock = Lock()

        # 创建事件队列，用于线程间通信
        self.event_queue = queue.Queue()

        # 初始化UNI实例
        if UNI_AVAILABLE:
            try:
                self.uni = UNI()
                safe_print("[INIT] UNI实例初始化成功")
            except Exception as e:
                self.uni = None
                safe_print(f"[ERROR] UNI实例初始化失败: {e}")
        else:
            self.uni = None
            safe_print("[WARNING] UNI模块不可用，将使用模拟数据")

        # 连接到X服务器
        try:
            self.display = display.Display()
            self.screen = self.display.screen()
            self.root = self.screen.root

            # 获取屏幕尺寸
            self.screen_width = self.screen.width_in_pixels
            self.screen_height = self.screen.height_in_pixels
            safe_print(f"[INIT] 屏幕尺寸: {self.screen_width}x{self.screen_height}")

            # 高亮窗口属性
            self.highlight_width = 100  # 固定宽度
            self.highlight_height = 100  # 固定高度
            self.border_width = 2  # 边框宽度

            # 四个边框窗口
            self.top_border = None
            self.bottom_border = None
            self.left_border = None
            self.right_border = None

            # 图形上下文
            self.gc = None

            # 当前鼠标位置
            self.mouse_x = 0
            self.mouse_y = 0

            # 当前高亮位置
            self.highlight_x = 0
            self.highlight_y = 0

            # Ctrl键状态
            self.ctrl_pressed = False

            # 鼠标抓取状态
            self.mouse_grabbed = False

            # 高亮显示状态
            self.highlight_visible = False

            # 创建高亮窗口（但不显示）
            self.create_highlight_windows()

            # 启动鼠标跟踪线程
            self.running = True
            self.mouse_thread = threading.Thread(target=self.track_mouse)
            self.mouse_thread.daemon = True
            self.mouse_thread.start()

            # 启动事件处理线程
            self.event_thread = threading.Thread(target=self.handle_events)
            self.event_thread.daemon = True
            self.event_thread.start()

            # 如果pynput可用，启动全局键盘监听
            if PYNPUT_AVAILABLE:
                self.setup_pynput_keyboard_listener()

            safe_print("[INIT] 初始化完成")

        except Exception as e:
            safe_print(f"[ERROR] 初始化失败: {e}")
            sys.exit(1)

    def create_highlight_windows(self):
        """创建高亮窗口（只有四个边框）"""
        safe_print("[WINDOW] 创建高亮窗口")

        try:
            # 创建四个边框窗口
            # 上边框
            self.top_border = self.root.create_window(
                0, 0,  # 初始位置
                self.highlight_width, self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=self.screen.white_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 下边框
            self.bottom_border = self.root.create_window(
                0, self.highlight_height - self.border_width,  # 初始位置
                self.highlight_width, self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=self.screen.white_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 左边框
            self.left_border = self.root.create_window(
                0, self.border_width,  # 初始位置
                self.border_width, self.highlight_height - 2 * self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=self.screen.white_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 右边框
            self.right_border = self.root.create_window(
                self.highlight_width - self.border_width, self.border_width,  # 初始位置
                self.border_width, self.highlight_height - 2 * self.border_width,  # 窗口大小
                0,  # 边框宽度
                self.screen.root_depth,
                X.InputOutput,
                X.CopyFromParent,
                background_pixel=self.screen.white_pixel,
                override_redirect=1,  # 绕过窗口管理器
                event_mask=X.ExposureMask | X.StructureNotifyMask
            )

            # 设置窗口名称 (使用ASCII字符避免编码问题)
            for window, name in [
                (self.top_border, "Top Border"),
                (self.bottom_border, "Bottom Border"),
                (self.left_border, "Left Border"),
                (self.right_border, "Right Border")
            ]:
                window.set_wm_name(name)
                window.set_wm_class("highlight", "Highlight")

            # 设置窗口为始终在最上层
            for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                try:
                    window.change_property(
                        self.display.intern_atom('_NET_WM_STATE'),
                        Xatom.ATOM,
                        32,
                        [self.display.intern_atom('_NET_WM_STATE_ABOVE')]
                    )
                except Exception as e:
                    safe_print(f"[WARNING] 设置窗口为始终在最上层失败: {e}")

            # 设置窗口为无边框
            for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                try:
                    window.change_property(
                        self.display.intern_atom('_MOTIF_WM_HINTS'),
                        self.display.intern_atom('_MOTIF_WM_HINTS'),
                        32,
                        [2, 0, 0, 0, 0]
                    )
                except Exception as e:
                    safe_print(f"[WARNING] 设置窗口为无边框失败: {e}")

            # 创建GC（图形上下文）
            self.gc = self.top_border.create_gc(
                foreground=self.screen.black_pixel,
                background=self.screen.white_pixel
            )

            # 设置红色
            try:
                colormap = self.screen.default_colormap
                color = colormap.alloc_named_color("red")
                self.gc.change(foreground=color.pixel)

                # 设置所有边框为红色
                for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                    window.change_attributes(background_pixel=color.pixel)
            except Exception as e:
                safe_print(f"[WARNING] 设置红色失败: {e}")
                # 使用默认颜色

            # 初始状态不显示高亮窗口
            self.hide_highlight()

            safe_print("[WINDOW] 高亮窗口创建成功")
            return True

        except Exception as e:
            safe_print(f"[ERROR] 创建高亮窗口时发生错误: {e}")
            return False

    def setup_pynput_keyboard_listener(self):
        """设置pynput全局键盘监听"""
        safe_print("[PYNPUT] 设置pynput全局键盘监听")

        # 键盘监听器
        def on_key_press(key):
            try:
                if key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                    safe_print("[DEBUG] 检测到Ctrl键按下事件")
                    safe_print(f"[DEBUG] 按下的键: {key}")

                    # 设置Ctrl按下状态
                    self.ctrl_pressed = True
                    safe_print("[KEYBOARD] Ctrl键按下，self.ctrl_pressed设置为True")

                    # 当Ctrl键按下时，抓取鼠标但不立即显示高亮
                    # 高亮将在鼠标移动时根据下方控件显示
                    safe_print("[DEBUG] 准备调用grab_mouse()方法")
                    self.grab_mouse()

                    # 获取当前鼠标位置
                    with self.x11_lock:
                        pointer = self.root.query_pointer()
                        x, y = pointer.root_x, pointer.root_y

                    # 尝试获取当前位置的控件信息并显示高亮
                    # 将事件放入队列，由事件处理线程处理
                    self.event_queue.put(('highlight', (x, y)))

                    safe_print("[DEBUG] Ctrl键按下事件处理完成")
            except Exception as e:
                safe_print(f"[ERROR] 处理键盘按下事件时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)

        def on_key_release(key):
            try:
                if key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                    safe_print("[KEYBOARD] Ctrl键释放")

                    try:
                        # 首先释放鼠标，避免死锁
                        self.ungrab_mouse()

                        # 然后重置Ctrl按下状态
                        self.ctrl_pressed = False

                        # 最后隐藏高亮
                        # 将事件放入队列，由事件处理线程处理
                        self.event_queue.put(('hide_highlight', None))
                    except Exception as e:
                        safe_print(f"[ERROR] 处理Ctrl键释放时发生错误: {e}")
                        import traceback
                        traceback.print_exc(file=sys.stderr)
            except Exception as e:
                safe_print(f"[ERROR] 处理键盘释放事件时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)

        # 创建并启动监听器
        self.keyboard_listener = keyboard.Listener(on_press=on_key_press, on_release=on_key_release)
        self.keyboard_listener.daemon = True
        self.keyboard_listener.start()

        safe_print("[PYNPUT] pynput全局键盘监听已启动")

    def grab_mouse(self):
        """抓取鼠标"""
        safe_print("[DEBUG] 进入grab_mouse方法")
        safe_print(f"[DEBUG] 当前鼠标抓取状态: grabbed={self.mouse_grabbed}")

        if not self.mouse_grabbed:
            try:
                # 抓取鼠标
                safe_print("[DEBUG] 准备抓取鼠标")
                with self.x11_lock:
                    grab_result = self.root.grab_pointer(
                        owner_events=True,
                        event_mask=X.ButtonPressMask | X.ButtonReleaseMask | X.PointerMotionMask,
                        pointer_mode=X.GrabModeAsync,
                        keyboard_mode=X.GrabModeAsync,
                        confine_to=X.NONE,
                        cursor=X.NONE,
                        time=X.CurrentTime
                    )
                safe_print(f"[DEBUG] 鼠标抓取返回结果: {grab_result}")

                self.mouse_grabbed = True
                safe_print("[MOUSE] 鼠标已抓取，self.mouse_grabbed设置为True")

            except Exception as e:
                safe_print(f"[ERROR] 抓取鼠标时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)
        else:
            safe_print("[DEBUG] 鼠标已经被抓取，跳过抓取操作")

    def ungrab_mouse(self):
        """释放鼠标"""
        safe_print("[DEBUG] 进入ungrab_mouse方法")
        safe_print(f"[DEBUG] 当前鼠标抓取状态: grabbed={self.mouse_grabbed}")

        # 无论当前状态如何，都尝试释放鼠标
        try:
            # 释放鼠标
            with self.x11_lock:
                try:
                    self.display.ungrab_pointer(X.CurrentTime)
                    safe_print("[MOUSE] 鼠标释放命令已发送")
                except Exception as inner_e:
                    safe_print(f"[WARNING] 发送鼠标释放命令时发生错误: {inner_e}")

            # 无论是否成功释放，都将状态设置为未抓取
            self.mouse_grabbed = False
            safe_print("[MOUSE] 鼠标抓取状态已重置，self.mouse_grabbed设置为False")

            # 刷新显示，确保状态更新
            try:
                with self.x11_lock:
                    self.display.flush()
            except Exception as flush_e:
                safe_print(f"[WARNING] 刷新显示时发生错误: {flush_e}")

        except Exception as e:
            safe_print(f"[ERROR] 释放鼠标时发生错误: {e}")
            import traceback
            traceback.print_exc(file=sys.stderr)

            # 即使发生错误，也将状态设置为未抓取
            self.mouse_grabbed = False

    def try_highlight_widget_at(self, x, y):
        """尝试获取指定位置的控件信息并显示高亮"""
        safe_print(f"[HIGHLIGHT] 尝试获取并高亮位置 ({x}, {y}) 的控件")

        # 如果UNI可用，使用UNI.py的kdk_getElement_Uni方法获取控件信息
        if hasattr(self, 'uni') and self.uni:
            try:
                safe_print(f"[CAPTURE] 调用UNI.kdk_getElement_Uni({x}, {y}, True)")
                # 使用quick模式获取控件位置信息
                extents, info_text = self.uni.kdk_getElement_Uni(x, y, True)
                safe_print(f"[CAPTURE] UNI返回信息: {info_text}")

                if extents and info_text == "找到":
                    # 使用控件的实际大小更新高亮位置
                    widget_x = extents.x
                    widget_y = extents.y
                    widget_width = extents.width
                    widget_height = extents.height

                    safe_print(f"[HIGHLIGHT] 找到控件: 位置=({widget_x}, {widget_y}), 大小={widget_width}x{widget_height}")

                    # 更新高亮窗口大小和位置
                    self.update_highlight_size(widget_width, widget_height)
                    self.update_highlight_position(widget_x, widget_y)
                    self.show_highlight()
                    return True
                else:
                    safe_print(f"[HIGHLIGHT] 在位置 ({x}, {y}) 未找到控件")
                    self.hide_highlight()
                    return False
            except Exception as e:
                safe_print(f"[ERROR] 获取控件信息时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)
                self.hide_highlight()
                return False
        else:
            # UNI不可用时使用固定大小的高亮框
            safe_print("[WARNING] UNI实例不可用，使用固定大小的高亮框")
            # 在鼠标位置创建一个固定大小的高亮框
            # 将鼠标位置作为控件的左上角
            self.update_highlight_size(100, 50)  # 固定大小
            self.update_highlight_position(x, y)
            self.show_highlight()
            return True

    def update_highlight_size(self, width, height):
        """更新高亮窗口大小"""
        try:
            # 更新高亮窗口属性
            self.highlight_width = max(width, 10)  # 确保最小宽度
            self.highlight_height = max(height, 10)  # 确保最小高度

            safe_print(f"[HIGHLIGHT] 更新高亮窗口大小: {self.highlight_width}x{self.highlight_height}")

            # 重新调整四个边框窗口的大小
            # 上边框
            self.top_border.configure(
                width=self.highlight_width
            )

            # 下边框
            self.bottom_border.configure(
                width=self.highlight_width
            )

            # 左边框
            self.left_border.configure(
                height=self.highlight_height - 2 * self.border_width
            )

            # 右边框
            self.right_border.configure(
                height=self.highlight_height - 2 * self.border_width
            )

            # 强制刷新显示
            self.display.flush()

            return True
        except Exception as e:
            safe_print(f"[ERROR] 更新高亮窗口大小时发生错误: {e}")
            return False

    def show_highlight_at_current_position(self):
        """在当前鼠标位置显示高亮"""
        safe_print("[DEBUG] 进入show_highlight_at_current_position方法")

        try:
            # 获取当前鼠标位置
            pointer = self.root.query_pointer()
            x, y = pointer.root_x, pointer.root_y
            safe_print(f"[DEBUG] 当前鼠标位置: ({x}, {y})")

            # 尝试获取并高亮控件
            self.try_highlight_widget_at(x, y)
        except Exception as e:
            safe_print(f"[ERROR] 在当前鼠标位置显示高亮时发生错误: {e}")
            import traceback
            traceback.print_exc(file=sys.stderr)

    def show_highlight(self):
        """显示高亮"""
        safe_print("[DEBUG] 进入show_highlight方法")
        safe_print(f"[DEBUG] 当前高亮显示状态: visible={self.highlight_visible}")

        if not self.highlight_visible:
            try:
                # 映射窗口（显示窗口）
                safe_print("[DEBUG] 准备映射四个边框窗口")
                for i, window in enumerate([self.top_border, self.bottom_border, self.left_border, self.right_border]):
                    safe_print(f"[DEBUG] 映射第{i+1}个边框窗口")
                    window.map()

                # 强制刷新显示
                safe_print("[DEBUG] 准备刷新显示")
                self.display.flush()
                self.highlight_visible = True
                safe_print("[HIGHLIGHT] 高亮已显示，self.highlight_visible设置为True")

            except Exception as e:
                safe_print(f"[ERROR] 显示高亮时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)
        else:
            safe_print("[DEBUG] 高亮已经显示，跳过显示操作")

    def hide_highlight(self):
        """隐藏高亮"""
        if self.highlight_visible:
            try:
                # 取消映射窗口（隐藏窗口）
                for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                    window.unmap()

                # 强制刷新显示
                self.display.flush()
                self.highlight_visible = False
                safe_print("[HIGHLIGHT] 高亮已隐藏")

            except Exception as e:
                safe_print(f"[ERROR] 隐藏高亮时发生错误: {e}")

    def is_point_in_highlight(self, x, y):
        """检查点是否在高亮区域内"""
        return (self.highlight_x <= x <= self.highlight_x + self.highlight_width and
                self.highlight_y <= y <= self.highlight_y + self.highlight_height)

    def update_highlight_position(self, x, y):
        """更新高亮窗口位置"""
        try:
            # 直接使用控件的左上角坐标，不进行调整
            window_x = x
            window_y = y

            safe_print(f"[HIGHLIGHT] 直接使用控件坐标: ({window_x}, {window_y})")

            # 保存当前高亮位置
            self.highlight_x = window_x
            self.highlight_y = window_y

            # 移动上边框
            self.top_border.configure(
                x=window_x,
                y=window_y
            )

            # 移动下边框
            self.bottom_border.configure(
                x=window_x,
                y=window_y + self.highlight_height - self.border_width
            )

            # 移动左边框
            self.left_border.configure(
                x=window_x,
                y=window_y + self.border_width
            )

            # 移动右边框
            self.right_border.configure(
                x=window_x + self.highlight_width - self.border_width,
                y=window_y + self.border_width
            )

            # 强制刷新显示
            self.display.flush()

            safe_print(f"[HIGHLIGHT] 更新高亮位置: ({window_x}, {window_y})")
            return True

        except Exception as e:
            safe_print(f"[ERROR] 更新高亮位置时发生错误: {e}")
            return False

    def track_mouse(self):
        """跟踪鼠标位置"""
        safe_print("[MOUSE] 开始跟踪鼠标位置")

        last_x, last_y = 0, 0
        last_highlight_check = 0

        while self.running:
            try:
                # 使用互斥锁保护X11操作
                with self.x11_lock:
                    # 获取鼠标位置
                    pointer = self.root.query_pointer()
                    x, y = pointer.root_x, pointer.root_y

                # 更新当前鼠标位置
                self.mouse_x = x
                self.mouse_y = y

                # 如果鼠标位置变化且Ctrl键按下
                current_time = time.time()
                if (x != last_x or y != last_y) and self.ctrl_pressed:
                    # 记录新的鼠标位置
                    last_x, last_y = x, y

                    # 限制控件检测频率，避免过多调用UNI
                    if current_time - last_highlight_check > 0.1:  # 每100ms检测一次
                        safe_print(f"[MOUSE] 鼠标移动到: ({x}, {y})，尝试获取控件")
                        # 尝试获取并高亮当前位置的控件
                        # 将事件放入队列，由事件处理线程处理
                        self.event_queue.put(('highlight', (x, y)))
                        last_highlight_check = current_time

                # 等待一段时间
                time.sleep(0.02)  # 50fps，提高响应速度

            except Exception as e:
                safe_print(f"[ERROR] 跟踪鼠标位置时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)
                time.sleep(0.5)

    def handle_events(self):
        """处理X事件"""
        safe_print("[EVENT] 开始处理X事件")

        while self.running:
            try:
                # 检查是否有事件
                event = None
                with self.x11_lock:
                    if self.display.pending_events() > 0:
                        event = self.display.next_event()
                    else:
                        time.sleep(0.01)
                        continue

                # 如果没有事件，继续循环
                if not event:
                    continue

                # 处理鼠标按下事件
                if event.type == X.ButtonPress:
                    safe_print(f"[EVENT] 鼠标按下: 按钮={event.detail}, 位置=({event.root_x}, {event.root_y})")

                    # 检查是否是左键点击且按下了Ctrl键
                    if event.detail == 1 and self.ctrl_pressed:
                        safe_print("[EVENT] 检测到Ctrl+左键点击")

                        # 无论是否在高亮区域内，都尝试捕获控件信息
                        # 这样可以确保即使高亮框不准确，也能捕获到控件
                        safe_print(f"[EVENT] 尝试捕获位置 ({event.root_x}, {event.root_y}) 的控件")

                        try:
                            # 首先释放鼠标，避免死锁
                            self.ungrab_mouse()

                            # 重置 Ctrl 按下状态
                            self.ctrl_pressed = False

                            # 隐藏高亮
                            self.event_queue.put(('hide_highlight', None))

                            # 捕获控件信息（使用完整模式获取所有控件信息）
                            # 将事件放入队列，由事件处理线程处理
                            self.event_queue.put(('capture', (event.root_x, event.root_y)))
                        except Exception as e:
                            safe_print(f"[ERROR] 处理Ctrl+点击时发生错误: {e}")
                            import traceback
                            traceback.print_exc(file=sys.stderr)

                # 已经在上面处理了没有事件的情况

            except Exception as e:
                safe_print(f"[ERROR] 处理X事件时发生错误: {e}")
                time.sleep(1)

    def capture_widget_info(self, x, y):
        """捕获控件信息"""
        safe_print(f"[CAPTURE] 捕获控件信息: 位置=({x}, {y})")

        # 如果UNI可用，使用UNI.py的kdk_getElement_Uni方法
        if hasattr(self, 'uni') and self.uni:
            try:
                safe_print(f"[CAPTURE] 调用UNI.kdk_getElement_Uni({x}, {y}, False)")
                widget_info, info_text = self.uni.kdk_getElement_Uni(x, y, False)
                safe_print(f"[CAPTURE] UNI返回信息: {info_text}")

                if widget_info:
                    # 确保坐标信息存在
                    coords = widget_info.get('Coords', {})
                    if isinstance(coords, dict) and 'x' in coords and 'y' in coords and 'width' in coords and 'height' in coords:
                        # 使用控件的实际坐标和大小
                        widget_coords = coords
                    else:
                        # 如果坐标信息不完整，使用默认值
                        widget_coords = {
                            'x': x - 50,
                            'y': y - 25,
                            'width': 100,
                            'height': 50
                        }
                        safe_print(f"[WARNING] 控件坐标信息不完整，使用默认值: {widget_coords}")

                    # 处理UNI返回的控件信息，转换为标准格式
                    processed_info = {
                        'name': widget_info.get('Name', f'捕获控件_{x}_{y}'),
                        'type': widget_info.get('Rolename', '按钮'),
                        'coords': widget_coords,
                        'datamap': widget_info,  # 保存完整的控件信息
                        'description': widget_info.get('Description', ''),
                        'states': widget_info.get('States', []),
                        'actions': widget_info.get('Actions', [])
                    }
                    # 如果 widget_info 中包含 capture_status，则沿用
                    if 'capture_status' in widget_info:
                        processed_info['capture_status'] = widget_info['capture_status']
                        if 'error' in widget_info:
                             processed_info['error'] = widget_info['error']
                    elif processed_info['name'] == f'捕获控件_{x}_{y}': # UNI未能成功获取名称
                        processed_info['capture_status'] = 'fallback'
                        processed_info['error'] = '未能获取到控件的特定名称，已使用通用名称。'

                    # 输出捕获到的控件信息
                    safe_print(f"[CAPTURE] 捕获到控件: {processed_info['name']}, 类型: {processed_info['type']}")
                    safe_print(f"[CAPTURE] 控件坐标: x={widget_coords['x']}, y={widget_coords['y']}, width={widget_coords['width']}, height={widget_coords['height']}")
                else:
                    # 如果没有获取到控件信息，使用模拟数据
                    safe_print(f"[WARNING] 在位置 ({x}, {y}) 未获取到控件信息，使用模拟数据")
                    processed_info = self._create_mock_widget_info(x, y)
            except Exception as e:
                safe_print(f"[ERROR] 调用UNI.kdk_getElement_Uni时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)
                # 发生错误时使用模拟数据
                processed_info = self._create_mock_widget_info(x, y)
        else:
            # UNI不可用时使用模拟数据
            safe_print("[WARNING] UNI实例不可用，使用模拟数据")
            processed_info = self._create_mock_widget_info(x, y)

        # 输出控件信息到标准输出，供主进程捕获
        safe_print(f"[CAPTURE] 控件信息已捕获并输出")
        print(json.dumps(processed_info))
        sys.stdout.flush()

        return processed_info

    def _create_mock_widget_info(self, x, y):
        """创建模拟的控件信息"""
        return {
            'name': f'捕获控件_{x}_{y}',
            'type': '按钮',
            'coords': {
                'x': x - 50,
                'y': y - 25,
                'width': 100,
                'height': 50
            },
            'datamap': {
                'id': f'captured_widget_{x}_{y}',
                'text': f'已捕获位置({x},{y})的控件',
                'enabled': True
            },
            'is_mock': True,
            'capture_status': 'error', # 表明这是错误或模拟数据
            'error': 'UNI模块不可用或捕获失败，已使用模拟控件信息。'
        }

    def process_event_queue(self):
        """处理事件队列"""
        safe_print("[EVENT] 开始处理事件队列")

        while self.running:
            try:
                # 尝试从队列中获取事件，超时时间为0.1秒
                try:
                    event_type, event_data = self.event_queue.get(timeout=0.1)
                except queue.Empty:
                    continue

                # 处理事件
                try:
                    if event_type == 'highlight':
                        safe_print(f"[EVENT_QUEUE] 处理highlight事件: {event_data}")
                        x, y = event_data
                        # 使用互斥锁保护X11操作
                        with self.x11_lock:
                            self.try_highlight_widget_at(x, y)
                    elif event_type == 'capture':
                        safe_print(f"[EVENT_QUEUE] 处理capture事件: {event_data}")
                        x, y = event_data
                        # 使用互斥锁保护X11操作
                        with self.x11_lock:
                            self.capture_widget_info(x, y)
                    elif event_type == 'hide_highlight':
                        safe_print(f"[EVENT_QUEUE] 处理hide_highlight事件")
                        # 使用互斥锁保护X11操作
                        with self.x11_lock:
                            self.hide_highlight()
                    else:
                        safe_print(f"[WARNING] 未知事件类型: {event_type}")
                except Exception as event_e:
                    safe_print(f"[ERROR] 处理{event_type}事件时发生错误: {event_e}")
                    import traceback
                    traceback.print_exc(file=sys.stderr)

                # 标记事件已处理
                self.event_queue.task_done()

            except Exception as e:
                safe_print(f"[ERROR] 处理事件队列时发生错误: {e}")
                import traceback
                traceback.print_exc(file=sys.stderr)
                time.sleep(0.5)

    def cleanup(self):
        """清理资源"""
        safe_print("[CLEANUP] 开始清理资源")

        # 释放鼠标
        self.ungrab_mouse()

        # 停止线程
        self.running = False
        if hasattr(self, 'mouse_thread'):
            self.mouse_thread.join(1)
        if hasattr(self, 'event_thread'):
            self.event_thread.join(1)

        # 停止pynput监听器
        if PYNPUT_AVAILABLE:
            if hasattr(self, 'keyboard_listener'):
                self.keyboard_listener.stop()

        try:
            # 释放GC
            if hasattr(self, 'gc') and self.gc:
                self.gc.free()

            # 销毁窗口
            for window in [self.top_border, self.bottom_border, self.left_border, self.right_border]:
                if window:
                    window.destroy()

            # 关闭显示连接
            if hasattr(self, 'display') and self.display:
                self.display.close()

            safe_print("[CLEANUP] 资源清理完成")

        except Exception as e:
            safe_print(f"[ERROR] 清理资源时发生错误: {e}")

def signal_handler(sig, _):
    """处理信号"""
    safe_print(f"[SIGNAL] 收到信号: {sig}")

    # 清理资源
    if 'highlighter' in globals():
        highlighter.cleanup()

    sys.exit(0)

if __name__ == "__main__":
    safe_print("[MAIN] 按需显示版Xlib高亮系统启动")

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建高亮器
    highlighter = OnDemandHighlighter()

    # 启动事件队列处理线程
    queue_thread = threading.Thread(target=highlighter.process_event_queue)
    queue_thread.daemon = True
    queue_thread.start()

    # 主循环
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        safe_print("[MAIN] 收到中断信号，退出程序")
        highlighter.cleanup()
        sys.exit(0)
