#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
检查AT-SPI窗口状态
详细查看hellobig窗口的所有AT-SPI属性和状态
"""

import sys
import os
import pyatspi


def check_all_atspi_states():
    """检查所有可用的AT-SPI状态常量"""
    print("🔍 检查所有可用的AT-SPI状态常量:")
    print("=" * 50)
    
    # 列出所有STATE_*常量
    state_constants = []
    for attr_name in dir(pyatspi):
        if attr_name.startswith('STATE_'):
            try:
                state_value = getattr(pyatspi, attr_name)
                state_constants.append((attr_name, state_value))
            except Exception:
                pass
    
    state_constants.sort()
    
    for name, value in state_constants:
        print(f"   {name}: {value}")
    
    print(f"\n总共找到 {len(state_constants)} 个状态常量")
    
    # 特别检查ICONIFIED状态
    if hasattr(pyatspi, 'STATE_ICONIFIED'):
        print(f"\n✅ STATE_ICONIFIED 可用: {pyatspi.STATE_ICONIFIED}")
    else:
        print(f"\n❌ STATE_ICONIFIED 不可用")
    
    return state_constants


def get_state_description(state_name):
    """获取状态的描述"""
    descriptions = {
        'STATE_ACTIVE': '窗口是活动的',
        'STATE_ARMED': '控件已准备好',
        'STATE_BUSY': '控件正忙',
        'STATE_CHECKED': '控件已选中',
        'STATE_COLLAPSED': '控件已折叠',
        'STATE_DEFUNCT': '控件已失效',
        'STATE_EDITABLE': '控件可编辑',
        'STATE_ENABLED': '控件已启用',
        'STATE_EXPANDABLE': '控件可展开',
        'STATE_EXPANDED': '控件已展开',
        'STATE_FOCUSABLE': '控件可获得焦点',
        'STATE_FOCUSED': '控件已获得焦点',
        'STATE_HAS_TOOLTIP': '控件有工具提示',
        'STATE_HORIZONTAL': '控件是水平的',
        'STATE_ICONIFIED': '窗口已图标化（最小化）',
        'STATE_MODAL': '窗口是模态的',
        'STATE_MULTI_LINE': '控件是多行的',
        'STATE_MULTISELECTABLE': '控件支持多选',
        'STATE_OPAQUE': '控件是不透明的',
        'STATE_PRESSED': '控件已按下',
        'STATE_RESIZABLE': '窗口可调整大小',
        'STATE_SELECTABLE': '控件可选择',
        'STATE_SELECTED': '控件已选择',
        'STATE_SENSITIVE': '控件敏感（可交互）',
        'STATE_SHOWING': '控件正在显示',
        'STATE_SINGLE_LINE': '控件是单行的',
        'STATE_STALE': '控件信息过时',
        'STATE_TRANSIENT': '窗口是临时的',
        'STATE_VERTICAL': '控件是垂直的',
        'STATE_VISIBLE': '控件可见',
        'STATE_MANAGES_DESCENDANTS': '控件管理子元素',
        'STATE_INDETERMINATE': '控件状态不确定',
        'STATE_REQUIRED': '控件是必需的',
        'STATE_TRUNCATED': '控件内容被截断',
        'STATE_ANIMATED': '控件有动画',
        'STATE_INVALID_ENTRY': '控件输入无效',
        'STATE_SUPPORTS_AUTOCOMPLETION': '控件支持自动完成',
        'STATE_SELECTABLE_TEXT': '控件文本可选择',
        'STATE_IS_DEFAULT': '控件是默认的',
        'STATE_VISITED': '控件已访问',
        'STATE_CHECKABLE': '控件可选中',
        'STATE_HAS_POPUP': '控件有弹出菜单',
        'STATE_READ_ONLY': '控件只读'
    }
    
    return descriptions.get(state_name, '未知状态')


def check_hellobig_window_states():
    """检查hellobig窗口的详细状态"""
    print(f"\n🎯 查找hellobig窗口的AT-SPI状态:")
    print("=" * 50)
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            print(f"检查应用: {app_name}")
            
            if 'hellobig' in app_name.lower():
                print(f"✅ 找到hellobig应用: {app_name}")
                
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        window_name = window.name or "Unknown"
                        
                        print(f"\n  窗口 {j}: {window_name}")
                        
                        if 'AT-SPI测试' in window_name or '测试主窗口' in window_name:
                            print(f"  🎯 这是目标窗口: {window_name}")
                            
                            # 获取窗口的详细信息
                            print(f"  📋 窗口详细信息:")
                            print(f"     名称: {window_name}")
                            print(f"     角色: {window.getRoleName() if hasattr(window, 'getRoleName') else 'Unknown'}")
                            
                            # 获取几何信息
                            try:
                                if hasattr(window, 'queryComponent'):
                                    component = window.queryComponent()
                                    if component:
                                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                        print(f"     位置: ({extents.x}, {extents.y})")
                                        print(f"     尺寸: {extents.width}×{extents.height}")
                            except Exception as e:
                                print(f"     ⚠️  获取几何信息失败: {e}")
                            
                            # 检查所有状态
                            print(f"  🔍 窗口状态检查:")
                            
                            try:
                                if hasattr(window, 'getState'):
                                    state = window.getState()
                                    print(f"     状态对象: {state}")
                                    print(f"     状态对象类型: {type(state)}")
                                    
                                    # 检查所有可能的状态
                                    state_constants = []
                                    for attr_name in dir(pyatspi):
                                        if attr_name.startswith('STATE_'):
                                            try:
                                                state_value = getattr(pyatspi, attr_name)
                                                state_constants.append((attr_name, state_value))
                                            except Exception:
                                                pass
                                    
                                    print(f"     检查 {len(state_constants)} 个状态:")
                                    
                                    active_states = []
                                    inactive_states = []
                                    
                                    for state_name, state_value in state_constants:
                                        try:
                                            if state.contains(state_value):
                                                active_states.append(state_name)
                                                description = get_state_description(state_name)
                                                print(f"       ✅ {state_name}: {description}")
                                            else:
                                                inactive_states.append(state_name)
                                        except Exception as e:
                                            print(f"       ❌ 检查 {state_name} 失败: {e}")
                                    
                                    print(f"\n  📊 状态总结:")
                                    print(f"     激活状态 ({len(active_states)} 个): {', '.join(active_states[:5])}{'...' if len(active_states) > 5 else ''}")
                                    print(f"     未激活状态 ({len(inactive_states)} 个): {', '.join(inactive_states[:5])}{'...' if len(inactive_states) > 5 else ''}")
                                    
                                    # 特别关注关键状态
                                    print(f"\n  🎯 关键状态检查:")
                                    key_states = [
                                        ('STATE_VISIBLE', '窗口可见'),
                                        ('STATE_SHOWING', '窗口正在显示'),
                                        ('STATE_ICONIFIED', '窗口已最小化'),
                                        ('STATE_ACTIVE', '窗口是活动的'),
                                        ('STATE_FOCUSED', '窗口已获得焦点'),
                                        ('STATE_MODAL', '窗口是模态的'),
                                        ('STATE_RESIZABLE', '窗口可调整大小')
                                    ]
                                    
                                    for state_name, description in key_states:
                                        if hasattr(pyatspi, state_name):
                                            state_value = getattr(pyatspi, state_name)
                                            if state.contains(state_value):
                                                print(f"     ✅ {state_name}: {description}")
                                            else:
                                                print(f"     ❌ {state_name}: {description}")
                                        else:
                                            print(f"     ⚠️  {state_name}: 不可用")
                                    
                                    # 特别检查ICONIFIED状态
                                    if hasattr(pyatspi, 'STATE_ICONIFIED'):
                                        if state.contains(pyatspi.STATE_ICONIFIED):
                                            print(f"\n  🔴 重要发现: 窗口已最小化 (STATE_ICONIFIED)")
                                        else:
                                            print(f"\n  🟢 重要发现: 窗口未最小化 (无STATE_ICONIFIED)")
                                    else:
                                        print(f"\n  ⚠️  无法检查STATE_ICONIFIED (常量不存在)")
                                
                                else:
                                    print(f"     ❌ 窗口没有getState方法")
                                    
                            except Exception as e:
                                print(f"     ❌ 获取窗口状态失败: {e}")
                                import traceback
                                traceback.print_exc()
                            
                            return  # 找到目标窗口后退出
                            
                    except Exception as e:
                        print(f"  ❌ 检查窗口 {j} 失败: {e}")
                        continue
                
                break  # 找到hellobig应用后退出
        
        print(f"❌ 未找到hellobig应用或目标窗口")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🔍 AT-SPI状态检查器")
    print("=" * 60)
    print("🎯 专门检查hellobig窗口的STATE_ICONIFIED状态")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        # 首先检查所有可用的状态常量
        check_all_atspi_states()
        
        # 然后检查hellobig窗口的具体状态
        check_hellobig_window_states()
        
        print(f"\n💡 结论:")
        print("=" * 30)
        print("如果窗口已最小化但STATE_ICONIFIED为❌，")
        print("说明AT-SPI的状态信息不准确或有延迟。")
        print("这解释了为什么我们的检测器会在虚无位置绘制控件。")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
