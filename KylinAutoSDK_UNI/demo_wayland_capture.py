#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
Wayland控件捕获功能演示
展示三种不同的使用方式
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>inAutoSDK_UNI')

def demo_introduction():
    """演示介绍"""
    print("🌊 Wayland环境控件捕获解决方案演示")
    print("=" * 60)
    print()
    print("📋 背景说明:")
    print("   在X11环境中，origin_UNImethod/xlib_on_demand.py 可以:")
    print("   - 全局监听鼠标和键盘事件")
    print("   - Ctrl+鼠标移动实时高亮控件")
    print("   - Ctrl+点击捕获控件信息")
    print()
    print("   但在Wayland环境中，由于安全限制，这些功能无法使用。")
    print()
    print("🚀 我们的解决方案:")
    print("   基于 universal_offset_detector.py 开发了三套工具:")
    print("   1. 增强版通用检测器 - 支持连续监控模式")
    print("   2. 专业控件捕获工具 - 实时显示控件信息")
    print("   3. 快速捕获工具 - 模拟Ctrl+点击功能")
    print()
    print("=" * 60)

def demo_quick_capture():
    """演示快速捕获功能"""
    print("\n🎯 演示1: 快速捕获功能")
    print("-" * 40)
    print("💡 这个功能最接近X11的Ctrl+点击体验")
    print()
    
    # 检查是否有合适的测试窗口
    from universal_offset_detector import get_window_at_mouse_position, get_mouse_position
    
    mouse_pos = get_mouse_position()
    if mouse_pos == (None, None):
        print("❌ 无法获取鼠标位置，跳过演示")
        return
    
    mouse_x, mouse_y = mouse_pos
    print(f"📍 当前鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 检查窗口
    window_info = get_window_at_mouse_position(mouse_x, mouse_y)
    if not window_info:
        print("⚠️  当前鼠标位置没有合适的应用窗口")
        print("💡 请将鼠标移动到应用窗口中，然后重新运行演示")
        return
    
    print(f"🪟 检测到窗口: {window_info['title']}")
    print()
    print("🚀 执行快速捕获...")
    
    # 执行快速捕获
    os.system("DISPLAY=:0 python3 quick_capture.py --mode quick --format simple")

def demo_monitor_mode():
    """演示监控模式"""
    print("\n🔍 演示2: 连续监控模式")
    print("-" * 40)
    print("💡 这个功能替代X11的全局监听，实现连续控件检测")
    print()
    print("⚠️  注意: 这是一个后台运行的演示，会持续5秒")
    print("   在这期间，请移动鼠标到不同的控件上观察效果")
    print()
    
    for i in range(3, 0, -1):
        print(f"⏰ {i}秒后开始...")
        time.sleep(1)
    
    print("🚀 启动监控模式（5秒演示）...")
    
    # 启动监控模式，5秒后自动停止
    os.system("timeout 5 bash -c 'DISPLAY=:0 python3 universal_offset_detector.py --mode monitor --interval 0.5 --duration 1' || true")
    
    print("\n✅ 监控模式演示完成")

def demo_interactive_capture():
    """演示交互式捕获"""
    print("\n🎨 演示3: 专业交互式捕获工具")
    print("-" * 40)
    print("💡 这个工具提供最佳的用户体验，实时显示控件信息")
    print()
    print("📋 功能特点:")
    print("   - 实时显示鼠标下的控件信息")
    print("   - 清屏式界面，信息更清晰")
    print("   - 按Enter键捕获完整控件信息")
    print("   - 轻量级预览高亮 + 确认高亮")
    print()
    
    choice = input("是否启动交互式演示? (y/N): ").strip().lower()
    if choice in ['y', 'yes']:
        print("\n🚀 启动交互式捕获工具...")
        print("💡 提示: 按 Ctrl+C 可退出演示")
        time.sleep(2)
        os.system("DISPLAY=:0 python3 wayland_control_capture.py")
    else:
        print("⏭️  跳过交互式演示")

def show_usage_summary():
    """显示使用总结"""
    print("\n📚 使用总结")
    print("=" * 60)
    print()
    print("🎯 日常使用推荐:")
    print()
    print("1. 快速捕获单个控件:")
    print("   DISPLAY=:0 python3 quick_capture.py --mode quick")
    print()
    print("2. 连续监控控件:")
    print("   DISPLAY=:0 python3 universal_offset_detector.py --mode monitor")
    print()
    print("3. 专业分析工具:")
    print("   DISPLAY=:0 python3 wayland_control_capture.py")
    print()
    print("4. 交互式批量捕获:")
    print("   DISPLAY=:0 python3 quick_capture.py --mode interactive")
    print()
    print("🔧 参数说明:")
    print("   --interval: 监控间隔（秒）")
    print("   --duration: 高亮持续时间（秒）")
    print("   --format: 输出格式 (simple/original)")
    print()
    print("📄 详细文档: WAYLAND_CONTROL_CAPTURE_GUIDE.md")
    print()
    print("=" * 60)

def main():
    """主函数"""
    if not os.getenv('DISPLAY'):
        os.environ['DISPLAY'] = ':0'
    
    try:
        # 演示介绍
        demo_introduction()
        
        # 演示1: 快速捕获
        demo_quick_capture()
        
        # 演示2: 监控模式
        demo_monitor_mode()
        
        # 演示3: 交互式捕获
        demo_interactive_capture()
        
        # 使用总结
        show_usage_summary()
        
        print("🎉 演示完成! 现在您可以在Wayland环境下享受强大的控件捕获功能了!")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
