#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
实时高亮测试脚本
解决 test_enhanced_control_detection.py 的坐标时差问题
"""

import sys
import os
import subprocess
import time

sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>in<PERSON>utoSDK_UNI/src')

def get_current_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def test_realtime_highlight():
    """测试实时高亮"""
    print("🧪 实时高亮测试")
    print("=" * 50)
    print("💡 这个测试会在每次高亮前重新获取鼠标位置")
    print("   解决坐标时差导致的高亮位置错误问题")
    print()
    
    try:
        from UNI_new import UNI
        
        # 创建UNI实例
        uni = UNI()
        
        for i in range(3):
            print(f"🔍 测试 {i+1}/3: 实时坐标高亮")
            
            # 每次都重新获取鼠标位置
            mouse_x, mouse_y = get_current_mouse_position()
            
            if mouse_x is None:
                print("   ❌ 无法获取鼠标位置")
                continue
                
            print(f"   📍 当前鼠标位置: ({mouse_x}, {mouse_y})")
            
            # 立即进行高亮测试
            print("   🎯 立即执行高亮识别...")
            
            start_time = time.time()
            control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
            end_time = time.time()
            
            if control_data:
                name = control_data.get('Name', 'N/A')
                role = control_data.get('Rolename', 'N/A')
                print(f"   ✅ 找到控件: {name} ({role})")
                print(f"   ⏱️ 识别耗时: {end_time-start_time:.3f} 秒")
                print(f"   🎨 高亮应该在 ({mouse_x}, {mouse_y}) 位置显示")
                
                # 显示坐标信息
                coords = control_data.get('Coords', {})
                if coords:
                    print(f"   📏 控件位置: ({coords.get('x')}, {coords.get('y')})")
                    print(f"   📏 控件尺寸: {coords.get('width')} x {coords.get('height')}")
                
            else:
                print(f"   ❌ 未找到控件: {info}")
            
            if i < 2:
                print("   ⏳ 等待 3 秒，请观察高亮效果...")
                time.sleep(3)
                print("   📍 请移动鼠标到新位置进行下一次测试")
                time.sleep(2)
                print()
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_coordinate_timing_issue():
    """测试坐标时差问题"""
    print(f"\n{'='*50}")
    print("🔍 坐标时差问题演示")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        # 模拟 test_enhanced_control_detection.py 的行为
        print("📍 步骤1: 获取初始鼠标位置")
        initial_x, initial_y = get_current_mouse_position()
        if initial_x is None:
            print("   ❌ 无法获取鼠标位置")
            return
            
        print(f"   初始位置: ({initial_x}, {initial_y})")
        
        print("\n⏳ 步骤2: 模拟测试1和测试2的延迟（5秒）")
        print("   请在这5秒内移动鼠标到其他位置...")
        for i in range(5):
            print(f"   倒计时: {5-i} 秒")
            time.sleep(1)
        
        print("\n📍 步骤3: 检查当前鼠标位置")
        current_x, current_y = get_current_mouse_position()
        print(f"   当前位置: ({current_x}, {current_y})")
        
        if (current_x, current_y) != (initial_x, initial_y):
            print(f"   🎯 发现坐标变化: 移动了 ({current_x-initial_x}, {current_y-initial_y}) 像素")
        else:
            print(f"   📌 鼠标位置未变化")
        
        print(f"\n🔍 步骤4: 使用初始坐标进行高亮（模拟原脚本行为）")
        print(f"   使用坐标: ({initial_x}, {initial_y})")
        print(f"   但鼠标实际在: ({current_x}, {current_y})")
        
        control_data, info = uni.kdk_getElement_Uni(initial_x, initial_y, quick=False, highlight=True)
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            print(f"   ✅ 在初始位置找到控件: {name} ({role})")
            print(f"   🎨 高亮显示在: ({initial_x}, {initial_y})")
            print(f"   👀 但您的鼠标在: ({current_x}, {current_y})")
            print(f"   💡 这就是为什么您看不到高亮效果！")
        else:
            print(f"   ❌ 在初始位置未找到控件: {info}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def explain_solution():
    """解释解决方案"""
    print(f"\n{'='*50}")
    print("💡 问题解决方案")
    print("=" * 50)
    
    print("🔍 问题根源:")
    print("   test_enhanced_control_detection.py 在脚本开始时获取鼠标位置")
    print("   然后在整个测试过程中使用这个固定坐标")
    print("   但用户可能在测试过程中移动了鼠标")
    print("   导致高亮显示在错误的位置")
    print()
    print("✅ 解决方案:")
    print("   1. 在每次高亮前重新获取鼠标位置")
    print("   2. 减少获取坐标和执行高亮之间的时间间隔")
    print("   3. 提供实时坐标反馈")
    print("   4. 明确告知用户高亮位置")
    print()
    print("🎯 改进建议:")
    print("   - 使用实时坐标获取")
    print("   - 添加坐标变化检测")
    print("   - 提供更清晰的用户指导")
    print("   - 缩短测试步骤之间的间隔")

def main():
    """主函数"""
    print("实时高亮测试 - 解决坐标时差问题")
    print("=" * 60)
    
    # 测试实时高亮
    test_realtime_highlight()
    
    # 演示坐标时差问题
    test_coordinate_timing_issue()
    
    # 解释解决方案
    explain_solution()
    
    print(f"\n{'='*60}")
    print("🎉 测试完成!")
    print()
    print("💡 总结:")
    print("   test_enhanced_control_detection.py 的高亮功能本身正常")
    print("   问题在于坐标获取和使用之间的时间差")
    print("   解决方案是使用实时坐标获取")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
