#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
完全透明内部的无边框高亮
"""

import subprocess
import time
import os

def transparent_highlight(x, y, width, height, 
                         duration=2,
                         color='red', 
                         border_width=2):
    """
    创建边框内部完全透明的高亮
    
    Args:
        x, y: 控件位置
        width, height: 控件尺寸  
        duration: 高亮持续时间（秒）
        color: 边框颜色
        border_width: 边框宽度
    
    Returns:
        bool: 是否成功显示高亮
    """
    
    # 创建完全透明内部的X11脚本
    x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    # 连接X11
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取边框颜色
    colormap = screen.default_colormap
    border_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 创建窗口 - 关键：background_pixel=0 确保完全透明
    win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width={border_width},
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,  # 关键：0 = 完全透明背景
        border_pixel=border_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    
    # 设置窗口属性
    win.set_wm_name("Transparent Highlight")
    win.set_wm_class("transparent", "Transparent")
    
    # 设置窗口类型和状态
    try:
        win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
        win.change_property(
            disp.intern_atom("_NET_WM_STATE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
             disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
             disp.intern_atom("_NET_WM_STATE_ABOVE")]
        )
    except:
        pass
    
    # 显示窗口
    win.map()
    disp.sync()
    
    # 等待指定时间
    time.sleep({duration})
    
    # 清理
    win.unmap()
    win.destroy()
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    print(f"透明高亮失败: {{e}}")
    exit(1)
'''
    
    try:
        # 执行高亮
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"透明高亮执行失败: {e}")
        return False

def hollow_border_highlight(x, y, width, height, 
                           duration=2,
                           color='red', 
                           border_width=3):
    """
    创建空心边框高亮（使用绘制方法确保透明）
    """
    
    # 使用绘制方法创建空心边框
    draw_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取颜色
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 创建透明窗口
    win = root.create_window(
        {x - border_width}, {y - border_width}, 
        {width + 2 * border_width}, {height + 2 * border_width}, 
        border_width=0,  # 窗口本身无边框
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,  # 完全透明
        override_redirect=True,
        colormap=X.CopyFromParent,
        event_mask=X.ExposureMask
    )
    
    # 设置窗口属性
    win.set_wm_name("Hollow Border")
    try:
        win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
    except:
        pass
    
    # 创建图形上下文
    gc = win.create_gc(
        foreground=color_pixel,
        background=0,
        line_width={border_width}
    )
    
    win.map()
    disp.sync()
    
    # 绘制空心矩形
    border_w = {border_width}
    inner_x = border_w
    inner_y = border_w  
    inner_width = {width}
    inner_height = {height}
    
    # 绘制四条边
    # 上边
    win.fill_rectangle(gc, 0, 0, inner_width + 2*border_w, border_w)
    # 下边  
    win.fill_rectangle(gc, 0, inner_height + border_w, inner_width + 2*border_w, border_w)
    # 左边
    win.fill_rectangle(gc, 0, 0, border_w, inner_height + 2*border_w)
    # 右边
    win.fill_rectangle(gc, inner_width + border_w, 0, border_w, inner_height + 2*border_w)
    
    disp.sync()
    
    time.sleep({duration})
    
    win.unmap()
    win.destroy()
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    print(f"空心边框失败: {{e}}")
    exit(1)
'''
    
    try:
        result = subprocess.run(['python3', '-c', draw_script], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"空心边框执行失败: {e}")
        return False

def test_transparent_highlights():
    """测试完全透明的高亮效果"""
    print("🎯 完全透明内部高亮测试")
    print("=" * 60)
    
    # 获取鼠标位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    test_x = mouse_x - 100
    test_y = mouse_y - 75
    test_w = 200
    test_h = 150
    
    print(f"测试位置: ({test_x}, {test_y}) {test_w}×{test_h}")
    print()
    
    # 测试1: 标准透明高亮
    print("🔍 测试1: 标准透明边框高亮")
    print("   • 边框内部完全透明")
    print("   • 可以看到下方内容")
    success1 = transparent_highlight(test_x, test_y, test_w, test_h,
                                   duration=3, color='red', border_width=2)
    if success1:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    # 测试2: 空心边框高亮
    print("🔍 测试2: 空心边框高亮")
    print("   • 使用绘制方法")
    print("   • 确保内部区域透明")
    success2 = hollow_border_highlight(test_x, test_y, test_w, test_h,
                                     duration=3, color='blue', border_width=3)
    if success2:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    # 测试3: 超细透明边框
    print("🔍 测试3: 超细透明边框")
    print("   • 1像素细边框")
    print("   • 完全透明内部")
    success3 = transparent_highlight(test_x, test_y, test_w, test_h,
                                   duration=3, color='green', border_width=1)
    if success3:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    print("=" * 60)
    print("🎊 透明高亮测试完成！")
    print("\n💡 透明效果说明:")
    print("   • background_pixel=0 确保窗口内部完全透明")
    print("   • 只有边框可见，内部内容不被遮挡")
    print("   • 适合需要保持内容可见的场景")
    print("   • 无任何窗口装饰")

# 便捷函数
def quick_transparent_highlight(x, y, width, height, duration=2):
    """快速透明高亮 - 红色细边框，透明内部"""
    return transparent_highlight(x, y, width, height, duration, 'red', 2)

if __name__ == "__main__":
    test_transparent_highlights()