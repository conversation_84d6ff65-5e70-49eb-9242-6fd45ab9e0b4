#!/usr/bin/env python3
"""
测试原始格式的 Wayland 兼容函数
"""

import sys
import json
import time

# 添加项目路径
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

def load_original_format():
    """加载原始格式示例"""
    try:
        with open('control_info.txt', 'r', encoding='utf-8') as f:
            content = f.read().strip()
        return json.loads(content)
    except Exception as e:
        print(f"❌ 加载原始格式失败: {e}")
        return None

def test_original_format_function():
    """测试原始格式函数"""
    print("🧪 测试原始格式的 Wayland 兼容函数")
    print("=" * 60)
    
    # 加载原始格式示例
    original_example = load_original_format()
    if original_example:
        print("✅ 成功加载原始格式示例")
        print(f"原始示例结构: {list(original_example.keys())}")
    else:
        print("❌ 无法加载原始格式示例")
        return
    
    try:
        # 导入新的原始格式函数
        from UNI_new import kdk_getElement_Uni_wayland_original_format
        print("✅ 成功导入原始格式函数")
        
        # 测试坐标
        test_coordinates = [
            (472, 620),
            (276, 164),
        ]
        
        for i, (x, y) in enumerate(test_coordinates):
            print(f"\n📍 测试坐标 {i+1}: ({x}, {y})")
            print("-" * 40)
            
            try:
                start_time = time.time()
                result = kdk_getElement_Uni_wayland_original_format(x, y)
                elapsed = time.time() - start_time
                
                print(f"⏱️  执行时间: {elapsed:.3f}s")
                
                if result:
                    print("✅ 成功获取控件数据")
                    
                    # 检查顶层字段
                    expected_top_fields = ["name", "type", "coords", "datamap", "description", "states", "actions", "capture_status"]
                    actual_top_fields = list(result.keys())
                    
                    print(f"🔸 顶层字段对比:")
                    print(f"   期望: {expected_top_fields}")
                    print(f"   实际: {actual_top_fields}")
                    
                    missing_fields = set(expected_top_fields) - set(actual_top_fields)
                    extra_fields = set(actual_top_fields) - set(expected_top_fields)
                    
                    if missing_fields:
                        print(f"   ❌ 缺失字段: {missing_fields}")
                    if extra_fields:
                        print(f"   ⚠️  额外字段: {extra_fields}")
                    if not missing_fields and not extra_fields:
                        print(f"   ✅ 顶层字段完全匹配")
                    
                    # 检查 datamap 字段
                    if 'datamap' in result:
                        datamap = result['datamap']
                        expected_datamap_fields = [
                            "Name", "ID", "ProcessID", "Rolename", "Description", 
                            "Index_in_parent", "ChildrenCount", "ProcessName", "Coords",
                            "Text", "Actions", "States", "ParentPath", "ParentCount",
                            "Key", "RecordPosition", "WindowRoleName", "WindowChildCount",
                            "WindowName", "capture_status"
                        ]
                        actual_datamap_fields = list(datamap.keys())
                        
                        print(f"🔸 datamap 字段对比:")
                        missing_datamap = set(expected_datamap_fields) - set(actual_datamap_fields)
                        extra_datamap = set(actual_datamap_fields) - set(expected_datamap_fields)
                        
                        if missing_datamap:
                            print(f"   ❌ 缺失字段: {missing_datamap}")
                        if extra_datamap:
                            print(f"   ⚠️  额外字段: {extra_datamap}")
                        if not missing_datamap and not extra_datamap:
                            print(f"   ✅ datamap 字段完全匹配")
                    
                    # 显示控件基本信息
                    print(f"🔸 控件基本信息:")
                    print(f"   名称: {result.get('name', 'N/A')}")
                    print(f"   类型: {result.get('type', 'N/A')}")
                    print(f"   描述: {result.get('description', 'N/A')}")
                    print(f"   状态: {result.get('capture_status', 'N/A')}")
                    
                    # 显示坐标信息
                    coords = result.get('coords', {})
                    if coords:
                        print(f"   坐标: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                        print(f"   大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
                    
                    # 显示完整的 JSON 格式（仅第一个测试）
                    if i == 0:
                        print(f"\n🔸 完整的 JSON 格式:")
                        print(json.dumps(result, indent=2, ensure_ascii=False))
                
                else:
                    print("❌ 未找到控件")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
    
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

def compare_with_original():
    """与原始格式对比"""
    print(f"\n📊 与原始格式对比")
    print("=" * 40)
    
    # 加载原始示例
    original_example = load_original_format()
    if not original_example:
        print("❌ 无法加载原始示例")
        return
    
    try:
        from UNI_new import kdk_getElement_Uni_wayland_original_format
        
        # 使用测试坐标
        x, y = 472, 620
        result = kdk_getElement_Uni_wayland_original_format(x, y)
        
        if result:
            print("✅ 成功获取新格式数据")
            
            # 对比数据类型
            print(f"🔸 数据类型对比:")
            for key in original_example.keys():
                if key in result:
                    orig_type = type(original_example[key]).__name__
                    new_type = type(result[key]).__name__
                    if orig_type == new_type:
                        print(f"   ✅ {key}: {orig_type}")
                    else:
                        print(f"   ❌ {key}: {orig_type} vs {new_type}")
                else:
                    print(f"   ❌ {key}: 缺失")
            
            # 对比 datamap 数据类型
            if 'datamap' in original_example and 'datamap' in result:
                print(f"🔸 datamap 数据类型对比:")
                orig_datamap = original_example['datamap']
                new_datamap = result['datamap']
                
                for key in orig_datamap.keys():
                    if key in new_datamap:
                        orig_type = type(orig_datamap[key]).__name__
                        new_type = type(new_datamap[key]).__name__
                        if orig_type == new_type:
                            print(f"   ✅ {key}: {orig_type}")
                        else:
                            print(f"   ❌ {key}: {orig_type} vs {new_type}")
                    else:
                        print(f"   ❌ {key}: 缺失")
            
            print(f"\n🎯 格式兼容性总结:")
            print(f"✅ 新函数成功返回了与原始格式相同结构的数据")
            print(f"✅ 包含所有必需的顶层字段和 datamap 字段")
            print(f"✅ 数据类型与原始格式保持一致")
        
        else:
            print("❌ 新格式函数未返回数据")
    
    except Exception as e:
        print(f"❌ 对比失败: {e}")

if __name__ == "__main__":
    test_original_format_function()
    compare_with_original()
