#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
精确控件识别测试
专门测试小控件的识别和精确高亮
"""

import sys
import os
import time
import subprocess
import re

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_hellobig_window_info():
    """获取hellobig窗口信息"""
    try:
        result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        lines = result.stdout.split('\n')
        hellobig_uuid = None
        
        for i, line in enumerate(lines):
            if 'AT-SPI测试界面' in line:
                for j in range(i-1, -1, -1):
                    if 'toplevel' in lines[j]:
                        uuid_match = re.search(r'"([^"]+)"', lines[j])
                        if uuid_match:
                            hellobig_uuid = uuid_match.group(1)
                            break
                break
        
        if not hellobig_uuid:
            return None
        
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', hellobig_uuid], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        geo_match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', result.stdout)
        if geo_match:
            x, y, width, height = map(int, geo_match.groups())
            return {'x': x, 'y': y, 'width': width, 'height': height}
        
        return None
    except Exception:
        return None

def highlight_precisely(x, y, width, height, color='red', duration=3):
    """精确高亮显示"""
    try:
        from ultimate_highlight import ultimate_highlight
        return ultimate_highlight(x, y, width, height, duration, color, 3)
    except Exception as e:
        print(f"高亮失败: {e}")
        return False

def test_specific_control_positions():
    """测试具体的控件位置"""
    print("🎯 测试具体控件位置的精确识别")
    print("=" * 60)
    
    window_info = get_hellobig_window_info()
    if not window_info:
        print("❌ 无法获取窗口信息")
        return
    
    win_x, win_y = window_info['x'], window_info['y']
    print(f"✅ 窗口位置: ({win_x}, {win_y})")
    
    # 定义hellobig应用中的具体控件位置（基于界面布局）
    specific_positions = [
        # 左侧按钮区域
        (win_x + 70, win_y + 80, "普通按钮"),
        (win_x + 70, win_y + 110, "切换按钮"), 
        (win_x + 70, win_y + 140, "复选框"),
        (win_x + 70, win_y + 170, "单选按钮1"),
        (win_x + 70, win_y + 200, "单选按钮2"),
        
        # 输入框区域
        (win_x + 270, win_y + 80, "用户名输入框"),
        (win_x + 270, win_y + 110, "密码输入框"),
        
        # 右侧控件区域
        (win_x + 500, win_y + 80, "音量滑块"),
        (win_x + 500, win_y + 110, "数字输入框"),
        (win_x + 500, win_y + 140, "进度条"),
        
        # 下拉框区域
        (win_x + 700, win_y + 80, "城市下拉框"),
        (win_x + 700, win_y + 110, "日期选择器"),
        
        # 文本区域
        (win_x + 200, win_y + 250, "多行文本框"),
        
        # 列表区域
        (win_x + 100, win_y + 400, "列表项"),
        (win_x + 350, win_y + 400, "树形项"),
        (win_x + 600, win_y + 400, "表格单元格"),
    ]
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        successful_controls = []
        failed_controls = []
        
        for test_x, test_y, description in specific_positions:
            print(f"\n📍 测试 {description} - 位置: ({test_x}, {test_y})")
            
            # 先显示一个小绿点标记测试位置
            highlight_precisely(test_x-2, test_y-2, 4, 4, 'green', 1)
            print(f"   🟢 绿色标记显示在测试位置")
            time.sleep(0.5)
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(test_x, test_y, quick=False, highlight=False)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    # 跳过大的容器控件，寻找具体的小控件
                    if role in ['filler', 'panel'] and coords:
                        width = coords.get('width', 0)
                        height = coords.get('height', 0)
                        if width > 500 or height > 300:  # 过滤掉大的容器
                            print(f"   ⚠️ 跳过大容器: {role} ({width}×{height})")
                            failed_controls.append(description)
                            continue
                    
                    print(f"   ✅ 识别成功 ({elapsed:.2f}s): {name} ({role})")
                    
                    if coords:
                        ctrl_x = coords.get('x', 0)
                        ctrl_y = coords.get('y', 0) 
                        ctrl_w = coords.get('width', 0)
                        ctrl_h = coords.get('height', 0)
                        
                        print(f"   AT-SPI坐标: ({ctrl_x}, {ctrl_y}) 大小: {ctrl_w}×{ctrl_h}")
                        
                        # 计算高亮坐标（AT-SPI坐标 + 窗口偏移）
                        highlight_x = win_x + ctrl_x
                        highlight_y = win_y + ctrl_y
                        
                        print(f"   高亮坐标: ({highlight_x}, {highlight_y})")
                        
                        # 显示精确高亮
                        success = highlight_precisely(
                            highlight_x, highlight_y, 
                            max(ctrl_w, 10), max(ctrl_h, 10), 
                            'red', 3
                        )
                        
                        if success:
                            print(f"   🔴 红色高亮显示控件边界")
                            successful_controls.append({
                                'description': description,
                                'name': name,
                                'role': role,
                                'test_pos': (test_x, test_y),
                                'highlight_pos': (highlight_x, highlight_y),
                                'size': (ctrl_w, ctrl_h)
                            })
                        else:
                            print(f"   ❌ 高亮显示失败")
                            failed_controls.append(description)
                    else:
                        print(f"   ⚠️ 无坐标信息")
                        failed_controls.append(description)
                else:
                    print(f"   ❌ 识别失败 ({elapsed:.2f}s): {info}")
                    failed_controls.append(description)
                
                time.sleep(3)  # 等待观察高亮效果
                
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
                failed_controls.append(description)
        
        # 统计结果
        print(f"\n📊 精确控件识别结果")
        print("=" * 50)
        print(f"总测试数: {len(specific_positions)}")
        print(f"识别成功: {len(successful_controls)}")
        print(f"识别失败: {len(failed_controls)}")
        print(f"成功率: {len(successful_controls)/len(specific_positions)*100:.1f}%")
        
        if successful_controls:
            print(f"\n✅ 成功识别的控件:")
            for ctrl in successful_controls:
                print(f"   - {ctrl['description']}: {ctrl['name']} ({ctrl['role']})")
                print(f"     测试位置: {ctrl['test_pos']} -> 高亮位置: {ctrl['highlight_pos']}")
        
        if failed_controls:
            print(f"\n❌ 识别失败的控件:")
            for desc in failed_controls:
                print(f"   - {desc}")
        
        return len(successful_controls) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mouse_position_precise():
    """测试当前鼠标位置的精确识别"""
    print(f"\n🖱️ 当前鼠标位置精确识别测试")
    print("=" * 60)
    
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            print("❌ 无法获取鼠标位置")
            return False
    except:
        print("❌ 获取鼠标位置异常")
        return False
    
    print(f"当前鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 检查是否在hellobig窗口内
    window_info = get_hellobig_window_info()
    if window_info:
        if (window_info['x'] <= mouse_x <= window_info['x'] + window_info['width'] and
            window_info['y'] <= mouse_y <= window_info['y'] + window_info['height']):
            print("✅ 鼠标在hellobig窗口内")
        else:
            print("⚠️ 鼠标不在hellobig窗口内，可能影响测试效果")
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        # 显示绿色标记在鼠标位置
        highlight_precisely(mouse_x-3, mouse_y-3, 6, 6, 'green', 1)
        print(f"🟢 绿色标记显示在鼠标位置")
        time.sleep(1)
        
        # 进行控件识别
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        elapsed = time.time() - start_time
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            
            print(f"✅ 识别成功 ({elapsed:.2f}s): {name} ({role})")
            
            if coords and window_info:
                ctrl_x = coords.get('x', 0)
                ctrl_y = coords.get('y', 0)
                ctrl_w = coords.get('width', 0)
                ctrl_h = coords.get('height', 0)
                
                # 计算精确高亮位置
                highlight_x = window_info['x'] + ctrl_x
                highlight_y = window_info['y'] + ctrl_y
                
                print(f"控件信息:")
                print(f"   AT-SPI坐标: ({ctrl_x}, {ctrl_y})")
                print(f"   控件大小: {ctrl_w} × {ctrl_h}")
                print(f"   高亮坐标: ({highlight_x}, {highlight_y})")
                
                # 计算精度
                diff_x = highlight_x - mouse_x
                diff_y = highlight_y - mouse_y
                print(f"   与鼠标偏差: X={diff_x}, Y={diff_y}")
                
                # 显示精确高亮
                success = highlight_precisely(
                    highlight_x, highlight_y, 
                    max(ctrl_w, 10), max(ctrl_h, 10),
                    'red', 4
                )
                
                if success:
                    print(f"🔴 红色高亮已显示，请观察:")
                    print(f"   - 绿色点是否在红色框内？")
                    print(f"   - 红色框是否准确对准控件？")
                    
                    if abs(diff_x) < 20 and abs(diff_y) < 20:
                        print(f"✅ 坐标精度良好（偏差<20像素）")
                    else:
                        print(f"⚠️ 坐标偏差较大，可能需要进一步调整")
                    
                    return True
                else:
                    print(f"❌ 高亮显示失败")
                    return False
            else:
                print(f"⚠️ 缺少坐标信息或窗口信息")
                return False
        else:
            print(f"❌ 控件识别失败 ({elapsed:.2f}s): {info}")
            return False
    
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("精确控件识别测试")
    print("=" * 80)
    print("🎯 目标: 测试具体小控件的精确识别和高亮")
    print("🔍 方法: 针对已知控件位置进行精确测试")
    print("🎨 效果: 绿色点标记测试位置，红色框显示识别结果")
    print()
    
    try:
        # 1. 测试具体控件位置
        print("🎯 步骤1: 测试具体控件位置")
        specific_success = test_specific_control_positions()
        
        # 2. 测试当前鼠标位置
        print("\n🖱️ 步骤2: 测试当前鼠标位置")
        mouse_success = test_mouse_position_precise()
        
        # 总结
        print(f"\n📊 测试总结")
        print("=" * 50)
        
        if specific_success:
            print("✅ 具体控件位置测试成功")
        else:
            print("❌ 具体控件位置测试失败")
        
        if mouse_success:
            print("✅ 鼠标位置测试成功")
        else:
            print("❌ 鼠标位置测试失败")
        
        if specific_success or mouse_success:
            print("\n🎉 部分或全部测试成功！")
            print("💡 观察要点:")
            print("   - 绿色标记显示测试的目标位置")
            print("   - 红色边框显示识别到的控件边界")
            print("   - 理想情况下绿色点应该在红色框内")
            print("   - 红色框应该准确对准实际的界面控件")
        else:
            print("\n⚠️ 所有测试都失败了")
            print("💡 可能的原因:")
            print("   - hellobig应用没有运行或不可见")
            print("   - AT-SPI服务有问题")
            print("   - 坐标转换算法需要进一步调整")
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 80)
    print("🎉 精确控件识别测试完成!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())