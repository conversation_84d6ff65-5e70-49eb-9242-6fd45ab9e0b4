#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
hellobig应用控件识别自动测试
自动运行，无需交互输入
"""

import sys
import os
import time
import subprocess
from collections import defaultdict

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def find_hellobig_window():
    """查找hellobig应用窗口"""
    try:
        from uni_sdk.utils.helpers import get_windows_by_wlcctrl
        
        windows = get_windows_by_wlcctrl()
        
        for title, geometry in windows.items():
            if 'hellobig' in title.lower():
                print(f"✅ 找到hellobig窗口: {title}")
                print(f"   窗口位置: ({geometry['x']}, {geometry['y']})")
                print(f"   窗口大小: {geometry['width']} × {geometry['height']}")
                return {
                    'title': title,
                    'geometry': geometry
                }
        
        print("❌ 未找到hellobig应用窗口")
        return None
        
    except Exception as e:
        print(f"❌ 查找窗口失败: {e}")
        return None

def test_key_control_areas():
    """测试关键控件区域"""
    print("🔍 测试关键控件区域识别")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        
        # 查找hellobig窗口
        window_info = find_hellobig_window()
        if not window_info:
            return []
        
        geometry = window_info['geometry']
        
        # 定义关键测试点（基于Qt Creator界面的典型位置）
        test_areas = [
            {
                'name': '文件菜单',
                'point': (geometry['x'] + 45, geometry['y'] + 85),
                'description': '主菜单栏中的文件菜单'
            },
            {
                'name': '编辑菜单',
                'point': (geometry['x'] + 95, geometry['y'] + 85),
                'description': '主菜单栏中的编辑菜单'
            },
            {
                'name': '视图菜单',
                'point': (geometry['x'] + 145, geometry['y'] + 85),
                'description': '主菜单栏中的视图菜单'
            },
            {
                'name': '构建菜单',
                'point': (geometry['x'] + 195, geometry['y'] + 85),
                'description': '主菜单栏中的构建菜单'
            },
            {
                'name': '工具栏按钮',
                'point': (geometry['x'] + 150, geometry['y'] + 125),
                'description': '工具栏中的按钮'
            },
            {
                'name': '侧边栏切换',
                'point': (geometry['x'] + 50, geometry['y'] + 1500),
                'description': '底部状态栏的侧边栏切换'
            },
            {
                'name': '主窗口区域',
                'point': (geometry['x'] + 500, geometry['y'] + 300),
                'description': '主要内容区域'
            },
            {
                'name': '右侧面板',
                'point': (geometry['x'] + 2000, geometry['y'] + 400),
                'description': '右侧工具面板'
            },
        ]
        
        print(f"📋 测试 {len(test_areas)} 个关键区域")
        
        successful_tests = []
        failed_tests = []
        
        for i, area in enumerate(test_areas):
            print(f"\n📍 区域 {i+1}/{len(test_areas)}: {area['name']}")
            print(f"   位置: {area['point']}")
            print(f"   描述: {area['description']}")
            
            x, y = area['point']
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(x, y, quick=False, highlight=True)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    print(f"   ✅ 识别成功 ({elapsed:.2f}s)")
                    print(f"      控件名称: {name}")
                    print(f"      控件类型: {role}")
                    
                    if coords:
                        print(f"      控件位置: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                        print(f"      控件大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
                    
                    print("   🎨 红色高亮边框已显示")
                    
                    successful_tests.append({
                        'area': area['name'],
                        'name': name,
                        'role': role,
                        'coords': coords,
                        'time': elapsed
                    })
                    
                    # 等待观察高亮效果
                    time.sleep(3)
                    
                else:
                    print(f"   ❌ 识别失败 ({elapsed:.2f}s)")
                    print(f"      原因: {info}")
                    
                    failed_tests.append({
                        'area': area['name'],
                        'reason': info,
                        'time': elapsed
                    })
                
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
                failed_tests.append({
                    'area': area['name'],
                    'reason': f"异常: {e}",
                    'time': 0
                })
        
        # 统计结果
        print(f"\n📊 测试结果统计")
        print("=" * 40)
        print(f"成功识别: {len(successful_tests)}/{len(test_areas)} 个区域")
        print(f"识别成功率: {len(successful_tests)/len(test_areas)*100:.1f}%")
        
        if successful_tests:
            avg_time = sum(t['time'] for t in successful_tests) / len(successful_tests)
            print(f"平均识别时间: {avg_time:.2f}s")
            
            # 控件类型统计
            type_count = defaultdict(int)
            for test in successful_tests:
                type_count[test['role']] += 1
            
            print("\n识别的控件类型:")
            for role, count in sorted(type_count.items()):
                print(f"  - {role}: {count} 个")
        
        if failed_tests:
            print(f"\n失败的测试:")
            for test in failed_tests:
                print(f"  - {test['area']}: {test['reason']}")
        
        return successful_tests
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_mouse_position():
    """测试当前鼠标位置的控件识别"""
    print(f"\n🖱️ 测试当前鼠标位置")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        
        # 获取鼠标位置
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            print("❌ 无法获取鼠标位置")
            return
        
        print(f"当前鼠标位置: ({mouse_x}, {mouse_y})")
        
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
        elapsed = time.time() - start_time
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            
            print(f"✅ 识别成功 ({elapsed:.2f}s)")
            print(f"   控件名称: {name}")
            print(f"   控件类型: {role}")
            
            if coords:
                print(f"   控件位置: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                print(f"   控件大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
            
            print("🎨 红色高亮边框已显示，持续5秒")
            time.sleep(5)
            
        else:
            print(f"❌ 未找到控件 ({elapsed:.2f}s)")
            print(f"   详情: {info}")
        
    except Exception as e:
        print(f"❌ 鼠标位置测试失败: {e}")

def performance_test():
    """性能测试"""
    print(f"\n⚡ 性能测试")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        
        # 查找hellobig窗口
        window_info = find_hellobig_window()
        if not window_info:
            return
        
        geometry = window_info['geometry']
        
        # 生成测试点
        test_points = [
            (geometry['x'] + 100, geometry['y'] + 100),
            (geometry['x'] + 300, geometry['y'] + 200),
            (geometry['x'] + 500, geometry['y'] + 300),
            (geometry['x'] + 700, geometry['y'] + 400),
            (geometry['x'] + 900, geometry['y'] + 500),
        ]
        
        print(f"📋 对 {len(test_points)} 个位置进行性能测试")
        
        times = []
        success_count = 0
        
        for i, (x, y) in enumerate(test_points):
            print(f"测试 {i+1}/{len(test_points)}: ({x}, {y})", end=" ")
            
            start_time = time.time()
            control_data, info = uni.kdk_getElement_Uni(x, y, quick=False, highlight=False)
            elapsed = time.time() - start_time
            
            times.append(elapsed)
            
            if control_data:
                success_count += 1
                name = control_data.get('Name', 'N/A')
                role = control_data.get('Rolename', 'N/A')
                print(f"✅ {elapsed:.2f}s - {name} ({role})")
            else:
                print(f"❌ {elapsed:.2f}s - 未找到控件")
        
        # 性能统计
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        success_rate = success_count / len(test_points) * 100
        
        print(f"\n📊 性能测试结果:")
        print(f"   平均时间: {avg_time:.2f}s")
        print(f"   最快时间: {min_time:.2f}s")
        print(f"   最慢时间: {max_time:.2f}s")
        print(f"   成功率: {success_rate:.1f}% ({success_count}/{len(test_points)})")
        
        # 性能评估
        if avg_time <= 1.0:
            print("✅ 性能优秀: 平均响应时间在1秒以内")
        elif avg_time <= 2.0:
            print("⚠️ 性能良好: 平均响应时间1-2秒")
        else:
            print("❌ 性能需要优化: 平均响应时间超过2秒")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def main():
    """主函数"""
    print("hellobig应用控件识别自动测试")
    print("=" * 60)
    print("🎯 目标: 快速准确识别各类控件并进行高亮显示")
    print("🤖 自动运行，无需手动输入")
    print()
    
    # 检查hellobig应用是否运行
    window_info = find_hellobig_window()
    if not window_info:
        print("❌ 请确保hellobig应用正在运行")
        print("💡 提示: 当前检测到的是Qt Creator，请确保它正在运行")
        return 1
    
    print("✅ hellobig应用已就绪，开始自动测试")
    print()
    
    try:
        # 1. 测试关键控件区域
        print("🔍 步骤1: 关键控件区域测试")
        successful_tests = test_key_control_areas()
        
        # 2. 测试当前鼠标位置
        print("\n🖱️ 步骤2: 当前鼠标位置测试")
        test_mouse_position()
        
        # 3. 性能测试
        print("\n⚡ 步骤3: 性能测试")
        performance_test()
        
        # 总结
        print(f"\n📋 测试总结")
        print("=" * 40)
        
        if successful_tests:
            print(f"✅ 成功识别了 {len(successful_tests)} 个控件")
            print("主要控件类型:")
            
            type_count = defaultdict(int)
            for test in successful_tests:
                type_count[test['role']] += 1
            
            for role, count in sorted(type_count.items()):
                print(f"  - {role}: {count} 个")
        else:
            print("❌ 未成功识别任何控件")
        
        print("\n💡 功能特点:")
        print("  - 支持多种控件类型识别")
        print("  - 提供红色边框高亮显示")
        print("  - 返回详细的控件信息（名称、类型、位置、大小）")
        print("  - 具有良好的性能表现")
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 hellobig控件识别自动测试完成!")
    print("🎨 测试中的红色边框高亮显示了成功识别的控件")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())