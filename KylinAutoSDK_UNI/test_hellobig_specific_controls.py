#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
测试hellobig应用中的具体控件
基于探索器发现的实际控件位置进行测试
"""

import sys
import os
import time
from offset_corrected_detector import OffsetCorrectedDetector


class HellobigControlTester:
    """hellobig控件测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.detector = OffsetCorrectedDetector(target_app_name="hellobig")
        
        # 基于探索器发现的实际控件位置
        self.test_controls = [
            {
                'name': '普通按钮',
                'position': (20, 20),
                'size': (100, 30),
                'type': 'push button'
            },
            {
                'name': '切换按钮',
                'position': (140, 20),
                'size': (100, 30),
                'type': 'check box'
            },
            {
                'name': '复选框',
                'position': (20, 71),
                'size': (58, 22),
                'type': 'check box'
            },
            {
                'name': '单选按钮1',
                'position': (160, 71),
                'size': (54, 22),
                'type': 'radio button'
            },
            {
                'name': '单选按钮2',
                'position': (280, 71),
                'size': (54, 22),
                'type': 'radio button'
            },
            {
                'name': '用户名输入框',
                'position': (110, 110),
                'size': (200, 25),
                'type': 'text'
            },
            {
                'name': '密码输入框',
                'position': (420, 110),
                'size': (200, 25),
                'type': 'password text'
            },
            {
                'name': '音量滑块',
                'position': (110, 160),
                'size': (200, 20),
                'type': 'slider'
            },
            {
                'name': '数字输入框',
                'position': (330, 160),
                'size': (80, 25),
                'type': 'spin button'
            },
            {
                'name': '城市下拉框',
                'position': (110, 210),
                'size': (150, 25),
                'type': 'combo box'
            }
        ]
        
        print("🧪 hellobig控件测试器")
        print("=" * 60)
        print(f"📋 准备测试 {len(self.test_controls)} 个控件")
    
    def test_control_center(self, control_info):
        """测试控件中心点"""
        x, y = control_info['position']
        width, height = control_info['size']
        
        # 计算中心点
        center_x = x + width // 2
        center_y = y + height // 2
        
        print(f"\n🎯 测试控件: {control_info['name']}")
        print(f"   类型: {control_info['type']}")
        print(f"   位置: ({x}, {y}) 尺寸: {width}×{height}")
        print(f"   中心点: ({center_x}, {center_y})")
        print("-" * 40)
        
        # 测试检测
        result = self.detector.get_control_at_point_corrected(center_x, center_y)
        
        if result['success']:
            detected_info = result['control_info']
            print(f"✅ 检测成功!")
            print(f"   检测到名称: {detected_info['name']}")
            print(f"   检测到类型: {detected_info['role']}")
            
            # 验证是否匹配
            name_match = control_info['name'] in detected_info['name'] or detected_info['name'] in control_info['name']
            type_match = control_info['type'] == detected_info['role']
            
            if name_match and type_match:
                print(f"   🎉 完全匹配!")
                return True
            elif name_match:
                print(f"   ⚠️  名称匹配，但类型不匹配 (期望: {control_info['type']}, 实际: {detected_info['role']})")
                return True
            elif type_match:
                print(f"   ⚠️  类型匹配，但名称不匹配 (期望: {control_info['name']}, 实际: {detected_info['name']})")
                return True
            else:
                print(f"   ❌ 不匹配 (期望: {control_info['name']}/{control_info['type']}, 实际: {detected_info['name']}/{detected_info['role']})")
                return False
        else:
            print(f"❌ 检测失败: {result['message']}")
            return False
    
    def test_control_with_highlight(self, control_info):
        """测试控件并高亮显示"""
        x, y = control_info['position']
        width, height = control_info['size']
        
        # 计算中心点
        center_x = x + width // 2
        center_y = y + height // 2
        
        print(f"\n✨ 高亮测试控件: {control_info['name']}")
        print(f"   中心点: ({center_x}, {center_y})")
        
        # 执行高亮
        result = self.detector.highlight_control_corrected(center_x, center_y, duration=3, color='green', border_width=3)
        
        if result['success'] and result['highlighted']:
            print(f"✅ 高亮成功!")
            detected_info = result['control_info']
            print(f"   检测到: {detected_info['name']} ({detected_info['role']})")
            return True
        else:
            print(f"❌ 高亮失败: {result['message']}")
            return False
    
    def test_all_controls(self):
        """测试所有控件"""
        print("🚀 开始测试所有控件...")
        
        success_count = 0
        total_count = len(self.test_controls)
        
        for i, control in enumerate(self.test_controls):
            print(f"\n📍 测试 {i+1}/{total_count}")
            
            if self.test_control_center(control):
                success_count += 1
        
        print(f"\n📊 测试结果统计:")
        print("=" * 40)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        return success_count, total_count
    
    def test_with_highlights(self):
        """测试并高亮显示所有控件"""
        print("\n✨ 开始高亮测试...")
        
        success_count = 0
        total_count = len(self.test_controls)
        
        for i, control in enumerate(self.test_controls):
            print(f"\n🎯 高亮测试 {i+1}/{total_count}")
            
            if self.test_control_with_highlight(control):
                success_count += 1
            
            # 等待一下，让用户看到高亮效果
            time.sleep(1)
        
        print(f"\n📊 高亮测试结果:")
        print("=" * 40)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        return success_count, total_count
    
    def interactive_test(self):
        """交互式测试"""
        print("\n🎮 交互式测试模式")
        print("=" * 40)
        print("选择测试模式:")
        print("1. 仅检测测试")
        print("2. 高亮测试")
        print("3. 单个控件测试")
        print("4. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-4): ").strip()
                
                if choice == '1':
                    self.test_all_controls()
                elif choice == '2':
                    self.test_with_highlights()
                elif choice == '3':
                    self.single_control_test()
                elif choice == '4':
                    print("👋 退出测试")
                    break
                else:
                    print("❌ 无效选择，请输入 1-4")
                    
            except KeyboardInterrupt:
                print("\n👋 退出测试")
                break
            except Exception as e:
                print(f"❌ 测试异常: {e}")
    
    def single_control_test(self):
        """单个控件测试"""
        print("\n📋 可用控件列表:")
        for i, control in enumerate(self.test_controls):
            print(f"   {i+1}. {control['name']} ({control['type']})")
        
        try:
            choice = int(input(f"\n请选择控件 (1-{len(self.test_controls)}): ")) - 1
            
            if 0 <= choice < len(self.test_controls):
                control = self.test_controls[choice]
                print(f"\n🎯 测试控件: {control['name']}")
                
                # 先检测
                success = self.test_control_center(control)
                
                if success:
                    # 再高亮
                    print(f"\n✨ 执行高亮...")
                    self.test_control_with_highlight(control)
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 测试异常: {e}")


def main():
    """主函数"""
    print("🧪 hellobig应用控件测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        tester = HellobigControlTester()
        tester.interactive_test()
        
    except Exception as e:
        print(f"❌ 测试器启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
