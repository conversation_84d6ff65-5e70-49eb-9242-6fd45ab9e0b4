#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
调试坐标精度问题
精确分析鼠标位置与控件检测的坐标转换
"""

import sys
import os
import subprocess
import time
import re
import pyatspi


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_window_offset():
    """获取窗口偏移"""
    try:
        # 获取窗口列表
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 查找hellobig窗口ID
        window_id = None
        lines = result.stdout.strip().split('\n')
        for i, line in enumerate(lines):
            if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                if i > 0:
                    prev_line = lines[i-1]
                    if 'toplevel' in prev_line:
                        window_id = prev_line.split('"')[1]
                        break
        
        if not window_id:
            return None
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 解析几何信息
        for line in result.stdout.strip().split('\n'):
            if 'geometry:' in line:
                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)', line)
                if match:
                    x, y = int(match.group(1)), int(match.group(2))
                    return (x, y)
        
        return None
        
    except Exception as e:
        print(f"❌ 获取窗口偏移失败: {e}")
        return None


def find_all_controls_at_point(screen_x, screen_y, window_offset):
    """查找指定屏幕坐标处的所有控件"""
    print(f"\n🔍 详细坐标分析:")
    print("=" * 50)
    print(f"🖱️  鼠标屏幕坐标: ({screen_x}, {screen_y})")
    
    if window_offset:
        atspi_x = screen_x - window_offset[0]
        atspi_y = screen_y - window_offset[1]
        print(f"🔄 转换为AT-SPI坐标: ({atspi_x}, {atspi_y})")
        print(f"   使用窗口偏移: {window_offset}")
    else:
        atspi_x, atspi_y = screen_x, screen_y
        print(f"🔄 AT-SPI坐标 (无偏移): ({atspi_x}, {atspi_y})")
    
    # 收集所有控件
    all_controls = []
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            if 'hellobig' in app_name.lower():
                print(f"📱 搜索应用: {app_name}")
                collect_controls_recursive(app, all_controls, max_depth=6)
                break
        
        print(f"📊 总共收集到 {len(all_controls)} 个控件")
        
        # 查找匹配的控件
        matches = []
        for ctrl in all_controls:
            ext = ctrl['extents']
            
            # 检查点是否在控件范围内
            if (ext.x <= atspi_x < ext.x + ext.width and
                ext.y <= atspi_y < ext.y + ext.height):
                
                # 计算距离中心的距离
                center_x = ext.x + ext.width // 2
                center_y = ext.y + ext.height // 2
                distance = ((atspi_x - center_x) ** 2 + (atspi_y - center_y) ** 2) ** 0.5
                
                matches.append({
                    'control': ctrl,
                    'distance': distance,
                    'area': ext.width * ext.height
                })
        
        print(f"\n🎯 找到 {len(matches)} 个匹配的控件:")
        print("-" * 40)
        
        for i, match in enumerate(matches):
            ctrl = match['control']
            ext = ctrl['extents']
            
            print(f"{i+1}. {ctrl['name']} ({ctrl['role']})")
            print(f"   AT-SPI位置: ({ext.x}, {ext.y}) 尺寸: {ext.width}×{ext.height}")
            print(f"   面积: {match['area']}, 距离中心: {match['distance']:.1f}")
            
            # 计算屏幕坐标
            if window_offset:
                screen_left = ext.x + window_offset[0]
                screen_top = ext.y + window_offset[1]
                screen_right = screen_left + ext.width
                screen_bottom = screen_top + ext.height
                
                print(f"   屏幕坐标: ({screen_left}, {screen_top}) 到 ({screen_right}, {screen_bottom})")
                
                # 检查鼠标是否真的在屏幕坐标范围内
                mouse_in_screen_bounds = (screen_left <= screen_x <= screen_right and 
                                        screen_top <= screen_y <= screen_bottom)
                print(f"   鼠标在屏幕边界内: {'✅' if mouse_in_screen_bounds else '❌'}")
                
                if not mouse_in_screen_bounds:
                    x_diff = 0
                    y_diff = 0
                    
                    if screen_x < screen_left:
                        x_diff = screen_x - screen_left
                    elif screen_x > screen_right:
                        x_diff = screen_x - screen_right
                    
                    if screen_y < screen_top:
                        y_diff = screen_y - screen_top
                    elif screen_y > screen_bottom:
                        y_diff = screen_y - screen_bottom
                    
                    print(f"   偏差: X={x_diff}, Y={y_diff}")
            
            print()
        
        return matches
        
    except Exception as e:
        print(f"❌ 查找控件失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def collect_controls_recursive(element, control_list, depth=0, max_depth=6):
    """递归收集控件"""
    if depth > max_depth:
        return
    
    try:
        name = element.name or "N/A"
        role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
        
        # 获取坐标
        extents = None
        try:
            if hasattr(element, 'queryComponent'):
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
        except Exception:
            pass
        
        # 收集所有有坐标的控件
        if extents and extents.width > 0 and extents.height > 0:
            control_list.append({
                'element': element,
                'name': name,
                'role': role,
                'extents': extents,
                'depth': depth
            })
        
        # 递归处理子元素
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                collect_controls_recursive(child, control_list, depth + 1, max_depth)
        except Exception:
            pass
            
    except Exception:
        pass


def test_coordinate_precision():
    """测试坐标精度"""
    print("🔬 坐标精度测试")
    print("=" * 60)
    
    # 获取窗口偏移
    window_offset = get_window_offset()
    if not window_offset:
        print("❌ 无法获取窗口偏移")
        return
    
    print(f"🪟 窗口偏移: {window_offset}")
    
    while True:
        try:
            input("\n按回车键测试当前鼠标位置 (Ctrl+C 退出)...")
            
            # 获取鼠标位置
            mouse_pos = get_mouse_position()
            if mouse_pos == (None, None):
                print("❌ 无法获取鼠标位置")
                continue
            
            mouse_x, mouse_y = mouse_pos
            
            # 查找所有匹配的控件
            matches = find_all_controls_at_point(mouse_x, mouse_y, window_offset)
            
            if matches:
                print(f"🎯 推荐选择:")
                
                # 按面积排序，选择最小的（最具体的）
                specific_matches = [m for m in matches if m['control']['role'] not in 
                                  ['filler', 'panel', 'frame', 'window', 'application']]
                
                if specific_matches:
                    best_match = min(specific_matches, key=lambda x: x['area'])
                    ctrl = best_match['control']
                    
                    print(f"✅ 最具体的控件: {ctrl['name']} ({ctrl['role']})")
                    
                    # 测试高亮
                    choice = input("是否高亮此控件? (y/n): ").strip().lower()
                    if choice == 'y':
                        test_highlight_at_control(ctrl, window_offset, mouse_x, mouse_y)
                else:
                    print("❌ 没有找到具体控件，只有容器控件")
            else:
                print("❌ 未找到任何匹配的控件")
                
        except KeyboardInterrupt:
            print("\n👋 退出测试")
            break
        except Exception as e:
            print(f"❌ 测试异常: {e}")


def test_highlight_at_control(ctrl, window_offset, mouse_x, mouse_y):
    """测试控件高亮"""
    ext = ctrl['extents']
    
    print(f"\n✨ 高亮测试:")
    print(f"   控件: {ctrl['name']} ({ctrl['role']})")
    print(f"   AT-SPI坐标: ({ext.x}, {ext.y}) {ext.width}×{ext.height}")
    
    # 计算高亮坐标
    if window_offset:
        highlight_x = ext.x + window_offset[0]
        highlight_y = ext.y + window_offset[1]
    else:
        highlight_x = ext.x
        highlight_y = ext.y
    
    print(f"   高亮坐标: ({highlight_x}, {highlight_y}) {ext.width}×{ext.height}")
    print(f"   鼠标坐标: ({mouse_x}, {mouse_y})")
    
    # 分析偏差
    mouse_in_highlight = (highlight_x <= mouse_x <= highlight_x + ext.width and
                         highlight_y <= mouse_y <= highlight_y + ext.height)
    
    print(f"   鼠标在高亮区域内: {'✅' if mouse_in_highlight else '❌'}")
    
    if not mouse_in_highlight:
        x_offset = mouse_x - (highlight_x + ext.width // 2)
        y_offset = mouse_y - (highlight_y + ext.height // 2)
        print(f"   偏差 (相对中心): X={x_offset}, Y={y_offset}")
    
    # 执行高亮
    try:
        from ultimate_highlight import ultimate_highlight
        highlight_success = ultimate_highlight(
            highlight_x, highlight_y, ext.width, ext.height,
            duration=3, color='orange', border_width=2
        )
        
        if highlight_success:
            print(f"✅ 高亮已执行")
        else:
            print(f"❌ 高亮失败")
            
    except Exception as e:
        print(f"❌ 高亮异常: {e}")


def main():
    """主函数"""
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    test_coordinate_precision()


if __name__ == "__main__":
    main()
