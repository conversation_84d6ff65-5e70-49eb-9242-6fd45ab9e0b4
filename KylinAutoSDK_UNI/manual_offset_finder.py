#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
手动偏移查找器
直接进入手动调整模式，快速找到正确偏移
"""

import sys
import os
import subprocess
import time
import re
import pyatspi


def get_wlcctrl_offset():
    """获取wlcctrl报告的窗口偏移"""
    try:
        # 获取窗口列表
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 查找hellobig窗口ID
        window_id = None
        lines = result.stdout.strip().split('\n')
        for i, line in enumerate(lines):
            if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                if i > 0:
                    prev_line = lines[i-1]
                    if 'toplevel' in prev_line:
                        window_id = prev_line.split('"')[1]
                        break
        
        if not window_id:
            return None
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 解析几何信息
        for line in result.stdout.strip().split('\n'):
            if 'geometry:' in line:
                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)', line)
                if match:
                    x, y = int(match.group(1)), int(match.group(2))
                    return (x, y)
        
        return None
        
    except Exception as e:
        print(f"❌ 获取wlcctrl偏移失败: {e}")
        return None


def find_test_control():
    """查找测试用的控件"""
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            if 'hellobig' in app_name.lower():
                print(f"📱 找到应用: {app_name}")
                
                # 查找普通按钮
                control = find_control_recursive(app, "普通按钮", "push button")
                if control:
                    return control
                
                # 如果没找到普通按钮，查找其他已知控件
                known_controls = [
                    ("切换按钮", "check box"),
                    ("复选框", "check box"),
                    ("单选按钮1", "radio button"),
                    ("用户名输入框", "text")
                ]
                
                for name, role in known_controls:
                    control = find_control_recursive(app, name, role)
                    if control:
                        return control
        
        return None
        
    except Exception as e:
        print(f"❌ 查找控件失败: {e}")
        return None


def find_control_recursive(element, target_name, target_role, depth=0, max_depth=4):
    """递归查找指定控件"""
    if depth > max_depth:
        return None
    
    try:
        name = element.name or "N/A"
        role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
        
        # 检查是否是目标控件
        if name == target_name and role == target_role:
            # 获取坐标
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        if extents.width > 0 and extents.height > 0:
                            return {
                                'name': name,
                                'role': role,
                                'atspi_position': (extents.x, extents.y),
                                'size': (extents.width, extents.height),
                                'element': element
                            }
            except Exception:
                pass
        
        # 递归处理子元素
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                result = find_control_recursive(child, target_name, target_role, depth + 1, max_depth)
                if result:
                    return result
        except Exception:
            pass
            
    except Exception:
        pass
    
    return None


def test_offset_with_control(control, test_offset):
    """使用控件测试偏移"""
    atspi_x, atspi_y = control['atspi_position']
    width, height = control['size']
    
    # 计算控件在屏幕上的预期位置
    expected_screen_x = atspi_x + test_offset[0]
    expected_screen_y = atspi_y + test_offset[1]
    
    print(f"\n🧪 测试偏移: {test_offset}")
    print(f"   控件: {control['name']} ({control['role']})")
    print(f"   AT-SPI位置: ({atspi_x}, {atspi_y}) {width}×{height}")
    print(f"   预期屏幕位置: ({expected_screen_x}, {expected_screen_y}) {width}×{height}")
    
    # 执行高亮测试
    try:
        from ultimate_highlight import ultimate_highlight
        
        print(f"   执行高亮...")
        highlight_success = ultimate_highlight(
            expected_screen_x, expected_screen_y, width, height,
            duration=3, color='red', border_width=3
        )
        
        if highlight_success:
            print(f"   ✅ 高亮成功")
            return True
        else:
            print(f"   ❌ 高亮失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 高亮异常: {e}")
        return False


def manual_offset_finder():
    """手动偏移查找器"""
    print("🔧 手动偏移查找器")
    print("=" * 60)
    
    # 获取基础偏移
    wlc_offset = get_wlcctrl_offset()
    if wlc_offset:
        print(f"🪟 wlcctrl报告的偏移: {wlc_offset}")
    else:
        print("❌ 无法获取wlcctrl偏移，使用默认值")
        wlc_offset = (564, 155)
    
    # 查找测试控件
    control = find_test_control()
    if not control:
        print("❌ 未找到测试控件")
        return
    
    print(f"🎯 测试控件: {control['name']} ({control['role']})")
    print(f"   AT-SPI位置: {control['atspi_position']}")
    print(f"   尺寸: {control['size']}")
    
    print(f"\n💡 使用说明:")
    print(f"   - 输入偏移值格式: x,y (例如: 564,115)")
    print(f"   - 基于你的反馈，如果高亮偏高40像素，尝试减少Y值")
    print(f"   - 当前wlcctrl偏移: {wlc_offset}")
    print(f"   - 建议尝试: {wlc_offset[0]},{wlc_offset[1]-40}")
    print(f"   - 输入 'q' 退出")
    
    # 建议的偏移值
    suggested_offsets = [
        (wlc_offset[0], wlc_offset[1] - 40),  # 减少40像素Y偏移
        (wlc_offset[0], wlc_offset[1] - 30),  # 减少30像素Y偏移
        (wlc_offset[0], wlc_offset[1] - 50),  # 减少50像素Y偏移
        (wlc_offset[0] - 10, wlc_offset[1] - 40),  # 同时调整X和Y
        (wlc_offset[0] + 10, wlc_offset[1] - 40),  # 同时调整X和Y
    ]
    
    print(f"\n🎯 建议的偏移值:")
    for i, offset in enumerate(suggested_offsets):
        print(f"   {i+1}. {offset}")
    
    while True:
        try:
            user_input = input(f"\n请输入偏移值 (或输入数字1-{len(suggested_offsets)}选择建议值): ").strip()
            
            if user_input.lower() == 'q':
                break
            
            # 检查是否是选择建议值
            if user_input.isdigit():
                choice = int(user_input)
                if 1 <= choice <= len(suggested_offsets):
                    test_offset = suggested_offsets[choice - 1]
                else:
                    print("❌ 无效选择")
                    continue
            elif ',' in user_input:
                # 解析自定义偏移
                x_str, y_str = user_input.split(',')
                test_offset = (int(x_str.strip()), int(y_str.strip()))
            else:
                print("❌ 格式错误，请使用 x,y 格式或选择数字")
                continue
            
            # 测试偏移
            if test_offset_with_control(control, test_offset):
                feedback = input(f"   高亮是否准确覆盖了 {control['name']}? (y/n): ").strip().lower()
                
                if feedback == 'y':
                    print(f"\n🎉 找到正确偏移: {test_offset}")
                    
                    # 验证其他控件
                    verify_offset(test_offset)
                    return test_offset
                else:
                    print(f"   偏移 {test_offset} 不正确")
                    
                    # 提供调整建议
                    direction = input(f"   高亮位置相对正确位置如何? (上方输入'up', 下方输入'down', 左侧输入'left', 右侧输入'right'): ").strip().lower()
                    
                    if direction == 'up':
                        suggested = (test_offset[0], test_offset[1] - 20)
                        print(f"   建议尝试: {suggested} (减少Y偏移)")
                    elif direction == 'down':
                        suggested = (test_offset[0], test_offset[1] + 20)
                        print(f"   建议尝试: {suggested} (增加Y偏移)")
                    elif direction == 'left':
                        suggested = (test_offset[0] - 20, test_offset[1])
                        print(f"   建议尝试: {suggested} (减少X偏移)")
                    elif direction == 'right':
                        suggested = (test_offset[0] + 20, test_offset[1])
                        print(f"   建议尝试: {suggested} (增加X偏移)")
            
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            break
    
    print(f"\n👋 退出偏移查找")
    return None


def verify_offset(offset):
    """验证偏移在其他控件上的效果"""
    print(f"\n🔬 验证偏移 {offset} 在其他控件上的效果...")
    
    # 查找其他控件进行验证
    verification_controls = [
        ("切换按钮", "check box"),
        ("复选框", "check box"),
        ("用户名输入框", "text")
    ]
    
    for name, role in verification_controls:
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                if 'hellobig' in app_name.lower():
                    control = find_control_recursive(app, name, role)
                    if control:
                        print(f"\n验证控件: {name}")
                        if test_offset_with_control(control, offset):
                            feedback = input(f"   高亮是否准确覆盖了 {name}? (y/n): ").strip().lower()
                            if feedback == 'y':
                                print(f"   ✅ {name} 验证成功")
                            else:
                                print(f"   ❌ {name} 验证失败")
                        break
                    break
        except Exception:
            continue


def main():
    """主函数"""
    print("🔧 手动偏移查找器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        correct_offset = manual_offset_finder()
        
        if correct_offset:
            print(f"\n🎯 最终结果:")
            print(f"正确的坐标偏移: {correct_offset}")
            print(f"使用方法: 屏幕坐标 = AT-SPI坐标 + {correct_offset}")
        else:
            print(f"\n❌ 未能确定正确的偏移")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
