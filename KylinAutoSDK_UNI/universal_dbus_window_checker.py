#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
通用D-Bus窗口状态检查器
适用于不同桌面环境的窗口状态检查
"""

import sys
import os
import subprocess
import json
import re


def run_dbus_command(service, path, interface, method, *args):
    """执行D-Bus命令"""
    cmd = ['dbus-send', '--session', '--print-reply', '--dest=' + service, path, interface + '.' + method]
    if args:
        cmd.extend(args)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"
    except Exception as e:
        return -1, "", str(e)


def check_dbus_services():
    """检查可用的D-Bus服务"""
    print("🔍 检查可用的D-Bus服务...")
    
    try:
        result = subprocess.run(['dbus-send', '--session', '--print-reply', 
                               '--dest=org.freedesktop.DBus', '/org/freedesktop/DBus',
                               'org.freedesktop.DBus.ListNames'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            services = []
            lines = result.stdout.split('\n')
            for line in lines:
                if 'string' in line:
                    # 提取服务名称
                    matches = re.findall(r'"([^"]*)"', line)
                    services.extend(matches)
            
            # 过滤窗口管理相关的服务
            window_services = []
            keywords = ['kwin', 'mutter', 'compiz', 'window', 'wm', 'desktop', 'gnome', 'kde', 'xfce', 'mate', 'ukui']
            
            for service in services:
                service_lower = service.lower()
                if any(keyword in service_lower for keyword in keywords):
                    window_services.append(service)
            
            print(f"   找到 {len(services)} 个D-Bus服务")
            print(f"   窗口管理相关服务 ({len(window_services)} 个):")
            for service in window_services[:10]:  # 显示前10个
                print(f"     - {service}")
            
            return window_services
        else:
            print(f"   ❌ 获取D-Bus服务失败: {result.stderr}")
            return []
    
    except Exception as e:
        print(f"   ❌ 检查D-Bus服务失败: {e}")
        return []


def try_window_manager_interfaces():
    """尝试不同窗口管理器的D-Bus接口"""
    print("\n🔍 尝试不同窗口管理器的D-Bus接口...")
    
    # 定义不同桌面环境的D-Bus接口
    interfaces = [
        # KDE/KWin
        ('org.kde.KWin', '/KWin', 'org.kde.KWin', 'windowList'),
        ('org.kde.kwin', '/KWin', 'org.kde.KWin', 'getWindowList'),
        
        # GNOME/Mutter
        ('org.gnome.Mutter', '/org/gnome/Mutter', 'org.gnome.Mutter', 'GetWindows'),
        ('org.gnome.Shell', '/org/gnome/Shell', 'org.gnome.Shell', 'GetWindows'),
        
        # XFCE
        ('org.xfce.Xfwm4', '/org/xfce/Xfwm4', 'org.xfce.Xfwm4', 'GetWindows'),
        
        # MATE
        ('org.mate.Marco', '/org/mate/Marco', 'org.mate.Marco', 'GetWindows'),
        
        # UKUI (基于MATE)
        ('org.ukui.Marco', '/org/ukui/Marco', 'org.ukui.Marco', 'GetWindows'),
        ('org.ukui.WindowManager', '/org/ukui/WindowManager', 'org.ukui.WindowManager', 'GetWindows'),
        
        # 通用接口
        ('org.freedesktop.WindowManager', '/org/freedesktop/WindowManager', 'org.freedesktop.WindowManager', 'GetWindows'),
    ]
    
    successful_interfaces = []
    
    for service, path, interface, method in interfaces:
        print(f"   尝试: {service} -> {method}")
        
        returncode, stdout, stderr = run_dbus_command(service, path, interface, method)
        
        if returncode == 0 and stdout.strip():
            print(f"     ✅ 成功!")
            successful_interfaces.append((service, path, interface, method, stdout))
        else:
            print(f"     ❌ 失败: {stderr.strip()}")
    
    return successful_interfaces


def check_wmctrl_window_states():
    """使用wmctrl检查窗口状态"""
    print("\n🔍 使用wmctrl检查窗口状态...")
    
    try:
        # 获取窗口列表
        result = subprocess.run(['wmctrl', '-l'], capture_output=True, text=True)
        if result.returncode != 0:
            print("   ❌ wmctrl -l 失败")
            return []
        
        windows = []
        lines = result.stdout.strip().split('\n')
        
        for line in lines:
            parts = line.split(None, 3)
            if len(parts) >= 4:
                window_id = parts[0]
                desktop = parts[1]
                hostname = parts[2]
                title = parts[3]
                
                windows.append({
                    'id': window_id,
                    'desktop': desktop,
                    'hostname': hostname,
                    'title': title
                })
        
        print(f"   找到 {len(windows)} 个窗口:")
        for window in windows:
            print(f"     - {window['id']}: {window['title']}")
        
        # 查找hellobig窗口
        hellobig_windows = [w for w in windows if 'AT-SPI测试' in w['title'] or 'hellobig' in w['title'].lower()]
        
        if hellobig_windows:
            print(f"\n   🎯 找到hellobig相关窗口:")
            for window in hellobig_windows:
                print(f"     - {window['id']}: {window['title']}")
                
                # 检查窗口的详细状态
                check_window_detailed_state(window['id'], window['title'])
        else:
            print(f"   ❌ 未找到hellobig相关窗口")
        
        return windows
        
    except Exception as e:
        print(f"   ❌ wmctrl检查失败: {e}")
        return []


def check_window_detailed_state(window_id, window_title):
    """检查窗口的详细状态"""
    print(f"     🔍 检查窗口 {window_id} 的详细状态...")
    
    # 方法1: 使用wmctrl -G 获取几何信息
    try:
        result = subprocess.run(['wmctrl', '-G'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.startswith(window_id):
                    parts = line.split()
                    if len(parts) >= 7:
                        x, y, width, height = parts[3:7]
                        print(f"       几何信息: ({x}, {y}) {width}×{height}")
                        
                        # 检查是否在屏幕外（可能是最小化）
                        try:
                            x, y, width, height = int(x), int(y), int(width), int(height)
                            if x < -10000 or y < -10000 or width <= 0 or height <= 0:
                                print(f"       ⚠️  窗口可能已最小化（几何信息异常）")
                            else:
                                print(f"       ✅ 窗口几何信息正常")
                        except ValueError:
                            print(f"       ⚠️  无法解析几何信息")
                    break
    except Exception as e:
        print(f"       ❌ 获取几何信息失败: {e}")
    
    # 方法2: 使用wmctrl -p 获取进程信息
    try:
        result = subprocess.run(['wmctrl', '-p'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.startswith(window_id):
                    parts = line.split(None, 4)
                    if len(parts) >= 4:
                        pid = parts[2]
                        print(f"       进程ID: {pid}")
                        
                        # 检查进程是否存在
                        try:
                            subprocess.run(['ps', '-p', pid], capture_output=True, check=True)
                            print(f"       ✅ 进程存在")
                        except subprocess.CalledProcessError:
                            print(f"       ❌ 进程不存在")
                    break
    except Exception as e:
        print(f"       ❌ 获取进程信息失败: {e}")
    
    # 方法3: 尝试激活窗口来测试是否可见
    try:
        result = subprocess.run(['wmctrl', '-i', '-a', window_id], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"       ✅ 窗口可以被激活（可能可见）")
        else:
            print(f"       ❌ 窗口无法被激活: {result.stderr.strip()}")
    except Exception as e:
        print(f"       ❌ 测试窗口激活失败: {e}")


def check_xprop_window_state():
    """使用xprop检查窗口状态"""
    print("\n🔍 使用xprop检查窗口状态...")
    
    try:
        # 获取根窗口的客户端列表
        result = subprocess.run(['xprop', '-root', '_NET_CLIENT_LIST'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   客户端列表: {result.stdout.strip()}")
            
            # 提取窗口ID
            window_ids = re.findall(r'0x[0-9a-fA-F]+', result.stdout)
            print(f"   找到 {len(window_ids)} 个窗口ID")
            
            for window_id in window_ids[:5]:  # 检查前5个窗口
                check_xprop_window_properties(window_id)
        else:
            print(f"   ❌ 获取客户端列表失败: {result.stderr}")
    
    except Exception as e:
        print(f"   ❌ xprop检查失败: {e}")


def check_xprop_window_properties(window_id):
    """使用xprop检查单个窗口的属性"""
    print(f"     🔍 检查窗口 {window_id}...")
    
    try:
        result = subprocess.run(['xprop', '-id', window_id, 'WM_NAME', '_NET_WM_STATE'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            window_name = ""
            window_state = ""
            
            for line in lines:
                if 'WM_NAME' in line:
                    match = re.search(r'"([^"]*)"', line)
                    if match:
                        window_name = match.group(1)
                elif '_NET_WM_STATE' in line:
                    window_state = line
            
            if window_name:
                print(f"       名称: {window_name}")
                print(f"       状态: {window_state}")
                
                # 检查是否是hellobig窗口
                if 'AT-SPI测试' in window_name or 'hellobig' in window_name.lower():
                    print(f"       🎯 这是hellobig窗口!")
                    
                    # 分析状态
                    if '_NET_WM_STATE_HIDDEN' in window_state:
                        print(f"       🔴 窗口已隐藏/最小化")
                    elif '_NET_WM_STATE_MAXIMIZED' in window_state:
                        print(f"       🟢 窗口已最大化")
                    elif window_state.strip().endswith('='):
                        print(f"       🟢 窗口状态正常（无特殊状态）")
                    else:
                        print(f"       ❓ 窗口状态未知")
    
    except Exception as e:
        print(f"       ❌ 检查窗口属性失败: {e}")


def main():
    """主函数"""
    print("🔍 通用D-Bus窗口状态检查器")
    print("=" * 60)
    print("🎯 适用于不同桌面环境的窗口状态检查")
    print("=" * 60)
    
    # 检查D-Bus服务
    services = check_dbus_services()
    
    # 尝试窗口管理器接口
    interfaces = try_window_manager_interfaces()
    
    # 使用wmctrl检查
    wmctrl_windows = check_wmctrl_window_states()
    
    # 使用xprop检查
    check_xprop_window_state()
    
    print(f"\n💡 总结:")
    print("=" * 30)
    print(f"D-Bus服务: {len(services)} 个")
    print(f"可用接口: {len(interfaces)} 个")
    print(f"wmctrl窗口: {len(wmctrl_windows)} 个")
    
    if interfaces:
        print(f"\n✅ 找到可用的D-Bus接口，可以集成到检测器中")
    else:
        print(f"\n⚠️  未找到可用的D-Bus接口，建议使用wmctrl/xprop方法")


if __name__ == "__main__":
    main()
