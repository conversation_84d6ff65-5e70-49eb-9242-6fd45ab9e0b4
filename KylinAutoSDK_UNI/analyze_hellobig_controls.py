#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
分析hellobig应用的控件结构
专门针对树形控件的层次结构分析
"""

import sys
import os
import subprocess
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def find_hellobig_application():
    """查找hellobig应用程序"""
    try:
        import pyatspi
        from uni_sdk.utils.atspi_helpers import get_all_applications
        
        apps = get_all_applications()
        hellobig_app = None
        
        print("🔍 查找hellobig应用...")
        for app in apps:
            try:
                app_name = app.name.lower() if app.name else ""
                if 'hellobig' in app_name or 'hello' in app_name:
                    hellobig_app = app
                    print(f"✅ 找到hellobig应用: {app.name}")
                    break
            except:
                continue
        
        if not hellobig_app:
            print("❌ 未找到hellobig应用")
            print("\n📋 当前运行的应用程序:")
            for i, app in enumerate(apps):
                try:
                    print(f"   {i+1}. {app.name}")
                except:
                    print(f"   {i+1}. <无名应用>")
        
        return hellobig_app
    except Exception as e:
        print(f"❌ 查找应用失败: {e}")
        return None

def print_control_tree(element, depth=0, max_depth=5, prefix=""):
    """递归打印控件树结构"""
    if depth > max_depth:
        return
    
    try:
        # 获取控件基本信息
        name = getattr(element, 'name', '') or ''
        role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
        
        # 获取坐标信息
        coords_info = ""
        try:
            if hasattr(element, 'queryComponent'):
                component = element.queryComponent()
                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                coords_info = f" [{extents.x}, {extents.y}, {extents.width}x{extents.height}]"
        except:
            pass
        
        # 获取状态信息
        state_info = ""
        try:
            if hasattr(element, 'getState'):
                state_set = element.getState()
                states = []
                if state_set.contains(pyatspi.STATE_VISIBLE):
                    states.append("VISIBLE")
                if state_set.contains(pyatspi.STATE_ENABLED):
                    states.append("ENABLED")
                if state_set.contains(pyatspi.STATE_FOCUSED):
                    states.append("FOCUSED")
                if state_set.contains(pyatspi.STATE_SELECTED):
                    states.append("SELECTED")
                if states:
                    state_info = f" |{','.join(states)}|"
        except:
            pass
        
        # 打印当前控件信息
        indent = "  " * depth
        print(f"{indent}{prefix}📁 {role}: '{name}'{coords_info}{state_info}")
        
        # 递归打印子控件
        try:
            child_count = element.childCount if hasattr(element, 'childCount') else 0
            for i in range(child_count):
                try:
                    child = element.getChildAtIndex(i)
                    if child:
                        child_prefix = f"[{i}] "
                        print_control_tree(child, depth + 1, max_depth, child_prefix)
                except Exception as e:
                    print(f"{indent}  ❌ 子控件{i}获取失败: {e}")
        except Exception as e:
            print(f"{indent}  ❌ 获取子控件失败: {e}")
            
    except Exception as e:
        print(f"{'  ' * depth}❌ 控件信息获取失败: {e}")

def analyze_control_at_coordinate(x, y):
    """分析指定坐标处的控件"""
    try:
        import pyatspi
        
        print(f"\n🎯 分析坐标 ({x}, {y}) 处的控件层次结构:")
        print("=" * 60)
        
        # 使用AT-SPI直接查找控件
        try:
            element = pyatspi.findDescendant(pyatspi.Registry.getDesktop(), 
                                           lambda x: True)
            
            def find_control_at_point(element, target_x, target_y, depth=0):
                """递归查找指定坐标的控件"""
                if depth > 10:  # 防止无限递归
                    return []
                
                matching_controls = []
                
                try:
                    # 检查当前控件是否包含目标坐标
                    if hasattr(element, 'queryComponent'):
                        component = element.queryComponent()
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        
                        if (extents.x <= target_x <= extents.x + extents.width and
                            extents.y <= target_y <= extents.y + extents.height):
                            
                            # 添加控件信息
                            name = getattr(element, 'name', '') or ''
                            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                            
                            control_info = {
                                'element': element,
                                'name': name,
                                'role': role,
                                'coords': (extents.x, extents.y, extents.width, extents.height),
                                'depth': depth
                            }
                            matching_controls.append(control_info)
                            
                            # 递归检查子控件
                            try:
                                child_count = element.childCount if hasattr(element, 'childCount') else 0
                                for i in range(child_count):
                                    try:
                                        child = element.getChildAtIndex(i)
                                        if child:
                                            child_matches = find_control_at_point(child, target_x, target_y, depth + 1)
                                            matching_controls.extend(child_matches)
                                    except:
                                        continue
                            except:
                                pass
                                
                except:
                    pass
                
                return matching_controls
            
            # 从桌面开始查找
            desktop = pyatspi.Registry.getDesktop()
            all_matches = []
            
            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)
                    if app:
                        matches = find_control_at_point(app, x, y)
                        all_matches.extend(matches)
                except:
                    continue
            
            # 按深度排序，显示从浅到深的控件层次
            all_matches.sort(key=lambda x: x['depth'])
            
            print(f"📊 找到 {len(all_matches)} 个包含坐标的控件:")
            print()
            
            for i, match in enumerate(all_matches):
                depth = match['depth']
                name = match['name']
                role = match['role']
                coords = match['coords']
                
                indent = "  " * depth
                print(f"{indent}📍 层次{depth}: {role}")
                print(f"{indent}   名称: '{name}'")
                print(f"{indent}   坐标: ({coords[0]}, {coords[1]}) 尺寸: {coords[2]}x{coords[3]}")
                print(f"{indent}   面积: {coords[2] * coords[3]}")
                print()
            
            # 分析最深层的控件（最具体的控件）
            if all_matches:
                deepest = max(all_matches, key=lambda x: x['depth'])
                print(f"🎯 最深层控件 (最具体): {deepest['role']} '{deepest['name']}'")
                
                # 分析最小面积的控件（最精确的控件）
                smallest = min(all_matches, key=lambda x: x['coords'][2] * x['coords'][3])
                print(f"📏 最小面积控件 (最精确): {smallest['role']} '{smallest['name']}'")
                
        except Exception as e:
            print(f"❌ AT-SPI分析失败: {e}")
            
    except Exception as e:
        print(f"❌ 坐标控件分析失败: {e}")

def compare_uni_detection():
    """对比UNI检测结果"""
    try:
        from UNI_new import UNI
        
        mouse_x, mouse_y = get_mouse_position()
        if not mouse_x:
            print("❌ 无法获取鼠标位置")
            return
        
        print(f"\n🔍 UNI检测结果对比 (坐标: {mouse_x}, {mouse_y}):")
        print("=" * 60)
        
        uni = UNI()
        
        # 测试快速模式
        print("⚡ 快速模式检测:")
        try:
            control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=True, highlight=False)
            if control_data:
                name = control_data.get('Name', 'N/A')
                role = control_data.get('Rolename', 'N/A')
                coords = control_data.get('Coords', {})
                print(f"   结果: {role} '{name}'")
                if coords:
                    print(f"   坐标: ({coords.get('x')}, {coords.get('y')}) 尺寸: {coords.get('width')}x{coords.get('height')}")
            else:
                print(f"   未找到控件: {info}")
        except Exception as e:
            print(f"   错误: {e}")
        
        print()
        
        # 测试普通模式
        print("🐌 普通模式检测:")
        try:
            control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
            if control_data:
                name = control_data.get('Name', 'N/A')
                role = control_data.get('Rolename', 'N/A')
                coords = control_data.get('Coords', {})
                print(f"   结果: {role} '{name}'")
                if coords:
                    print(f"   坐标: ({coords.get('x')}, {coords.get('y')}) 尺寸: {coords.get('width')}x{coords.get('height')}")
            else:
                print(f"   未找到控件: {info}")
        except Exception as e:
            print(f"   错误: {e}")
            
    except Exception as e:
        print(f"❌ UNI对比失败: {e}")

def main():
    """主函数"""
    print("🔍 Hellobig应用控件结构分析")
    print("=" * 60)
    
    # 第一步：查找并分析hellobig应用的完整控件树
    hellobig_app = find_hellobig_application()
    
    if hellobig_app:
        print(f"\n📊 {hellobig_app.name} 应用控件树结构:")
        print("=" * 60)
        print_control_tree(hellobig_app, max_depth=6)
    
    # 第二步：分析当前鼠标位置的控件
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x:
        analyze_control_at_coordinate(mouse_x, mouse_y)
        
        # 第三步：对比UNI的检测结果
        compare_uni_detection()
    else:
        print("⚠️ 无法获取鼠标位置，跳过坐标分析")
    
    print("\n" + "=" * 60)
    print("🎉 分析完成!")
    print()
    print("💡 使用说明:")
    print("   1. 请将鼠标悬停在hellobig应用的树形控件节点上")
    print("   2. 运行此脚本查看完整的控件层次结构")
    print("   3. 对比AT-SPI原始结果和UNI检测结果的差异")
    print("   4. 重点关注树形控件的子节点层次和坐标信息")
    
    return 0

if __name__ == "__main__":
    # 导入必要的模块
    try:
        import pyatspi
    except ImportError:
        print("❌ 需要安装 python3-pyatspi")
        print("   sudo apt-get install python3-pyatspi")
        sys.exit(1)
    
    sys.exit(main())