#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
准确的窗口检测器
专注于wlcctrl的准确性，忽略不可靠的AT-SPI可见性检查
"""

import sys
import os
import subprocess
import pyatspi
import re


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_all_windows_with_geometry():
    """获取所有窗口及其几何信息"""
    print("📋 获取所有窗口信息...")
    
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            return []
        
        all_windows = []
        lines = result.stdout.strip().split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                # 获取几何信息
                geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                          capture_output=True, text=True)
                if geo_result.returncode == 0:
                    for geo_line in geo_result.stdout.strip().split('\n'):
                        if 'geometry:' in geo_line:
                            match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                            if match:
                                x, y, width, height = map(int, match.groups())
                                
                                all_windows.append({
                                    'window_id': window_id,
                                    'title': window_title,
                                    'position': (x, y),
                                    'size': (width, height),
                                    'area': width * height
                                })
                                break
                
                i += 2
            else:
                i += 1
        
        return all_windows
        
    except Exception as e:
        print(f"❌ 获取窗口信息失败: {e}")
        return []


def filter_suspicious_windows(windows):
    """过滤可疑的窗口（可能是最小化或隐藏的）"""
    print("🔍 过滤可疑窗口...")
    
    filtered_windows = []
    
    for window in windows:
        title = window['title']
        x, y = window['position']
        width, height = window['size']
        
        print(f"   检查窗口: {title}")
        print(f"     位置: ({x}, {y}) 尺寸: {width}×{height}")
        
        # 过滤规则1: 异常大的尺寸（可能是最小化窗口的错误报告）
        if width >= 2500 or height >= 1500:
            print(f"     ❌ 尺寸异常大，可能是最小化窗口")
            continue
        
        # 过滤规则2: 在原点且尺寸很大
        if x == 0 and y == 0 and (width > 1500 or height > 1000):
            print(f"     ❌ 在原点且尺寸大，可能是最小化窗口")
            continue
        
        # 过滤规则3: 明显的系统窗口
        if title.lower() in ['桌面', 'desktop', 'ukui-panel', 'ukui-sidebar']:
            print(f"     ⏭️  跳过系统窗口")
            continue
        
        # 过滤规则4: 特殊检查hellobig相关窗口
        if ('hellobig' in title.lower() or 'AT-SPI测试' in title or 'Qt Creator' in title):
            # 如果hellobig相关窗口报告异常位置或尺寸，很可能是最小化的
            if (x == 0 and y == 0) or width >= 2000 or height >= 1200:
                print(f"     ❌ hellobig相关窗口位置/尺寸异常，可能已最小化")
                continue
        
        print(f"     ✅ 窗口通过过滤")
        filtered_windows.append(window)
    
    return filtered_windows


def find_window_at_mouse_position(mouse_x, mouse_y):
    """找到鼠标位置下的真实窗口"""
    print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 获取所有窗口
    all_windows = get_all_windows_with_geometry()
    print(f"   找到 {len(all_windows)} 个窗口")
    
    # 过滤可疑窗口
    valid_windows = filter_suspicious_windows(all_windows)
    print(f"   过滤后剩余 {len(valid_windows)} 个有效窗口")
    
    # 查找鼠标位置匹配的窗口
    matching_windows = []
    for window in valid_windows:
        x, y = window['position']
        width, height = window['size']
        
        if (x <= mouse_x < x + width and y <= mouse_y < y + height):
            matching_windows.append(window)
    
    print(f"\n🎯 鼠标位置匹配的窗口:")
    if not matching_windows:
        print("❌ 未找到匹配的窗口")
        return None
    
    for i, window in enumerate(matching_windows):
        print(f"   {i+1}. {window['title']}")
        print(f"      位置: {window['position']} 尺寸: {window['size'][0]}×{window['size'][1]}")
    
    # 选择面积最小的窗口（通常是最上层的）
    best_window = min(matching_windows, key=lambda w: w['area'])
    
    print(f"\n✅ 选择窗口: {best_window['title']}")
    print(f"   位置: {best_window['position']}")
    print(f"   尺寸: {best_window['size']}")
    
    return best_window


def find_atspi_window_for_real_window(window_info):
    """根据真实窗口信息找到对应的AT-SPI窗口"""
    if not window_info:
        return None
    
    window_title = window_info['title']
    print(f"\n🔍 查找AT-SPI窗口: {window_title}")
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            print(f"   检查应用: {app_name}")
            
            # 跳过系统应用
            if app_name.lower() in ['at-spi-bus-launcher', 'gnome-shell', 'ibus-daemon']:
                continue
            
            for j in range(app.childCount):
                try:
                    atspi_window = app.getChildAtIndex(j)
                    window_name = atspi_window.name or ""
                    
                    print(f"     检查窗口: {window_name}")
                    
                    # 多种匹配策略
                    if window_titles_match(window_title, window_name, app_name):
                        print(f"   ✅ 找到匹配的AT-SPI窗口: {window_name}")
                        
                        # 获取AT-SPI窗口坐标
                        if hasattr(atspi_window, 'queryComponent'):
                            component = atspi_window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                
                                return {
                                    'app': app,
                                    'app_name': app_name,
                                    'window': atspi_window,
                                    'window_name': window_name,
                                    'atspi_position': (extents.x, extents.y),
                                    'atspi_size': (extents.width, extents.height)
                                }
                except Exception:
                    continue
        
        print(f"   ❌ 未找到匹配的AT-SPI窗口")
        return None
        
    except Exception as e:
        print(f"❌ 查找AT-SPI窗口失败: {e}")
        return None


def window_titles_match(real_title, atspi_title, app_name):
    """检查窗口标题是否匹配"""
    if not real_title:
        return False
    
    # 直接匹配
    if atspi_title and (real_title.lower() in atspi_title.lower() or atspi_title.lower() in real_title.lower()):
        return True
    
    # 应用名称匹配
    if app_name and any(keyword in app_name.lower() for keyword in real_title.lower().split() if len(keyword) > 3):
        return True
    
    # 特殊匹配规则
    special_matches = [
        ('mate-terminal', 'terminal'),
        ('terminal', 'mate-terminal'),
        ('hellobig', 'AT-SPI测试'),
        ('AT-SPI测试', 'hellobig'),
        ('Qt Creator', 'mainwindow'),
    ]
    
    for pattern1, pattern2 in special_matches:
        if (pattern1.lower() in real_title.lower() and pattern2.lower() in atspi_title.lower()) or \
           (pattern2.lower() in real_title.lower() and pattern1.lower() in atspi_title.lower()):
            return True
    
    return False


def main():
    """主函数"""
    print("🎯 准确的窗口检测器")
    print("=" * 60)
    print("🔍 专注于wlcctrl的准确性")
    print("❌ 忽略不可靠的AT-SPI可见性检查")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        mouse_x, mouse_y = get_mouse_position()
        if not mouse_x:
            print("❌ 无法获取鼠标位置")
            return
        
        # 找到鼠标位置下的真实窗口
        real_window = find_window_at_mouse_position(mouse_x, mouse_y)
        if not real_window:
            print("❌ 未找到真实窗口")
            return
        
        # 查找对应的AT-SPI窗口
        atspi_window_info = find_atspi_window_for_real_window(real_window)
        if not atspi_window_info:
            print("❌ 未找到对应的AT-SPI窗口")
            return
        
        # 计算偏移
        real_x, real_y = real_window['position']
        real_width, real_height = real_window['size']
        atspi_x, atspi_y = atspi_window_info['atspi_position']
        atspi_width, atspi_height = atspi_window_info['atspi_size']
        
        offset_x = real_x - atspi_x
        offset_y = real_y - atspi_y
        
        # 考虑标题栏高度
        height_diff = real_height - atspi_height
        if height_diff > 0:
            corrected_offset_y = offset_y + height_diff
            calculated_offset = (offset_x, corrected_offset_y)
        else:
            calculated_offset = (offset_x, offset_y)
        
        print(f"\n📊 最终结果:")
        print("=" * 40)
        print(f"🪟 真实窗口: {real_window['title']}")
        print(f"   位置: {real_window['position']} 尺寸: {real_window['size']}")
        print(f"🔍 AT-SPI窗口: {atspi_window_info['window_name']}")
        print(f"   位置: {atspi_window_info['atspi_position']} 尺寸: {atspi_window_info['atspi_size']}")
        print(f"📐 计算偏移: {calculated_offset}")
        
        if height_diff > 0:
            print(f"🔍 检测到标题栏高度: {height_diff}像素")
        
        print(f"\n✅ 这是真正准确的窗口检测结果！")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
