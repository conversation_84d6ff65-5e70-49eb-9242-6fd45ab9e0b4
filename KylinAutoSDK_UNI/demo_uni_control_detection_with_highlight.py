#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
UNI控件检测与终极高亮完整演示
根据当前鼠标位置获取控件信息并高亮显示
"""

import sys
import os
import subprocess
import time

# 添加路径
sys.path.insert(0, '/home/<USER>/<PERSON>ylinAutoSDK_UNI/src')

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            print("⚠️ 无法获取鼠标位置，使用默认坐标")
            return 1000, 500
    except Exception as e:
        print(f"⚠️ 获取鼠标位置失败: {e}，使用默认坐标")
        return 1000, 500

def demo_control_detection_with_highlight():
    """完整的控件检测与高亮演示"""
    print("🎯 UNI控件检测与终极高亮完整演示")
    print("=" * 70)
    
    try:
        # 导入UNI模块
        print("📦 正在导入UNI模块...")
        from UNI_new import UNI
        print("✅ UNI模块导入成功")
        
        # 创建UNI实例
        print("🔧 正在创建UNI实例...")
        uni = UNI()
        print("✅ UNI实例创建成功")
        print()
        
        # 获取当前鼠标位置
        print("🖱️ 正在获取当前鼠标位置...")
        mouse_x, mouse_y = get_mouse_position()
        print(f"📍 当前鼠标位置: ({mouse_x}, {mouse_y})")
        print()
        
        print("💡 演示说明:")
        print("   • 将在当前鼠标位置检测控件")
        print("   • 如果找到控件，将使用终极高亮技术显示边框")
        print("   • 高亮效果: 完全无窗口装饰 + 100%透明内部 + 纯边框线条")
        print()
        
        # 倒计时准备
        print("⏱️ 准备开始检测，倒计时:")
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        print("   🚀 开始检测!")
        print()
        
        # 执行控件检测与高亮
        print("🔍 正在检测控件...")
        print("-" * 50)
        
        # 方法1: 基本检测带高亮
        print("📋 方法1: 基本检测带高亮")
        try:
            control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
            
            if control_data:
                print("✅ 成功检测到控件并进行高亮显示!")
                print()
                print("📊 控件详细信息:")
                print(f"   🏷️  控件名称: {control_data.get('Name', 'N/A')}")
                print(f"   🎭 控件类型: {control_data.get('Rolename', 'N/A')}")
                print(f"   🖼️  窗口名称: {control_data.get('WindowName', 'N/A')}")
                print(f"   🔧 进程名称: {control_data.get('ProcessName', 'N/A')}")
                print(f"   📝 描述信息: {control_data.get('Description', 'N/A')}")
                
                # 坐标信息
                coords = control_data.get('Coords', {})
                if coords:
                    print(f"   📍 屏幕坐标: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                    print(f"   📏 控件尺寸: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
                
                # 相对坐标
                rel_coords = control_data.get('RelativeCoords', {})
                if rel_coords:
                    print(f"   🎯 相对坐标: ({rel_coords.get('x', 'N/A')}, {rel_coords.get('y', 'N/A')})")
                
                # 状态和动作
                states = control_data.get('States', [])
                if states:
                    print(f"   🎛️  控件状态: {', '.join(states[:3])}{'...' if len(states) > 3 else ''}")
                
                actions = control_data.get('Actions', [])
                if actions:
                    print(f"   ⚡ 可用动作: {', '.join(actions[:3])}{'...' if len(actions) > 3 else ''}")
                
                print()
                print("🎨 高亮显示效果:")
                print("   ✅ 完全无窗口装饰 (无标题栏、按钮)")
                print("   ✅ 中间区域100%透明 (不遮挡内容)")
                print("   ✅ 纯边框线条显示 (红色边框)")
                print("   ✅ 四个独立边框窗口 (绕过窗口管理器)")
                print()
                
                # 等待用户观察效果
                print("⏰ 高亮效果将持续3秒，请观察...")
                time.sleep(3)
                print("✨ 高亮效果已结束")
                
            else:
                print("❌ 在当前鼠标位置未检测到控件")
                print(f"   📋 检测信息: {info}")
                print()
                print("💡 建议:")
                print("   • 将鼠标移动到按钮、文本框等UI控件上")
                print("   • 确保鼠标位置在应用窗口内")
                print("   • 重新运行此演示脚本")
                
        except Exception as e:
            print(f"❌ 控件检测失败: {e}")
            print()
            
        print()
        print("-" * 50)
        
        # 额外演示：快速检测模式
        print("📋 方法2: 快速检测模式演示")
        print("🚀 正在进行快速检测...")
        
        try:
            control_data_quick, info_quick = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=True, highlight=True)
            
            if control_data_quick:
                print("✅ 快速模式也成功检测到控件!")
                print(f"   🏷️  控件: {control_data_quick.get('Name', 'N/A')} ({control_data_quick.get('Rolename', 'N/A')})")
                print("   ⚡ 快速模式特点: 更快的检测速度，适合实时应用")
            else:
                print("❌ 快速模式未检测到控件")
                print(f"   📋 信息: {info_quick}")
                
        except Exception as e:
            print(f"❌ 快速检测失败: {e}")
            
        print()
        print("=" * 70)
        print("🎉 UNI控件检测与终极高亮演示完成!")
        print()
        print("🌟 演示总结:")
        print("   ✅ 成功展示了鼠标位置控件检测功能")
        print("   ✅ 验证了终极高亮技术的完美效果")
        print("   ✅ 展示了无边框、透明内部的高亮显示")
        print("   ✅ 证明了集成方案的稳定性和可靠性")
        print()
        print("🎯 实际应用:")
        print("   • 自动化测试: 精确定位和验证UI控件")
        print("   • 辅助功能: 为用户提供清晰的控件指示")
        print("   • 开发调试: 快速识别和分析界面元素")
        print("   • 质量保证: 确保界面控件的正确性")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print()
        print("🔧 解决方案:")
        print("   1. 确保在正确的目录运行脚本")
        print("   2. 检查UNI_new.py文件是否存在")
        print("   3. 确认依赖模块已正确安装")
        return False
        
    except Exception as e:
        print(f"❌ 演示执行失败: {e}")
        print()
        print("🔧 请检查:")
        print("   1. 系统环境配置")
        print("   2. X11/Wayland显示服务")
        print("   3. 必要的依赖库")
        return False

def main():
    """主函数"""
    print()
    print("🎮 欢迎使用UNI控件检测与终极高亮演示程序!")
    print()
    print("📖 使用说明:")
    print("   1. 将鼠标移动到您想要检测的控件上（按钮、文本框等）")
    print("   2. 运行此脚本")
    print("   3. 观察控件检测结果和高亮效果")
    print("   4. 高亮将显示完美的无边框效果")
    print()
    
    input("按回车键开始演示...")
    print()
    
    success = demo_control_detection_with_highlight()
    
    print()
    if success:
        print("✨ 演示成功完成! 感谢您的使用!")
    else:
        print("⚠️ 演示遇到问题，请检查环境配置后重试。")
    print()

if __name__ == "__main__":
    main()