#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
KDE窗口状态检查器
使用D-Bus接口检查KDE/KWin中窗口的最小化状态
"""

import sys
import os
import subprocess
import json
import re


def run_dbus_command(service, path, interface, method, *args):
    """执行D-Bus命令"""
    cmd = ['dbus-send', '--session', '--print-reply', '--dest=' + service, path, interface + '.' + method]
    if args:
        cmd.extend(args)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"
    except Exception as e:
        return -1, "", str(e)


def get_kwin_window_list():
    """获取KWin管理的所有窗口列表"""
    print("🔍 获取KWin窗口列表...")
    
    # 尝试不同的KWin D-Bus接口
    kwin_interfaces = [
        ('org.kde.KWin', '/KWin', 'org.kde.KWin'),
        ('org.kde.kwin', '/KWin', 'org.kde.KWin'),
        ('org.kde.KWin', '/', 'org.kde.KWin'),
        ('org.kde.kwin', '/', 'org.kde.kwin')
    ]
    
    for service, path, interface in kwin_interfaces:
        print(f"   尝试接口: {service} {path} {interface}")
        
        # 尝试获取窗口列表的不同方法
        methods = ['windowList', 'getWindowList', 'clients', 'getClients']
        
        for method in methods:
            print(f"     尝试方法: {method}")
            returncode, stdout, stderr = run_dbus_command(service, path, interface, method)
            
            if returncode == 0 and stdout.strip():
                print(f"     ✅ 成功: {method}")
                return parse_dbus_output(stdout)
            else:
                print(f"     ❌ 失败: {stderr}")
    
    print("❌ 无法获取KWin窗口列表")
    return []


def parse_dbus_output(output):
    """解析D-Bus输出"""
    print(f"📋 解析D-Bus输出:")
    print(f"原始输出: {output[:200]}...")
    
    # 尝试解析不同格式的输出
    lines = output.strip().split('\n')
    window_ids = []
    
    for line in lines:
        # 查找窗口ID模式
        if 'int32' in line or 'uint32' in line:
            # 提取数字
            numbers = re.findall(r'\d+', line)
            window_ids.extend(numbers)
        elif 'string' in line:
            # 提取字符串中的窗口ID
            strings = re.findall(r'"([^"]*)"', line)
            window_ids.extend(strings)
    
    print(f"   找到 {len(window_ids)} 个窗口ID: {window_ids[:5]}...")
    return window_ids


def get_window_properties(window_id):
    """获取指定窗口的属性"""
    print(f"🔍 获取窗口 {window_id} 的属性...")
    
    # 尝试不同的属性获取方法
    properties = {}
    
    # 方法1: 使用KWin接口
    kwin_methods = [
        ('getWindowInfo', [f'int32:{window_id}']),
        ('windowInfo', [f'int32:{window_id}']),
        ('getClientInfo', [f'int32:{window_id}']),
        ('clientInfo', [f'int32:{window_id}'])
    ]
    
    for method, args in kwin_methods:
        returncode, stdout, stderr = run_dbus_command(
            'org.kde.KWin', '/KWin', 'org.kde.KWin', method, *args
        )
        
        if returncode == 0 and stdout.strip():
            print(f"   ✅ {method} 成功")
            properties.update(parse_window_properties(stdout))
            break
        else:
            print(f"   ❌ {method} 失败: {stderr}")
    
    # 方法2: 使用窗口特定的D-Bus路径
    window_path = f'/KWin/Window_{window_id}'
    window_methods = [
        'isMinimized',
        'minimized',
        'iconified',
        'isIconified',
        'isVisible',
        'visible',
        'isShown',
        'shown'
    ]
    
    for method in window_methods:
        returncode, stdout, stderr = run_dbus_command(
            'org.kde.KWin', window_path, 'org.kde.KWin.Window', method
        )
        
        if returncode == 0:
            print(f"   ✅ {method}: {stdout.strip()}")
            properties[method] = parse_boolean_output(stdout)
        else:
            print(f"   ❌ {method} 失败: {stderr}")
    
    return properties


def parse_window_properties(output):
    """解析窗口属性输出"""
    properties = {}
    
    # 解析D-Bus输出中的属性
    lines = output.strip().split('\n')
    
    for line in lines:
        if 'boolean' in line:
            if 'true' in line.lower():
                properties['boolean_value'] = True
            elif 'false' in line.lower():
                properties['boolean_value'] = False
        elif 'string' in line:
            # 提取字符串值
            strings = re.findall(r'"([^"]*)"', line)
            if strings:
                properties['string_value'] = strings[0]
    
    return properties


def parse_boolean_output(output):
    """解析布尔值输出"""
    output_lower = output.lower().strip()
    
    if 'true' in output_lower:
        return True
    elif 'false' in output_lower:
        return False
    else:
        return None


def check_window_by_title(target_title):
    """根据窗口标题检查窗口状态"""
    print(f"🎯 查找标题包含 '{target_title}' 的窗口...")
    
    # 使用wmctrl获取窗口信息（如果可用）
    try:
        result = subprocess.run(['wmctrl', '-l'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 使用wmctrl获取窗口列表")
            
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if target_title.lower() in line.lower():
                    parts = line.split(None, 3)
                    if len(parts) >= 4:
                        window_id = parts[0]
                        window_title = parts[3]
                        
                        print(f"   找到匹配窗口: {window_id} - {window_title}")
                        
                        # 检查窗口状态
                        properties = get_window_properties(window_id)
                        
                        print(f"   窗口属性: {properties}")
                        
                        # 分析状态
                        is_minimized = analyze_window_state(properties)
                        
                        return {
                            'window_id': window_id,
                            'title': window_title,
                            'properties': properties,
                            'is_minimized': is_minimized
                        }
            
            print(f"❌ 未找到标题包含 '{target_title}' 的窗口")
        else:
            print(f"❌ wmctrl失败: {result.stderr}")
    
    except Exception as e:
        print(f"❌ wmctrl检查失败: {e}")
    
    return None


def analyze_window_state(properties):
    """分析窗口状态"""
    print(f"📊 分析窗口状态: {properties}")
    
    # 检查明确的最小化状态
    minimized_indicators = [
        'isMinimized',
        'minimized', 
        'iconified',
        'isIconified'
    ]
    
    for indicator in minimized_indicators:
        if indicator in properties:
            if properties[indicator] is True:
                print(f"   ✅ 窗口已最小化 ({indicator} = True)")
                return True
            elif properties[indicator] is False:
                print(f"   ❌ 窗口未最小化 ({indicator} = False)")
                return False
    
    # 检查可见性状态
    visibility_indicators = [
        'isVisible',
        'visible',
        'isShown', 
        'shown'
    ]
    
    visible_count = 0
    invisible_count = 0
    
    for indicator in visibility_indicators:
        if indicator in properties:
            if properties[indicator] is True:
                visible_count += 1
            elif properties[indicator] is False:
                invisible_count += 1
    
    if invisible_count > visible_count:
        print(f"   ⚠️  窗口可能不可见 (不可见指标: {invisible_count}, 可见指标: {visible_count})")
        return True
    elif visible_count > 0:
        print(f"   ✅ 窗口可能可见 (可见指标: {visible_count}, 不可见指标: {invisible_count})")
        return False
    
    print(f"   ❓ 无法确定窗口状态")
    return None


def check_kde_environment():
    """检查是否在KDE环境中"""
    print("🔍 检查KDE环境...")
    
    # 检查环境变量
    desktop_session = os.getenv('DESKTOP_SESSION', '').lower()
    kde_session = os.getenv('KDE_SESSION_VERSION', '')
    xdg_desktop = os.getenv('XDG_CURRENT_DESKTOP', '').lower()
    
    print(f"   DESKTOP_SESSION: {desktop_session}")
    print(f"   KDE_SESSION_VERSION: {kde_session}")
    print(f"   XDG_CURRENT_DESKTOP: {xdg_desktop}")
    
    is_kde = ('kde' in desktop_session or 
              kde_session or 
              'kde' in xdg_desktop)
    
    if is_kde:
        print("✅ 检测到KDE环境")
    else:
        print("⚠️  未检测到KDE环境，但仍可尝试KWin D-Bus接口")
    
    # 检查KWin是否运行
    try:
        result = subprocess.run(['pgrep', 'kwin'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ KWin正在运行")
        else:
            print("❌ KWin未运行")
    except Exception:
        print("⚠️  无法检查KWin状态")
    
    return is_kde


def main():
    """主函数"""
    print("🔍 KDE窗口状态检查器")
    print("=" * 60)
    print("🎯 使用D-Bus检查窗口最小化状态")
    print("=" * 60)
    
    # 检查KDE环境
    check_kde_environment()
    
    # 检查hellobig窗口
    target_title = "AT-SPI测试界面"
    
    result = check_window_by_title(target_title)
    
    if result:
        print(f"\n📋 检查结果:")
        print("=" * 40)
        print(f"窗口ID: {result['window_id']}")
        print(f"窗口标题: {result['title']}")
        print(f"是否最小化: {result['is_minimized']}")
        print(f"详细属性: {result['properties']}")
        
        if result['is_minimized'] is True:
            print(f"\n🔴 确认: 窗口已最小化")
            print(f"   这解释了为什么AT-SPI状态不准确")
        elif result['is_minimized'] is False:
            print(f"\n🟢 确认: 窗口未最小化")
            print(f"   窗口应该是可见的")
        else:
            print(f"\n❓ 无法确定窗口状态")
            print(f"   需要其他方法验证")
    else:
        print(f"\n❌ 未找到目标窗口")
        print(f"   可能窗口标题不匹配或窗口不存在")
    
    print(f"\n💡 D-Bus方法总结:")
    print("=" * 30)
    print("如果D-Bus方法能准确检测最小化状态，")
    print("我们可以将其集成到通用检测器中，")
    print("替代不可靠的AT-SPI状态检查。")


if __name__ == "__main__":
    main()
