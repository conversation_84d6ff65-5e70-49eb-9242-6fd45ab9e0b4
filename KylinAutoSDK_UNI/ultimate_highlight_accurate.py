#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
精确高亮模块
集成坐标修正功能，确保高亮位置准确
"""

import sys
import os
import time

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from coordinate_fix import get_accurate_highlight_coords, smart_coordinate_correction
except ImportError:
    # 如果导入失败，使用简单的坐标修正
    def smart_coordinate_correction(mouse_x, mouse_y, atspi_coords, window_info, target_app_info=None):
        if not atspi_coords:
            return atspi_coords
        corrected = atspi_coords.copy()
        corrected['y'] = atspi_coords.get('y', 0) - 40  # 简单的Y轴修正
        return corrected
    
    def get_accurate_highlight_coords(mouse_x, mouse_y, control_data, window_info, target_app_info=None):
        coords = control_data.get('Coords', {}) if control_data else {}
        return smart_coordinate_correction(mouse_x, mouse_y, coords, window_info, target_app_info)

def ultimate_highlight_accurate(x=None, y=None, width=None, height=None, 
                               mouse_x=None, mouse_y=None, 
                               control_data=None, window_info=None, target_app_info=None,
                               duration=3, color='red', border_width=3):
    """
    精确高亮显示函数
    
    Args:
        x, y, width, height: 直接指定坐标和大小
        mouse_x, mouse_y: 鼠标位置（用于坐标修正参考）
        control_data: 控件数据（包含AT-SPI坐标）
        window_info: 窗口信息
        target_app_info: 目标应用信息
        duration: 持续时间
        color: 颜色
        border_width: 边框宽度
    
    Returns:
        bool: 是否成功显示高亮
    """
    
    # 确定最终的高亮坐标
    final_x, final_y, final_width, final_height = None, None, None, None
    
    if x is not None and y is not None and width is not None and height is not None:
        # 直接使用提供的坐标
        final_x, final_y, final_width, final_height = x, y, width, height
        print(f"🎯 使用直接坐标: ({final_x}, {final_y}) {final_width}×{final_height}")
        
    elif control_data and mouse_x is not None and mouse_y is not None:
        # 使用智能坐标修正
        print(f"🧠 使用智能坐标修正...")
        
        corrected_coords = get_accurate_highlight_coords(
            mouse_x, mouse_y, control_data, window_info, target_app_info
        )
        
        if corrected_coords:
            final_x = corrected_coords.get('x', mouse_x)
            final_y = corrected_coords.get('y', mouse_y)
            final_width = corrected_coords.get('width', 100)
            final_height = corrected_coords.get('height', 30)
            
            print(f"🎯 修正后坐标: ({final_x}, {final_y}) {final_width}×{final_height}")
        else:
            # 回退到鼠标位置
            final_x, final_y = mouse_x - 25, mouse_y - 12
            final_width, final_height = 50, 25
            print(f"🎯 回退到鼠标位置: ({final_x}, {final_y}) {final_width}×{final_height}")
    
    elif control_data:
        # 只有控件数据，使用原始AT-SPI坐标
        coords = control_data.get('Coords', {})
        if coords:
            final_x = coords.get('x', 0)
            final_y = coords.get('y', 0) - 40  # 简单Y轴修正
            final_width = coords.get('width', 100)
            final_height = coords.get('height', 30)
            print(f"🎯 使用AT-SPI坐标(Y轴修正): ({final_x}, {final_y}) {final_width}×{final_height}")
    
    # 如果仍然没有有效坐标，返回失败
    if final_x is None or final_y is None:
        print(f"❌ 无法确定高亮坐标")
        return False
    
    # 调用实际的高亮显示函数
    try:
        # 尝试导入高亮函数
        try:
            from ultimate_highlight import ultimate_highlight
        except ImportError:
            try:
                from ultimate_highlight_corrected import ultimate_highlight_corrected as ultimate_highlight
            except ImportError:
                print(f"❌ 无法导入高亮函数")
                return False
        
        # 执行高亮显示
        print(f"🎨 执行高亮显示: 位置({final_x}, {final_y}) 大小{final_width}×{final_height} 颜色{color}")
        
        success = ultimate_highlight(
            x=final_x,
            y=final_y,
            width=final_width,
            height=final_height,
            duration=duration,
            color=color,
            border_width=border_width
        )
        
        if success:
            print(f"✅ 精确高亮显示成功")
        else:
            print(f"❌ 高亮显示失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 高亮显示异常: {e}")
        return False

def highlight_found_control_accurate(control_data, mouse_x=None, mouse_y=None, 
                                   window_info=None, target_app_info=None,
                                   duration=3, color='red', thickness=3):
    """
    精确高亮找到的控件
    """
    return ultimate_highlight_accurate(
        control_data=control_data,
        mouse_x=mouse_x,
        mouse_y=mouse_y,
        window_info=window_info,
        target_app_info=target_app_info,
        duration=duration,
        color=color,
        border_width=thickness
    )

# 便捷的调用函数
def highlight_at_mouse(mouse_x, mouse_y, width=50, height=25, **kwargs):
    """在鼠标位置显示高亮"""
    return ultimate_highlight_accurate(
        x=mouse_x-width//2, 
        y=mouse_y-height//2, 
        width=width, 
        height=height, 
        **kwargs
    )

def highlight_with_smart_correction(control_data, mouse_x, mouse_y, **kwargs):
    """使用智能修正的高亮"""
    return ultimate_highlight_accurate(
        control_data=control_data,
        mouse_x=mouse_x,
        mouse_y=mouse_y,
        **kwargs
    )