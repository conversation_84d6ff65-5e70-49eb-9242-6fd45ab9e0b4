#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
测试wlcctrl最小化方法
测试对已经最小化的窗口执行最小化操作会发生什么
"""

import sys
import os
import subprocess
import re
import time


def get_hellobig_windows():
    """获取hellobig相关窗口"""
    print("🔍 获取hellobig相关窗口...")
    
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ wlcctrl --list 失败")
            return []
        
        windows = []
        lines = result.stdout.strip().split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                # 检查是否是hellobig相关窗口
                if ('AT-SPI测试' in window_title or 'hellobig' in window_title.lower() or 
                    'Qt控件集合' in window_title):
                    
                    # 获取几何信息
                    geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                              capture_output=True, text=True)
                    
                    window_info = {
                        'id': window_id,
                        'title': window_title,
                        'geometry_raw': geo_result.stdout if geo_result.returncode == 0 else None
                    }
                    
                    if geo_result.returncode == 0:
                        # 解析几何信息
                        for geo_line in geo_result.stdout.strip().split('\n'):
                            if 'geometry:' in geo_line:
                                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                                if match:
                                    x, y, width, height = map(int, match.groups())
                                    window_info.update({
                                        'x': x, 'y': y, 'width': width, 'height': height,
                                        'position': (x, y), 'size': (width, height)
                                    })
                    
                    windows.append(window_info)
                
                i += 2
            else:
                i += 1
        
        print(f"   找到 {len(windows)} 个hellobig相关窗口")
        for window in windows:
            print(f"     - {window['id']}: {window['title']}")
            if 'size' in window:
                print(f"       位置: {window['position']} 尺寸: {window['size']}")
        
        return windows
        
    except Exception as e:
        print(f"❌ 获取窗口失败: {e}")
        return []


def analyze_window_state(window):
    """分析窗口状态"""
    print(f"\n📊 分析窗口状态: {window['title']}")
    
    if 'size' not in window:
        print("   ❌ 无几何信息")
        return "unknown"
    
    width, height = window['size']
    x, y = window['position']
    
    print(f"   位置: ({x}, {y})")
    print(f"   尺寸: {width}×{height}")
    
    # 判断是否最小化
    if width >= 2500 or height >= 1500:
        print("   🔴 判断: 已最小化（异常大尺寸）")
        return "minimized"
    elif x == 0 and y == 0 and (width > 1500 or height > 1000):
        print("   🔴 判断: 已最小化（原点大尺寸）")
        return "minimized"
    else:
        print("   🟢 判断: 正常显示")
        return "normal"


def test_minimize_on_window(window):
    """测试对窗口执行最小化操作"""
    print(f"\n🧪 测试对窗口执行最小化操作...")
    print(f"   窗口: {window['title']}")
    print(f"   ID: {window['id']}")
    
    # 记录操作前的状态
    before_state = analyze_window_state(window)
    print(f"   操作前状态: {before_state}")
    
    # 执行最小化操作
    print(f"\n⚡ 执行最小化操作...")
    try:
        result = subprocess.run(['wlcctrl', '--windowminimize', window['id']], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"   ✅ 最小化命令执行成功")
            print(f"   输出: {result.stdout.strip()}")
        else:
            print(f"   ❌ 最小化命令失败")
            print(f"   错误: {result.stderr.strip()}")
            return False
    
    except Exception as e:
        print(f"   ❌ 执行最小化失败: {e}")
        return False
    
    # 等待一下让操作生效
    print(f"   ⏳ 等待操作生效...")
    time.sleep(2)
    
    # 重新获取窗口状态
    print(f"\n🔍 重新获取窗口状态...")
    updated_windows = get_hellobig_windows()
    
    # 找到对应的窗口
    updated_window = None
    for w in updated_windows:
        if w['id'] == window['id']:
            updated_window = w
            break
    
    if not updated_window:
        print(f"   ❌ 窗口消失了！可能被关闭或隐藏")
        return False
    
    # 分析操作后的状态
    after_state = analyze_window_state(updated_window)
    print(f"   操作后状态: {after_state}")
    
    # 比较前后状态
    print(f"\n📊 状态变化分析:")
    print(f"   操作前: {before_state}")
    print(f"   操作后: {after_state}")
    
    if before_state == "minimized" and after_state == "normal":
        print(f"   🔄 结果: 最小化窗口被恢复了！")
        return "restored"
    elif before_state == "normal" and after_state == "minimized":
        print(f"   🔽 结果: 正常窗口被最小化了")
        return "minimized"
    elif before_state == after_state:
        print(f"   ➡️  结果: 状态没有变化")
        return "no_change"
    else:
        print(f"   ❓ 结果: 状态变化不明确")
        return "unclear"


def test_minimize_behavior():
    """测试最小化行为"""
    print("🧪 测试wlcctrl最小化行为")
    print("=" * 60)
    print("🎯 测试对已最小化窗口执行最小化操作的效果")
    print("=" * 60)
    
    # 获取hellobig窗口
    windows = get_hellobig_windows()
    
    if not windows:
        print("❌ 未找到hellobig窗口")
        return
    
    # 测试每个窗口
    results = []
    
    for i, window in enumerate(windows):
        print(f"\n{'='*20} 测试窗口 {i+1} {'='*20}")
        
        result = test_minimize_on_window(window)
        results.append({
            'window': window,
            'result': result
        })
        
        # 如果不是最后一个窗口，等待一下
        if i < len(windows) - 1:
            print(f"\n⏳ 等待5秒后测试下一个窗口...")
            time.sleep(5)
    
    # 总结结果
    print(f"\n📊 测试总结:")
    print("=" * 40)
    
    for i, result_info in enumerate(results):
        window = result_info['window']
        result = result_info['result']
        
        print(f"窗口 {i+1}: {window['title']}")
        print(f"   结果: {result}")
        
        if result == "restored":
            print(f"   💡 发现: 对已最小化窗口执行最小化会恢复窗口！")
        elif result == "no_change":
            print(f"   💡 发现: 对已最小化窗口执行最小化无效果")
        elif result == "minimized":
            print(f"   💡 发现: 正常窗口被成功最小化")
    
    print(f"\n🔧 实用价值:")
    print("=" * 30)
    print("如果对已最小化窗口执行最小化会恢复窗口，")
    print("我们可以用这个方法来测试窗口是否真的最小化：")
    print("1. 执行最小化操作")
    print("2. 检查窗口状态是否改变")
    print("3. 如果状态改变，说明窗口之前是最小化的")
    print("4. 再次执行最小化恢复原状态")


def main():
    """主函数"""
    print("🧪 wlcctrl最小化方法测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        test_minimize_behavior()
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
