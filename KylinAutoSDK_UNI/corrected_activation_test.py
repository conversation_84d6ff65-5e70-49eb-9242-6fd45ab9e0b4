#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
修正的激活测试
正确处理窗口ID格式，准确检测窗口可见性
"""

import sys
import os
import subprocess
import time


def extract_window_id(window_id_string):
    """提取窗口ID，处理可能的前缀"""
    if not window_id_string:
        return ""
    
    # 移除可能的前缀
    cleaned = window_id_string.strip()
    
    if cleaned.startswith('uuid : '):
        return cleaned[7:]  # 移除 "uuid : " 前缀
    elif cleaned.startswith('uuid:'):
        return cleaned[5:]  # 移除 "uuid:" 前缀
    else:
        return cleaned


def test_window_activation(window_id, window_title):
    """测试窗口激活，正确处理窗口ID格式"""
    print(f"🧪 测试窗口激活: {window_title}")
    print(f"   窗口ID: {window_id}")
    
    # 1. 尝试激活窗口
    print(f"   ⚡ 执行激活命令...")
    try:
        result = subprocess.run(['wlcctrl', '--windowactivate', window_id], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"   ❌ 激活命令失败: {result.stderr.strip()}")
            return False
        
        print(f"   ✅ 激活命令成功")
        
    except Exception as e:
        print(f"   ❌ 激活命令异常: {e}")
        return False
    
    # 2. 等待一下让操作生效
    print(f"   ⏳ 等待激活生效...")
    time.sleep(1)
    
    # 3. 检查当前活动窗口
    print(f"   🔍 检查当前活动窗口...")
    try:
        active_result = subprocess.run(['wlcctrl', '--getactivewindow'], 
                                     capture_output=True, text=True)
        
        if active_result.returncode != 0:
            print(f"   ❌ 无法获取活动窗口: {active_result.stderr.strip()}")
            return None
        
        current_active_raw = active_result.stdout.strip()
        current_active = extract_window_id(current_active_raw)
        
        print(f"   当前活动窗口 (原始): {current_active_raw}")
        print(f"   当前活动窗口 (清理): {current_active}")
        print(f"   目标窗口ID: {window_id}")
        
        # 4. 比较窗口ID
        if current_active == window_id:
            print(f"   ✅ 窗口成功激活 → 窗口可见")
            return True
        else:
            print(f"   ❌ 窗口未成为活动窗口 → 窗口可能已最小化")
            return False
    
    except Exception as e:
        print(f"   ❌ 检查活动窗口异常: {e}")
        return None


def test_all_hellobig_windows():
    """测试所有hellobig窗口的激活状态"""
    print("🔍 测试所有hellobig窗口的激活状态")
    print("=" * 60)
    
    # 获取hellobig窗口
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 无法获取窗口列表")
            return
        
        hellobig_windows = []
        lines = result.stdout.strip().split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                if ('AT-SPI测试' in window_title or 'hellobig' in window_title.lower()):
                    hellobig_windows.append((window_id, window_title))
                
                i += 2
            else:
                i += 1
        
        if not hellobig_windows:
            print("❌ 未找到hellobig窗口")
            return
        
        print(f"找到 {len(hellobig_windows)} 个hellobig窗口:")
        for window_id, window_title in hellobig_windows:
            print(f"   - {window_id}: {window_title}")
        
        # 测试每个窗口
        results = []
        
        for i, (window_id, window_title) in enumerate(hellobig_windows):
            print(f"\n{'='*20} 测试窗口 {i+1} {'='*20}")
            
            result = test_window_activation(window_id, window_title)
            results.append({
                'window_id': window_id,
                'title': window_title,
                'is_visible': result
            })
            
            # 如果不是最后一个窗口，等待一下
            if i < len(hellobig_windows) - 1:
                print(f"\n⏳ 等待3秒后测试下一个窗口...")
                time.sleep(3)
        
        # 总结结果
        print(f"\n📊 测试总结:")
        print("=" * 40)
        
        for i, result_info in enumerate(results):
            window_title = result_info['title']
            is_visible = result_info['is_visible']
            
            print(f"窗口 {i+1}: {window_title}")
            
            if is_visible is True:
                print(f"   ✅ 可见 - 窗口成功激活")
            elif is_visible is False:
                print(f"   ❌ 不可见 - 窗口无法激活")
            else:
                print(f"   ❓ 不确定 - 检测失败")
        
        # 分析结果
        visible_count = sum(1 for r in results if r['is_visible'] is True)
        hidden_count = sum(1 for r in results if r['is_visible'] is False)
        
        print(f"\n💡 结论:")
        print("=" * 20)
        print(f"可见窗口: {visible_count}")
        print(f"隐藏窗口: {hidden_count}")
        
        if visible_count > 0:
            print(f"✅ 有可见的hellobig窗口，可以进行控件检测")
        else:
            print(f"❌ 所有hellobig窗口都已隐藏，应拒绝控件检测")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def main():
    """主函数"""
    print("🔧 修正的激活测试")
    print("=" * 60)
    print("🎯 正确处理窗口ID格式，准确检测窗口可见性")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        results = test_all_hellobig_windows()
        
        if results:
            print(f"\n🔧 集成建议:")
            print("=" * 30)
            print("这个修正的激活测试方法可以准确检测窗口可见性，")
            print("应该集成到universal_offset_detector.py中，")
            print("替代不可靠的AT-SPI状态检查。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
