#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
综合可见性检测器
结合多种方法来检测窗口的真实可见性状态
"""

import sys
import os
import subprocess
import re
import time


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def check_window_geometry_sanity(window_info):
    """检查窗口几何信息的合理性"""
    print(f"   📐 检查几何信息合理性...")
    
    if 'size' not in window_info:
        print(f"      ❌ 无几何信息")
        return False, "no_geometry"
    
    width, height = window_info['size']
    x, y = window_info['position']
    
    print(f"      位置: ({x}, {y}) 尺寸: {width}×{height}")
    
    # 检查异常大尺寸（明显的最小化标志）
    if width >= 2500 or height >= 1500:
        print(f"      ❌ 异常大尺寸，明显已最小化")
        return False, "oversized"
    
    # 检查在原点且很大
    if x == 0 and y == 0 and (width > 1500 or height > 1000):
        print(f"      ❌ 在原点且尺寸大，可能已最小化")
        return False, "origin_large"
    
    # 检查负坐标或超出合理范围
    if x < -width or y < -height or x > 5000 or y > 3000:
        print(f"      ❌ 坐标超出合理范围")
        return False, "out_of_bounds"
    
    print(f"      ✅ 几何信息合理")
    return True, "reasonable"


def check_mouse_window_intersection(window_info, mouse_x, mouse_y):
    """检查鼠标是否在窗口范围内"""
    print(f"   🖱️  检查鼠标窗口交集...")
    
    if not mouse_x or not mouse_y:
        print(f"      ⚠️  无法获取鼠标位置")
        return None, "no_mouse"
    
    if 'size' not in window_info:
        print(f"      ❌ 无窗口几何信息")
        return False, "no_geometry"
    
    x, y = window_info['position']
    width, height = window_info['size']
    
    print(f"      鼠标位置: ({mouse_x}, {mouse_y})")
    print(f"      窗口范围: ({x}, {y}) 到 ({x + width}, {y + height})")
    
    if (x <= mouse_x < x + width and y <= mouse_y < y + height):
        print(f"      ✅ 鼠标在窗口范围内")
        return True, "mouse_inside"
    else:
        print(f"      ❌ 鼠标不在窗口范围内")
        return False, "mouse_outside"


def check_window_in_current_workspace(window_id):
    """检查窗口是否在当前工作区"""
    print(f"   🏢 检查工作区...")
    
    try:
        # 获取当前工作区
        current_ws_result = subprocess.run(['wlcctrl', '--getcurrentworkspace'], 
                                         capture_output=True, text=True)
        if current_ws_result.returncode != 0:
            print(f"      ⚠️  无法获取当前工作区")
            return None, "no_current_workspace"
        
        current_workspace = current_ws_result.stdout.strip()
        print(f"      当前工作区: {current_workspace}")
        
        # 获取窗口工作区
        window_ws_result = subprocess.run(['wlcctrl', '--getwindowworkspace', window_id], 
                                        capture_output=True, text=True)
        if window_ws_result.returncode != 0:
            print(f"      ⚠️  无法获取窗口工作区")
            return None, "no_window_workspace"
        
        window_workspace = window_ws_result.stdout.strip()
        print(f"      窗口工作区: {window_workspace}")
        
        if window_workspace == current_workspace:
            print(f"      ✅ 窗口在当前工作区")
            return True, "same_workspace"
        else:
            print(f"      ❌ 窗口不在当前工作区")
            return False, "different_workspace"
    
    except Exception as e:
        print(f"      ❌ 工作区检查失败: {e}")
        return None, "workspace_error"


def check_window_process_status(window_id):
    """检查窗口进程状态"""
    print(f"   🔄 检查进程状态...")
    
    try:
        # 获取窗口PID
        pid_result = subprocess.run(['wlcctrl', '--getwindowpid', window_id], 
                                  capture_output=True, text=True)
        if pid_result.returncode != 0:
            print(f"      ⚠️  无法获取窗口PID")
            return None, "no_pid"
        
        pid = pid_result.stdout.strip()
        print(f"      窗口PID: {pid}")
        
        # 检查进程是否存在
        ps_result = subprocess.run(['ps', '-p', pid, '-o', 'pid,stat,cmd'], 
                                 capture_output=True, text=True)
        if ps_result.returncode != 0:
            print(f"      ❌ 进程不存在")
            return False, "process_dead"
        
        ps_lines = ps_result.stdout.strip().split('\n')
        if len(ps_lines) >= 2:
            process_info = ps_lines[1].strip()
            print(f"      进程信息: {process_info}")
            
            # 检查进程状态
            parts = process_info.split()
            if len(parts) >= 2:
                stat = parts[1]
                print(f"      进程状态: {stat}")
                
                # 检查是否是僵尸进程或停止进程
                if 'Z' in stat:
                    print(f"      ❌ 僵尸进程")
                    return False, "zombie_process"
                elif 'T' in stat:
                    print(f"      ⚠️  停止进程")
                    return False, "stopped_process"
                else:
                    print(f"      ✅ 进程状态正常")
                    return True, "process_ok"
        
        print(f"      ✅ 进程存在")
        return True, "process_exists"
    
    except Exception as e:
        print(f"      ❌ 进程检查失败: {e}")
        return None, "process_error"


def comprehensive_visibility_check(window_id, window_title, mouse_x=None, mouse_y=None):
    """综合可见性检查"""
    print(f"🔍 综合可见性检查: {window_title}")
    print(f"   窗口ID: {window_id}")
    
    # 获取窗口几何信息
    window_info = {'id': window_id, 'title': window_title}
    
    try:
        geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                  capture_output=True, text=True)
        if geo_result.returncode == 0:
            for geo_line in geo_result.stdout.strip().split('\n'):
                if 'geometry:' in geo_line:
                    match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                    if match:
                        x, y, width, height = map(int, match.groups())
                        window_info.update({
                            'position': (x, y), 'size': (width, height)
                        })
                        break
    except Exception as e:
        print(f"   ❌ 获取几何信息失败: {e}")
    
    # 执行各种检查
    checks = {}
    
    # 检查1: 几何信息合理性
    geometry_ok, geometry_reason = check_window_geometry_sanity(window_info)
    checks['geometry'] = (geometry_ok, geometry_reason)
    
    # 检查2: 鼠标窗口交集（如果提供了鼠标位置）
    if mouse_x and mouse_y:
        mouse_ok, mouse_reason = check_mouse_window_intersection(window_info, mouse_x, mouse_y)
        checks['mouse'] = (mouse_ok, mouse_reason)
    
    # 检查3: 工作区检查
    workspace_ok, workspace_reason = check_window_in_current_workspace(window_id)
    checks['workspace'] = (workspace_ok, workspace_reason)
    
    # 检查4: 进程状态检查
    process_ok, process_reason = check_window_process_status(window_id)
    checks['process'] = (process_ok, process_reason)
    
    # 综合分析
    print(f"\n📊 检查结果汇总:")
    print(f"   几何信息: {'✅' if geometry_ok else '❌'} ({geometry_reason})")
    if 'mouse' in checks:
        mouse_ok, mouse_reason = checks['mouse']
        print(f"   鼠标交集: {'✅' if mouse_ok else '❌' if mouse_ok is False else '❓'} ({mouse_reason})")
    print(f"   工作区: {'✅' if workspace_ok else '❌' if workspace_ok is False else '❓'} ({workspace_reason})")
    print(f"   进程状态: {'✅' if process_ok else '❌' if process_ok is False else '❓'} ({process_reason})")
    
    # 计算可见性得分
    positive_checks = sum(1 for result, _ in checks.values() if result is True)
    negative_checks = sum(1 for result, _ in checks.values() if result is False)
    uncertain_checks = sum(1 for result, _ in checks.values() if result is None)
    
    print(f"\n📈 可见性评分:")
    print(f"   正面指标: {positive_checks}")
    print(f"   负面指标: {negative_checks}")
    print(f"   不确定指标: {uncertain_checks}")
    
    # 综合判断
    if negative_checks > 0:
        if geometry_ok is False and geometry_reason in ['oversized', 'origin_large']:
            print(f"   🔴 结论: 窗口不可见 (明显的最小化标志)")
            return False, "clearly_minimized"
        else:
            print(f"   🟡 结论: 窗口可能不可见 (有负面指标)")
            return False, "probably_hidden"
    elif positive_checks >= 2:
        print(f"   🟢 结论: 窗口可见 (多个正面指标)")
        return True, "probably_visible"
    elif positive_checks >= 1 and uncertain_checks > 0:
        print(f"   🟡 结论: 窗口可能可见 (有正面指标但不确定)")
        return True, "maybe_visible"
    else:
        print(f"   ❓ 结论: 无法确定窗口状态")
        return None, "uncertain"


def main():
    """主函数"""
    print("🔍 综合可见性检测器")
    print("=" * 60)
    print("🎯 结合多种方法检测窗口真实可见性")
    print("💡 适用于Wayland环境的复杂情况")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    # 获取鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x and mouse_y:
        print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
    else:
        print(f"⚠️  无法获取鼠标位置")
    
    try:
        # 获取hellobig窗口
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 无法获取窗口列表")
            return
        
        hellobig_windows = []
        lines = result.stdout.strip().split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                if ('AT-SPI测试' in window_title or 'hellobig' in window_title.lower()):
                    hellobig_windows.append((window_id, window_title))
                
                i += 2
            else:
                i += 1
        
        if not hellobig_windows:
            print("❌ 未找到hellobig窗口")
            return
        
        print(f"\n找到 {len(hellobig_windows)} 个hellobig窗口")
        
        # 检查每个窗口
        results = []
        
        for i, (window_id, window_title) in enumerate(hellobig_windows):
            print(f"\n{'='*50}")
            print(f"检查窗口 {i+1}: {window_title}")
            print(f"{'='*50}")
            
            is_visible, reason = comprehensive_visibility_check(window_id, window_title, mouse_x, mouse_y)
            results.append({
                'window_id': window_id,
                'title': window_title,
                'is_visible': is_visible,
                'reason': reason
            })
        
        # 最终总结
        print(f"\n🎯 最终总结:")
        print("=" * 40)
        
        for i, result in enumerate(results):
            title = result['title']
            is_visible = result['is_visible']
            reason = result['reason']
            
            print(f"窗口 {i+1}: {title}")
            if is_visible is True:
                print(f"   ✅ 可见 ({reason})")
            elif is_visible is False:
                print(f"   ❌ 不可见 ({reason})")
            else:
                print(f"   ❓ 不确定 ({reason})")
        
        # 给出建议
        visible_windows = [r for r in results if r['is_visible'] is True]
        
        if visible_windows:
            print(f"\n💡 建议:")
            print(f"✅ 有 {len(visible_windows)} 个可见窗口，可以进行控件检测")
        else:
            print(f"\n💡 建议:")
            print(f"❌ 没有明确可见的窗口，建议拒绝控件检测")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
