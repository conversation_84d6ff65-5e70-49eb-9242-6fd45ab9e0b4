#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
调试窗口可见性检测
详细分析hellobig窗口的状态信息
"""

import sys
import os
import subprocess
import pyatspi
import re


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def debug_hellobig_window_visibility():
    """调试hellobig窗口的可见性状态"""
    print("🔍 调试hellobig窗口可见性")
    print("=" * 60)
    
    mouse_x, mouse_y = get_mouse_position()
    print(f"🖱️  当前鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 1. 检查wlcctrl报告的窗口信息
    print(f"\n📋 1. wlcctrl窗口信息:")
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            
            i = 0
            while i < len(lines):
                line = lines[i]
                if 'toplevel' in line:
                    window_id = line.split('"')[1]
                    
                    window_title = ""
                    if i + 1 < len(lines):
                        title_line = lines[i + 1].strip()
                        if title_line.startswith('title: '):
                            window_title = title_line[7:]
                        else:
                            window_title = title_line
                    
                    if 'hellobig' in window_title.lower() or 'AT-SPI测试界面' in window_title:
                        print(f"   找到hellobig窗口:")
                        print(f"     ID: {window_id}")
                        print(f"     标题: {window_title}")
                        
                        # 获取详细几何信息
                        geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                                  capture_output=True, text=True)
                        if geo_result.returncode == 0:
                            print(f"     几何信息:")
                            for geo_line in geo_result.stdout.strip().split('\n'):
                                print(f"       {geo_line}")
                        
                        break
                    
                    i += 2
                else:
                    i += 1
    except Exception as e:
        print(f"   ❌ wlcctrl检查失败: {e}")
    
    # 2. 检查AT-SPI窗口状态
    print(f"\n🔍 2. AT-SPI窗口状态:")
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            if 'hellobig' in app_name.lower():
                print(f"   找到hellobig应用: {app_name}")
                
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        window_name = window.name or "Unknown"
                        
                        if 'AT-SPI测试' in window_name or '测试主窗口' in window_name:
                            print(f"   找到窗口: {window_name}")
                            
                            # 获取窗口状态
                            try:
                                if hasattr(window, 'getState'):
                                    state = window.getState()
                                    print(f"     窗口状态:")
                                    
                                    # 检查所有可能的状态
                                    states_to_check = [
                                        (pyatspi.STATE_VISIBLE, "VISIBLE"),
                                        (pyatspi.STATE_SHOWING, "SHOWING"),
                                        (pyatspi.STATE_ICONIFIED, "ICONIFIED"),
                                        (pyatspi.STATE_MINIMIZED, "MINIMIZED"),
                                        (pyatspi.STATE_ACTIVE, "ACTIVE"),
                                        (pyatspi.STATE_ENABLED, "ENABLED"),
                                        (pyatspi.STATE_SENSITIVE, "SENSITIVE"),
                                        (pyatspi.STATE_FOCUSABLE, "FOCUSABLE"),
                                        (pyatspi.STATE_FOCUSED, "FOCUSED"),
                                        (pyatspi.STATE_MODAL, "MODAL"),
                                        (pyatspi.STATE_RESIZABLE, "RESIZABLE")
                                    ]
                                    
                                    for state_const, state_name in states_to_check:
                                        if state.contains(state_const):
                                            print(f"       ✅ {state_name}")
                                        else:
                                            print(f"       ❌ {state_name}")
                                
                                # 获取窗口几何信息
                                if hasattr(window, 'queryComponent'):
                                    component = window.queryComponent()
                                    if component:
                                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                        print(f"     AT-SPI几何信息:")
                                        print(f"       位置: ({extents.x}, {extents.y})")
                                        print(f"       尺寸: {extents.width}×{extents.height}")
                                        
                                        # 检查鼠标是否在AT-SPI窗口范围内
                                        if (extents.x <= mouse_x < extents.x + extents.width and
                                            extents.y <= mouse_y < extents.y + extents.height):
                                            print(f"       🖱️  鼠标在AT-SPI窗口范围内")
                                        else:
                                            print(f"       🖱️  鼠标不在AT-SPI窗口范围内")
                                
                            except Exception as e:
                                print(f"     ❌ 获取窗口状态失败: {e}")
                            
                            break
                    except Exception:
                        continue
                break
    except Exception as e:
        print(f"   ❌ AT-SPI检查失败: {e}")
    
    # 3. 尝试其他可见性检测方法
    print(f"\n🔬 3. 其他可见性检测方法:")
    
    # 方法1: 检查窗口是否在活动工作区
    try:
        result = subprocess.run(['wmctrl', '-l'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   wmctrl窗口列表:")
            for line in result.stdout.strip().split('\n'):
                if 'hellobig' in line.lower() or 'AT-SPI测试' in line:
                    print(f"     {line}")
        else:
            print(f"   ❌ wmctrl不可用")
    except Exception:
        print(f"   ❌ wmctrl不可用")
    
    # 方法2: 检查窗口是否在屏幕截图中可见（概念性）
    print(f"   💡 建议的额外检测方法:")
    print(f"     - 检查窗口是否在当前工作区")
    print(f"     - 检查窗口是否被其他窗口完全遮挡")
    print(f"     - 检查窗口管理器的窗口状态")
    print(f"     - 尝试与窗口交互（如获取焦点）")


def test_practical_visibility_check():
    """实用的可见性检查测试"""
    print(f"\n🧪 4. 实用可见性检查测试:")
    
    mouse_x, mouse_y = get_mouse_position()
    
    # 获取鼠标位置下实际可见的窗口
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   检查鼠标位置 ({mouse_x}, {mouse_y}) 下的实际可见窗口:")
            
            lines = result.stdout.strip().split('\n')
            visible_windows = []
            
            i = 0
            while i < len(lines):
                line = lines[i]
                if 'toplevel' in line:
                    window_id = line.split('"')[1]
                    
                    window_title = ""
                    if i + 1 < len(lines):
                        title_line = lines[i + 1].strip()
                        if title_line.startswith('title: '):
                            window_title = title_line[7:]
                        else:
                            window_title = title_line
                    
                    # 获取几何信息
                    geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                              capture_output=True, text=True)
                    if geo_result.returncode == 0:
                        for geo_line in geo_result.stdout.strip().split('\n'):
                            if 'geometry:' in geo_line:
                                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                                if match:
                                    x, y, width, height = map(int, match.groups())
                                    
                                    # 检查鼠标是否在窗口范围内
                                    if (x <= mouse_x < x + width and y <= mouse_y < y + height):
                                        visible_windows.append({
                                            'title': window_title,
                                            'position': (x, y),
                                            'size': (width, height),
                                            'area': width * height
                                        })
                                    break
                    
                    i += 2
                else:
                    i += 1
            
            print(f"   鼠标下的可见窗口 ({len(visible_windows)} 个):")
            for window in visible_windows:
                print(f"     - {window['title']}: {window['position']} {window['size'][0]}×{window['size'][1]}")
                
                # 特别标记hellobig窗口
                if 'hellobig' in window['title'].lower() or 'AT-SPI测试' in window['title']:
                    print(f"       ⚠️  这是hellobig窗口，但它应该是最小化的！")
                    print(f"       💡 这说明wlcctrl仍然报告最小化窗口的几何信息")
    
    except Exception as e:
        print(f"   ❌ 实用检查失败: {e}")


def main():
    """主函数"""
    print("🔍 hellobig窗口可见性调试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    debug_hellobig_window_visibility()
    test_practical_visibility_check()
    
    print(f"\n💡 结论和建议:")
    print("=" * 40)
    print("1. 如果hellobig窗口确实是最小化的，但仍被检测到，")
    print("   说明wlcctrl和AT-SPI都不能准确反映窗口的实际可见状态")
    print("2. 可能需要使用其他方法，如:")
    print("   - 检查窗口管理器的状态")
    print("   - 使用屏幕截图验证")
    print("   - 尝试与窗口交互来验证可见性")
    print("3. 或者添加用户确认机制，让用户确认窗口是否真的可见")


if __name__ == "__main__":
    main()
