#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
专门检测最底层具体控件的检测器
避免检测到filler等容器控件，专注于真正的交互控件
"""

import sys
import os
import subprocess
import time
import re
import pyatspi
from offset_corrected_detector import OffsetCorrectedDetector


class SpecificControlDetector:
    """专门检测具体控件的检测器"""
    
    def __init__(self, target_app_name="hellobig"):
        """初始化检测器"""
        self.target_app_name = target_app_name
        self.window_offset = self.get_window_offset()
        self.all_controls = []
        
        # 定义具体控件类型（排除容器类型）
        self.specific_control_types = {
            'push button', 'button', 'toggle button',
            'check box', 'radio button', 
            'text', 'password text', 'entry',
            'combo box', 'list', 'list item',
            'slider', 'spin button', 'scroll bar',
            'menu item', 'tab', 'tree item',
            'label', 'image', 'progress bar',
            'dial', 'calendar'
        }
        
        # 排除的容器类型
        self.container_types = {
            'filler', 'panel', 'frame', 'window', 'application',
            'layered pane', 'split pane', 'scroll pane',
            'unknown', 'page tab list', 'menu bar', 'status bar'
        }
        
        print("🎯 专门检测具体控件的检测器")
        print("=" * 60)
        if self.window_offset:
            print(f"🪟 窗口偏移: {self.window_offset}")
        else:
            print("❌ 无法获取窗口偏移")
    
    def get_window_offset(self):
        """获取窗口偏移"""
        try:
            # 获取窗口列表
            result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
            if result.returncode != 0:
                return None
            
            # 查找hellobig窗口ID
            window_id = None
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                    if i > 0:
                        prev_line = lines[i-1]
                        if 'toplevel' in prev_line:
                            window_id = prev_line.split('"')[1]
                            break
            
            if not window_id:
                return None
            
            # 获取窗口几何信息
            result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                return None
            
            # 解析几何信息
            for line in result.stdout.strip().split('\n'):
                if 'geometry:' in line:
                    match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)', line)
                    if match:
                        x, y = int(match.group(1)), int(match.group(2))
                        return (x, y)
            
            return None
            
        except Exception as e:
            print(f"❌ 获取窗口偏移失败: {e}")
            return None
    
    def refresh_specific_controls(self):
        """刷新具体控件缓存"""
        print("🔄 刷新具体控件缓存...")
        
        self.all_controls = []
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                # 如果指定了目标应用名称，只收集该应用的元素
                if self.target_app_name:
                    if self.target_app_name.lower() not in app_name.lower():
                        continue
                
                app_controls = []
                self._collect_specific_controls_recursive(app, app_controls, max_depth=6)
                
                if app_controls:
                    print(f"   📱 {app_name}: {len(app_controls)} 个具体控件")
                    self.all_controls.extend(app_controls)
            
            print(f"✅ 缓存完成，总共 {len(self.all_controls)} 个具体控件")
            
        except Exception as e:
            print(f"❌ 刷新缓存失败: {e}")
    
    def _collect_specific_controls_recursive(self, element, control_list, depth=0, max_depth=6):
        """递归收集具体控件（排除容器控件）"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 只收集具体控件类型，排除容器类型
            if (extents and extents.width > 0 and extents.height > 0 and 
                role in self.specific_control_types and 
                role not in self.container_types):
                
                control_list.append({
                    'element': element,
                    'name': name,
                    'role': role,
                    'extents': extents,
                    'depth': depth
                })
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_specific_controls_recursive(child, control_list, depth + 1, max_depth)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def find_most_specific_control_at_point(self, screen_x, screen_y):
        """在指定屏幕坐标找到最具体的控件"""
        print(f"🔍 在屏幕坐标 ({screen_x}, {screen_y}) 查找最具体的控件")
        
        if not self.all_controls:
            self.refresh_specific_controls()
        
        # 将屏幕坐标转换为AT-SPI坐标
        if self.window_offset:
            atspi_x = screen_x - self.window_offset[0]
            atspi_y = screen_y - self.window_offset[1]
        else:
            atspi_x = screen_x
            atspi_y = screen_y
        
        print(f"   转换为AT-SPI坐标: ({atspi_x}, {atspi_y})")
        
        # 查找匹配的控件
        matches = []
        for ctrl in self.all_controls:
            ext = ctrl['extents']
            
            if (ext.x <= atspi_x < ext.x + ext.width and
                ext.y <= atspi_y < ext.y + ext.height):
                matches.append(ctrl)
        
        if not matches:
            print("❌ 未找到匹配的具体控件")
            return None
        
        print(f"   找到 {len(matches)} 个匹配的具体控件:")
        for i, match in enumerate(matches):
            print(f"     {i+1}. {match['name']} ({match['role']}) - 面积: {match['extents'].width * match['extents'].height}")
        
        # 选择面积最小的控件（最具体的）
        best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
        
        print(f"   ✅ 选择最具体的控件: {best_match['name']} ({best_match['role']})")
        
        return best_match
    
    def get_control_info(self, ctrl):
        """获取控件详细信息"""
        try:
            # 获取状态
            states = []
            try:
                state = ctrl['element'].getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass
            
            # 获取动作
            actions = []
            try:
                if hasattr(ctrl['element'], 'queryAction'):
                    action = ctrl['element'].queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass
            
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': states,
                'actions': actions,
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
        except Exception:
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': [],
                'actions': [],
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
    
    def test_specific_control_detection(self, screen_x, screen_y):
        """测试具体控件检测"""
        print(f"\n🎯 测试具体控件检测")
        print(f"   屏幕坐标: ({screen_x}, {screen_y})")
        print("-" * 40)
        
        # 查找最具体的控件
        ctrl = self.find_most_specific_control_at_point(screen_x, screen_y)
        
        if ctrl:
            info = self.get_control_info(ctrl)
            
            print(f"✅ 检测成功!")
            print(f"   名称: {info['name']}")
            print(f"   类型: {info['role']}")
            print(f"   位置: {info['position']}")
            print(f"   尺寸: {info['size']}")
            print(f"   深度: {info['depth']}")
            
            if info['states']:
                print(f"   状态: {', '.join(info['states'][:3])}")
            
            if info['actions']:
                print(f"   动作: {', '.join(info['actions'])}")
            
            return True
        else:
            print(f"❌ 未找到具体控件")
            return False
    
    def highlight_specific_control(self, screen_x, screen_y, duration=2, color='green', border_width=3):
        """高亮具体控件"""
        print(f"\n✨ 高亮具体控件")
        print(f"   屏幕坐标: ({screen_x}, {screen_y})")
        
        # 查找最具体的控件
        ctrl = self.find_most_specific_control_at_point(screen_x, screen_y)
        
        if not ctrl:
            print("❌ 未找到可高亮的具体控件")
            return False
        
        # 计算高亮坐标（需要转换回屏幕坐标）
        atspi_x, atspi_y = ctrl['extents'].x, ctrl['extents'].y
        width, height = ctrl['extents'].width, ctrl['extents'].height
        
        if self.window_offset:
            highlight_x = atspi_x + self.window_offset[0]
            highlight_y = atspi_y + self.window_offset[1]
        else:
            highlight_x = atspi_x
            highlight_y = atspi_y
        
        print(f"   控件: {ctrl['name']} ({ctrl['role']})")
        print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y}) {width}×{height}")
        print(f"   高亮坐标: ({highlight_x}, {highlight_y}) {width}×{height}")
        
        try:
            from ultimate_highlight import ultimate_highlight
            highlight_success = ultimate_highlight(
                highlight_x, highlight_y, width, height,
                duration=duration, color=color, border_width=border_width
            )
            
            if highlight_success:
                print(f"✅ 高亮成功!")
                return True
            else:
                print(f"❌ 高亮失败")
                return False
                
        except Exception as e:
            print(f"❌ 高亮异常: {e}")
            return False


def main():
    """主函数"""
    print("🎯 专门检测具体控件的测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        detector = SpecificControlDetector()
        
        # 基于之前分析的控件位置进行测试
        test_controls = [
            ('普通按钮', 20, 20, 100, 30),
            ('切换按钮', 140, 20, 100, 30),
            ('复选框', 20, 71, 58, 22),
            ('单选按钮1', 160, 71, 54, 22),
            ('单选按钮2', 280, 71, 54, 22),
            ('用户名输入框', 110, 110, 200, 25),
            ('密码输入框', 420, 110, 200, 25),
            ('音量滑块', 110, 160, 200, 20),
            ('数字输入框', 330, 160, 80, 25),
            ('城市下拉框', 110, 210, 150, 25),
        ]
        
        print("\n🔬 开始测试具体控件检测...")
        
        success_count = 0
        for i, (name, atspi_x, atspi_y, width, height) in enumerate(test_controls):
            # 计算中心点
            center_x = atspi_x + width // 2
            center_y = atspi_y + height // 2
            
            # 转换为屏幕坐标
            if detector.window_offset:
                screen_x = detector.window_offset[0] + center_x
                screen_y = detector.window_offset[1] + center_y
            else:
                screen_x = center_x
                screen_y = center_y
            
            print(f"\n📍 测试 {i+1}/{len(test_controls)}: {name}")
            
            if detector.test_specific_control_detection(screen_x, screen_y):
                success_count += 1
                
                # 询问是否高亮
                try:
                    choice = input("   是否高亮此控件? (y/n): ").strip().lower()
                    if choice == 'y':
                        detector.highlight_specific_control(screen_x, screen_y)
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n👋 退出测试")
                    break
        
        print(f"\n📊 测试结果:")
        print("=" * 40)
        print(f"总测试数: {len(test_controls)}")
        print(f"成功数: {success_count}")
        print(f"成功率: {success_count/len(test_controls)*100:.1f}%")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
