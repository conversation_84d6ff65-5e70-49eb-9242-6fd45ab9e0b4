#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
验证正确的偏移量
基于用户提供的普通按钮真实位置计算并验证偏移
"""

import sys
import os
import subprocess
import time
import re
import pyatspi


def find_hellobig_controls():
    """查找hellobig应用的所有已知控件"""
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            if 'hellobig' in app_name.lower():
                print(f"📱 找到应用: {app_name}")
                
                controls = []
                collect_known_controls(app, controls)
                
                return controls
        
        return []
        
    except Exception as e:
        print(f"❌ 查找控件失败: {e}")
        return []


def collect_known_controls(element, control_list, depth=0, max_depth=4):
    """收集已知的控件"""
    if depth > max_depth:
        return
    
    try:
        name = element.name or "N/A"
        role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
        
        # 获取坐标
        extents = None
        try:
            if hasattr(element, 'queryComponent'):
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
        except Exception:
            pass
        
        # 收集已知的具体控件
        known_controls = {
            '普通按钮': 'push button',
            '切换按钮': 'check box', 
            '复选框': 'check box',
            '单选按钮1': 'radio button',
            '单选按钮2': 'radio button',
            '用户名输入框': 'text',
            '密码输入框': 'password text',
            '音量滑块': 'slider',
            '数字输入框': 'spin button'
        }
        
        if (extents and extents.width > 0 and extents.height > 0 and 
            name in known_controls and role == known_controls[name]):
            
            control_list.append({
                'name': name,
                'role': role,
                'atspi_position': (extents.x, extents.y),
                'size': (extents.width, extents.height),
                'element': element
            })
        
        # 递归处理子元素
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                collect_known_controls(child, control_list, depth + 1, max_depth)
        except Exception:
            pass
            
    except Exception:
        pass


def test_offset_on_control(control, offset):
    """在指定控件上测试偏移"""
    atspi_x, atspi_y = control['atspi_position']
    width, height = control['size']
    
    # 计算预期屏幕位置
    expected_screen_x = atspi_x + offset[0]
    expected_screen_y = atspi_y + offset[1]
    
    print(f"\n🧪 测试控件: {control['name']}")
    print(f"   AT-SPI位置: ({atspi_x}, {atspi_y}) {width}×{height}")
    print(f"   使用偏移: {offset}")
    print(f"   预期屏幕位置: ({expected_screen_x}, {expected_screen_y}) {width}×{height}")
    
    # 执行高亮
    try:
        from ultimate_highlight import ultimate_highlight
        
        print(f"   执行高亮...")
        highlight_success = ultimate_highlight(
            expected_screen_x, expected_screen_y, width, height,
            duration=3, color='lime', border_width=3
        )
        
        if highlight_success:
            print(f"   ✅ 高亮成功")
            return True
        else:
            print(f"   ❌ 高亮失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 高亮异常: {e}")
        return False


def verify_calculated_offset():
    """验证计算出的偏移量"""
    print("🔬 验证计算出的偏移量")
    print("=" * 60)
    
    # 基于用户提供的普通按钮位置计算偏移
    button_atspi_pos = (20, 20)  # 普通按钮的AT-SPI位置
    button_real_pos = (587, 212)  # 用户提供的真实位置
    
    calculated_offset = (
        button_real_pos[0] - button_atspi_pos[0],
        button_real_pos[1] - button_atspi_pos[1]
    )
    
    print(f"📊 偏移计算:")
    print(f"   普通按钮AT-SPI位置: {button_atspi_pos}")
    print(f"   普通按钮真实位置: {button_real_pos}")
    print(f"   计算出的偏移: {calculated_offset}")
    
    # 查找所有控件
    controls = find_hellobig_controls()
    if not controls:
        print("❌ 未找到控件")
        return None
    
    print(f"\n📋 找到 {len(controls)} 个控件:")
    for i, ctrl in enumerate(controls):
        print(f"   {i+1}. {ctrl['name']} ({ctrl['role']}) - AT-SPI: {ctrl['atspi_position']}")
    
    # 验证偏移在所有控件上的效果
    print(f"\n🧪 开始验证偏移 {calculated_offset}...")
    
    correct_count = 0
    total_count = len(controls)
    
    for i, control in enumerate(controls):
        print(f"\n--- 验证 {i+1}/{total_count} ---")
        
        if test_offset_on_control(control, calculated_offset):
            try:
                feedback = input(f"   高亮是否准确覆盖了 {control['name']}? (y/n/s=跳过): ").strip().lower()
                
                if feedback == 'y':
                    print(f"   ✅ {control['name']} 验证成功")
                    correct_count += 1
                elif feedback == 's':
                    print(f"   ⏭️  跳过 {control['name']}")
                    continue
                else:
                    print(f"   ❌ {control['name']} 验证失败")
                    
                    # 询问偏差方向
                    direction = input(f"   高亮相对正确位置在哪里? (up/down/left/right): ").strip().lower()
                    if direction in ['up', 'down', 'left', 'right']:
                        print(f"   记录偏差方向: {direction}")
                        
            except KeyboardInterrupt:
                print(f"\n🛑 用户中断验证")
                break
        else:
            print(f"   ❌ {control['name']} 高亮失败")
    
    # 统计结果
    print(f"\n📊 验证结果:")
    print("=" * 40)
    print(f"总控件数: {total_count}")
    print(f"验证成功: {correct_count}")
    print(f"成功率: {correct_count/total_count*100:.1f}%")
    
    if correct_count >= total_count * 0.8:
        print(f"🎉 偏移 {calculated_offset} 验证成功!")
        return calculated_offset
    else:
        print(f"⚠️  偏移 {calculated_offset} 需要进一步调整")
        return None


def create_corrected_detector_with_offset(offset):
    """创建使用正确偏移的检测器代码"""
    print(f"\n📝 生成使用正确偏移的检测器代码...")
    
    code_template = f'''#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
使用正确偏移的控件检测器
偏移量: {offset}
"""

import sys
import os
import subprocess
import time
import re
import pyatspi


class CorrectedControlDetector:
    """使用正确偏移的控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        # 使用验证过的正确偏移
        self.correct_offset = {offset}
        self.all_controls = []
        
        print("✅ 使用正确偏移的控件检测器")
        print(f"🎯 偏移量: {{self.correct_offset}}")
    
    def get_mouse_position(self):
        """获取当前鼠标位置"""
        try:
            result = subprocess.run(['xdotool', 'getmouselocation'], 
                                  capture_output=True, text=True, env={{'DISPLAY': ':0'}})
            if result.returncode == 0:
                parts = result.stdout.strip().split()
                mouse_x = int(parts[0].split(':')[1])
                mouse_y = int(parts[1].split(':')[1])
                return mouse_x, mouse_y
        except Exception:
            pass
        return None, None
    
    def find_control_at_mouse(self):
        """在鼠标位置查找控件"""
        mouse_pos = self.get_mouse_position()
        if mouse_pos == (None, None):
            return None
        
        mouse_x, mouse_y = mouse_pos
        
        # 转换为AT-SPI坐标
        atspi_x = mouse_x - self.correct_offset[0]
        atspi_y = mouse_y - self.correct_offset[1]
        
        print(f"🖱️  鼠标位置: ({{mouse_x}}, {{mouse_y}})")
        print(f"🔄 AT-SPI坐标: ({{atspi_x}}, {{atspi_y}})")
        
        # 在这里添加控件查找逻辑...
        # (使用之前的控件查找代码)
        
        return None
    
    def highlight_control_correctly(self, control):
        """正确高亮控件"""
        if not control:
            return False
        
        atspi_x, atspi_y = control['atspi_position']
        width, height = control['size']
        
        # 计算正确的屏幕坐标
        screen_x = atspi_x + self.correct_offset[0]
        screen_y = atspi_y + self.correct_offset[1]
        
        try:
            from ultimate_highlight import ultimate_highlight
            return ultimate_highlight(screen_x, screen_y, width, height, 
                                    duration=2, color='green', border_width=2)
        except Exception:
            return False


def main():
    """主函数"""
    detector = CorrectedControlDetector()
    # 添加你的测试代码...


if __name__ == "__main__":
    main()
'''
    
    # 保存代码到文件
    with open('corrected_control_detector.py', 'w', encoding='utf-8') as f:
        f.write(code_template)
    
    print(f"✅ 代码已保存到 corrected_control_detector.py")
    print(f"🎯 使用偏移: {offset}")


def main():
    """主函数"""
    print("🔬 验证正确的偏移量")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        correct_offset = verify_calculated_offset()
        
        if correct_offset:
            print(f"\n🎉 找到正确偏移: {correct_offset}")
            
            # 询问是否生成代码
            choice = input(f"是否生成使用此偏移的检测器代码? (y/n): ").strip().lower()
            if choice == 'y':
                create_corrected_detector_with_offset(correct_offset)
        else:
            print(f"\n❌ 偏移验证失败，需要进一步调整")

    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
