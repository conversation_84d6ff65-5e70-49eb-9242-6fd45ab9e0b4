#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
测试交互式捕获功能
模拟在应用窗口中的控件捕获过程
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>in<PERSON>utoSDK_UNI')

from universal_offset_detector import UniversalOffsetDetector
from interactive_highlight import interactive_highlight


def test_with_known_position():
    """使用已知的控件位置进行测试"""
    print("🧪 测试交互式捕获功能")
    print("=" * 50)
    
    # 使用AT-SPI测试界面中的已知控件位置
    test_positions = [
        (1044, 213, "切换按钮区域"),  # 之前测试成功的位置
        (1000, 300, "文本输入区域"),
        (1100, 400, "按钮区域"),
    ]
    
    detector = UniversalOffsetDetector()
    
    for i, (x, y, desc) in enumerate(test_positions, 1):
        print(f"\n🎯 测试 {i}: {desc}")
        print(f"📍 测试位置: ({x}, {y})")
        
        try:
            # 计算窗口偏移
            offset, atspi_window_info = detector.calculate_window_offset(x, y)
            if not offset or not atspi_window_info:
                print(f"❌ 无法计算窗口偏移")
                continue
            
            print(f"✅ 找到窗口: {atspi_window_info['app_name']}")
            print(f"📐 计算偏移: {offset}")
            
            # 收集应用控件
            detector.collect_app_controls(atspi_window_info)
            if not detector.current_app_controls:
                print(f"❌ 未找到应用控件")
                continue
            
            print(f"📋 找到 {len(detector.current_app_controls)} 个控件")
            
            # 查找控件
            ctrl = detector.find_control_with_offset(x, y, offset)
            if not ctrl:
                print(f"❌ 未找到匹配的控件")
                continue
            
            # 获取控件信息
            info = detector.get_control_info(ctrl)
            print(f"🎯 找到控件: {info['name']} ({info['role']})")
            
            # 计算屏幕坐标
            ext = ctrl['extents']
            screen_x = ext.x + offset[0]
            screen_y = ext.y + offset[1]
            
            print(f"📍 屏幕坐标: ({screen_x}, {screen_y}) {ext.width}×{ext.height}")
            
            # 显示交互式高亮
            print("✨ 显示交互式高亮...")
            user_choice = interactive_highlight(
                screen_x, screen_y, ext.width, ext.height,
                control_info=info,
                duration=10,  # 10秒超时
                color='blue',
                border_width=2
            )
            
            print(f"👤 用户选择: {user_choice}")
            
            if user_choice == 'confirm':
                print("✅ 用户确认，模拟保存控件信息...")
                
                # 模拟保存过程
                save_success = save_control_info_test(ctrl, offset, atspi_window_info, info)
                if save_success:
                    print("🎉 控件信息保存成功!")
                else:
                    print("❌ 控件信息保存失败")
                    
            elif user_choice == 'cancel':
                print("❌ 用户取消捕获")
                
            elif user_choice == 'timeout':
                print("⏰ 用户操作超时")
                
            else:
                print("❌ 交互过程出现错误")
            
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ 测试 {i} 失败: {e}")
            import traceback
            traceback.print_exc()


def save_control_info_test(ctrl, offset, atspi_window_info, control_info):
    """测试保存控件信息 - 使用真正的original格式"""
    try:
        import json
        from datetime import datetime
        from universal_offset_detector import UniversalOffsetDetector, get_mouse_position

        # 使用真正的原始格式
        mouse_pos = get_mouse_position()
        mouse_x, mouse_y = mouse_pos if mouse_pos != (None, None) else (0, 0)

        # 使用检测器的原始格式方法
        detector = UniversalOffsetDetector()
        original_info = detector.get_control_info_original_format(
            ctrl, atspi_window_info, mouse_x, mouse_y, offset
        )

        if original_info:
            # 保存到主文件（与universal_offset_detector.py完全一致）
            with open('detected_control_info.txt', 'w', encoding='utf-8') as f:
                json.dump(original_info, f, ensure_ascii=False)

            print(f"📄 详细信息已保存到: detected_control_info.txt")

            # 保存带时间戳的备份
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"captured_control_{timestamp}.json"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(original_info, f, ensure_ascii=False, indent=2)

            print(f"💾 备份副本: {backup_file}")

            # 显示原始格式的JSON内容
            print("\n📋 保存的控件信息 (真正的原始格式):")
            print("=" * 60)
            print(json.dumps(original_info, indent=2, ensure_ascii=False))
            print("=" * 60)

            return True
        else:
            print("❌ 无法生成原始格式信息")
            return False

    except Exception as e:
        print(f"❌ 保存控件信息错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_output():
    """测试文件输出"""
    print("\n📄 检查输出文件...")
    
    files_to_check = [
        'detected_control_info.txt',
        'captured_control_*.json'
    ]
    
    for file_pattern in files_to_check:
        if '*' in file_pattern:
            # 使用glob查找匹配的文件
            import glob
            matching_files = glob.glob(file_pattern)
            if matching_files:
                latest_file = max(matching_files, key=os.path.getctime)
                print(f"✅ 找到文件: {latest_file}")
                
                # 显示文件内容摘要
                try:
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"📏 文件大小: {len(content)} 字符")
                    
                    if latest_file.endswith('.json'):
                        import json
                        data = json.loads(content)
                        print(f"📋 JSON结构: {list(data.keys())}")
                        
                except Exception as e:
                    print(f"❌ 读取文件失败: {e}")
            else:
                print(f"❌ 未找到匹配文件: {file_pattern}")
        else:
            if os.path.exists(file_pattern):
                print(f"✅ 找到文件: {file_pattern}")
                
                # 显示文件内容摘要
                try:
                    with open(file_pattern, 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"📏 文件大小: {len(content)} 字符")
                    
                    if file_pattern.endswith('.txt') or file_pattern.endswith('.json'):
                        import json
                        data = json.loads(content)
                        print(f"📋 JSON结构: {list(data.keys())}")
                        
                except Exception as e:
                    print(f"❌ 读取文件失败: {e}")
            else:
                print(f"❌ 文件不存在: {file_pattern}")


def main():
    """主函数"""
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        # 测试交互式捕获
        test_with_known_position()
        
        # 检查输出文件
        test_file_output()
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
