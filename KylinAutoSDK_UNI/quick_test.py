#!/usr/bin/env python3
import sys
sys.path.insert(0, 'src')
from UNI_new import UNI

uni = UNI()
print('测试优化后的智能匹配策略...')

test_x, test_y = 634, 235

try:
    control_data, info = uni.kdk_getElement_Uni(test_x, test_y, quick=False, highlight=False)
    
    if control_data and isinstance(control_data, dict):
        name = control_data.get('Name', 'N/A')
        role = control_data.get('Rolename', 'N/A')
        coords = control_data.get('Coords', {})
        
        print(f'匹配结果: {name} ({role})')
        
        if coords:
            width = coords.get('width', 0)
            height = coords.get('height', 0)
            print(f'坐标和尺寸: ({coords.get("x")}, {coords.get("y")}) {width}x{height}')
            
            if width < 500 and height < 200 and name != 'N/A':
                print('成功找到具体控件!')
            else:
                print('找到的是容器控件')
    else:
        print(f'匹配失败: {info}')
        
except Exception as e:
    print(f'错误: {e}')