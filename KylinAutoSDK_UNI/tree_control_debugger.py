#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
树形控件调试器
用于调试特定坐标下的控件识别问题
"""

import sys
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>inAutoSDK_UNI/src')

from UNI_new import UNI
import pyatspi

def debug_tree_control_at_position(x, y):
    """调试指定位置的树形控件识别"""
    print(f"🔍 调试位置 ({x}, {y}) 的控件识别")
    print("=" * 60)
    
    uni = UNI()
    
    # 获取控件信息
    result, info = uni.kdk_getElement_Uni(x, y, quick=False, highlight=True)
    
    print(f"识别结果: {info}")
    if result:
        print(f"控件名称: {result.get('Name', 'N/A')}")
        print(f"控件角色: {result.get('Rolename', 'N/A')}")
        print(f"控件坐标: {result.get('Coords', 'N/A')}")
        print(f"控件状态: {result.get('States', 'N/A')}")
        print(f"控件动作: {result.get('Actions', 'N/A')}")
    
    return result

if __name__ == "__main__":
    # 使用示例 - 请替换为实际的问题坐标
    # debug_tree_control_at_position(400, 300)
    print("请调用 debug_tree_control_at_position(x, y) 来调试特定位置")
