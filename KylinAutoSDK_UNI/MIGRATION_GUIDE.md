# KylinAutoSDK_UNI 代码结构优化迁移指南

## 概述

为了提高代码的可维护性和可扩展性，我们对KylinAutoSDK_UNI项目进行了模块化重构。原本1600+行的单文件`UNI.py`已被拆分为多个功能明确的模块。

## 主要变化

### 1. 文件结构变化

**旧结构：**
```
src/
└── UNI.py  # 所有代码集中在一个文件
```

**新结构：**
```
src/
├── UNI.py         # 原文件（保留）
├── UNI_new.py     # 兼容性入口
└── uni_sdk/       # 新的模块化代码
    ├── __init__.py
    ├── uni.py
    ├── core/
    ├── utils/
    └── handlers/
```

### 2. 导入方式

#### 方式一：无需修改（推荐）
如果您的代码使用 `from UNI import UNI`，可以简单地将导入语句改为：
```python
# 旧代码
from UNI import UNI

# 新代码（使用兼容性入口）
from UNI_new import UNI
```

#### 方式二：使用新模块
```python
# 使用新的模块化结构
from uni_sdk import UNI
```

### 3. API保持不变

所有公开的API方法保持不变，您的现有代码无需修改即可继续使用：
- `kdk_getElement_Uni()`
- `kdk_getElePos_Uni()`
- `kdk_KBToJson_Uni()`
- `kdk_getWinAllEle_Uni()`
- 等等...

## 迁移步骤

### 快速迁移（最小改动）

1. 将您的导入语句从：
   ```python
   from UNI import UNI
   ```
   
   改为：
   ```python
   from UNI_new import UNI
   ```

2. 其他代码无需修改！

### 完整迁移（推荐）

1. 更新导入语句：
   ```python
   from uni_sdk import UNI
   ```

2. 如果您直接使用了内部方法（以`_`开头），请注意这些方法仍然可用，但建议逐步迁移到使用公开API。

3. 如果您需要单独使用某些功能，可以直接导入子模块：
   ```python
   from uni_sdk.core import WindowManager
   from uni_sdk.handlers import ScreenshotHandler
   ```

## 示例代码对比

### 示例1：基本使用
```python
# 旧代码
from UNI import UNI

uni = UNI()
data, info = uni.kdk_getElement_Uni(100, 200)

# 新代码（方式一）
from UNI_new import UNI

uni = UNI()
data, info = uni.kdk_getElement_Uni(100, 200)

# 新代码（方式二）
from uni_sdk import UNI

uni = UNI()
data, info = uni.kdk_getElement_Uni(100, 200)
```

### 示例2：使用内部方法
```python
# 旧代码
from UNI import UNI

uni = UNI()
window = uni._get_window_by_windowname("文件管理器")

# 新代码（兼容方式）
from UNI_new import UNI

uni = UNI()
window = uni._get_window_by_windowname("文件管理器")

# 新代码（推荐方式）
from uni_sdk.core import WindowManager

wm = WindowManager()
window = wm.get_window_by_name("文件管理器")
```

## 新增功能

### 1. 模块化使用
现在可以单独使用特定功能模块：
```python
# 仅使用截图功能
from uni_sdk.handlers import ScreenshotHandler

sh = ScreenshotHandler()
img_path = sh.take_screenshot("test", "2024-01-01")

# 仅使用验证功能
from uni_sdk.handlers import VerificationHandler

vh = VerificationHandler()
is_visible = vh.is_element_visible(control_info)
```

### 2. 配置集中管理
所有常量和配置现在集中在`constants.py`中：
```python
from uni_sdk.utils.constants import DEFAULT_WAIT_COUNT, SCREENSHOT_DIR
```

## 注意事项

1. **原文件保留**：`src/UNI.py`文件仍然保留，您可以继续使用它，但不推荐。

2. **内部方法**：以`_`开头的内部方法仍然可用，但建议使用公开API。

3. **数据兼容性**：生成的JSON文件格式保持不变，无需转换。

4. **demo兼容性**：demo目录下的示例代码只需修改导入语句即可运行。

## 获得帮助

如果在迁移过程中遇到任何问题：

1. 检查是否正确修改了导入语句
2. 确认使用的方法名称是否正确
3. 查看`uni_sdk/README.md`了解模块详细说明
4. 参考demo目录下的示例代码

## 总结

这次重构主要是内部实现的优化，对外接口保持不变。通过简单修改导入语句，您的代码即可继续正常工作，同时享受到模块化带来的好处。 