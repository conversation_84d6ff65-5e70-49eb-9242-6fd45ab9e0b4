#!/usr/bin/env python3
"""
分析原始 UNI 输出格式
"""

import json
import sys

def analyze_original_format():
    """分析原始控件信息格式"""
    print("🔍 分析原始 UNI 控件信息格式")
    print("=" * 50)
    
    try:
        # 读取原始控件信息
        with open('control_info.txt', 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 解析 JSON
        original_data = json.loads(content)
        
        print("📊 原始数据结构分析:")
        print("-" * 30)
        
        # 顶层字段
        print("🔸 顶层字段:")
        for key, value in original_data.items():
            value_type = type(value).__name__
            if isinstance(value, (dict, list)):
                length = len(value)
                print(f"   {key}: {value_type} (长度: {length})")
            else:
                print(f"   {key}: {value_type} = {value}")
        
        print()
        
        # datamap 字段详细分析
        if 'datamap' in original_data:
            datamap = original_data['datamap']
            print("🔸 datamap 字段详细结构:")
            for key, value in datamap.items():
                value_type = type(value).__name__
                if isinstance(value, dict):
                    print(f"   {key}: {value_type}")
                    for sub_key, sub_value in value.items():
                        print(f"     {sub_key}: {sub_value} ({type(sub_value).__name__})")
                elif isinstance(value, list):
                    print(f"   {key}: {value_type} = {value}")
                else:
                    print(f"   {key}: {value_type} = {value}")
        
        print()
        
        # 对比我们当前的输出格式
        print("🔸 需要匹配的完整格式:")
        print(json.dumps(original_data, indent=2, ensure_ascii=False))
        
        return original_data
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def create_format_template():
    """创建格式模板"""
    print(f"\n📋 创建兼容格式模板")
    print("=" * 40)
    
    template = {
        "name": "控件名称",
        "type": "控件类型",
        "coords": {
            "x": "x坐标",
            "y": "y坐标", 
            "width": "宽度",
            "height": "高度"
        },
        "datamap": {
            "Name": "控件名称",
            "ID": "元素ID",
            "ProcessID": "进程ID",
            "Rolename": "角色名称",
            "Description": "描述",
            "Index_in_parent": "父级索引",
            "ChildrenCount": "子元素数量",
            "ProcessName": "进程名称",
            "Coords": {
                "x": "x坐标",
                "y": "y坐标",
                "width": "宽度", 
                "height": "高度"
            },
            "Text": "文本内容",
            "Actions": ["动作列表"],
            "States": ["状态列表"],
            "ParentPath": ["父路径列表"],
            "ParentCount": "父路径数量",
            "Key": "唯一标识",
            "RecordPosition": ["记录位置x", "记录位置y"],
            "WindowRoleName": "窗口角色",
            "WindowChildCount": "窗口子控件数",
            "WindowName": "窗口名称",
            "capture_status": "捕获状态"
        },
        "description": "控件描述",
        "states": ["状态列表"],
        "actions": ["动作列表"],
        "capture_status": "捕获状态"
    }
    
    print("模板结构:")
    print(json.dumps(template, indent=2, ensure_ascii=False))
    
    return template

if __name__ == "__main__":
    original_data = analyze_original_format()
    if original_data:
        create_format_template()
