#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
调试版控件检测器
专门用于诊断控件检测问题，提供详细的调试信息

功能:
1. 详细的AT-SPI遍历过程
2. 控件筛选条件的逐步检查
3. 坐标匹配的详细分析
4. 元素属性的完整输出
"""

import sys
import os
import subprocess
import pyatspi

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_element_extents(element):
    """安全地获取元素范围"""
    try:
        if hasattr(element, 'queryComponent'):
            component = element.queryComponent()
            if component:
                return component.getExtents(pyatspi.DESKTOP_COORDS)
    except Exception as e:
        print(f"      获取元素范围失败: {e}")
    return None


def get_element_states(element):
    """获取元素状态"""
    try:
        state = element.getState()
        states = []
        for i in range(len(state)):
            if state.contains(i):
                states.append(pyatspi.stateToString(i))
        return states
    except Exception:
        return []


def get_element_actions(element):
    """获取元素动作"""
    try:
        if hasattr(element, 'queryAction'):
            action = element.queryAction()
            if action:
                actions = []
                for i in range(action.nActions):
                    actions.append(action.getName(i))
                return actions
    except Exception:
        pass
    return []


def is_point_in_rect(x, y, rect_x, rect_y, rect_width, rect_height):
    """检查点是否在矩形内"""
    return (rect_x <= x < rect_x + rect_width and 
            rect_y <= y < rect_y + rect_height)


class DebugControlDetector:
    """调试版控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        print("🐛 调试版控件检测器已初始化")
        self.debug_level = 2  # 调试级别: 0=基本, 1=详细, 2=完整
    
    def debug_at_spi_tree(self, target_x, target_y):
        """调试AT-SPI树结构"""
        print(f"\n🌳 调试AT-SPI树结构，目标坐标: ({target_x}, {target_y})")
        print("=" * 80)
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            print(f"📱 桌面childCount: {desktop.childCount}")
            
            matching_elements = []
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                print(f"\n🔍 应用 {i}: {app.name} (childCount: {app.childCount})")
                
                # 检查是否是hellobig相关的应用
                app_name = app.name.lower() if app.name else ""
                if 'hello' in app_name or 'big' in app_name or 'test' in app_name:
                    print(f"🎯 找到疑似hellobig应用: {app.name}")
                
                try:
                    self._debug_element_recursive(app, target_x, target_y, matching_elements, depth=1, max_depth=5)
                except Exception as e:
                    print(f"   ❌ 遍历应用异常: {e}")
                    continue
            
            print(f"\n📊 总共找到 {len(matching_elements)} 个匹配元素")
            
            if matching_elements:
                print("\n🔍 匹配元素详情:")
                for i, elem_info in enumerate(matching_elements):
                    print(f"   {i+1}. {elem_info['name'] or 'N/A'} ({elem_info['role']}) - 面积: {elem_info['area']}")
                
                # 选择最佳匹配
                best = self._select_best_element_debug(matching_elements, target_x, target_y)
                if best:
                    print(f"\n🏆 最佳匹配: {best['name'] or 'N/A'} ({best['role']})")
                    return best['element']
            else:
                print("\n❌ 未找到任何匹配元素")
                
        except Exception as e:
            print(f"❌ AT-SPI树调试异常: {e}")
            import traceback
            traceback.print_exc()
        
        return None
    
    def _debug_element_recursive(self, element, target_x, target_y, matching_elements, depth=0, max_depth=5):
        """递归调试元素"""
        if depth > max_depth:
            return
            
        indent = "  " * depth
        
        try:
            # 获取基本信息
            name = element.name if hasattr(element, 'name') else ''
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取位置信息
            extents = get_element_extents(element)
            
            if self.debug_level >= 1:
                print(f"{indent}📦 {name or 'N/A'} ({role})")
                
            if extents:
                if self.debug_level >= 2:
                    print(f"{indent}   位置: ({extents.x}, {extents.y}) 尺寸: {extents.width}×{extents.height}")
                
                # 检查坐标是否匹配
                coord_match = is_point_in_rect(target_x, target_y, extents.x, extents.y, extents.width, extents.height)
                
                if coord_match:
                    print(f"{indent}   ✅ 坐标匹配!")
                    
                    # 获取详细信息
                    states = get_element_states(element)
                    actions = get_element_actions(element)
                    area = extents.width * extents.height
                    
                    if self.debug_level >= 1:
                        print(f"{indent}   状态: {states}")
                        print(f"{indent}   动作: {actions}")
                        print(f"{indent}   面积: {area}")
                    
                    # 检查是否为有意义的控件
                    is_meaningful = self._debug_meaningful_check(element, role, name, '', actions, area, indent)
                    
                    if is_meaningful:
                        matching_elements.append({
                            'element': element,
                            'extents': extents,
                            'area': area,
                            'depth': depth,
                            'role': role,
                            'name': name,
                            'states': states,
                            'actions': actions
                        })
                        print(f"{indent}   🎯 添加到匹配列表")
                    else:
                        print(f"{indent}   ❌ 不符合有意义控件条件")
                elif self.debug_level >= 2:
                    print(f"{indent}   ❌ 坐标不匹配")
            else:
                if self.debug_level >= 2:
                    print(f"{indent}   ⚠️  无法获取位置信息")
            
            # 递归检查子元素
            try:
                child_count = element.childCount
                if child_count > 0 and depth < max_depth:
                    if self.debug_level >= 2:
                        print(f"{indent}   🔍 检查 {child_count} 个子元素...")
                    
                    for i in range(child_count):
                        try:
                            child = element.getChildAtIndex(i)
                            self._debug_element_recursive(child, target_x, target_y, matching_elements, depth + 1, max_depth)
                        except Exception as e:
                            if self.debug_level >= 2:
                                print(f"{indent}     ❌ 子元素 {i} 访问异常: {e}")
                            continue
            except Exception as e:
                if self.debug_level >= 2:
                    print(f"{indent}   ❌ 获取子元素异常: {e}")
                
        except Exception as e:
            print(f"{indent}❌ 元素处理异常: {e}")
    
    def _debug_meaningful_check(self, element, role, name, description, actions, area, indent=""):
        """调试有意义控件检查"""
        print(f"{indent}   🔍 检查是否为有意义控件:")
        
        # 常见的可交互控件角色
        interactive_roles = [
            'button', 'push button', 'toggle button', 'check box', 'radio button',
            'text', 'entry', 'password text', 'spin button',
            'combo box', 'list', 'list item', 'tree', 'tree item',
            'menu', 'menu item', 'menu bar', 'tool bar',
            'label', 'static text', 'heading',
            'link', 'image', 'icon',
            'scroll bar', 'slider', 'progress bar',
            'tab', 'tab list', 'page tab',
            'table', 'table cell', 'column header', 'row header',
            'frame', 'window', 'dialog', 'panel'  # 添加更多容器类型
        ]
        
        checks = []
        
        # 检查角色
        role_match = role.lower() in [r.lower() for r in interactive_roles]
        checks.append(f"角色匹配({role}): {'✅' if role_match else '❌'}")
        
        # 检查动作
        has_actions = bool(actions)
        checks.append(f"有动作: {'✅' if has_actions else '❌'}")
        
        # 检查名称
        has_name = bool(name and len(name.strip()) > 0)
        checks.append(f"有名称({name}): {'✅' if has_name else '❌'}")
        
        # 检查面积
        area_ok = 10 <= area <= 100000  # 放宽面积限制
        checks.append(f"面积合理({area}): {'✅' if area_ok else '❌'}")
        
        for check in checks:
            print(f"{indent}     {check}")
        
        # 任何一个条件满足就认为有意义
        result = role_match or has_actions or has_name or area_ok
        print(f"{indent}   结果: {'✅ 有意义的控件' if result else '❌ 无意义的控件'}")
        
        return result
    
    def _select_best_element_debug(self, matching_elements, x, y):
        """调试版选择最佳元素"""
        if not matching_elements:
            return None
        
        print(f"\n🎯 选择最佳元素 (从{len(matching_elements)}个候选中):")
        
        # 给每个元素打分
        for i, item in enumerate(matching_elements):
            score = 0
            score_details = []
            
            # 面积越小越好
            area_score = 1000000 // max(item['area'], 1)
            score += area_score
            score_details.append(f"面积分数: {area_score}")
            
            # 深度越大越好
            depth_score = item['depth'] * 1000
            score += depth_score
            score_details.append(f"深度分数: {depth_score}")
            
            # 有动作加分
            if item['actions']:
                score += 500
                score_details.append("动作加分: +500")
            
            # 有名称加分
            if item['name']:
                score += 300
                score_details.append("名称加分: +300")
            
            # 常见控件加分
            common_roles = ['button', 'label', 'text', 'entry']
            if item['role'].lower() in common_roles:
                score += 200
                score_details.append("常见控件加分: +200")
            
            item['score'] = score
            
            print(f"  {i+1}. {item['name'] or 'N/A'} ({item['role']}) - 总分: {score}")
            for detail in score_details:
                print(f"     {detail}")
        
        # 按分数排序
        matching_elements.sort(key=lambda x: x['score'], reverse=True)
        best = matching_elements[0]
        
        print(f"\n🏆 最佳选择: {best['name'] or 'N/A'} ({best['role']}) - 分数: {best['score']}")
        
        return best


def debug_hellobig_detection():
    """专门调试hellobig程序的控件检测"""
    print("🐛 hellobig程序控件检测调试")
    print("=" * 80)
    
    # 获取鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is None:
        print("❌ 无法获取鼠标位置")
        return
    
    print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 创建调试检测器
    detector = DebugControlDetector()
    
    # 首先查看所有应用
    print("\n📱 所有可用应用:")
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            print(f"  {i}: {app.name} (childCount: {app.childCount})")
    except Exception as e:
        print(f"❌ 获取应用列表失败: {e}")
    
    # 调试AT-SPI树
    element = detector.debug_at_spi_tree(mouse_x, mouse_y)
    
    if element:
        print(f"\n✅ 找到元素，尝试高亮...")
        
        # 获取元素实际位置
        extents = get_element_extents(element)
        if extents:
            print(f"🎯 元素位置: ({extents.x}, {extents.y}) {extents.width}×{extents.height}")
            
            # 尝试高亮
            try:
                from ultimate_highlight import ultimate_highlight
                result = ultimate_highlight(extents.x, extents.y, extents.width, extents.height, 
                                          duration=3, color='green', border_width=3)
                print(f"高亮结果: {'成功' if result else '失败'}")
            except Exception as e:
                print(f"高亮异常: {e}")
        else:
            print("❌ 无法获取元素位置")
    else:
        print(f"\n❌ 未找到任何元素")
        
        # 提供诊断建议
        print(f"\n🔧 诊断建议:")
        print(f"1. 确保hellobig程序支持AT-SPI (辅助功能)")
        print(f"2. 尝试点击或聚焦hellobig窗口")
        print(f"3. 检查hellobig是否使用了标准的GUI框架")
        print(f"4. 确保鼠标确实悬停在hellobig窗口内的控件上")


def show_all_applications():
    """显示所有可用的应用程序"""
    print("\n📱 当前桌面上的所有应用程序:")
    print("=" * 60)
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            print(f"\n应用 {i}: {app.name}")
            print(f"  子窗口数量: {app.childCount}")
            
            # 显示窗口信息
            for j in range(min(app.childCount, 3)):  # 只显示前3个窗口
                try:
                    window = app.getChildAtIndex(j)
                    extents = get_element_extents(window)
                    if extents:
                        print(f"    窗口 {j}: {window.name} - ({extents.x}, {extents.y}) {extents.width}×{extents.height}")
                    else:
                        print(f"    窗口 {j}: {window.name} - 无位置信息")
                except Exception as e:
                    print(f"    窗口 {j}: 获取失败 - {e}")
                    
    except Exception as e:
        print(f"❌ 获取应用列表失败: {e}")


def main():
    """主函数"""
    print("🐛 控件检测调试工具")
    print("=" * 60)
    
    # 检查环境
    if not sys.platform.startswith('linux'):
        print("❌ 此程序仅支持Linux系统")
        sys.exit(1)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    while True:
        print("\n选择调试模式:")
        print("1. 调试当前鼠标位置的控件检测")
        print("2. 显示所有应用程序")
        print("3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            debug_hellobig_detection()
        elif choice == '2':
            show_all_applications()
        elif choice == '3':
            print("👋 调试结束")
            break
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()