#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
成功演示脚本 - 验证修复后的应用匹配功能
"""

import sys
import os
import time
import subprocess
import re

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_hellobig_window_info():
    """获取hellobig窗口信息"""
    try:
        result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        lines = result.stdout.split('\n')
        hellobig_uuid = None
        
        for i, line in enumerate(lines):
            if 'AT-SPI测试界面' in line:
                for j in range(i-1, -1, -1):
                    if 'toplevel' in lines[j]:
                        uuid_match = re.search(r'"([^"]+)"', lines[j])
                        if uuid_match:
                            hellobig_uuid = uuid_match.group(1)
                            break
                break
        
        if not hellobig_uuid:
            return None
        
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', hellobig_uuid], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        geo_match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', result.stdout)
        if geo_match:
            x, y, width, height = map(int, geo_match.groups())
            return {'x': x, 'y': y, 'width': width, 'height': height}
        
        return None
    except Exception:
        return None

def simple_highlight(x, y, width, height, duration=3):
    """简单的高亮函数，避免复杂的错误"""
    try:
        from ultimate_highlight import ultimate_highlight
        return ultimate_highlight(x, y, width, height, duration, 'red', 3)
    except Exception as e:
        print(f"高亮失败: {e}")
        return False

def test_application_matching():
    """测试应用匹配功能是否修复"""
    print("🎯 测试修复后的应用匹配功能")
    print("=" * 60)
    
    window_info = get_hellobig_window_info()
    if not window_info:
        print("❌ 无法获取hellobig窗口信息")
        return False
    
    print(f"✅ hellobig窗口: ({window_info['x']}, {window_info['y']}) {window_info['width']}×{window_info['height']}")
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        # 测试按钮位置
        test_x = window_info['x'] + 70  # 相对位置70
        test_y = window_info['y'] + 80  # 相对位置80
        
        print(f"\n📍 测试按钮位置: ({test_x}, {test_y})")
        
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(test_x, test_y, quick=True, highlight=False)
        elapsed = time.time() - start_time
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            
            print(f"✅ 识别成功 ({elapsed:.2f}s)")
            print(f"   控件名称: {name}")
            print(f"   控件类型: {role}")
            
            # 尝试获取坐标信息
            coords = control_data.get('Coords', {})
            if coords:
                if hasattr(coords, 'get'):
                    # 普通字典
                    ctrl_x = coords.get('x', 0)
                    ctrl_y = coords.get('y', 0)
                    ctrl_w = coords.get('width', 0)
                    ctrl_h = coords.get('height', 0)
                else:
                    # BoundingBox对象
                    try:
                        ctrl_x = coords.x if hasattr(coords, 'x') else 0
                        ctrl_y = coords.y if hasattr(coords, 'y') else 0
                        ctrl_w = coords.width if hasattr(coords, 'width') else 0
                        ctrl_h = coords.height if hasattr(coords, 'height') else 0
                    except:
                        ctrl_x = ctrl_y = ctrl_w = ctrl_h = 0
                
                print(f"   AT-SPI坐标: ({ctrl_x}, {ctrl_y}) 大小: {ctrl_w}×{ctrl_h}")
                
                if ctrl_w > 0 and ctrl_h > 0:
                    # 计算桌面绝对坐标
                    desktop_x = window_info['x'] + ctrl_x
                    desktop_y = window_info['y'] + ctrl_y
                    
                    print(f"   转换后桌面坐标: ({desktop_x}, {desktop_y})")
                    print(f"   与目标位置偏差: X={desktop_x - test_x}, Y={desktop_y - test_y}")
                    
                    # 显示高亮
                    print("🎨 显示精确高亮...")
                    success = simple_highlight(desktop_x, desktop_y, max(ctrl_w, 10), max(ctrl_h, 10))
                    
                    if success:
                        print("✅ 高亮显示成功！红色边框应该准确对准控件")
                        return True
                    else:
                        print("❌ 高亮显示失败")
                        return False
                else:
                    print("⚠️ 控件尺寸无效")
                    return False
            else:
                print("⚠️ 无坐标信息")
                return False
        else:
            print(f"❌ 识别失败 ({elapsed:.2f}s): {info}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("修复验证演示")
    print("=" * 80)
    print("🎯 目标: 验证AT-SPI应用匹配修复后的效果")
    print("🔧 修复: 添加了'at-spi测试界面' → 'hellobig'的映射规则")
    print()
    
    success = test_application_matching()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 修复验证成功！")
        print("✅ 应用匹配问题已解决")
        print("✅ 坐标转换算法正常工作")
        print("✅ 高亮显示功能正常")
        print("\n💡 现在可以准确识别hellobig应用中的控件并进行精确高亮了!")
    else:
        print("⚠️ 还需要进一步调试")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())