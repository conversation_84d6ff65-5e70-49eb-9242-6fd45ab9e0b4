#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标偏移诊断工具
分析鼠标坐标、AT-SPI坐标、高亮坐标之间的偏移关系
"""

import sys
import os
import subprocess
import time
import re
import pyatspi
from specific_control_detector import SpecificControlDetector


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


class CoordinateOffsetDiagnoser:
    """坐标偏移诊断器"""
    
    def __init__(self):
        """初始化诊断器"""
        self.detector = SpecificControlDetector()
        self.window_offset = self.detector.window_offset
        
        print("🔬 坐标偏移诊断工具")
        print("=" * 60)
        
        if self.window_offset:
            print(f"🪟 当前使用的窗口偏移: {self.window_offset}")
        else:
            print("❌ 无法获取窗口偏移")
            return
        
        # 获取更详细的窗口信息
        self.analyze_window_geometry()
    
    def analyze_window_geometry(self):
        """分析窗口几何信息"""
        print("\n📐 详细窗口几何分析:")
        print("-" * 40)
        
        try:
            # 获取wlcctrl信息
            wlc_info = self.get_wlcctrl_info()
            if wlc_info:
                print(f"🪟 wlcctrl报告:")
                print(f"   窗口位置: {wlc_info['position']}")
                print(f"   窗口尺寸: {wlc_info['size']}")
            
            # 获取AT-SPI信息
            atspi_info = self.get_atspi_window_info()
            if atspi_info:
                print(f"🔍 AT-SPI报告:")
                print(f"   窗口位置: {atspi_info['position']}")
                print(f"   窗口尺寸: {atspi_info['size']}")
            
            # 计算差异
            if wlc_info and atspi_info:
                pos_diff = (wlc_info['position'][0] - atspi_info['position'][0],
                           wlc_info['position'][1] - atspi_info['position'][1])
                size_diff = (wlc_info['size'][0] - atspi_info['size'][0],
                            wlc_info['size'][1] - atspi_info['size'][1])
                
                print(f"📊 差异分析:")
                print(f"   位置差异: {pos_diff}")
                print(f"   尺寸差异: {size_diff}")
                print(f"   可能的标题栏高度: {size_diff[1]}像素")
                
        except Exception as e:
            print(f"❌ 窗口几何分析失败: {e}")
    
    def get_wlcctrl_info(self):
        """获取wlcctrl窗口信息"""
        try:
            # 获取窗口列表
            result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
            if result.returncode != 0:
                return None
            
            # 查找hellobig窗口ID
            window_id = None
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                    if i > 0:
                        prev_line = lines[i-1]
                        if 'toplevel' in prev_line:
                            window_id = prev_line.split('"')[1]
                            break
            
            if not window_id:
                return None
            
            # 获取窗口几何信息
            result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                return None
            
            # 解析几何信息
            for line in result.stdout.strip().split('\n'):
                if 'geometry:' in line:
                    match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', line)
                    if match:
                        x, y, width, height = map(int, match.groups())
                        return {
                            'position': (x, y),
                            'size': (width, height)
                        }
            
            return None
            
        except Exception:
            return None
    
    def get_atspi_window_info(self):
        """获取AT-SPI窗口信息"""
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                if 'hellobig' in app_name.lower():
                    for j in range(app.childCount):
                        window = app.getChildAtIndex(j)
                        window_name = window.name or "unknown"
                        
                        if 'AT-SPI测试' in window_name or '测试主窗口' in window_name:
                            if hasattr(window, 'queryComponent'):
                                component = window.queryComponent()
                                if component:
                                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                    return {
                                        'position': (extents.x, extents.y),
                                        'size': (extents.width, extents.height)
                                    }
            
            return None
            
        except Exception:
            return None
    
    def diagnose_coordinate_at_mouse(self):
        """诊断当前鼠标位置的坐标转换"""
        mouse_pos = get_mouse_position()
        if mouse_pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return
        
        mouse_x, mouse_y = mouse_pos
        
        print(f"\n🖱️  鼠标位置诊断:")
        print("=" * 50)
        print(f"🖱️  鼠标屏幕坐标: ({mouse_x}, {mouse_y})")
        
        # 计算AT-SPI坐标
        if self.window_offset:
            atspi_x = mouse_x - self.window_offset[0]
            atspi_y = mouse_y - self.window_offset[1]
            print(f"🔍 转换为AT-SPI坐标: ({atspi_x}, {atspi_y})")
            print(f"   使用偏移: {self.window_offset}")
        else:
            atspi_x, atspi_y = mouse_x, mouse_y
            print(f"🔍 AT-SPI坐标 (无偏移): ({atspi_x}, {atspi_y})")
        
        # 查找控件
        print(f"\n🔍 在AT-SPI坐标 ({atspi_x}, {atspi_y}) 查找控件...")
        ctrl = self.detector.find_most_specific_control_at_point(mouse_x, mouse_y)
        
        if ctrl:
            info = self.detector.get_control_info(ctrl)
            
            print(f"✅ 找到控件: {info['name']} ({info['role']})")
            print(f"📍 控件AT-SPI位置: {info['position']}")
            print(f"📏 控件尺寸: {info['size']}")
            
            # 计算控件中心
            ctrl_center_x = info['position'][0] + info['size'][0] // 2
            ctrl_center_y = info['position'][1] + info['size'][1] // 2
            print(f"🎯 控件AT-SPI中心: ({ctrl_center_x}, {ctrl_center_y})")
            
            # 计算控件在屏幕上的位置
            if self.window_offset:
                ctrl_screen_x = info['position'][0] + self.window_offset[0]
                ctrl_screen_y = info['position'][1] + self.window_offset[1]
                ctrl_screen_center_x = ctrl_center_x + self.window_offset[0]
                ctrl_screen_center_y = ctrl_center_y + self.window_offset[1]
                
                print(f"🖥️  控件屏幕位置: ({ctrl_screen_x}, {ctrl_screen_y})")
                print(f"🎯 控件屏幕中心: ({ctrl_screen_center_x}, {ctrl_screen_center_y})")
            
            # 计算偏差
            print(f"\n📊 偏差分析:")
            print("-" * 30)
            
            # 鼠标与控件中心的偏差
            if self.window_offset:
                center_offset_x = mouse_x - ctrl_screen_center_x
                center_offset_y = mouse_y - ctrl_screen_center_y
                print(f"🖱️  鼠标相对控件中心偏差: ({center_offset_x}, {center_offset_y})")
                
                # 鼠标与控件边界的偏差
                edge_offset_x = mouse_x - ctrl_screen_x
                edge_offset_y = mouse_y - ctrl_screen_y
                print(f"📐 鼠标相对控件左上角偏差: ({edge_offset_x}, {edge_offset_y})")
                
                # 分析偏差原因
                if abs(center_offset_y) > 20:
                    print(f"⚠️  Y轴偏差较大 ({center_offset_y}像素)，可能原因:")
                    if center_offset_y < 0:
                        print(f"   - 鼠标在控件上方，可能是标题栏高度计算错误")
                    else:
                        print(f"   - 鼠标在控件下方，可能是窗口装饰高度问题")
                
                if abs(center_offset_x) > 20:
                    print(f"⚠️  X轴偏差较大 ({center_offset_x}像素)，可能原因:")
                    print(f"   - 窗口边框宽度计算错误")
            
            # 建议的修正偏移
            if self.window_offset:
                suggested_offset_x = self.window_offset[0] + center_offset_x
                suggested_offset_y = self.window_offset[1] + center_offset_y
                print(f"\n💡 建议的修正偏移: ({suggested_offset_x}, {suggested_offset_y})")
                print(f"   当前偏移: {self.window_offset}")
                print(f"   修正量: ({center_offset_x}, {center_offset_y})")
        else:
            print(f"❌ 未找到控件")
    
    def test_highlight_accuracy(self):
        """测试高亮准确性"""
        mouse_pos = get_mouse_position()
        if mouse_pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return
        
        mouse_x, mouse_y = mouse_pos
        
        print(f"\n✨ 高亮准确性测试:")
        print("=" * 40)
        print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        
        # 查找控件
        ctrl = self.detector.find_most_specific_control_at_point(mouse_x, mouse_y)
        
        if not ctrl:
            print("❌ 未找到控件，无法测试高亮")
            return
        
        info = self.detector.get_control_info(ctrl)
        print(f"🎯 目标控件: {info['name']} ({info['role']})")
        
        # 执行高亮并分析坐标
        print(f"✨ 执行高亮...")
        
        # 计算高亮坐标
        atspi_x, atspi_y = info['position']
        width, height = info['size']
        
        if self.window_offset:
            highlight_x = atspi_x + self.window_offset[0]
            highlight_y = atspi_y + self.window_offset[1]
        else:
            highlight_x = atspi_x
            highlight_y = atspi_y
        
        print(f"📍 高亮坐标: ({highlight_x}, {highlight_y}) {width}×{height}")
        print(f"🖱️  鼠标坐标: ({mouse_x}, {mouse_y})")
        
        # 计算鼠标与高亮区域的关系
        mouse_in_highlight_x = highlight_x <= mouse_x <= highlight_x + width
        mouse_in_highlight_y = highlight_y <= mouse_y <= highlight_y + height
        
        print(f"📊 鼠标是否在高亮区域内:")
        print(f"   X轴: {'✅' if mouse_in_highlight_x else '❌'} ({mouse_x} in [{highlight_x}, {highlight_x + width}])")
        print(f"   Y轴: {'✅' if mouse_in_highlight_y else '❌'} ({mouse_y} in [{highlight_y}, {highlight_y + height}])")
        
        if not (mouse_in_highlight_x and mouse_in_highlight_y):
            print(f"⚠️  鼠标不在高亮区域内，存在坐标偏差!")
            
            # 计算偏差
            if not mouse_in_highlight_x:
                if mouse_x < highlight_x:
                    x_offset = mouse_x - highlight_x
                    print(f"   X轴偏差: {x_offset}像素 (鼠标在高亮区域左侧)")
                else:
                    x_offset = mouse_x - (highlight_x + width)
                    print(f"   X轴偏差: {x_offset}像素 (鼠标在高亮区域右侧)")
            
            if not mouse_in_highlight_y:
                if mouse_y < highlight_y:
                    y_offset = mouse_y - highlight_y
                    print(f"   Y轴偏差: {y_offset}像素 (鼠标在高亮区域上方)")
                else:
                    y_offset = mouse_y - (highlight_y + height)
                    print(f"   Y轴偏差: {y_offset}像素 (鼠标在高亮区域下方)")
        
        # 执行实际高亮
        try:
            from ultimate_highlight import ultimate_highlight
            highlight_success = ultimate_highlight(
                highlight_x, highlight_y, width, height,
                duration=3, color='red', border_width=3
            )
            
            if highlight_success:
                print(f"✅ 高亮已执行，请观察高亮位置是否准确")
            else:
                print(f"❌ 高亮执行失败")
                
        except Exception as e:
            print(f"❌ 高亮异常: {e}")
    
    def interactive_diagnosis(self):
        """交互式诊断"""
        if not self.window_offset:
            print("❌ 无法进行诊断，未获取到窗口偏移")
            return
        
        print(f"\n🎮 交互式坐标诊断:")
        print("=" * 40)
        print("1. 诊断当前鼠标位置")
        print("2. 测试高亮准确性")
        print("3. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-3): ").strip()
                
                if choice == '1':
                    self.diagnose_coordinate_at_mouse()
                elif choice == '2':
                    self.test_highlight_accuracy()
                elif choice == '3':
                    print("👋 退出诊断")
                    break
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n👋 退出诊断")
                break


def main():
    """主函数"""
    print("🔬 坐标偏移诊断工具")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        diagnoser = CoordinateOffsetDiagnoser()
        diagnoser.interactive_diagnosis()
        
    except Exception as e:
        print(f"❌ 诊断工具启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
