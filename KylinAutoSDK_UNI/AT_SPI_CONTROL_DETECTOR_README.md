# AT-SPI控件检测和高亮库

基于AT-SPI接口的通用图形界面控件获取库，提供坐标到控件的映射和高亮显示功能。

## 🎯 主要特性

- ✅ **通用坐标检测**: 传入桌面坐标，获取该位置的具体控件信息
- ✅ **自适应窗口管理**: 自动根据鼠标位置获取对应的桌面应用窗口
- ✅ **坐标转换**: 自动处理AT-SPI中的坐标转换，确保坐标准确性
- ✅ **控件高亮**: 支持多种样式的控件边框高亮显示
- ✅ **跨桌面兼容**: 支持X11和Wayland环境（部分功能）
- ✅ **详细信息获取**: 提供控件的完整属性信息

## 📦 依赖安装

### 系统依赖
```bash
# Ubuntu/Debian
sudo apt-get install python3-pyatspi
sudo apt-get install libwnck-3-0 gir1.2-wnck-3.0
sudo apt-get install xdotool  # 用于鼠标位置获取

# 其他系统
# Fedora: sudo dnf install python3-pyatspi
# Arch: sudo pacman -S python-atspi
```

### Python依赖
```bash
pip install -r requirements.txt
```

requirements.txt包含:
- PyGObject>=3.42.0
- python-xlib>=0.31
- pynput>=1.7.6
- opencv-python>=4.5.0
- pyatspi>=2.38.0
- PyQt5>=5.15.0

## 🚀 快速开始

### 1. 基本使用

```python
from at_spi_control_detector import ATSPIControlDetector

# 创建检测器实例
detector = ATSPIControlDetector()

# 检测指定坐标的控件
result = detector.get_control_at_point(500, 300)

if result['success']:
    print(f"找到控件: {result['position']}")
    
    # 高亮显示控件
    detector.highlight_control_at_point(500, 300, duration=2, color='red')
else:
    print(f"未找到控件: {result['message']}")
```

### 2. 获取详细控件信息

```python
# 打印详细控件信息
detector.print_control_info(500, 300)

# 或获取详细信息字典
detailed_info = detector.get_detailed_control_info(500, 300)
if detailed_info['success']:
    basic_info = detailed_info['basic_info']
    window_info = detailed_info['window_info']
    print(f"控件名称: {basic_info['name']}")
    print(f"控件角色: {basic_info['role']}")
    print(f"所属窗口: {window_info['window_name']}")
```

### 3. 便捷函数

```python
from at_spi_control_detector import (
    detect_control_at_point,
    highlight_control_at_point,
    print_control_info_at_point
)

# 快速检测
result = detect_control_at_point(500, 300)

# 快速高亮
highlight_control_at_point(500, 300, duration=2, color='blue')

# 快速打印信息
print_control_info_at_point(500, 300)
```

## 🖱️ 交互式测试工具

### 1. 鼠标悬停检测器

实时监控鼠标位置，支持快捷键操作：

```bash
python3 mouse_hover_control_detector.py
```

**快捷键说明:**
- `Space` - 检测并高亮当前鼠标位置的控件
- `Enter` - 打印当前鼠标位置的详细控件信息
- `h` - 高亮最后检测到的控件
- `a` - 切换自动高亮模式
- `1-5` - 切换不同的高亮颜色
- `q/ESC` - 退出程序

### 2. 简单测试工具

基本功能测试和演示：

```bash
python3 simple_control_test.py
```

支持:
- 基本功能测试
- 多位置检测测试
- 交互式手动测试

## 🎨 高亮功能详解

### 高亮样式配置

```python
# 基本高亮
detector.highlight_control_at_point(x, y, 
                                   duration=2,      # 持续时间（秒）
                                   color='red',     # 颜色
                                   border_width=2)  # 边框宽度

# 支持的颜色
colors = ['red', 'blue', 'green', 'yellow', 'purple', 'cyan', 'orange']

# 不同粗细的边框
thin_border = 1      # 超细边框
normal_border = 2    # 标准边框  
thick_border = 3     # 粗边框
```

### 高亮特点

- ✅ **完全透明内部**: 中间区域100%透明，不遮挡内容
- ✅ **纯边框线条**: 只显示边框，无窗口装饰
- ✅ **四个独立窗口**: 上下左右独立边框，精确覆盖
- ✅ **绕过窗口管理器**: 使用override_redirect避免干扰
- ✅ **多种颜色样式**: 支持多种预定义颜色

## 📋 API 文档

### ATSPIControlDetector 类

#### 主要方法

**get_control_at_point(x, y, include_details=True)**
- 根据坐标获取控件信息
- 参数:
  - `x, y`: 桌面坐标
  - `include_details`: 是否包含详细信息
- 返回: 包含success、message、control_info、position的字典

**highlight_control_at_point(x, y, duration=2, color='red', border_width=2)**
- 检测并高亮指定坐标处的控件
- 参数:
  - `x, y`: 桌面坐标
  - `duration`: 高亮持续时间（秒）
  - `color`: 高亮颜色
  - `border_width`: 边框宽度
- 返回: 包含success、message、highlighted、control_position的字典

**get_detailed_control_info(x, y)**
- 获取详细的控件信息
- 返回: 包含basic_info、window_info、states、actions等的详细字典

**print_control_info(x, y)**
- 打印控件信息到控制台
- 格式化输出控件的各种属性信息

**highlight_last_detected_control(duration=2, color='blue', border_width=2)**
- 高亮最后检测到的控件
- 适用于重复高亮同一控件的场景

### 控件信息结构

检测到的控件信息包含以下字段:

```python
{
    'success': True/False,
    'message': '状态信息',
    'control_info': {
        'Name': '控件名称',
        'Description': '控件描述',
        'Rolename': '控件角色',
        'Coords': {'x': x, 'y': y, 'width': w, 'height': h},
        'States': ['状态列表'],
        'Actions': ['动作列表'],
        'WindowName': '窗口名称',
        'ProcessName': '进程名称',
        'ParentCount': 父级数量,
        'ChildCount': 子级数量,
        # ... 更多属性
    },
    'position': (x, y, width, height)
}
```

## 🔧 高级用法

### 1. 批量控件检测

```python
detector = ATSPIControlDetector()

# 检测多个位置
positions = [(100, 100), (200, 200), (300, 300)]
results = []

for x, y in positions:
    result = detector.get_control_at_point(x, y)
    results.append(result)
    
    if result['success']:
        # 依次高亮，使用不同颜色
        colors = ['red', 'blue', 'green']
        color = colors[len(results) % len(colors)]
        detector.highlight_control_at_point(x, y, color=color, duration=1)
```

### 2. 控件属性过滤

```python
def find_clickable_controls(detector, positions):
    """查找可点击的控件"""
    clickable_controls = []
    
    for x, y in positions:
        detailed_info = detector.get_detailed_control_info(x, y)
        if detailed_info['success']:
            states = detailed_info.get('states', [])
            if 'focusable' in states or 'clickable' in states:
                clickable_controls.append((x, y, detailed_info))
    
    return clickable_controls
```

### 3. 自定义高亮样式

```python
# 使用底层高亮函数
from ultimate_highlight import ultimate_highlight

# 自定义高亮参数
ultimate_highlight(x, y, width, height,
                  duration=5,        # 长时间高亮
                  color='purple',    # 紫色
                  border_width=1)    # 细边框
```

## 🐛 故障排除

### 常见问题

1. **"未检测到DISPLAY环境变量"**
   ```bash
   export DISPLAY=:0
   ```

2. **"pyatspi导入失败"**
   ```bash
   sudo apt-get install python3-pyatspi
   ```

3. **"高亮不显示"**
   - 检查X11环境是否正常
   - 确认运行在图形界面中
   - 尝试不同的高亮颜色

4. **"控件检测不准确"**
   - 等待应用完全加载
   - 检查坐标是否在可见区域内
   - 尝试点击控件使其获得焦点

### 环境兼容性

- **X11**: 完全支持所有功能
- **Wayland**: 基本控件检测支持，高亮功能可能受限
- **远程桌面**: 需要正确设置DISPLAY变量

## 📝 开发说明

### 项目结构
```
KylinAutoSDK_UNI/
├── at_spi_control_detector.py      # 主接口库
├── mouse_hover_control_detector.py # 鼠标悬停检测工具
├── simple_control_test.py          # 简单测试工具
├── ultimate_highlight.py           # 高亮功能实现
├── src/uni_sdk/                    # 底层UNI SDK
├── requirements.txt                # 依赖配置
└── AT_SPI_CONTROL_DETECTOR_README.md # 本文档
```

### 扩展开发

基于现有框架，可以扩展以下功能:
- 控件操作自动化
- 控件识别和分类
- 截图和图像处理
- 测试用例生成
- 无障碍访问支持

## 📄 许可证

本项目基于现有的KylinAutoSDK_UNI项目开发，遵循相同的许可证条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进本项目。

## 📞 支持

如遇问题，请查看故障排除部分或提交Issue获取帮助。