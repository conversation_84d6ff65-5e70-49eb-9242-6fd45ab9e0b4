#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
增强控件识别测试脚本
测试扩展后的控件识别范围
"""

import sys
import os
import subprocess
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>in<PERSON>utoSDK_UNI/src')

def get_current_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'],
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return 1000, 500
    except:
        return 1000, 500

def test_enhanced_control_detection():
    """测试增强后的控件识别"""
    print("🧪 增强控件识别测试")
    print("=" * 50)
    print("💡 修复版本：每次测试前重新获取鼠标位置")
    print()

    try:
        from UNI_new import UNI

        # 创建UNI实例
        uni = UNI()

        # 测试不同的搜索模式
        print("🔍 测试1: 快速搜索模式")
        # 实时获取鼠标位置
        mouse_x, mouse_y = get_current_mouse_position()
        print(f"   📍 当前鼠标位置: ({mouse_x}, {mouse_y})")

        control_data1, info1 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=True, highlight=False)

        if control_data1:
            print(f"   ✅ 找到控件: {control_data1.get('Name', 'N/A')} ({control_data1.get('Rolename', 'N/A')})")
        else:
            print(f"   ❌ 未找到控件: {info1}")

        print()
        print("🔍 测试2: 完整搜索模式")
        # 再次实时获取鼠标位置
        mouse_x, mouse_y = get_current_mouse_position()
        print(f"   📍 当前鼠标位置: ({mouse_x}, {mouse_y})")

        control_data2, info2 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)

        if control_data2:
            print(f"   ✅ 找到控件: {control_data2.get('Name', 'N/A')} ({control_data2.get('Rolename', 'N/A')})")

            # 显示详细信息
            print("   📊 控件详细信息:")
            print(f"      类型: {control_data2.get('Rolename', 'N/A')}")
            print(f"      名称: {control_data2.get('Name', 'N/A')}")
            print(f"      描述: {control_data2.get('Description', 'N/A')}")
            print(f"      状态: {', '.join(control_data2.get('States', [])[:3])}")
            print(f"      动作: {', '.join(control_data2.get('Actions', [])[:3])}")

        else:
            print(f"   ❌ 未找到控件: {info2}")

        print()
        print("🔍 测试3: 带高亮的控件识别")
        # 第三次实时获取鼠标位置
        mouse_x, mouse_y = get_current_mouse_position()
        print(f"   📍 当前鼠标位置: ({mouse_x}, {mouse_y})")
        print("   🎯 立即执行高亮识别...")

        control_data3, info3 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)

        if control_data3:
            print(f"   ✅ 找到控件并高亮: {control_data3.get('Name', 'N/A')} ({control_data3.get('Rolename', 'N/A')})")
            print(f"   🎨 高亮位置: ({mouse_x}, {mouse_y})")
            print("   🎨 高亮效果: 40像素偏移修正 + 无边框显示")

            # 显示坐标信息
            coords = control_data3.get('Coords', {})
            if coords:
                print(f"   📏 控件实际位置: ({coords.get('x')}, {coords.get('y')})")
                print(f"   📏 控件尺寸: {coords.get('width')} x {coords.get('height')}")
        else:
            print(f"   ❌ 未找到控件: {info3}")
        
        print()
        print("=" * 50)
        print("🎉 增强控件识别测试完成!")
        
        if control_data2:
            print("✅ 控件识别增强成功!")
            print(f"   识别到控件类型: {control_data2.get('Rolename', 'N/A')}")
            if control_data2.get('Rolename', '').lower() not in ['push button', 'button']:
                print("🎯 成功! 识别到了pushbutton以外的控件类型!")
        else:
            print("⚠️ 在当前位置未找到控件，请移动鼠标到其他控件上重试")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_enhanced_control_detection()
