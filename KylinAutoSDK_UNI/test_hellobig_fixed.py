#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
hellobig应用控件识别测试（修复版）
使用原版UNI进行稳定的控件识别和高亮
"""

import sys
import os
import time
import subprocess
from collections import defaultdict

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def find_hellobig_window():
    """查找hellobig应用窗口"""
    try:
        from uni_sdk.utils.helpers import get_windows_by_wlcctrl
        
        windows = get_windows_by_wlcctrl()
        
        for title, geometry in windows.items():
            if 'hellobig' in title.lower():
                print(f"✅ 找到hellobig窗口: {title}")
                print(f"   窗口位置: ({geometry['x']}, {geometry['y']})")
                print(f"   窗口大小: {geometry['width']} × {geometry['height']}")
                return {
                    'title': title,
                    'geometry': geometry
                }
        
        print("❌ 未找到hellobig应用窗口")
        return None
        
    except Exception as e:
        print(f"❌ 查找窗口失败: {e}")
        return None

def test_controls_at_points():
    """在指定点测试控件识别"""
    print("🔍 测试指定位置的控件识别")
    print("=" * 50)
    
    try:
        # 使用原版UNI以确保稳定性
        from UNI_new import UNI
        
        uni = UNI()
        
        # 查找hellobig窗口
        window_info = find_hellobig_window()
        if not window_info:
            return
        
        geometry = window_info['geometry']
        
        # 定义一些关键测试点（相对于窗口的典型UI位置）
        test_points = [
            # 标题栏区域
            (geometry['x'] + 100, geometry['y'] + 30),
            (geometry['x'] + 200, geometry['y'] + 30),
            
            # 菜单栏区域  
            (geometry['x'] + 50, geometry['y'] + 60),
            (geometry['x'] + 150, geometry['y'] + 60),
            (geometry['x'] + 250, geometry['y'] + 60),
            
            # 工具栏区域
            (geometry['x'] + 100, geometry['y'] + 100),
            (geometry['x'] + 200, geometry['y'] + 100),
            
            # 主内容区域
            (geometry['x'] + 400, geometry['y'] + 300),
            (geometry['x'] + 600, geometry['y'] + 400),
            (geometry['x'] + 800, geometry['y'] + 500),
            
            # 状态栏区域
            (geometry['x'] + 100, geometry['y'] + geometry['height'] - 50),
            (geometry['x'] + 300, geometry['y'] + geometry['height'] - 50),
        ]
        
        print(f"📋 测试 {len(test_points)} 个关键位置")
        
        controls_found = []
        
        for i, (x, y) in enumerate(test_points):
            print(f"\n📍 测试点 {i+1}/{len(test_points)}: ({x}, {y})")
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(x, y, quick=False, highlight=True)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    print(f"   ✅ 找到控件 ({elapsed:.2f}s): {name} ({role})")
                    if coords:
                        print(f"      坐标: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                        print(f"      大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
                    
                    print("   🎨 已显示红色高亮边框")
                    
                    controls_found.append({
                        'name': name,
                        'role': role,
                        'coords': coords,
                        'test_point': (x, y),
                        'time': elapsed
                    })
                    
                    time.sleep(3)  # 等待观察高亮效果
                    
                else:
                    print(f"   ❌ 未找到控件 ({elapsed:.2f}s): {info}")
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
        
        # 统计结果
        print(f"\n📊 测试结果统计")
        print("=" * 40)
        print(f"总共找到 {len(controls_found)} 个控件")
        
        if controls_found:
            avg_time = sum(c['time'] for c in controls_found) / len(controls_found)
            print(f"平均识别时间: {avg_time:.2f}s")
            
            # 按控件类型分组
            by_type = defaultdict(int)
            for control in controls_found:
                by_type[control['role']] += 1
            
            print("控件类型分布:")
            for role, count in sorted(by_type.items()):
                print(f"  - {role}: {count} 个")
        
        return controls_found
        
    except Exception as e:
        print(f"❌ 控件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def interactive_test():
    """交互式测试 - 手动指定坐标"""
    print(f"\n🖱️ 交互式控件识别测试")
    print("=" * 50)
    print("请输入要测试的坐标，格式: x,y")
    print("或者输入 'mouse' 使用当前鼠标位置")
    print("输入 'quit' 退出")
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        test_count = 0
        
        while True:
            try:
                user_input = input("\n请输入坐标 (x,y) 或 'mouse' 或 'quit': ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'mouse':
                    mouse_x, mouse_y = get_mouse_position()
                    if mouse_x is None:
                        print("❌ 无法获取鼠标位置")
                        continue
                    test_x, test_y = mouse_x, mouse_y
                else:
                    # 解析坐标输入
                    coords = user_input.split(',')
                    if len(coords) != 2:
                        print("❌ 格式错误，请输入: x,y")
                        continue
                    test_x = int(coords[0].strip())
                    test_y = int(coords[1].strip())
                
                test_count += 1
                print(f"\n🔍 第{test_count}次测试 - 坐标: ({test_x}, {test_y})")
                
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(
                    test_x, test_y,
                    quick=False,
                    highlight=True  # 启用高亮
                )
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    print(f"✅ 识别成功 ({elapsed:.2f}s)")
                    print(f"   控件名称: {name}")
                    print(f"   控件类型: {role}")
                    
                    if coords:
                        print(f"   控件位置: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                        print(f"   控件大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
                    
                    print("🎨 已显示红色高亮边框，将持续3秒")
                    
                else:
                    print(f"❌ 未找到控件 ({elapsed:.2f}s)")
                    print(f"   详情: {info}")
                
            except KeyboardInterrupt:
                print("\n中断测试")
                break
            except ValueError:
                print("❌ 坐标格式错误，请输入数字")
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        print(f"\n📊 交互测试统计: 共进行了 {test_count} 次测试")
        
    except Exception as e:
        print(f"❌ 交互测试失败: {e}")

def demonstrate_control_types():
    """演示不同类型控件的识别"""
    print(f"\n🎭 控件类型识别演示")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        
        # 查找hellobig窗口
        window_info = find_hellobig_window()
        if not window_info:
            return
        
        geometry = window_info['geometry']
        
        # 预定义一些可能的控件位置（基于Qt Creator的典型布局）
        demo_points = [
            {
                'name': '菜单项',
                'description': '文件菜单',
                'point': (geometry['x'] + 45, geometry['y'] + 85),
                'expected_type': 'menu item'
            },
            {
                'name': '按钮',
                'description': '工具栏按钮',
                'point': (geometry['x'] + 150, geometry['y'] + 120),
                'expected_type': 'push button'
            },
            {
                'name': '窗口框架',
                'description': '主窗口',
                'point': (geometry['x'] + 500, geometry['y'] + 200),
                'expected_type': 'frame'
            },
            {
                'name': '文本区域',
                'description': '编辑区域',
                'point': (geometry['x'] + 800, geometry['y'] + 400),
                'expected_type': 'text'
            },
        ]
        
        for i, demo in enumerate(demo_points):
            print(f"\n🎯 演示 {i+1}/{len(demo_points)}: {demo['name']}")
            print(f"   位置: {demo['point']}")
            print(f"   预期类型: {demo['expected_type']}")
            print(f"   描述: {demo['description']}")
            
            x, y = demo['point']
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(x, y, quick=False, highlight=True)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    print(f"   ✅ 识别成功 ({elapsed:.2f}s)")
                    print(f"      实际名称: {name}")
                    print(f"      实际类型: {role}")
                    
                    if coords:
                        print(f"      位置: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                        print(f"      大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
                    
                    # 检查是否匹配预期类型
                    if demo['expected_type'].lower() in role.lower():
                        print(f"   🎯 类型匹配预期!")
                    else:
                        print(f"   ⚠️ 类型不匹配预期，但仍然识别成功")
                    
                    print("   🎨 红色高亮边框已显示")
                    
                    # 等待观察
                    time.sleep(4)
                    
                else:
                    print(f"   ❌ 识别失败 ({elapsed:.2f}s): {info}")
                
            except Exception as e:
                print(f"   ❌ 演示失败: {e}")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def main():
    """主函数"""
    print("hellobig应用控件识别测试（修复版）")
    print("=" * 60)
    print("🎯 目标: 快速准确识别各类控件并进行高亮显示")
    print("📝 使用原版UNI确保稳定性")
    print()
    
    # 检查hellobig应用是否运行
    window_info = find_hellobig_window()
    if not window_info:
        print("❌ 请确保hellobig应用正在运行")
        print("💡 提示: 请打开Qt Creator或其他包含'hellobig'的应用")
        return 1
    
    print("✅ hellobig应用已就绪，开始测试")
    print()
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 自动测试关键位置")
    print("2. 控件类型识别演示")
    print("3. 交互式手动测试")
    print("4. 全部测试")
    
    try:
        choice = input("请输入选择 (1-4): ").strip()
        print()
        
        if choice == '1':
            test_controls_at_points()
        elif choice == '2':
            demonstrate_control_types()
        elif choice == '3':
            interactive_test()
        elif choice == '4':
            print("🔄 执行全部测试...")
            test_controls_at_points()
            demonstrate_control_types()
            interactive_test()
        else:
            print("❌ 无效选择，执行默认测试")
            test_controls_at_points()
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 hellobig控件识别测试完成!")
    print("💡 提示: 测试过程中的红色边框表示成功识别的控件")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())