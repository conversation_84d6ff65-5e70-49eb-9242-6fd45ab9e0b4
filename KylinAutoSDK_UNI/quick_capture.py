#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
快速控件捕获工具
模拟X11环境下的Ctrl+点击功能，在Wayland环境下快速捕获控件
支持全局键盘监听功能
"""

import sys
import os
import time
import json
import threading
import signal
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')

from universal_offset_detector import UniversalOffsetDetector, get_mouse_position

# 尝试导入全局键盘监听库
try:
    import pynput
    from pynput import keyboard
    GLOBAL_KEYBOARD_AVAILABLE = True
except ImportError:
    GLOBAL_KEYBOARD_AVAILABLE = False
    print("⚠️  pynput未安装，全局键盘监听功能不可用")
    print("   安装方法: pip install pynput")


def quick_capture_control(output_format='original', highlight_duration=3):
    """
    快速捕获当前鼠标位置的控件
    
    Args:
        output_format: 输出格式 ('simple' 或 'original')
        highlight_duration: 高亮持续时间（秒）
    
    Returns:
        bool: 是否成功捕获
    """
    print("🎯 快速控件捕获工具")
    print("=" * 50)
    
    # 获取当前鼠标位置
    mouse_pos = get_mouse_position()
    if mouse_pos == (None, None):
        print("❌ 无法获取鼠标位置")
        return False
    
    mouse_x, mouse_y = mouse_pos
    print(f"🖱️  当前鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 创建检测器
    detector = UniversalOffsetDetector()
    
    # 执行检测
    print("🔍 正在检测控件...")
    success = detector.detect_control_at_mouse(output_format=output_format)
    
    if success:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        print("✅ 控件捕获成功!")
        print(f"⏰ 捕获时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if output_format == 'original':
            print(f"📄 详细信息已保存到: detected_control_info.txt")
            
            # 额外保存带时间戳的副本
            try:
                with open('detected_control_info.txt', 'r', encoding='utf-8') as f:
                    data = f.read()
                
                backup_filename = f"captured_control_{timestamp}.json"
                with open(backup_filename, 'w', encoding='utf-8') as f:
                    f.write(data)
                
                print(f"💾 备份副本: {backup_filename}")
            except Exception as e:
                print(f"⚠️  保存备份失败: {e}")
        
        print(f"✨ 高亮显示 {highlight_duration} 秒")
        return True
    else:
        print("❌ 控件捕获失败")
        return False


# 移除轮询捕获类，专注于直接的交互式监控

# 轮询相关功能已移除，专注于直接的交互式监控


class GlobalKeyboardCapture:
    """全局键盘监听捕获器（在Wayland下可能不工作）"""

    def __init__(self, output_format='original', highlight_duration=3):
        """
        初始化全局键盘捕获器

        Args:
            output_format: 输出格式
            highlight_duration: 高亮持续时间
        """
        self.output_format = output_format
        self.highlight_duration = highlight_duration
        self.running = False
        self.listener = None
        self.capture_count = 0

        if not GLOBAL_KEYBOARD_AVAILABLE:
            raise ImportError("pynput库未安装，无法使用全局键盘监听功能")

    def start_global_listening(self):
        """启动全局键盘监听"""
        if self.running:
            print("⚠️  全局监听已在运行中")
            return

        self.running = True
        print("🌊 全局键盘监听模式 (可能在Wayland下不工作)")
        print("=" * 50)
        print("⚠️  注意: 在Wayland环境下，全局键盘监听可能受限")
        print("💡 建议使用: --mode polling 或设置系统快捷键")
        print("=" * 50)
        print("🎯 功能说明:")
        print("   - 将鼠标移动到目标控件上")
        print("   - 按 Enter 键全局捕获控件")
        print("   - 按 Esc 键退出监听")
        print("   - 按 Ctrl+C 也可退出")
        print("=" * 50)
        print("🚀 尝试启动全局键盘监听...")
        print()

        try:
            # 启动键盘监听器
            self.listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self.listener.start()

            print("✅ 全局键盘监听已启动 (如果在Wayland下不工作，请使用其他模式)")

            # 保持主线程运行
            while self.running and self.listener.running:
                time.sleep(0.1)
        except Exception as e:
            print(f"❌ 全局键盘监听启动失败: {e}")
            print("💡 建议使用: python3 quick_capture.py --mode polling")
        finally:
            self.stop_global_listening()

    def stop_global_listening(self):
        """停止全局键盘监听"""
        self.running = False
        if self.listener:
            self.listener.stop()
        print(f"\n🛑 全局键盘监听已停止")
        print(f"📊 本次会话共捕获 {self.capture_count} 个控件")

    def _on_key_press(self, key):
        """按键按下事件处理"""
        try:
            if key == keyboard.Key.enter:
                # Enter键触发捕获
                self._trigger_capture()
            elif key == keyboard.Key.esc:
                # Esc键退出
                print("\n🔚 检测到 Esc 键，正在退出...")
                self.stop_global_listening()
        except Exception as e:
            print(f"❌ 按键处理错误: {e}")

    def _on_key_release(self, key):
        """按键释放事件处理（可选）"""
        pass

    def _trigger_capture(self):
        """触发控件捕获"""
        try:
            self.capture_count += 1
            timestamp = time.strftime("%H:%M:%S")

            print(f"\n[{timestamp}] 🎯 第 {self.capture_count} 次捕获:")
            print("=" * 40)

            success = quick_capture_control(
                output_format=self.output_format,
                highlight_duration=self.highlight_duration
            )

            if success:
                print(f"✅ 捕获成功! 继续移动鼠标到下一个控件...")
            else:
                print(f"❌ 捕获失败，请重试...")

            print("💡 按 Enter 继续捕获，按 Esc 退出")
            print()

        except Exception as e:
            print(f"❌ 捕获过程错误: {e}")


def interactive_capture():
    """交互式捕获模式"""
    print("🌊 交互式控件捕获")
    print("=" * 50)
    print("💡 使用说明:")
    print("   1. 将鼠标移动到目标控件上")
    print("   2. 按 Enter 键捕获控件")
    print("   3. 输入 'q' 或 'quit' 退出")
    print("=" * 50)

    while True:
        try:
            print("\n📍 请将鼠标移动到目标控件上，然后按 Enter 键...")
            user_input = input(">>> ").strip().lower()

            if user_input in ['q', 'quit', 'exit']:
                print("👋 再见!")
                break
            elif user_input == "":
                # Enter键，执行捕获
                print()
                success = quick_capture_control(output_format='original', highlight_duration=2)
                if success:
                    print("\n🎉 捕获完成! 继续移动鼠标到下一个控件...")
                else:
                    print("\n❌ 捕获失败，请重试...")
            else:
                print("❓ 未知命令，按 Enter 捕获控件，输入 'q' 退出")

        except (EOFError, KeyboardInterrupt):
            print("\n👋 程序退出")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")


def global_keyboard_capture(output_format='original', highlight_duration=3):
    """全局键盘监听捕获模式（Wayland下可能不工作）"""
    try:
        capture = GlobalKeyboardCapture(
            output_format=output_format,
            highlight_duration=highlight_duration
        )

        # 设置信号处理
        def signal_handler(sig, frame):
            print(f"\n🛑 收到信号 {sig}，正在退出...")
            capture.stop_global_listening()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 启动全局监听
        capture.start_global_listening()

    except ImportError as e:
        print(f"❌ 全局键盘监听功能不可用: {e}")
        print("💡 请安装 pynput 库: sudo apt install python3-pynput")
        print("💡 或使用其他模式: --mode polling")
    except Exception as e:
        print(f"❌ 全局键盘监听启动失败: {e}")
        print("💡 在Wayland环境下，建议使用: --mode polling")


# 轮询捕获功能已移除


def show_wayland_solutions():
    """显示Wayland环境下的解决方案"""
    print("🌊 Wayland环境下的控件捕获解决方案")
    print("=" * 60)
    print()
    print("❌ 问题: 全局键盘监听在Wayland下受限")
    print()
    print("✅ 解决方案:")
    print()
    print("1. 🎯 交互式连续监控 (推荐)")
    print("   python3 interactive_monitor.py --interval 0.5")
    print("   自动监控鼠标位置，显示交互式确认按钮")
    print()
    print("2. 🔧 系统快捷键 (最佳)")
    print("   python3 hotkey_capture.py --all")
    print("   设置系统快捷键，如 Ctrl+Alt+C")
    print()
    print("3. 🎮 交互式模式")
    print("   python3 quick_capture.py --mode interactive")
    print("   在终端中按Enter键捕获")
    print()
    print("4. 📱 应用程序快捷方式")
    print("   python3 hotkey_capture.py --desktop")
    print("   创建桌面应用程序")
    print()
    print("5. 🔄 连续监控模式")
    print("   python3 universal_offset_detector.py --mode monitor")
    print("   自动监控鼠标位置的控件")
    print()
    print("💡 推荐使用方案1，它最接近X11的全局监听体验!")
    print("=" * 60)


def batch_capture_demo():
    """批量捕获演示"""
    print("🎯 批量捕获演示")
    print("=" * 50)
    print("💡 将在5秒后开始，每3秒捕获一次当前鼠标位置的控件")
    print("   请在这期间移动鼠标到不同的控件上")
    print("   按 Ctrl+C 可随时停止")
    
    try:
        # 倒计时
        for i in range(5, 0, -1):
            print(f"⏰ {i}秒后开始...")
            time.sleep(1)
        
        print("\n🚀 开始批量捕获!")
        
        capture_count = 0
        while capture_count < 5:  # 最多捕获5次
            capture_count += 1
            print(f"\n📸 第 {capture_count} 次捕获:")
            
            success = quick_capture_control(output_format='simple', highlight_duration=1)
            
            if capture_count < 5:
                print("⏳ 3秒后进行下一次捕获...")
                time.sleep(3)
        
        print(f"\n🎉 批量捕获完成! 共捕获 {capture_count} 次")
        
    except KeyboardInterrupt:
        print(f"\n🛑 用户中断，已完成 {capture_count} 次捕获")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='快速控件捕获工具 - Wayland环境优化版')
    parser.add_argument('--mode', choices=['quick', 'interactive', 'batch', 'global', 'help'], default='quick',
                       help='运行模式: quick (快速单次), interactive (交互式), batch (批量演示), global (全局键盘监听), help (显示解决方案)')
    parser.add_argument('--format', choices=['simple', 'original'], default='original',
                       help='输出格式: simple (简单) 或 original (完整)')
    parser.add_argument('--duration', type=float, default=3,
                       help='高亮持续时间（秒），默认3秒')

    args = parser.parse_args()

    if args.mode == 'help':
        show_wayland_solutions()
        return

    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)

    try:
        if args.mode == 'quick':
            # 快速单次捕获
            quick_capture_control(output_format=args.format, highlight_duration=args.duration)

        elif args.mode == 'interactive':
            # 交互式捕获
            interactive_capture()

        elif args.mode == 'batch':
            # 批量捕获演示
            batch_capture_demo()

        elif args.mode == 'global':
            # 全局键盘监听捕获（可能在Wayland下不工作）
            print("⚠️  注意: 全局键盘监听在Wayland环境下可能不工作")
            print("💡 推荐使用: python3 interactive_monitor.py")
            global_keyboard_capture(output_format=args.format, highlight_duration=args.duration)

    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
