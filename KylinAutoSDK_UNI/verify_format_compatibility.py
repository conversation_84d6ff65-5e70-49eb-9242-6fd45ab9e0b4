#!/usr/bin/env python3
"""
验证 universal_offset_detector.py 输出格式与原始格式的兼容性
"""

import json
import sys

def verify_format_compatibility():
    """验证格式兼容性"""
    print("🔍 验证格式兼容性")
    print("=" * 60)
    
    try:
        # 加载原始格式示例
        with open('control_info.txt', 'r', encoding='utf-8') as f:
            original_data = json.loads(f.read().strip())
        
        print("✅ 成功加载原始格式示例 (control_info.txt)")
        
        # 加载检测到的数据
        with open('detected_control_info.txt', 'r', encoding='utf-8') as f:
            detected_data = json.loads(f.read().strip())
        
        print("✅ 成功加载检测到的数据 (detected_control_info.txt)")
        
        print(f"\n📊 详细格式对比:")
        print("-" * 50)
        
        # 检查顶层字段
        original_top_fields = set(original_data.keys())
        detected_top_fields = set(detected_data.keys())
        
        print(f"🔸 顶层字段对比:")
        print(f"   原始格式: {sorted(original_top_fields)}")
        print(f"   检测格式: {sorted(detected_top_fields)}")
        
        if original_top_fields == detected_top_fields:
            print(f"   ✅ 顶层字段完全匹配")
        else:
            missing = original_top_fields - detected_top_fields
            extra = detected_top_fields - original_top_fields
            if missing:
                print(f"   ❌ 缺失字段: {missing}")
            if extra:
                print(f"   ⚠️  额外字段: {extra}")
        
        # 检查 datamap 字段
        if 'datamap' in original_data and 'datamap' in detected_data:
            original_datamap_fields = set(original_data['datamap'].keys())
            detected_datamap_fields = set(detected_data['datamap'].keys())
            
            print(f"\n🔸 datamap 字段对比:")
            print(f"   原始格式: {sorted(original_datamap_fields)}")
            print(f"   检测格式: {sorted(detected_datamap_fields)}")
            
            if original_datamap_fields == detected_datamap_fields:
                print(f"   ✅ datamap 字段完全匹配")
            else:
                missing_dm = original_datamap_fields - detected_datamap_fields
                extra_dm = detected_datamap_fields - original_datamap_fields
                if missing_dm:
                    print(f"   ❌ 缺失字段: {missing_dm}")
                if extra_dm:
                    print(f"   ⚠️  额外字段: {extra_dm}")
        
        # 检查数据类型
        print(f"\n🔸 数据类型对比:")
        type_match = True
        
        for key in original_data.keys():
            if key in detected_data:
                orig_type = type(original_data[key]).__name__
                detected_type = type(detected_data[key]).__name__
                if orig_type == detected_type:
                    print(f"   ✅ {key}: {orig_type}")
                else:
                    print(f"   ❌ {key}: {orig_type} vs {detected_type}")
                    type_match = False
            else:
                print(f"   ❌ {key}: 缺失")
                type_match = False
        
        # 检查 datamap 数据类型
        if 'datamap' in original_data and 'datamap' in detected_data:
            print(f"\n🔸 datamap 数据类型对比:")
            orig_datamap = original_data['datamap']
            detected_datamap = detected_data['datamap']
            
            for key in orig_datamap.keys():
                if key in detected_datamap:
                    orig_type = type(orig_datamap[key]).__name__
                    detected_type = type(detected_datamap[key]).__name__
                    if orig_type == detected_type:
                        print(f"   ✅ {key}: {orig_type}")
                    else:
                        print(f"   ❌ {key}: {orig_type} vs {detected_type}")
                        type_match = False
                else:
                    print(f"   ❌ {key}: 缺失")
                    type_match = False
        
        # 显示检测到的控件信息
        print(f"\n🔸 检测到的控件信息:")
        print(f"   名称: {detected_data.get('name', 'N/A')}")
        print(f"   类型: {detected_data.get('type', 'N/A')}")
        print(f"   描述: {detected_data.get('description', 'N/A')}")
        print(f"   状态: {detected_data.get('capture_status', 'N/A')}")
        
        coords = detected_data.get('coords', {})
        if coords:
            print(f"   坐标: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
            print(f"   大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
        
        datamap = detected_data.get('datamap', {})
        if datamap:
            print(f"   进程: {datamap.get('ProcessName', 'N/A')}")
            print(f"   窗口: {datamap.get('WindowName', 'N/A')}")
            print(f"   记录位置: {datamap.get('RecordPosition', 'N/A')}")
        
        # 最终结论
        print(f"\n🎯 兼容性验证结果:")
        print("=" * 40)
        
        if (original_top_fields == detected_top_fields and 
            original_datamap_fields == detected_datamap_fields and 
            type_match):
            print("✅ 格式完全兼容！")
            print("✅ 所有字段都存在且数据类型一致")
            print("✅ universal_offset_detector.py 成功输出了与原始格式相同的数据")
            print("✅ 可以直接替换原始 UNI 输出使用")
            return True
        else:
            print("❌ 格式存在差异")
            return False
    
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def show_usage_summary():
    """显示使用总结"""
    print(f"\n📝 使用总结")
    print("=" * 40)
    
    print("🔸 universal_offset_detector.py 现在支持两种输出格式:")
    print()
    print("1️⃣ 简单格式 (默认):")
    print("   python3 universal_offset_detector.py")
    print("   - 输出易读的控件信息")
    print("   - 适合调试和查看")
    print()
    
    print("2️⃣ 原始格式:")
    print("   python3 universal_offset_detector.py --format original")
    print("   - 输出与 control_info.txt 完全相同的 JSON 格式")
    print("   - 生成 detected_control_info.txt 文件")
    print("   - 可以直接替换原始 UNI 输出使用")
    print()
    
    print("🔸 输出特点:")
    print("✅ 包含所有原始字段: name, type, coords, datamap, description, states, actions, capture_status")
    print("✅ datamap 包含完整的 20 个字段")
    print("✅ 数据类型与原始格式完全一致")
    print("✅ 支持鼠标位置自动检测")
    print("✅ 包含窗口偏移自动计算")
    print("✅ 支持控件高亮显示")

if __name__ == "__main__":
    success = verify_format_compatibility()
    show_usage_summary()
    
    if success:
        print(f"\n🎉 验证成功！universal_offset_detector.py 已完全兼容原始格式！")
    else:
        print(f"\n❌ 验证失败，需要进一步调整")
        sys.exit(1)
