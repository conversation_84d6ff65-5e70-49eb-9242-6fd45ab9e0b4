#!/usr/bin/env python3
"""
通用的 kdk_getElement_Uni 函数
自动检测环境并选择合适的实现方式
"""

import os
import sys

# 添加项目路径
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

def detect_display_server():
    """检测当前的显示服务器类型"""
    # 检查环境变量
    if os.environ.get('WAYLAND_DISPLAY'):
        return 'wayland'
    elif os.environ.get('DISPLAY'):
        return 'x11'
    
    # 检查 XDG_SESSION_TYPE
    session_type = os.environ.get('XDG_SESSION_TYPE', '').lower()
    if session_type in ['wayland', 'x11']:
        return session_type
    
    return 'unknown'

def kdk_getElement_Uni_universal(x, y, quick=False, menuele=None):
    """
    通用的控件检测函数，自动适配 X11 和 Wayland 环境
    
    Args:
        x, y: 目标坐标
        quick: 是否使用快速模式
        menuele: 菜单元素列表
    
    Returns:
        tuple: (控件数据, 信息字符串) - 与原始 kdk_getElement_Uni 完全相同的格式
    """
    display_server = detect_display_server()
    
    if display_server == 'wayland':
        # 使用 Wayland 兼容实现
        try:
            from UNI_new import kdk_getElement_Uni_wayland
            return kdk_getElement_Uni_wayland(x, y, quick, menuele)
        except ImportError as e:
            return None, f"Wayland 实现不可用: {e}"
    
    elif display_server == 'x11':
        # 使用原始 X11 实现
        try:
            from UNI import UNI
            uni = UNI()
            return uni.kdk_getElement_Uni(x, y, quick, menuele)
        except ImportError as e:
            return None, f"X11 实现不可用: {e}"
    
    else:
        # 未知环境，尝试两种实现
        try:
            # 先尝试 Wayland 实现
            from UNI_new import kdk_getElement_Uni_wayland
            return kdk_getElement_Uni_wayland(x, y, quick, menuele)
        except ImportError:
            try:
                # 再尝试 X11 实现
                from UNI import UNI
                uni = UNI()
                return uni.kdk_getElement_Uni(x, y, quick, menuele)
            except ImportError as e:
                return None, f"无可用实现: {e}"

class UniversalUNI:
    """
    通用的 UNI 类，自动适配不同环境
    提供与原始 UNI 类完全相同的接口
    """
    
    def __init__(self):
        self.display_server = detect_display_server()
        self._uni_instance = None
        self._init_backend()
    
    def _init_backend(self):
        """初始化后端实现"""
        if self.display_server == 'x11':
            try:
                from UNI import UNI
                self._uni_instance = UNI()
                print(f"✅ UNI 初始化成功 (X11 模式)")
            except ImportError as e:
                print(f"⚠️  X11 后端不可用: {e}")
        elif self.display_server == 'wayland':
            print(f"✅ UNI 初始化成功 (Wayland 模式)")
        else:
            print(f"⚠️  未知显示服务器类型: {self.display_server}")
    
    def kdk_getElement_Uni(self, x, y, quick=False, menuele=None):
        """
        通用的控件检测方法
        
        Args:
            x, y: 目标坐标
            quick: 是否使用快速模式
            menuele: 菜单元素列表
        
        Returns:
            tuple: (控件数据, 信息字符串)
        """
        if self.display_server == 'x11' and self._uni_instance:
            # 使用 X11 实现
            return self._uni_instance.kdk_getElement_Uni(x, y, quick, menuele)
        else:
            # 使用 Wayland 实现
            return kdk_getElement_Uni_universal(x, y, quick, menuele)
    
    def get_environment_info(self):
        """获取环境信息"""
        return {
            'display_server': self.display_server,
            'backend': 'X11' if self._uni_instance else 'Wayland',
            'available': True
        }

def demo_universal_usage():
    """演示通用用法"""
    print("🌍 通用 kdk_getElement_Uni 演示")
    print("=" * 50)
    
    # 检测环境
    display_server = detect_display_server()
    print(f"🖥️  检测到显示服务器: {display_server}")
    
    # 方式1: 直接使用函数
    print(f"\n📍 方式1: 直接使用通用函数")
    x, y = 472, 620
    
    data, info = kdk_getElement_Uni_universal(x, y, False)
    print(f"结果: {info}")
    if data:
        print(f"控件: {data.get('Name', 'N/A')} ({data.get('Rolename', 'N/A')})")
    
    # 方式2: 使用通用类
    print(f"\n📍 方式2: 使用通用 UNI 类")
    uni = UniversalUNI()
    
    env_info = uni.get_environment_info()
    print(f"环境信息: {env_info}")
    
    data2, info2 = uni.kdk_getElement_Uni(x, y, False)
    print(f"结果: {info2}")
    if data2:
        print(f"控件: {data2.get('Name', 'N/A')} ({data2.get('Rolename', 'N/A')})")
    
    # 方式3: 替换原始代码
    print(f"\n📍 方式3: 替换原始代码风格")
    print("# 原始代码:")
    print("# a = UNI()")
    print("# a2, info = a.kdk_getElement_Uni(472, 620, False)")
    print()
    print("# 新代码:")
    print("# a = UniversalUNI()")
    print("# a2, info = a.kdk_getElement_Uni(472, 620, False)")
    
    a = UniversalUNI()
    a2, info = a.kdk_getElement_Uni(472, 620, False)
    
    print(f"\n执行结果:")
    print(f"  a2 = {type(a2).__name__} ({'有数据' if a2 else '无数据'})")
    print(f"  info = '{info}'")
    
    if a2:
        print(f"  控件信息: {a2.get('Name', 'N/A')} ({a2.get('Rolename', 'N/A')})")

def create_compatibility_wrapper():
    """创建兼容性包装器示例"""
    print(f"\n🔧 兼容性包装器示例")
    print("=" * 40)
    
    wrapper_code = '''
# 兼容性包装器 - 可以直接复制到您的项目中使用

import os
import sys

def get_uni_instance():
    """获取适合当前环境的 UNI 实例"""
    if os.environ.get('WAYLAND_DISPLAY'):
        # Wayland 环境
        from UNI_new import kdk_getElement_Uni_wayland
        
        class WaylandUNIWrapper:
            def kdk_getElement_Uni(self, x, y, quick=False, menuele=None):
                return kdk_getElement_Uni_wayland(x, y, quick, menuele)
        
        return WaylandUNIWrapper()
    else:
        # X11 环境
        from UNI import UNI
        return UNI()

# 使用示例:
# a = get_uni_instance()
# a2, info = a.kdk_getElement_Uni(472, 620, False)
'''
    
    print(wrapper_code)

if __name__ == "__main__":
    demo_universal_usage()
    create_compatibility_wrapper()
