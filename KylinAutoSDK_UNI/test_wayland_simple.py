#!/usr/bin/env python3
"""
简化的 Wayland 兼容性测试
直接测试新的 kdk_getElement_Uni_wayland 函数
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

def test_wayland_function():
    """测试 Wayland 兼容函数"""
    print("🧪 测试 Wayland 兼容的控件检测函数")
    print("=" * 50)
    
    # 测试坐标
    test_coordinates = [
        (472, 620),  # 您在代码中选择的坐标
        (276, 164),  # 之前测试成功的坐标
        (49, 206),   # 桌面图标坐标
    ]
    
    try:
        # 导入新的 Wayland 兼容函数
        from UNI_new import kdk_getElement_Uni_wayland
        print("✅ 成功导入 kdk_getElement_Uni_wayland 函数")
        
        for i, (x, y) in enumerate(test_coordinates):
            print(f"\n📍 测试坐标 {i+1}: ({x}, {y})")
            print("-" * 30)
            
            try:
                # 测试普通模式
                start_time = time.time()
                data, info = kdk_getElement_Uni_wayland(x, y, quick=False)
                elapsed = time.time() - start_time
                
                print(f"⏱️  耗时: {elapsed:.3f}s")
                print(f"📄 结果: {info}")
                
                if data:
                    print(f"✅ 找到控件:")
                    print(f"   名称: {data.get('Name', 'N/A')}")
                    print(f"   角色: {data.get('Rolename', 'N/A')}")
                    print(f"   进程: {data.get('ProcessName', 'N/A')}")
                    print(f"   描述: {data.get('Description', 'N/A')}")
                    
                    # 检查关键字段是否存在
                    required_fields = [
                        'Name', 'Rolename', 'ProcessName', 'Description',
                        'Coords', 'Actions', 'States', 'RecordPosition',
                        'WindowName', 'WindowRoleName', 'MenuElement'
                    ]
                    
                    missing_fields = []
                    for field in required_fields:
                        if field not in data:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"⚠️  缺失字段: {missing_fields}")
                    else:
                        print(f"✅ 所有必需字段都存在")
                    
                    # 显示坐标信息
                    coords = data.get('Coords', {})
                    if coords:
                        print(f"   坐标: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                        print(f"   大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
                    
                else:
                    print(f"❌ 未找到控件")
                
                # 测试快速模式
                print(f"\n🚀 测试快速模式:")
                start_time = time.time()
                extents, info_quick = kdk_getElement_Uni_wayland(x, y, quick=True)
                elapsed_quick = time.time() - start_time
                
                print(f"⏱️  耗时: {elapsed_quick:.3f}s")
                print(f"📄 结果: {info_quick}")
                
                if extents:
                    print(f"✅ 快速模式成功:")
                    print(f"   坐标: ({extents.x}, {extents.y})")
                    print(f"   大小: {extents.width} × {extents.height}")
                else:
                    print(f"❌ 快速模式失败")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
    
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保 UNI_new.py 文件存在且可访问")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_data_format():
    """测试数据格式兼容性"""
    print(f"\n🔍 测试数据格式兼容性")
    print("=" * 40)
    
    try:
        from UNI_new import kdk_getElement_Uni_wayland
        
        # 使用一个测试坐标
        x, y = 472, 620
        data, info = kdk_getElement_Uni_wayland(x, y, quick=False)
        
        if data:
            print(f"✅ 成功获取控件数据")
            print(f"📊 数据结构分析:")
            
            # 检查数据类型
            print(f"   数据类型: {type(data).__name__}")
            
            # 检查字段类型
            field_types = {}
            for key, value in data.items():
                if not key.startswith('_'):
                    field_types[key] = type(value).__name__
            
            print(f"   字段类型:")
            for field, field_type in sorted(field_types.items()):
                print(f"     {field}: {field_type}")
            
            # 检查坐标字段结构
            coords = data.get('Coords', {})
            if isinstance(coords, dict):
                print(f"   坐标字段结构:")
                for coord_key, coord_value in coords.items():
                    print(f"     {coord_key}: {coord_value} ({type(coord_value).__name__})")
            
            print(f"✅ 数据格式符合预期")
        else:
            print(f"⚠️  未找到控件数据进行格式测试")
    
    except Exception as e:
        print(f"❌ 数据格式测试失败: {e}")

if __name__ == "__main__":
    test_wayland_function()
    test_data_format()
