#!/usr/bin/env python3
# -*- coding=utf-8 -*-
import json
from pynput import mouse,keyboard
import time
import threading
import os
import pyautogui
import pyatspi
import sys
from UNI import UNI
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtGui import QPainter, QPen
from PyQt5.QtCore import Qt, QRect, QTimer

class HighlightWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.highlight_rect = None

    def initUI(self):
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setGeometry(0, 0, QApplication.primaryScreen().size().width(), QApplication.primaryScreen().size().height())

    def set_highlight_rect(self, x, y, width=100, height=100):
        rect = QRect(x - width // 2, y - height // 2, width, height)
        self.highlight_rect = rect
        self.update()

    def clear_highlight_rect(self):
        self.highlight_rect = None
        self.update()

    def paintEvent(self, event):
        if self.highlight_rect:
            painter = QPainter(self)
            pen = QPen(Qt.red, 2, Qt.SolidLine)
            painter.setPen(pen)
            painter.drawRect(self.highlight_rect)

class GAT():
    def __init__(self, action):
        self.app = QApplication(sys.argv)
        self.overlay = HighlightWindow()

        self.keyboardlisten = None
        self.action = action
        if action == "Kyrobot_UNI_KB":
            self.keyboardlisten = True
        self.listener = keyboard.Listener(on_press=self.on_press, on_release=self.on_release)

        self.current_keys_ordered = []
        self.shift_pressed = False
        self.data_list = {
            "keys": [],
            "controls": []
        }

        self.finddata = None
        self.findextents = None
        self.findfind = None

        self.last_appEle = []

    def start_listeners(self):
        """启动监听器"""
        self.listener.start()

        try:
            #self.listener.join()
            sys.exit(self.app.exec_())
        except KeyboardInterrupt:
            print("Program terminated.")
        finally:
            self.listener.stop()
            UNI._save_data_to_file(self)  # 确保在结束时保存数据
            # self.get_control_positions("data.json")
    
    # 键盘按键监听
    def on_press(self, key):
        if key in (keyboard.Key.shift, keyboard.Key.shift_r):
            self.shift_pressed = True
            if key not in self.current_keys_ordered:
                self.current_keys_ordered.append(key)
        else:
            if key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                self.overlay.show()
                position = mouse.Controller().position
                #print(position)
                x, y = position
                self.overlay.set_highlight_rect(x, y, 100, 100)
            if key not in self.current_keys_ordered:
                self.current_keys_ordered.append(key)
    
    # 键盘按键释放监听
    def on_release(self, key):
        if key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
            self.overlay.clear_highlight_rect()
            self.overlay.hide()

        if key in (keyboard.Key.shift, keyboard.Key.shift_r):
            self.shift_pressed = False

        if self.current_keys_ordered:
            # print("=======================keyboardlisten:", self.keyboardlisten)
            if self.keyboardlisten:
                if self.shift_pressed:
                    special_char = self._get_shift_special_char(key)
                    if special_char:
                        print(f"组合键 Shift+{key} => {special_char}")
                        UNI.kdk_KBToJson_Uni(self, special_char)
                    else:
                        UNI.kdk_KBToJson_Uni(self, [key])
                elif len(self.current_keys_ordered) == 2:
                    count = 0
                    for i in self.current_keys_ordered:
                        if i == keyboard.Key.ctrl_l or i == keyboard.Key.ctrl_r:
                            count = count + 1
                        try:
                            if i.char.upper() == ".":
                                count = count + 1
                        except:
                            pass
                    if count == 2:
                        self.listener.stop()
                        mouse1 = mouse.Controller()
                        mouse1.position = (1, 1)
                    else:
                        UNI.kdk_KBToJson_Uni(self, self.current_keys_ordered)
                else:
                    UNI.kdk_KBToJson_Uni(self, self.current_keys_ordered)
                self.current_keys_ordered.clear()
            else:
            # 控件记录
                if len(self.current_keys_ordered) == 2:
                    pp = 0
                    qq = 0
                    for i in self.current_keys_ordered:
                        if i == keyboard.Key.ctrl_l or i == keyboard.Key.ctrl_r:
                            pp = pp+1
                        try:
                            if i.char.upper() == ",":
                                pp = pp+1
                        except:
                            pass
                        if i == keyboard.Key.ctrl_l or i == keyboard.Key.ctrl_r:
                            qq = qq+1
                        try:
                            if i.char.upper() == ".":
                                qq = qq+1
                        except:
                            pass
                    if pp == 2:
                    # 判断如果按了ctrl+alt+n则记录当前xy的控件信息
                        # 获取当前鼠标位置
                        position = mouse.Controller().position
                        print(position)
                        x, y = position
                        # 调用UNI接口对xy控件进行获取
                        control_data, info = UNI.kdk_getElement_Uni(self, x, y)
                        print("==============")
                        print(str(control_data))
                        print(f"info:{info}")
                        print("==============")
                        if control_data:
                            UNI.kdk_KBToJson_Uni(self, control_data)
                    if qq == 2:
                        self.listener.stop()
                        self.overlay.close()
                        self.app.quit()
                        mouse1 = mouse.Controller()
                        mouse1.position = (1, 1)
                self.current_keys_ordered.clear()

    def _get_shift_special_char(self, key):
        shift_map = {
            '1': '!', '2': '@', '3': '#', '4': '$', '5': '%', '6': '^', '7': '&', '8': '*', '9': '(', '0': ')',
            '-': '_', '=': '+', '[': '{', ']': '}', '\\': '|', ';': ':', "'": '"', ',': '<', '.': '>', '/': '?'
        }
        try:
            if hasattr(key, 'char') and key.char in shift_map:
                return shift_map[key.char]
        except AttributeError:
            print(f"Error getting special char {key}")
        return None

    def get_control_positions(self, filename="data.json"):
        """从JSON文件中读取控件信息并调用kdk_getElePos_Uni获取位置"""
        try:
            with open(filename, 'r') as json_file:
                data = json.load(json_file)
                controls = data.get("controls", [])
                
                for control_info in controls:
                    position = UNI.kdk_getElePos_Uni(self, control_info)
                    if position:
                        # print(len(position))
                        if len(position) == 4:
                            print(
                                f"控件 {control_info['Name']} 的中心位置为: {position[:2]}, 宽度: {position[2]}, 高度: {position[3]}")
                            return
                        else:
                            print(f"无法获取控件 {control_info['Name']} 的位置, 位置信息不完整")
                            return
                    else:
                        print(f"无法获取控件 {control_info['Name']} 的位置")
        except Exception as e:
            print(f"Error reading control positions from file: {e}")


if __name__ == "__main__":
    #a = GAT(action="Kyrobot_UNI_KB")
    a = GAT(action="Kyrobot_Click")
    a.start_listeners()
    #a.get_control_positions("data.json")

