#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
最终无边框高亮解决方案 - 完全绕过窗口管理器
"""

import subprocess
import time
import os

class BorderlessHighlight:
    """无边框高亮类"""
    
    def __init__(self):
        self.display = ":0"
        os.environ["DISPLAY"] = self.display
    
    def create_borderless_highlight(self, x, y, width, height, duration=3):
        """创建无边框高亮"""
        print("🎯 最终无边框高亮")
        print("=" * 50)
        
        print(f"目标区域: ({x}, {y}) {width}×{height}")
        print(f"持续时间: {duration}秒")
        
        # 创建最终的X11脚本
        x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display, Xutil
    import time
    
    print("✅ 连接到X11...")
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    print("✅ 获取颜色...")
    colormap = screen.default_colormap
    red = colormap.alloc_named_color("red").pixel
    yellow = colormap.alloc_named_color("yellow").pixel
    white = colormap.alloc_named_color("white").pixel
    green = colormap.alloc_named_color("green").pixel
    
    print("✅ 创建主高亮窗口...")
    # 创建主窗口 - 红色半透明背景
    main_win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=red,
        override_redirect=True,  # 关键：完全绕过窗口管理器
        colormap=X.CopyFromParent,
        event_mask=X.ExposureMask
    )
    
    print("✅ 创建边框窗口...")
    # 创建边框窗口 - 黄色粗边框
    border_win = root.create_window(
        {x}-10, {y}-10, {width}+20, {height}+20, 
        border_width=10,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,
        border_pixel=yellow,
        override_redirect=True,  # 关键：完全绕过窗口管理器
        colormap=X.CopyFromParent
    )
    
    print("✅ 设置窗口属性...")
    # 设置窗口属性（防止任何窗口管理器干预）
    for win in [main_win, border_win]:
        win.set_wm_name("Borderless Highlight")
        win.set_wm_class("borderless", "Borderless")
        
        # 设置为dock类型
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_WINDOW_TYPE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
            )
        except:
            pass
        
        # 设置为跳过任务栏和分页器
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_STATE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
                 disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
                 disp.intern_atom("_NET_WM_STATE_ABOVE")]
            )
        except:
            pass
    
    print("✅ 映射窗口...")
    # 先映射边框，再映射主窗口
    border_win.map()
    disp.sync()
    time.sleep(0.1)
    
    main_win.map()
    disp.sync()
    
    print("✅ 创建绘图上下文...")
    # 创建GC用于绘制
    gc = main_win.create_gc(
        foreground=white,
        background=red,
        line_width=6
    )
    
    print("✅ 绘制标记...")
    # 在主窗口上绘制白色十字
    center_x = {width} // 2
    center_y = {height} // 2
    
    # 粗十字标记
    main_win.line(gc, center_x - 40, center_y, center_x + 40, center_y)
    main_win.line(gc, center_x, center_y - 40, center_x, center_y + 40)
    
    # 四个角的L形标记
    corner_size = 25
    main_win.line(gc, 15, 15, 15 + corner_size, 15)
    main_win.line(gc, 15, 15, 15, 15 + corner_size)
    
    main_win.line(gc, {width} - 15 - corner_size, 15, {width} - 15, 15)
    main_win.line(gc, {width} - 15, 15, {width} - 15, 15 + corner_size)
    
    main_win.line(gc, 15, {height} - 15 - corner_size, 15, {height} - 15)
    main_win.line(gc, 15, {height} - 15, 15 + corner_size, {height} - 15)
    
    main_win.line(gc, {width} - 15 - corner_size, {height} - 15, {width} - 15, {height} - 15)
    main_win.line(gc, {width} - 15, {height} - 15 - corner_size, {width} - 15, {height} - 15)
    
    disp.sync()
    
    print(f"✅ 显示高亮 {duration} 秒...")
    time.sleep({duration})
    
    print("✅ 清理窗口...")
    main_win.unmap()
    border_win.unmap()
    main_win.destroy()
    border_win.destroy()
    disp.sync()
    disp.close()
    
    print("✅ 无边框高亮完成！")
    
except Exception as e:
    print(f"❌ 无边框高亮失败: {{e}}")
    import traceback
    traceback.print_exc()
'''
        
        try:
            print("🚀 启动无边框高亮...")
            result = subprocess.run(['python3', '-c', x11_script], 
                                  capture_output=True, text=True)
            
            print("📄 输出:")
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print("🔴 错误:", result.stderr)
                
            if result.returncode == 0:
                print("✅ 无边框高亮执行成功！")
                return True
            else:
                print(f"⚠️ 返回码: {result.returncode}")
                return False
                
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
    
    def create_flash_borderless(self, x, y, width, height, duration=3):
        """创建闪烁无边框高亮"""
        print("\n🎯 闪烁无边框高亮")
        print("=" * 50)
        
        flash_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 创建多种颜色
    colormap = screen.default_colormap
    colors = [
        colormap.alloc_named_color("red").pixel,
        colormap.alloc_named_color("yellow").pixel,
        colormap.alloc_named_color("green").pixel,
        colormap.alloc_named_color("blue").pixel,
        colormap.alloc_named_color("magenta").pixel,
        colormap.alloc_named_color("cyan").pixel
    ]
    
    # 创建闪烁窗口
    flash_win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width=15,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=colors[0],
        border_pixel=colors[1],
        override_redirect=True,  # 关键：绕过窗口管理器
        colormap=X.CopyFromParent
    )
    
    # 设置窗口属性
    flash_win.set_wm_name("Flash Borderless")
    try:
        flash_win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
    except:
        pass
    
    flash_win.map()
    disp.sync()
    
    # 快速闪烁效果
    flash_count = max(1, int({duration} * 6))  # 每秒6次闪烁
    for i in range(flash_count):
        bg_color = colors[i % len(colors)]
        border_color = colors[(i + 2) % len(colors)]
        
        flash_win.change_attributes(
            background_pixel=bg_color,
            border_pixel=border_color
        )
        
        flash_win.clear_area(0, 0, {width}, {height})
        disp.sync()
        
        time.sleep(0.15)  # 快速闪烁
    
    flash_win.unmap()
    flash_win.destroy()
    disp.sync()
    disp.close()
    
    print("✅ 闪烁无边框完成！")
    
except Exception as e:
    print(f"❌ 闪烁失败: {{e}}")
'''
        
        try:
            result = subprocess.run(['python3', '-c', flash_script], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 闪烁无边框执行成功！")
                return True
            else:
                print(f"⚠️ 闪烁失败，返回码: {result.returncode}")
                if result.stderr:
                    print("错误:", result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 闪烁执行失败: {e}")
            return False

def test_final_borderless():
    """测试最终无边框方案"""
    print("🎯 最终无边框高亮方案测试")
    print("=" * 60)
    
    # 获取鼠标位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    test_x = mouse_x - 150
    test_y = mouse_y - 100
    test_w = 300
    test_h = 200
    
    print("✅ 最终方案特点:")
    print("   • 完全绕过GTK/Qt窗口系统")
    print("   • 使用纯X11协议")
    print("   • override_redirect=True 绕过窗口管理器")
    print("   • 绝对无任何窗口装饰")
    print("   • 红色背景 + 黄色边框 + 白色标记")
    print("   • 支持闪烁效果")
    print()
    
    # 创建高亮器
    highlighter = BorderlessHighlight()
    
    # 测试静态高亮
    print("🔥 测试静态无边框高亮...")
    success1 = highlighter.create_borderless_highlight(test_x, test_y, test_w, test_h, 4)
    
    # 测试闪烁高亮
    print("🔥 测试闪烁无边框高亮...")
    success2 = highlighter.create_flash_borderless(test_x, test_y, test_w, test_h, 3)
    
    print(f"\n" + "=" * 60)
    print("🎊 最终无边框测试完成！")
    
    if success1 or success2:
        print("\n🎉 成功！我们实现了真正的无边框高亮！")
        print("💡 这个方案的优势:")
        print("   • 完全没有窗口装饰")
        print("   • 不依赖窗口管理器")
        print("   • 可以集成到任何应用中")
        print("   • 性能高效")
        print("   • 支持各种视觉效果")
    else:
        print("\n⚠️ 需要进一步调试")

if __name__ == "__main__":
    test_final_borderless()