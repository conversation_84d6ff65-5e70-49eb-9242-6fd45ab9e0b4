#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
修复的AT-SPI控件检测器
解决坐标不准确和控件识别问题

主要修复:
1. 修正AT-SPI坐标系与实际显示坐标的偏差
2. 改进控件识别算法，支持label、button等具体控件
3. 修复高亮绘制坐标错误问题
4. 添加多种坐标验证机制
"""

import sys
import os
import subprocess
import time
import pyatspi

# 添加src路径到模块搜索路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ultimate_highlight import ultimate_highlight


def get_element_extents(element):
    """安全地获取元素范围"""
    try:
        if hasattr(element, 'queryComponent'):
            component = element.queryComponent()
            if component:
                return component.getExtents(pyatspi.DESKTOP_COORDS)
    except Exception:
        pass
    return None


def is_element_visible(element):
    """检查元素是否可见"""
    try:
        state = element.getState()
        state_names = []
        
        # 将StateSet转换为字符串列表
        for i in range(len(state)):
            if state.contains(i):
                state_names.append(pyatspi.stateToString(i))
        
        # 检查可见性状态
        return ('showing' in state_names or 
                'visible' in state_names or 
                'enabled' in state_names)
    except Exception:
        return False


def get_element_actions(element):
    """获取元素的动作列表"""
    try:
        if hasattr(element, 'queryAction'):
            action = element.queryAction()
            if action:
                actions = []
                for i in range(action.nActions):
                    actions.append(action.getName(i))
                return actions
    except Exception:
        pass
    return []


def is_point_in_rect(x, y, rect_x, rect_y, rect_width, rect_height):
    """检查点是否在矩形内"""
    return (rect_x <= x < rect_x + rect_width and 
            rect_y <= y < rect_y + rect_height)


def detect_coordinate_offset():
    """检测坐标偏移"""
    try:
        # 方法1: 尝试获取panel的位置来计算偏移
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            # 获取鼠标位置作为参考
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            
            # 简单的偏移检测逻辑
            # 通常panel高度在20-50像素之间
            potential_y_offset = 30  # 默认panel高度
            return 0, potential_y_offset
    except Exception:
        pass
    
    return 0, 0


class FixedControlDetector:
    """修复的控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.x_offset, self.y_offset = detect_coordinate_offset()
        self.last_detected_control = None
        
        print("🔧 修复的AT-SPI控件检测器已初始化")
        print(f"坐标偏移: ({self.x_offset}, {self.y_offset})")
    
    def correct_coordinates(self, x, y):
        """修正坐标"""
        return x + self.x_offset, y + self.y_offset
    
    def find_element_at_point(self, x, y):
        """在指定坐标查找元素"""
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            matching_elements = []
            
            # 遍历所有应用
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                try:
                    self._collect_elements_recursive(app, x, y, matching_elements)
                except Exception:
                    continue
            
            if matching_elements:
                # 选择最佳匹配 - 优先选择最小、最具体的控件
                best_element = self._select_best_element(matching_elements, x, y)
                return best_element
                
        except Exception as e:
            print(f"查找元素异常: {e}")
        
        return None
    
    def _collect_elements_recursive(self, element, x, y, matching_elements, depth=0):
        """递归收集匹配的元素"""
        try:
            # 获取元素范围
            extents = get_element_extents(element)
            if not extents:
                return
            
            # 检查坐标是否在元素内
            if not is_point_in_rect(x, y, extents.x, extents.y, extents.width, extents.height):
                return
            
            # 检查元素是否可见
            if not is_element_visible(element):
                return
            
            # 获取元素信息
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            name = element.name if hasattr(element, 'name') else ''
            description = element.description if hasattr(element, 'description') else ''
            actions = get_element_actions(element)
            
            # 计算面积
            area = extents.width * extents.height
            
            # 判断是否为有意义的控件
            is_meaningful = self._is_meaningful_control(element, role, name, description, actions, area)
            
            if is_meaningful:
                matching_elements.append({
                    'element': element,
                    'extents': extents,
                    'area': area,
                    'depth': depth,
                    'role': role,
                    'name': name,
                    'description': description,
                    'actions': actions
                })
            
            # 递归检查子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_elements_recursive(child, x, y, matching_elements, depth + 1)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def _is_meaningful_control(self, element, role, name, description, actions, area):
        """判断是否为有意义的控件"""
        # 常见的可交互控件角色
        interactive_roles = [
            'button', 'push button', 'toggle button', 'check box', 'radio button',
            'text', 'entry', 'password text', 'spin button',
            'combo box', 'list', 'list item', 'tree', 'tree item',
            'menu', 'menu item', 'menu bar', 'tool bar',
            'label', 'static text', 'heading',
            'link', 'image', 'icon',
            'scroll bar', 'slider', 'progress bar',
            'tab', 'tab list', 'page tab',
            'table', 'table cell', 'column header', 'row header'
        ]
        
        # 检查角色
        if role in interactive_roles:
            return True
        
        # 检查是否有动作
        if actions:
            return True
        
        # 检查是否有名称或描述
        if (name and len(name.strip()) > 0) or (description and len(description.strip()) > 0):
            return True
        
        # 检查面积 - 太大的可能是容器，太小的可能无意义
        if 100 <= area <= 50000:  # 10x10 到 约223x223
            return True
        
        return False
    
    def _select_best_element(self, matching_elements, x, y):
        """选择最佳匹配元素"""
        if not matching_elements:
            return None
        
        # 排序规则：优先选择最小、最深层的控件
        def sort_key(item):
            return (
                item['area'],                    # 面积小的优先（最重要）
                -item['depth'],                  # 深度大的优先（更具体）
                not bool(item['actions']),       # 有动作的优先
                not bool(item['name']),          # 有名称的优先
                item['role'] not in ['button', 'label', 'text', 'entry']  # 常见控件优先
            )
        
        matching_elements.sort(key=sort_key)
        return matching_elements[0]['element']
    
    def get_control_info_at_point(self, x, y):
        """获取指定坐标的控件信息"""
        try:
            # 查找元素
            element = self.find_element_at_point(x, y)
            
            if not element:
                return {
                    'success': False,
                    'message': f"未找到坐标 ({x}, {y}) 处的控件",
                    'control_info': None,
                    'position': None
                }
            
            # 获取元素信息
            extents = get_element_extents(element)
            if not extents:
                return {
                    'success': False,
                    'message': "无法获取控件位置信息",
                    'control_info': None,
                    'position': None
                }
            
            # 构建控件信息
            control_info = {
                'name': element.name if hasattr(element, 'name') else '',
                'description': element.description if hasattr(element, 'description') else '',
                'role': element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown',
                'position': {
                    'x': extents.x,
                    'y': extents.y,
                    'width': extents.width,
                    'height': extents.height
                },
                'actions': get_element_actions(element)
            }
            
            # 尝试获取状态信息
            try:
                state = element.getState()
                states = []
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
                control_info['states'] = states
            except Exception:
                control_info['states'] = []
            
            # 尝试获取窗口信息
            try:
                parent = element
                while parent and hasattr(parent, 'parent') and parent.parent:
                    parent = parent.parent
                    if hasattr(parent, 'getRoleName') and parent.getRoleName() in ['application', 'frame']:
                        control_info['window_name'] = parent.name if hasattr(parent, 'name') else ''
                        break
            except Exception:
                control_info['window_name'] = ''
            
            # 保存最后检测到的控件
            self.last_detected_control = {
                'element': element,
                'info': control_info
            }
            
            return {
                'success': True,
                'message': "控件检测成功",
                'control_info': control_info,
                'position': (extents.x, extents.y, extents.width, extents.height)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"检测异常: {e}",
                'control_info': None,
                'position': None
            }
    
    def highlight_control_at_point(self, x, y, duration=2, color='red', border_width=2):
        """检测并高亮指定坐标的控件"""
        result = self.get_control_info_at_point(x, y)
        
        if not result['success']:
            return {
                'success': False,
                'message': f"高亮失败: {result['message']}",
                'highlighted': False
            }
        
        # 获取控件实际位置
        pos_x, pos_y, width, height = result['position']
        
        if width <= 0 or height <= 0:
            return {
                'success': False,
                'message': "控件尺寸无效",
                'highlighted': False
            }
        
        # 执行高亮 - 使用实际检测到的坐标
        try:
            highlight_success = ultimate_highlight(
                pos_x, pos_y, width, height,
                duration=duration,
                color=color,
                border_width=border_width
            )
            
            return {
                'success': True,
                'message': f"控件高亮{'成功' if highlight_success else '失败'}",
                'highlighted': highlight_success,
                'control_position': (pos_x, pos_y, width, height),
                'control_info': result['control_info']
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"高亮异常: {e}",
                'highlighted': False
            }
    
    def print_control_info(self, x, y):
        """打印控件信息"""
        print(f"\n🔍 检测坐标 ({x}, {y}) 处的控件:")
        print("=" * 60)
        
        result = self.get_control_info_at_point(x, y)
        
        if not result['success']:
            print(f"❌ {result['message']}")
            return
        
        info = result['control_info']
        pos = result['position']
        
        print(f"📋 基本信息:")
        print(f"   名称: {info['name'] or 'N/A'}")
        print(f"   描述: {info['description'] or 'N/A'}")
        print(f"   角色: {info['role']}")
        print(f"   位置: ({pos[0]}, {pos[1]})")
        print(f"   尺寸: {pos[2]} × {pos[3]}")
        
        if info.get('window_name'):
            print(f"   所属窗口: {info['window_name']}")
        
        if info.get('actions'):
            print(f"   可用动作: {', '.join(info['actions'])}")
        
        if info.get('states'):
            print(f"   状态: {', '.join(info['states'])}")
        
        print("=" * 60)


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def demo_fixed_detector():
    """演示修复的控件检测器"""
    print("🔧 修复的AT-SPI控件检测器演示")
    print("=" * 60)
    
    detector = FixedControlDetector()
    
    # 获取鼠标位置进行测试
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        test_x, test_y = mouse_x, mouse_y
        print(f"使用鼠标当前位置: ({test_x}, {test_y})")
    else:
        test_x, test_y = 500, 300
        print(f"使用默认测试位置: ({test_x}, {test_y})")
    
    # 测试控件检测
    print(f"\n🔍 检测控件...")
    detector.print_control_info(test_x, test_y)
    
    # 测试高亮功能
    print(f"\n✨ 测试高亮功能...")
    result = detector.highlight_control_at_point(test_x, test_y, duration=3, color='green')
    
    if result['success']:
        if result['highlighted']:
            pos = result['control_position']
            print(f"✅ 高亮成功! 实际位置: ({pos[0]}, {pos[1]}) 尺寸: {pos[2]}×{pos[3]}")
            
            # 显示检测到的控件信息
            info = result['control_info']
            print(f"📋 检测到的控件:")
            print(f"   - 名称: {info['name'] or 'N/A'}")
            print(f"   - 角色: {info['role']}")
            print(f"   - 动作: {info.get('actions', [])}")
        else:
            print("⚠️  控件检测成功但高亮失败")
    else:
        print(f"❌ 高亮失败: {result['message']}")
    
    print("\n🎉 演示完成!")


if __name__ == "__main__":
    # 检查环境
    if not sys.platform.startswith('linux'):
        print("❌ 此程序仅支持Linux系统")
        sys.exit(1)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    demo_fixed_detector()