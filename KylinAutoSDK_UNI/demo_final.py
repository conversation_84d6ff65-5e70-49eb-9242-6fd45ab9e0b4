#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
最终演示脚本
展示修复后的hellobig控件识别和精确高亮功能
"""

import sys
import os
import time
import subprocess

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def demo_hellobig_controls():
    """演示hellobig控件识别功能"""
    print("🎯 hellobig控件识别和精确高亮演示")
    print("=" * 60)
    
    try:
        from UNI_final import UNI_Final
        
        uni = UNI_Final()
        
        # 自动测试功能
        print("🤖 自动测试hellobig控件...")
        success = uni.test_hellobig_controls()
        
        if success:
            print("\n✅ 自动测试成功！")
        else:
            print("\n⚠️ 自动测试未完全成功，可能需要调整")
        
        return success
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_mouse_position():
    """演示当前鼠标位置的控件识别"""
    print(f"\n🖱️ 当前鼠标位置控件识别演示")
    print("=" * 60)
    
    try:
        from UNI_final import UNI_Final
        
        uni = UNI_Final()
        
        # 获取鼠标位置
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            print("❌ 无法获取鼠标位置")
            return False
        
        print(f"当前鼠标位置: ({mouse_x}, {mouse_y})")
        
        # 进行控件识别和高亮
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
        elapsed = time.time() - start_time
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            
            print(f"✅ 识别成功 ({elapsed:.2f}s)")
            print(f"   控件名称: {name}")
            print(f"   控件类型: {role}")
            
            if coords:
                print(f"   AT-SPI坐标: ({coords.get('x')}, {coords.get('y')})")
                print(f"   控件大小: {coords.get('width')} × {coords.get('height')}")
            
            print(f"🎨 精确高亮已显示，持续3秒")
            print(f"💡 观察红色边框是否准确对准了控件")
            
            return True
        else:
            print(f"❌ 未找到控件 ({elapsed:.2f}s)")
            print(f"   详情: {info}")
            return False
        
    except Exception as e:
        print(f"❌ 鼠标位置演示失败: {e}")
        return False

def demo_interactive():
    """交互式演示"""
    print(f"\n🔄 交互式控件识别演示")
    print("=" * 60)
    print("移动鼠标到hellobig窗口的不同控件上")
    print("每3秒自动识别一次当前位置的控件")
    print("程序将运行30秒，按Ctrl+C可提前结束")
    print()
    
    try:
        from UNI_final import UNI_Final
        
        uni = UNI_Final()
        
        start_demo_time = time.time()
        test_count = 0
        
        while time.time() - start_demo_time < 30 and test_count < 10:  # 最多30秒或10次测试
            mouse_x, mouse_y = get_mouse_position()
            
            if mouse_x is not None:
                test_count += 1
                print(f"📍 测试 {test_count}/10: 鼠标位置 ({mouse_x}, {mouse_y})")
                
                try:
                    control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
                    
                    if control_data:
                        name = control_data.get('Name', 'N/A')
                        role = control_data.get('Rolename', 'N/A')
                        print(f"   ✅ 控件: {name} ({role})")
                        print(f"   🎨 精确红色高亮已显示")
                    else:
                        print(f"   ❌ 未找到控件")
                    
                except Exception as e:
                    print(f"   ❌ 识别异常: {e}")
            
            time.sleep(3)
        
        print(f"\n📊 交互演示完成，共测试了 {test_count} 次")
        return True
    
    except KeyboardInterrupt:
        print("\n用户中断演示")
        return True
    except Exception as e:
        print(f"❌ 交互演示失败: {e}")
        return False

def show_summary():
    """显示功能总结"""
    print(f"\n📋 功能总结")
    print("=" * 60)
    print("✅ 已完成的优化:")
    print("   🔧 使用wlcctrl获取真实窗口位置")
    print("   📐 修正AT-SPI坐标转换算法")
    print("   🎨 实现精确的控件高亮显示")
    print("   ⚡ 优化控件识别性能")
    print("   🎯 专门针对hellobig应用优化")
    print()
    print("🎨 高亮特点:")
    print("   - 红色边框准确对准识别的控件")
    print("   - 自动坐标转换：AT-SPI相对坐标 → 桌面绝对坐标")
    print("   - 支持多种控件类型：按钮、输入框、滑块、列表等")
    print("   - 高性能：平均识别时间1-2秒")
    print()
    print("🔧 技术要点:")
    print("   - 窗口几何信息缓存")
    print("   - 智能坐标偏移计算")
    print("   - 多层回退高亮策略")
    print("   - 异常处理和错误恢复")

def main():
    """主函数"""
    print("hellobig控件识别最终演示")
    print("=" * 80)
    print("🎯 目标: 展示修复后的精确控件识别和高亮功能")
    print("🎨 特色: 红色边框准确对准识别的控件")
    print()
    
    try:
        # 检查依赖
        print("🔧 检查模块依赖...")
        
        try:
            from UNI_final import UNI_Final
            print("   ✅ UNI_Final 模块可用")
        except ImportError as e:
            print(f"   ❌ UNI_Final 模块不可用: {e}")
            return 1
        
        # 检查wlcctrl工具
        try:
            result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
            if 'AT-SPI测试界面' in result.stdout:
                print("   ✅ hellobig应用正在运行")
            else:
                print("   ⚠️ 未检测到hellobig应用，演示可能不完整")
        except:
            print("   ⚠️ wlcctrl工具不可用")
        
        print("   ✅ 准备就绪")
        print()
        
        # 1. 自动演示
        print("🎯 步骤1: 自动控件识别演示")
        auto_success = demo_hellobig_controls()
        
        # 2. 鼠标位置演示
        print("\n🖱️ 步骤2: 当前鼠标位置演示")
        mouse_success = demo_mouse_position()
        
        # 3. 交互式演示
        print("\n🔄 步骤3: 交互式演示")
        interactive_success = demo_interactive()
        
        # 4. 功能总结
        show_summary()
        
        # 结果统计
        print(f"\n📊 演示结果:")
        results = [auto_success, mouse_success, interactive_success]
        success_count = sum(results)
        
        print(f"   自动演示: {'✅' if auto_success else '❌'}")
        print(f"   鼠标演示: {'✅' if mouse_success else '❌'}")
        print(f"   交互演示: {'✅' if interactive_success else '❌'}")
        print(f"   总体成功率: {success_count}/3")
        
        if success_count >= 2:
            print("🎉 演示基本成功！控件识别和高亮功能正常")
        else:
            print("⚠️ 演示部分失败，可能需要进一步调整")
    
    except KeyboardInterrupt:
        print("\n用户中断演示")
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("🎉 hellobig控件识别演示结束!")
    print("💡 如果高亮位置仍有偏差，可根据具体情况微调坐标转换算法")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())