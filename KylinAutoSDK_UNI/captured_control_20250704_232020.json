{"name": "项目", "type": "table column header", "coords": {"x": 1145, "y": 554, "width": 248, "height": 22}, "datamap": {"Name": "项目", "ID": -1, "ProcessID": 1521566, "Rolename": "table column header", "Description": "", "Index_in_parent": 0, "ChildrenCount": 0, "ProcessName": "hellobig", "Coords": {"x": 1145, "y": 554, "width": 248, "height": 22}, "Text": "Not available: ", "Actions": [], "States": ["enabled", "sensitive", "showing", "visible"], "ParentPath": [0, 0, 20, 0], "ParentCount": 4, "Key": "N项目-D-P00200", "RecordPosition": [1297, 604], "WindowRoleName": "frame", "WindowChildCount": 1, "WindowName": "AT-SPI测试主窗口", "capture_status": "success"}, "description": "", "states": ["enabled", "sensitive", "showing", "visible"], "actions": [], "capture_status": "success"}