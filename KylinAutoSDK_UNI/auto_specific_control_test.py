#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
自动化测试具体控件检测
验证我们能否正确检测到所有类型的具体控件
"""

import sys
import os
import time
from specific_control_detector import SpecificControlDetector


def main():
    """主函数"""
    print("🚀 自动化具体控件检测测试")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        detector = SpecificControlDetector()
        
        if not detector.window_offset:
            print("❌ 无法获取窗口偏移，无法进行测试")
            sys.exit(1)
        
        # 基于之前分析的控件位置进行测试
        test_controls = [
            {
                'name': '普通按钮',
                'atspi_position': (20, 20),
                'size': (100, 30),
                'expected_type': 'push button'
            },
            {
                'name': '切换按钮',
                'atspi_position': (140, 20),
                'size': (100, 30),
                'expected_type': 'check box'
            },
            {
                'name': '复选框',
                'atspi_position': (20, 71),
                'size': (58, 22),
                'expected_type': 'check box'
            },
            {
                'name': '单选按钮1',
                'atspi_position': (160, 71),
                'size': (54, 22),
                'expected_type': 'radio button'
            },
            {
                'name': '单选按钮2',
                'atspi_position': (280, 71),
                'size': (54, 22),
                'expected_type': 'radio button'
            },
            {
                'name': '用户名输入框',
                'atspi_position': (110, 110),
                'size': (200, 25),
                'expected_type': 'text'
            },
            {
                'name': '密码输入框',
                'atspi_position': (420, 110),
                'size': (200, 25),
                'expected_type': 'password text'
            },
            {
                'name': '音量滑块',
                'atspi_position': (110, 160),
                'size': (200, 20),
                'expected_type': 'slider'
            },
            {
                'name': '数字输入框',
                'atspi_position': (330, 160),
                'size': (80, 25),
                'expected_type': 'spin button'
            },
            {
                'name': '城市下拉框',
                'atspi_position': (110, 210),
                'size': (150, 25),
                'expected_type': 'combo box'
            }
        ]
        
        print(f"🔬 开始自动化测试 {len(test_controls)} 个控件...")
        print(f"🪟 使用窗口偏移: {detector.window_offset}")
        
        results = []
        
        for i, control in enumerate(test_controls):
            print(f"\n📍 测试 {i+1}/{len(test_controls)}: {control['name']}")
            
            # 计算中心点
            atspi_x, atspi_y = control['atspi_position']
            width, height = control['size']
            center_x = atspi_x + width // 2
            center_y = atspi_y + height // 2
            
            # 转换为屏幕坐标
            screen_x = detector.window_offset[0] + center_x
            screen_y = detector.window_offset[1] + center_y
            
            print(f"   AT-SPI中心: ({center_x}, {center_y})")
            print(f"   屏幕坐标: ({screen_x}, {screen_y})")
            print(f"   期望类型: {control['expected_type']}")
            
            # 查找控件
            ctrl = detector.find_most_specific_control_at_point(screen_x, screen_y)
            
            if ctrl:
                info = detector.get_control_info(ctrl)
                
                # 验证匹配度
                name_match = control['name'] in info['name'] or info['name'] in control['name']
                type_match = control['expected_type'] == info['role']
                
                result = {
                    'control': control,
                    'detected': info,
                    'name_match': name_match,
                    'type_match': type_match,
                    'success': name_match and type_match
                }
                
                if result['success']:
                    print(f"   ✅ 完全匹配: {info['name']} ({info['role']})")
                elif name_match:
                    print(f"   ⚠️  名称匹配: {info['name']} ({info['role']}) - 类型不匹配")
                elif type_match:
                    print(f"   ⚠️  类型匹配: {info['name']} ({info['role']}) - 名称不匹配")
                else:
                    print(f"   ❌ 不匹配: {info['name']} ({info['role']})")
                
                results.append(result)
            else:
                print(f"   ❌ 未找到控件")
                results.append({
                    'control': control,
                    'detected': None,
                    'name_match': False,
                    'type_match': False,
                    'success': False
                })
        
        # 统计结果
        print(f"\n📊 测试结果统计:")
        print("=" * 60)
        
        total_count = len(results)
        success_count = sum(1 for r in results if r['success'])
        name_match_count = sum(1 for r in results if r['name_match'])
        type_match_count = sum(1 for r in results if r['type_match'])
        detected_count = sum(1 for r in results if r['detected'] is not None)
        
        print(f"总测试数: {total_count}")
        print(f"检测到控件数: {detected_count}")
        print(f"完全匹配数: {success_count}")
        print(f"名称匹配数: {name_match_count}")
        print(f"类型匹配数: {type_match_count}")
        print(f"检测成功率: {detected_count/total_count*100:.1f}%")
        print(f"完全匹配率: {success_count/total_count*100:.1f}%")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        print("-" * 60)
        for i, result in enumerate(results):
            control = result['control']
            detected = result['detected']
            
            status = "✅" if result['success'] else "⚠️" if detected else "❌"
            
            print(f"{i+1:2d}. {status} {control['name']} ({control['expected_type']})")
            if detected:
                print(f"     → 检测到: {detected['name']} ({detected['role']})")
            else:
                print(f"     → 未检测到")
        
        # 成功的控件类型统计
        successful_types = {}
        for result in results:
            if result['success']:
                ctrl_type = result['control']['expected_type']
                successful_types[ctrl_type] = successful_types.get(ctrl_type, 0) + 1
        
        if successful_types:
            print(f"\n🎯 成功检测的控件类型:")
            print("-" * 30)
            for ctrl_type, count in successful_types.items():
                print(f"   {ctrl_type}: {count}")
        
        # 总结
        print(f"\n🎉 测试总结:")
        print("=" * 40)
        if success_count == total_count:
            print("🏆 完美！所有控件都被正确检测到了！")
        elif success_count >= total_count * 0.8:
            print("🎊 很好！大部分控件都被正确检测到了！")
        elif detected_count >= total_count * 0.8:
            print("👍 不错！大部分控件都能被检测到，但需要改进匹配逻辑")
        else:
            print("🔧 需要进一步改进检测逻辑")
        
        print(f"✅ 我们已经成功解决了filler问题，现在能检测到真正的具体控件！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
