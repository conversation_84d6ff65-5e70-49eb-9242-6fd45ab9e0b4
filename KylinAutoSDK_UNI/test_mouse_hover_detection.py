#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
鼠标悬停控件检测测试器
专门用于测试：将鼠标悬停在控件上，执行脚本进行检测和高亮
"""

import sys
import os
import subprocess
import pyatspi


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


class MouseHoverDetectionTester:
    """鼠标悬停检测测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 使用验证过的正确偏移
        self.correct_offset = (567, 192)
        self.all_controls = []
        
        # 具体控件类型
        self.specific_control_types = {
            'push button', 'button', 'toggle button',
            'check box', 'radio button', 
            'text', 'password text', 'entry',
            'combo box', 'list', 'list item',
            'slider', 'spin button', 'scroll bar',
            'menu item', 'tab', 'tree item',
            'label', 'image', 'progress bar'
        }
        
        print("🖱️  鼠标悬停控件检测测试器")
        print("=" * 60)
        print(f"✅ 使用验证过的偏移: {self.correct_offset}")
        print("📖 使用方法:")
        print("   1. 将鼠标悬停在要测试的控件上")
        print("   2. 运行此脚本")
        print("   3. 观察检测结果和高亮效果")
        print("=" * 60)
    
    def refresh_controls(self):
        """刷新控件缓存"""
        self.all_controls = []
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                if 'hellobig' in app_name.lower():
                    app_controls = []
                    self._collect_controls_recursive(app, app_controls, max_depth=6)
                    
                    if app_controls:
                        self.all_controls.extend(app_controls)
                    break
            
        except Exception as e:
            print(f"❌ 刷新缓存失败: {e}")
    
    def _collect_controls_recursive(self, element, control_list, depth=0, max_depth=6):
        """递归收集控件"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 收集所有有坐标的控件
            if extents and extents.width > 0 and extents.height > 0:
                control_list.append({
                    'element': element,
                    'name': name,
                    'role': role,
                    'extents': extents,
                    'depth': depth
                })
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_controls_recursive(child, control_list, depth + 1, max_depth)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def find_control_at_mouse_position(self, mouse_x, mouse_y):
        """在鼠标位置查找控件"""
        if not self.all_controls:
            self.refresh_controls()
        
        # 转换为AT-SPI坐标
        atspi_x = mouse_x - self.correct_offset[0]
        atspi_y = mouse_y - self.correct_offset[1]
        
        # 查找匹配的控件
        matches = []
        for ctrl in self.all_controls:
            ext = ctrl['extents']
            
            # 检查点是否在控件范围内
            if (ext.x <= atspi_x < ext.x + ext.width and
                ext.y <= atspi_y < ext.y + ext.height):
                matches.append(ctrl)
        
        if not matches:
            return None
        
        # 选择最具体的控件（优先选择具体控件类型，然后按面积最小）
        specific_matches = [m for m in matches if m['role'] in self.specific_control_types]
        
        if specific_matches:
            best_match = min(specific_matches, key=lambda x: x['extents'].width * x['extents'].height)
            return best_match
        else:
            # 如果没有具体控件，选择面积最小的
            best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
            return best_match
    
    def get_control_info(self, ctrl):
        """获取控件详细信息"""
        try:
            # 获取状态
            states = []
            try:
                state = ctrl['element'].getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass
            
            # 获取动作
            actions = []
            try:
                if hasattr(ctrl['element'], 'queryAction'):
                    action = ctrl['element'].queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass
            
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': states,
                'actions': actions,
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
        except Exception:
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': [],
                'actions': [],
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
    
    def highlight_control(self, ctrl, duration=3, color='lime', border_width=3):
        """高亮控件"""
        if not ctrl:
            return False
        
        ext = ctrl['extents']
        
        # 计算控件在屏幕上的真实位置
        screen_x = ext.x + self.correct_offset[0]
        screen_y = ext.y + self.correct_offset[1]
        width = ext.width
        height = ext.height
        
        try:
            from ultimate_highlight import ultimate_highlight
            highlight_success = ultimate_highlight(
                screen_x, screen_y, width, height,
                duration=duration, color=color, border_width=border_width
            )
            
            return highlight_success
                
        except Exception as e:
            print(f"❌ 高亮异常: {e}")
            return False
    
    def analyze_control_type(self, info):
        """分析控件类型"""
        role = info['role']
        
        # 控件类型分析
        if role == 'push button':
            return "🔘 普通按钮 - 可以点击执行操作"
        elif role == 'check box':
            return "☑️  复选框 - 可以切换选中状态"
        elif role == 'radio button':
            return "🔘 单选按钮 - 与其他单选按钮互斥"
        elif role == 'text':
            return "📝 文本输入框 - 可以输入文字"
        elif role == 'password text':
            return "🔒 密码输入框 - 输入内容会被隐藏"
        elif role == 'slider':
            return "🎚️  滑块控件 - 可以拖动调整数值"
        elif role == 'spin button':
            return "🔢 数字输入框 - 可以输入或调整数字"
        elif role == 'combo box':
            return "📋 下拉框控件 - 可以选择选项"
        elif role == 'list item':
            return "📄 列表项 - 可以选择"
        elif role == 'label':
            return "🏷️  标签控件 - 用于显示文本"
        else:
            return f"🔍 {role} 控件"
    
    def test_current_mouse_position(self):
        """测试当前鼠标位置"""
        # 获取鼠标位置
        mouse_pos = get_mouse_position()
        if mouse_pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return False
        
        mouse_x, mouse_y = mouse_pos
        
        print(f"\n🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        print("-" * 50)
        
        # 转换为AT-SPI坐标
        atspi_x = mouse_x - self.correct_offset[0]
        atspi_y = mouse_y - self.correct_offset[1]
        print(f"🔄 AT-SPI坐标: ({atspi_x}, {atspi_y})")
        print(f"   (使用偏移: {self.correct_offset})")
        
        # 查找控件
        ctrl = self.find_control_at_mouse_position(mouse_x, mouse_y)
        
        if not ctrl:
            print("❌ 未找到控件")
            print("💡 提示: 请确保鼠标在hellobig应用的控件上")
            return False
        
        # 获取控件信息
        info = self.get_control_info(ctrl)
        
        print(f"\n✅ 检测成功!")
        print("=" * 50)
        print(f"📋 控件信息:")
        print(f"   名称: {info['name']}")
        print(f"   类型: {info['role']}")
        print(f"   AT-SPI位置: {info['position']}")
        print(f"   尺寸: {info['size']}")
        print(f"   深度: {info['depth']}")
        
        if info['states']:
            print(f"   状态: {', '.join(info['states'][:3])}")
        
        if info['actions']:
            print(f"   动作: {', '.join(info['actions'])}")
        
        # 控件类型说明
        type_description = self.analyze_control_type(info)
        print(f"\n💡 {type_description}")
        
        # 计算屏幕坐标
        screen_x = info['position'][0] + self.correct_offset[0]
        screen_y = info['position'][1] + self.correct_offset[1]
        print(f"\n📍 屏幕坐标: ({screen_x}, {screen_y}) {info['size'][0]}×{info['size'][1]}")
        
        # 执行高亮
        print(f"\n✨ 执行高亮...")
        highlight_success = self.highlight_control(ctrl, duration=3, color='cyan', border_width=3)
        
        if highlight_success:
            print(f"✅ 高亮成功! 请观察控件是否被准确高亮")
        else:
            print(f"❌ 高亮失败")
        
        return True


def main():
    """主函数"""
    print("🖱️  鼠标悬停控件检测测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        tester = MouseHoverDetectionTester()
        
        print(f"\n🚀 开始检测当前鼠标位置的控件...")
        success = tester.test_current_mouse_position()
        
        if success:
            print(f"\n🎉 检测完成!")
        else:
            print(f"\n❌ 检测失败")
            print(f"💡 请确保:")
            print(f"   1. hellobig应用正在运行")
            print(f"   2. 鼠标悬停在控件上")
            print(f"   3. 控件可见且未被遮挡")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
