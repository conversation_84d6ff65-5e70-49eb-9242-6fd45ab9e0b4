#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
使用wlcctrl获取的真实窗口位置测试hellobig控件
修正AT-SPI坐标与实际窗口位置的偏移
"""

import sys
import os
import subprocess
import time
from offset_corrected_detector import OffsetCorrectedDetector


class RealOffsetTester:
    """使用真实窗口偏移的测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.detector = OffsetCorrectedDetector(target_app_name="hellobig")
        
        # 获取hellobig窗口的真实位置
        self.real_window_pos = self.get_real_window_position()
        
        # 基于探索器发现的AT-SPI控件位置（相对于AT-SPI坐标系）
        self.test_controls = [
            {
                'name': '普通按钮',
                'atspi_position': (20, 20),
                'size': (100, 30),
                'type': 'push button'
            },
            {
                'name': '切换按钮',
                'atspi_position': (140, 20),
                'size': (100, 30),
                'type': 'check box'
            },
            {
                'name': '复选框',
                'atspi_position': (20, 71),
                'size': (58, 22),
                'type': 'check box'
            },
            {
                'name': '单选按钮1',
                'atspi_position': (160, 71),
                'size': (54, 22),
                'type': 'radio button'
            },
            {
                'name': '单选按钮2',
                'atspi_position': (280, 71),
                'size': (54, 22),
                'type': 'radio button'
            },
            {
                'name': '用户名输入框',
                'atspi_position': (110, 110),
                'size': (200, 25),
                'type': 'text'
            },
            {
                'name': '密码输入框',
                'atspi_position': (420, 110),
                'size': (200, 25),
                'type': 'password text'
            },
            {
                'name': '音量滑块',
                'atspi_position': (110, 160),
                'size': (200, 20),
                'type': 'slider'
            },
            {
                'name': '数字输入框',
                'atspi_position': (330, 160),
                'size': (80, 25),
                'type': 'spin button'
            },
            {
                'name': '城市下拉框',
                'atspi_position': (110, 210),
                'size': (150, 25),
                'type': 'combo box'
            }
        ]
        
        print("🔧 使用真实窗口偏移的hellobig控件测试器")
        print("=" * 60)
        if self.real_window_pos:
            print(f"🪟 真实窗口位置: {self.real_window_pos}")
            print(f"📋 准备测试 {len(self.test_controls)} 个控件")
        else:
            print("❌ 无法获取真实窗口位置")
    
    def get_real_window_position(self):
        """使用wlcctrl获取hellobig窗口的真实位置"""
        try:
            print("🔍 使用wlcctrl查找hellobig窗口...")
            
            # 获取窗口列表
            result = subprocess.run(['wlcctrl', '--list'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ wlcctrl --list 失败: {result.stderr}")
                return None
            
            # 查找hellobig窗口ID
            window_id = None
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                # 查找包含"AT-SPI测试界面"的行，排除Qt Creator
                if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                    # 窗口ID在前一行
                    if i > 0:
                        prev_line = lines[i-1]
                        if 'toplevel' in prev_line:
                            window_id = prev_line.split('"')[1]
                            print(f"   找到窗口ID: {window_id}")
                            print(f"   窗口标题: {line.strip()}")
                            break

            # 如果没找到，再尝试查找其他可能的hellobig窗口
            if not window_id:
                for i, line in enumerate(lines):
                    if 'hellobig' in line.lower() and 'qt creator' not in line.lower():
                        if i > 0:
                            prev_line = lines[i-1]
                            if 'toplevel' in prev_line:
                                window_id = prev_line.split('"')[1]
                                print(f"   找到窗口ID: {window_id}")
                                print(f"   窗口标题: {line.strip()}")
                                break
            
            if not window_id:
                print("❌ 未找到hellobig窗口")
                return None
            
            # 获取窗口几何信息
            result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ 获取窗口几何信息失败: {result.stderr}")
                return None

            print(f"   几何信息输出: {repr(result.stdout)}")

            # 解析几何信息
            geometry_line = None
            for line in result.stdout.strip().split('\n'):
                if 'geometry:' in line:
                    geometry_line = line
                    break

            if not geometry_line:
                print("❌ 无法解析窗口几何信息")
                print(f"   完整输出: {result.stdout}")
                return None

            print(f"   几何信息行: {repr(geometry_line)}")

            # 解析坐标 "geometry: (564, 155) 1200 x 717"
            try:
                # 使用正则表达式提取坐标
                import re
                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)', geometry_line)
                if not match:
                    print(f"❌ 无法匹配坐标格式: {geometry_line}")
                    return None

                x, y = int(match.group(1)), int(match.group(2))
            except (ValueError, AttributeError) as e:
                print(f"❌ 解析坐标时出错: {e}")
                print(f"   几何信息行: {geometry_line}")
                return None
            
            print(f"   真实窗口位置: ({x}, {y})")
            return (x, y)
            
        except Exception as e:
            print(f"❌ 获取真实窗口位置失败: {e}")
            return None
    
    def calculate_real_screen_position(self, atspi_x, atspi_y):
        """计算控件在屏幕上的真实位置"""
        if not self.real_window_pos:
            return atspi_x, atspi_y
        
        real_x = self.real_window_pos[0] + atspi_x
        real_y = self.real_window_pos[1] + atspi_y
        return real_x, real_y
    
    def test_control_with_real_position(self, control_info):
        """使用真实屏幕位置测试控件"""
        atspi_x, atspi_y = control_info['atspi_position']
        width, height = control_info['size']
        
        # 计算AT-SPI中心点
        atspi_center_x = atspi_x + width // 2
        atspi_center_y = atspi_y + height // 2
        
        # 计算真实屏幕位置
        real_center_x, real_center_y = self.calculate_real_screen_position(atspi_center_x, atspi_center_y)
        
        print(f"\n🎯 测试控件: {control_info['name']}")
        print(f"   类型: {control_info['type']}")
        print(f"   AT-SPI位置: ({atspi_x}, {atspi_y}) 尺寸: {width}×{height}")
        print(f"   AT-SPI中心: ({atspi_center_x}, {atspi_center_y})")
        print(f"   真实屏幕中心: ({real_center_x}, {real_center_y})")
        print("-" * 40)
        
        # 使用真实屏幕坐标进行检测
        result = self.detector.get_control_at_point_corrected(real_center_x, real_center_y)
        
        if result['success']:
            detected_info = result['control_info']
            print(f"✅ 检测成功!")
            print(f"   检测到名称: {detected_info['name']}")
            print(f"   检测到类型: {detected_info['role']}")
            
            # 验证是否匹配
            name_match = control_info['name'] in detected_info['name'] or detected_info['name'] in control_info['name']
            type_match = control_info['type'] == detected_info['role']
            
            if name_match and type_match:
                print(f"   🎉 完全匹配!")
                return True
            elif name_match:
                print(f"   ⚠️  名称匹配，但类型不匹配 (期望: {control_info['type']}, 实际: {detected_info['role']})")
                return True
            elif type_match:
                print(f"   ⚠️  类型匹配，但名称不匹配 (期望: {control_info['name']}, 实际: {detected_info['name']})")
                return True
            else:
                print(f"   ❌ 不匹配 (期望: {control_info['name']}/{control_info['type']}, 实际: {detected_info['name']}/{detected_info['role']})")
                return False
        else:
            print(f"❌ 检测失败: {result['message']}")
            return False
    
    def test_control_with_highlight(self, control_info):
        """测试控件并高亮显示"""
        atspi_x, atspi_y = control_info['atspi_position']
        width, height = control_info['size']
        
        # 计算AT-SPI中心点
        atspi_center_x = atspi_x + width // 2
        atspi_center_y = atspi_y + height // 2
        
        # 计算真实屏幕位置
        real_center_x, real_center_y = self.calculate_real_screen_position(atspi_center_x, atspi_center_y)
        
        print(f"\n✨ 高亮测试控件: {control_info['name']}")
        print(f"   真实屏幕中心: ({real_center_x}, {real_center_y})")
        
        # 执行高亮
        result = self.detector.highlight_control_corrected(real_center_x, real_center_y, duration=3, color='red', border_width=3)
        
        if result['success'] and result['highlighted']:
            print(f"✅ 高亮成功!")
            detected_info = result['control_info']
            print(f"   检测到: {detected_info['name']} ({detected_info['role']})")
            return True
        else:
            print(f"❌ 高亮失败: {result['message']}")
            return False
    
    def test_all_controls(self):
        """测试所有控件"""
        if not self.real_window_pos:
            print("❌ 无法进行测试，未获取到真实窗口位置")
            return 0, 0
        
        print("🚀 开始测试所有控件（使用真实窗口偏移）...")
        
        success_count = 0
        total_count = len(self.test_controls)
        
        for i, control in enumerate(self.test_controls):
            print(f"\n📍 测试 {i+1}/{total_count}")
            
            if self.test_control_with_real_position(control):
                success_count += 1
        
        print(f"\n📊 测试结果统计:")
        print("=" * 40)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        return success_count, total_count
    
    def test_with_highlights(self):
        """测试并高亮显示所有控件"""
        if not self.real_window_pos:
            print("❌ 无法进行测试，未获取到真实窗口位置")
            return 0, 0
        
        print("\n✨ 开始高亮测试（使用真实窗口偏移）...")
        
        success_count = 0
        total_count = len(self.test_controls)
        
        for i, control in enumerate(self.test_controls):
            print(f"\n🎯 高亮测试 {i+1}/{total_count}")
            
            if self.test_control_with_highlight(control):
                success_count += 1
            
            # 等待一下，让用户看到高亮效果
            time.sleep(1.5)
        
        print(f"\n📊 高亮测试结果:")
        print("=" * 40)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        return success_count, total_count
    
    def interactive_test(self):
        """交互式测试"""
        print("\n🎮 交互式测试模式（真实窗口偏移）")
        print("=" * 40)
        print("选择测试模式:")
        print("1. 仅检测测试")
        print("2. 高亮测试")
        print("3. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-3): ").strip()
                
                if choice == '1':
                    self.test_all_controls()
                elif choice == '2':
                    self.test_with_highlights()
                elif choice == '3':
                    print("👋 退出测试")
                    break
                else:
                    print("❌ 无效选择，请输入 1-3")
                    
            except KeyboardInterrupt:
                print("\n👋 退出测试")
                break
            except Exception as e:
                print(f"❌ 测试异常: {e}")


def main():
    """主函数"""
    print("🔧 使用真实窗口偏移的hellobig控件测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        tester = RealOffsetTester()
        tester.interactive_test()
        
    except Exception as e:
        print(f"❌ 测试器启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
