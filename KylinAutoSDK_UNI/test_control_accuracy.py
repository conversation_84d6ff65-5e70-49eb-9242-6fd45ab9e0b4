#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
控件检测精度测试脚本
对比原版和修复版的检测精度

测试内容:
1. 坐标精度对比
2. 控件识别能力对比  
3. 高亮准确性对比
4. 性能对比
"""

import sys
import os
import time
import subprocess

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from at_spi_control_detector import ATSPIControlDetector
from fixed_control_detector import FixedControlDetector


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def test_detection_accuracy():
    """测试检测精度"""
    print("🎯 控件检测精度对比测试")
    print("=" * 80)
    
    # 获取测试坐标
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        test_positions = [
            (mouse_x, mouse_y, "鼠标当前位置"),
            (mouse_x - 50, mouse_y, "鼠标左侧50px"),
            (mouse_x + 50, mouse_y, "鼠标右侧50px"),
            (mouse_x, mouse_y - 50, "鼠标上方50px"),
            (mouse_x, mouse_y + 50, "鼠标下方50px")
        ]
    else:
        test_positions = [
            (500, 300, "屏幕中央"),
            (600, 300, "中央右侧"),
            (400, 300, "中央左侧"),
            (500, 200, "中央上方"),
            (500, 400, "中央下方")
        ]
    
    # 创建检测器实例
    print("📱 初始化检测器...")
    original_detector = ATSPIControlDetector()
    fixed_detector = FixedControlDetector()
    
    print("\n📊 开始对比测试:")
    print("-" * 80)
    
    results = []
    
    for i, (x, y, desc) in enumerate(test_positions):
        print(f"\n🔍 测试点 {i+1}: {desc} ({x}, {y})")
        print("=" * 40)
        
        test_result = {
            'position': (x, y),
            'description': desc,
            'original': {},
            'fixed': {}
        }
        
        # 测试原版检测器
        print("📋 原版检测器:")
        try:
            start_time = time.time()
            original_result = original_detector.get_control_at_point(x, y, include_details=True)
            original_time = time.time() - start_time
            
            if original_result['success']:
                pos = original_result['position']
                print(f"   ✅ 检测成功: ({pos[0]}, {pos[1]}) {pos[2]}×{pos[3]}")
                print(f"   ⏱️  耗时: {original_time:.3f}秒")
                
                test_result['original'] = {
                    'success': True,
                    'position': pos,
                    'time': original_time,
                    'info': original_result['control_info']
                }
            else:
                print(f"   ❌ 检测失败: {original_result['message']}")
                print(f"   ⏱️  耗时: {original_time:.3f}秒")
                
                test_result['original'] = {
                    'success': False,
                    'time': original_time,
                    'message': original_result['message']
                }
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            test_result['original'] = {'success': False, 'error': str(e)}
        
        # 测试修复版检测器
        print("\n🔧 修复版检测器:")
        try:
            start_time = time.time()
            fixed_result = fixed_detector.get_control_info_at_point(x, y)
            fixed_time = time.time() - start_time
            
            if fixed_result['success']:
                pos = fixed_result['position']
                info = fixed_result['control_info']
                print(f"   ✅ 检测成功: ({pos[0]}, {pos[1]}) {pos[2]}×{pos[3]}")
                print(f"   🏷️  控件名称: {info['name'] or 'N/A'}")
                print(f"   🎭 控件角色: {info['role']}")
                print(f"   ⏱️  耗时: {fixed_time:.3f}秒")
                
                test_result['fixed'] = {
                    'success': True,
                    'position': pos,
                    'time': fixed_time,
                    'info': info
                }
            else:
                print(f"   ❌ 检测失败: {fixed_result['message']}")
                print(f"   ⏱️  耗时: {fixed_time:.3f}秒")
                
                test_result['fixed'] = {
                    'success': False,
                    'time': fixed_time,
                    'message': fixed_result['message']
                }
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            test_result['fixed'] = {'success': False, 'error': str(e)}
        
        # 对比分析
        print(f"\n📊 对比分析:")
        original_success = test_result['original'].get('success', False)
        fixed_success = test_result['fixed'].get('success', False)
        
        if original_success and fixed_success:
            # 比较位置精度
            orig_pos = test_result['original']['position']
            fixed_pos = test_result['fixed']['position']
            
            pos_diff_x = abs(orig_pos[0] - fixed_pos[0])
            pos_diff_y = abs(orig_pos[1] - fixed_pos[1])
            size_diff_w = abs(orig_pos[2] - fixed_pos[2])
            size_diff_h = abs(orig_pos[3] - fixed_pos[3])
            
            print(f"   📏 位置差异: X={pos_diff_x}px, Y={pos_diff_y}px")
            print(f"   📐 尺寸差异: W={size_diff_w}px, H={size_diff_h}px")
            
            # 比较性能
            orig_time = test_result['original'].get('time', 0)
            fixed_time = test_result['fixed'].get('time', 0)
            time_diff = fixed_time - orig_time
            
            if time_diff > 0:
                print(f"   ⏱️  修复版慢 {time_diff:.3f}秒")
            else:
                print(f"   ⏱️  修复版快 {abs(time_diff):.3f}秒")
                
        elif not original_success and fixed_success:
            print(f"   🎯 修复版成功检测到原版未检测的控件")
        elif original_success and not fixed_success:
            print(f"   ⚠️  修复版未检测到原版检测的控件")
        else:
            print(f"   ❌ 两个版本都未检测到控件")
        
        results.append(test_result)
        
        time.sleep(0.5)  # 短暂暂停
    
    # 生成总结报告
    print("\n" + "=" * 80)
    print("📊 测试总结报告")
    print("=" * 80)
    
    original_success_count = sum(1 for r in results if r['original'].get('success', False))
    fixed_success_count = sum(1 for r in results if r['fixed'].get('success', False))
    
    print(f"🎯 检测成功率:")
    print(f"   原版: {original_success_count}/{len(results)} ({original_success_count/len(results)*100:.1f}%)")
    print(f"   修复版: {fixed_success_count}/{len(results)} ({fixed_success_count/len(results)*100:.1f}%)")
    
    # 计算平均性能
    original_times = [r['original'].get('time', 0) for r in results if 'time' in r['original']]
    fixed_times = [r['fixed'].get('time', 0) for r in results if 'time' in r['fixed']]
    
    if original_times and fixed_times:
        orig_avg = sum(original_times) / len(original_times)
        fixed_avg = sum(fixed_times) / len(fixed_times)
        
        print(f"\n⏱️  平均检测时间:")
        print(f"   原版: {orig_avg:.3f}秒")
        print(f"   修复版: {fixed_avg:.3f}秒")
        
        if fixed_avg < orig_avg:
            print(f"   🚀 修复版平均快 {orig_avg - fixed_avg:.3f}秒")
        else:
            print(f"   🐌 修复版平均慢 {fixed_avg - orig_avg:.3f}秒")
    
    # 分析控件类型识别能力
    print(f"\n🎭 控件类型识别能力:")
    fixed_roles = []
    for r in results:
        if r['fixed'].get('success') and 'info' in r['fixed']:
            role = r['fixed']['info'].get('role', '')
            if role and role not in fixed_roles:
                fixed_roles.append(role)
    
    if fixed_roles:
        print(f"   修复版识别的控件类型: {', '.join(fixed_roles)}")
    else:
        print(f"   修复版未识别到具体控件类型")
    
    return results


def test_highlight_accuracy():
    """测试高亮精度"""
    print("\n\n✨ 高亮精度测试")
    print("=" * 50)
    
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is None:
        print("❌ 无法获取鼠标位置，跳过高亮测试")
        return
    
    print(f"测试位置: ({mouse_x}, {mouse_y})")
    
    fixed_detector = FixedControlDetector()
    
    print("\n🔧 修复版高亮测试:")
    result = fixed_detector.highlight_control_at_point(mouse_x, mouse_y, duration=3, color='green')
    
    if result['success']:
        if result['highlighted']:
            pos = result['control_position']
            print(f"✅ 高亮成功! 位置: ({pos[0]}, {pos[1]}) 尺寸: {pos[2]}×{pos[3]}")
            
            # 验证坐标合理性
            if pos[0] >= 0 and pos[1] >= 0 and pos[2] > 0 and pos[3] > 0:
                print(f"✅ 坐标验证通过")
            else:
                print(f"❌ 坐标异常: 位置或尺寸包含无效值")
        else:
            print(f"❌ 高亮失败: {result['message']}")
    else:
        print(f"❌ 检测失败: {result['message']}")


def main():
    """主函数"""
    print("🧪 AT-SPI控件检测精度测试")
    print("=" * 80)
    
    # 检查环境
    if not sys.platform.startswith('linux'):
        print("❌ 此程序仅支持Linux系统")
        sys.exit(1)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        # 运行检测精度测试
        results = test_detection_accuracy()
        
        # 运行高亮精度测试
        test_highlight_accuracy()
        
        print("\n🎉 测试完成!")
        print("\n💡 建议:")
        print("1. 如果修复版检测成功率更高，说明修复有效")
        print("2. 观察高亮位置是否准确对应到实际控件")
        print("3. 测试不同类型的控件（按钮、标签、输入框等）")
        
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()