#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
严格的可见性检测器
添加多重验证机制来确保窗口真的可见
"""

import sys
import os
import subprocess
import pyatspi
import re
import time


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def verify_window_actually_visible(window_info):
    """严格验证窗口是否真的可见"""
    print(f"🔍 严格验证窗口可见性: {window_info['title']}")
    
    # 验证1: 检查窗口是否在活动窗口列表中
    try:
        result = subprocess.run(['wlcctrl', '--getactivewindow'], capture_output=True, text=True)
        if result.returncode == 0:
            active_window_id = result.stdout.strip()
            if window_info['window_id'] == active_window_id:
                print(f"   ✅ 窗口是当前活动窗口")
                return True
            else:
                print(f"   ⚠️  窗口不是活动窗口 (活动窗口: {active_window_id})")
        else:
            print(f"   ⚠️  无法获取活动窗口信息")
    except Exception as e:
        print(f"   ⚠️  检查活动窗口失败: {e}")
    
    # 验证2: 尝试与窗口交互
    try:
        # 尝试获取窗口的详细状态
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_info['window_id']], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'minimized:' in line.lower() and 'true' in line.lower():
                    print(f"   ❌ wlcctrl确认窗口已最小化")
                    return False
                elif 'visible:' in line.lower() and 'false' in line.lower():
                    print(f"   ❌ wlcctrl确认窗口不可见")
                    return False
        else:
            print(f"   ⚠️  无法获取窗口详细状态")
    except Exception as e:
        print(f"   ⚠️  检查窗口状态失败: {e}")
    
    # 验证3: 用户确认机制
    print(f"   🤔 请用户确认:")
    print(f"      窗口标题: {window_info['title']}")
    print(f"      窗口位置: {window_info['position']}")
    print(f"      窗口尺寸: {window_info['size']}")
    print(f"      💡 这个窗口现在是否真的在桌面上可见？")
    print(f"      💡 如果窗口已最小化或隐藏，检测器应该忽略它")
    
    # 简单的自动判断：如果鼠标在窗口范围内但窗口实际不可见，
    # 可能是窗口信息过时了
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x and mouse_y:
        x, y = window_info['position']
        width, height = window_info['size']
        
        if (x <= mouse_x < x + width and y <= mouse_y < y + height):
            print(f"   🖱️  鼠标在窗口几何范围内")
            print(f"   💡 如果用户看不到这个窗口，说明几何信息过时了")
        else:
            print(f"   🖱️  鼠标不在窗口几何范围内")
            print(f"   ❌ 窗口几何信息可能不准确")
            return False
    
    # 验证4: 检查窗口是否被其他窗口完全遮挡
    # 这里可以添加更复杂的遮挡检测逻辑
    
    # 保守策略：如果无法确定，返回False
    print(f"   ⚠️  无法确定窗口可见性，采用保守策略")
    return False


def get_user_confirmation(window_info):
    """获取用户确认"""
    print(f"\n❓ 用户确认请求:")
    print(f"=" * 50)
    print(f"检测到窗口: {window_info['title']}")
    print(f"位置: {window_info['position']}")
    print(f"尺寸: {window_info['size']}")
    print(f"")
    print(f"请确认:")
    print(f"1. 这个窗口现在是否在桌面上可见？")
    print(f"2. 这个窗口是否是你鼠标悬停的窗口？")
    print(f"3. 如果窗口已最小化或隐藏，请回答 'n'")
    print(f"")
    
    # 在实际应用中，这里可以添加GUI确认对话框
    # 或者通过其他方式获取用户输入
    
    # 目前返回False，要求明确的可见性证据
    print(f"💡 由于无法获取用户输入，采用保守策略：假设窗口不可见")
    return False


def main():
    """主函数"""
    print("🔍 严格的窗口可见性验证器")
    print("=" * 60)
    print("🎯 多重验证机制确保窗口真的可见")
    print("❌ 防止在虚无位置绘制控件")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        mouse_x, mouse_y = get_mouse_position()
        if not mouse_x:
            print("❌ 无法获取鼠标位置")
            return
        
        print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        
        # 获取鼠标位置下的窗口
        try:
            result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ wlcctrl命令失败")
                return
            
            matching_windows = []
            lines = result.stdout.strip().split('\n')
            
            i = 0
            while i < len(lines):
                line = lines[i]
                if 'toplevel' in line:
                    window_id = line.split('"')[1]
                    
                    window_title = ""
                    if i + 1 < len(lines):
                        title_line = lines[i + 1].strip()
                        if title_line.startswith('title: '):
                            window_title = title_line[7:]
                        else:
                            window_title = title_line
                    
                    # 获取几何信息
                    geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                              capture_output=True, text=True)
                    if geo_result.returncode == 0:
                        for geo_line in geo_result.stdout.strip().split('\n'):
                            if 'geometry:' in geo_line:
                                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                                if match:
                                    x, y, width, height = map(int, match.groups())
                                    
                                    # 检查鼠标是否在窗口范围内
                                    if (x <= mouse_x < x + width and y <= mouse_y < y + height):
                                        window_info = {
                                            'window_id': window_id,
                                            'title': window_title,
                                            'position': (x, y),
                                            'size': (width, height),
                                            'area': width * height
                                        }
                                        matching_windows.append(window_info)
                                    break
                    
                    i += 2
                else:
                    i += 1
            
            if not matching_windows:
                print("❌ 鼠标位置下没有找到任何窗口")
                return
            
            print(f"\n📋 找到 {len(matching_windows)} 个匹配窗口:")
            for i, window in enumerate(matching_windows):
                print(f"   {i+1}. {window['title']}")
                print(f"      位置: {window['position']} 尺寸: {window['size'][0]}×{window['size'][1]}")
            
            # 过滤系统窗口
            app_windows = [w for w in matching_windows 
                          if w['title'].lower() not in ['桌面', 'desktop', 'ukui-panel', 'ukui-sidebar']]
            
            if not app_windows:
                print("❌ 没有找到应用窗口")
                return
            
            # 选择面积最小的窗口
            target_window = min(app_windows, key=lambda w: w['area'])
            
            print(f"\n🎯 目标窗口: {target_window['title']}")
            
            # 严格验证窗口可见性
            if verify_window_actually_visible(target_window):
                print(f"\n✅ 窗口通过严格可见性验证")
                print(f"   可以安全地进行控件检测和高亮")
            else:
                print(f"\n❌ 窗口未通过严格可见性验证")
                print(f"   拒绝进行控件检测，防止在虚无位置绘制")
                print(f"   💡 这可能是因为:")
                print(f"      - 窗口已最小化")
                print(f"      - 窗口被隐藏")
                print(f"      - 窗口几何信息过时")
                print(f"      - wlcctrl信息不准确")
        
        except Exception as e:
            print(f"❌ 检测失败: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
