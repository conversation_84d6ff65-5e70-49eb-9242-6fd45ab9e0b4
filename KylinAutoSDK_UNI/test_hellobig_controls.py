#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
hellobig应用控件识别测试
快速准确识别各类控件并进行高亮显示
"""

import sys
import os
import time
import subprocess
from collections import defaultdict

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def find_hellobig_window():
    """查找hellobig应用窗口"""
    try:
        from uni_sdk.utils.helpers import get_windows_by_wlcctrl
        
        windows = get_windows_by_wlcctrl()
        
        for title, geometry in windows.items():
            if 'hellobig' in title.lower():
                print(f"✅ 找到hellobig窗口: {title}")
                print(f"   窗口位置: ({geometry['x']}, {geometry['y']})")
                print(f"   窗口大小: {geometry['width']} × {geometry['height']}")
                return {
                    'title': title,
                    'geometry': geometry
                }
        
        print("❌ 未找到hellobig应用窗口")
        return None
        
    except Exception as e:
        print(f"❌ 查找窗口失败: {e}")
        return None

def analyze_hellobig_structure():
    """分析hellobig应用的界面结构"""
    print("🔍 分析hellobig应用界面结构")
    print("=" * 50)
    
    try:
        # 使用优化版本进行快速分析
        from UNI_optimized import UNI_Optimized
        
        uni = UNI_Optimized()
        
        # 查找hellobig窗口
        window_info = find_hellobig_window()
        if not window_info:
            return None
        
        geometry = window_info['geometry']
        
        # 定义一些关键测试点
        test_points = []
        
        # 窗口区域的网格采样点
        x_start = geometry['x'] + 20
        y_start = geometry['y'] + 50  # 跳过标题栏
        x_end = geometry['x'] + geometry['width'] - 20
        y_end = geometry['y'] + geometry['height'] - 20
        
        # 生成网格测试点（5x5网格）
        for i in range(5):
            for j in range(5):
                x = x_start + (x_end - x_start) * i // 4
                y = y_start + (y_end - y_start) * j // 4
                test_points.append((x, y))
        
        print(f"📋 生成 {len(test_points)} 个测试点")
        print(f"   窗口范围: ({x_start}, {y_start}) 到 ({x_end}, {y_end})")
        
        # 分析每个测试点的控件
        controls_found = []
        control_types = defaultdict(int)
        
        for i, (x, y) in enumerate(test_points):
            print(f"\n📍 测试点 {i+1}/25: ({x}, {y})")
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(x, y, quick=True, highlight=False)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    # 避免重复记录相同控件
                    control_key = f"{role}_{name}_{coords.get('x', 0)}_{coords.get('y', 0)}"
                    if control_key not in [c['key'] for c in controls_found]:
                        control_info = {
                            'key': control_key,
                            'name': name,
                            'role': role,
                            'coords': coords,
                            'test_point': (x, y),
                            'time': elapsed
                        }
                        controls_found.append(control_info)
                        control_types[role] += 1
                    
                    print(f"   ✅ 找到控件 ({elapsed:.2f}s): {name} ({role})")
                    if coords:
                        print(f"      坐标: ({coords.get('x')}, {coords.get('y')}) 大小: {coords.get('width')}×{coords.get('height')}")
                
                else:
                    print(f"   ❌ 未找到控件 ({elapsed:.2f}s): {info}")
                
                time.sleep(0.1)  # 短暂延迟避免过快
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
        
        # 统计结果
        print(f"\n📊 界面结构分析结果")
        print("=" * 40)
        print(f"总共找到 {len(controls_found)} 个不同的控件")
        print(f"控件类型统计:")
        
        for role, count in sorted(control_types.items()):
            print(f"  - {role}: {count} 个")
        
        return {
            'window_info': window_info,
            'controls': controls_found,
            'control_types': control_types,
            'test_points': test_points
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_specific_controls(analysis_result):
    """测试特定控件的识别和高亮"""
    print(f"\n🎯 测试特定控件识别和高亮")
    print("=" * 50)
    
    if not analysis_result:
        print("❌ 没有分析结果，无法进行测试")
        return
    
    try:
        from UNI_optimized import UNI_Optimized
        
        uni = UNI_Optimized()
        controls = analysis_result['controls']
        
        # 按控件类型分组
        controls_by_type = defaultdict(list)
        for control in controls:
            controls_by_type[control['role']].append(control)
        
        # 测试每种类型的代表性控件
        for role, role_controls in controls_by_type.items():
            if not role_controls:
                continue
                
            # 选择第一个控件进行测试
            test_control = role_controls[0]
            test_point = test_control['test_point']
            
            print(f"\n🔍 测试 {role} 控件")
            print(f"   控件名称: {test_control['name']}")
            print(f"   测试坐标: {test_point}")
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(
                    test_point[0], test_point[1], 
                    quick=True, 
                    highlight=True  # 启用高亮
                )
                elapsed = time.time() - start_time
                
                if control_data:
                    print(f"   ✅ 识别成功 ({elapsed:.2f}s) - 已高亮显示")
                    
                    # 显示详细信息
                    coords = control_data.get('Coords', {})
                    if coords:
                        print(f"      位置: ({coords.get('x')}, {coords.get('y')})")
                        print(f"      大小: {coords.get('width')} × {coords.get('height')}")
                    
                    # 等待查看高亮效果
                    time.sleep(2)
                    
                else:
                    print(f"   ❌ 识别失败 ({elapsed:.2f}s): {info}")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
    except Exception as e:
        print(f"❌ 控件测试失败: {e}")

def interactive_control_test():
    """交互式控件测试 - 跟随鼠标进行实时识别"""
    print(f"\n🖱️ 交互式控件测试")
    print("=" * 50)
    print("将鼠标移动到hellobig应用的不同控件上，按Enter进行识别和高亮")
    print("输入 'q' 退出测试")
    
    try:
        from UNI_optimized import UNI_Optimized
        
        uni = UNI_Optimized()
        test_count = 0
        
        while True:
            # 获取当前鼠标位置
            mouse_x, mouse_y = get_mouse_position()
            if mouse_x is None:
                print("❌ 无法获取鼠标位置")
                break
            
            print(f"\n当前鼠标位置: ({mouse_x}, {mouse_y})")
            user_input = input("按Enter进行识别，输入'q'退出: ").strip().lower()
            
            if user_input == 'q':
                break
            
            test_count += 1
            print(f"\n🔍 第{test_count}次测试 - 坐标: ({mouse_x}, {mouse_y})")
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(
                    mouse_x, mouse_y,
                    quick=True,
                    highlight=True  # 启用高亮
                )
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    print(f"✅ 识别成功 ({elapsed:.2f}s)")
                    print(f"   控件名称: {name}")
                    print(f"   控件类型: {role}")
                    
                    if coords:
                        print(f"   控件位置: ({coords.get('x')}, {coords.get('y')})")
                        print(f"   控件大小: {coords.get('width')} × {coords.get('height')}")
                    
                    print("🎨 已显示高亮边框")
                    
                else:
                    print(f"❌ 未找到控件 ({elapsed:.2f}s)")
                    print(f"   详情: {info}")
                
            except Exception as e:
                print(f"❌ 识别异常: {e}")
        
        print(f"\n📊 交互测试统计: 共进行了 {test_count} 次测试")
        
    except Exception as e:
        print(f"❌ 交互测试失败: {e}")

def test_performance_on_hellobig():
    """在hellobig应用上进行性能测试"""
    print(f"\n⚡ hellobig应用性能测试")
    print("=" * 50)
    
    try:
        from UNI_optimized import UNI_Optimized
        
        uni = UNI_Optimized()
        
        # 查找hellobig窗口
        window_info = find_hellobig_window()
        if not window_info:
            return
        
        geometry = window_info['geometry']
        
        # 生成10个随机测试点
        import random
        test_points = []
        
        for i in range(10):
            x = random.randint(geometry['x'] + 50, geometry['x'] + geometry['width'] - 50)
            y = random.randint(geometry['y'] + 50, geometry['y'] + geometry['height'] - 50)
            test_points.append((x, y))
        
        print(f"📋 生成 {len(test_points)} 个随机测试点")
        
        # 性能测试
        times = []
        success_count = 0
        
        for i, (x, y) in enumerate(test_points):
            print(f"测试 {i+1}/10: ({x}, {y})", end=" ")
            
            start_time = time.time()
            control_data, info = uni.kdk_getElement_Uni(x, y, quick=True, highlight=False)
            elapsed = time.time() - start_time
            
            times.append(elapsed)
            
            if control_data:
                success_count += 1
                name = control_data.get('Name', 'N/A')
                role = control_data.get('Rolename', 'N/A')
                print(f"✅ {elapsed:.2f}s - {name} ({role})")
            else:
                print(f"❌ {elapsed:.2f}s - {info}")
            
            time.sleep(0.1)
        
        # 统计结果
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        success_rate = success_count / len(test_points) * 100
        
        print(f"\n📊 性能测试结果:")
        print(f"   平均时间: {avg_time:.2f}s")
        print(f"   最快时间: {min_time:.2f}s")
        print(f"   最慢时间: {max_time:.2f}s")
        print(f"   成功率: {success_rate:.1f}% ({success_count}/{len(test_points)})")
        
        # 性能评估
        if avg_time <= 1.0:
            print("✅ 性能优秀: 平均时间在1秒以内")
        elif avg_time <= 2.0:
            print("⚠️ 性能一般: 平均时间1-2秒")
        else:
            print("❌ 性能较差: 平均时间超过2秒")
        
        if success_rate >= 80:
            print("✅ 识别率优秀: 成功率超过80%")
        elif success_rate >= 60:
            print("⚠️ 识别率一般: 成功率60-80%")
        else:
            print("❌ 识别率较差: 成功率低于60%")
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def main():
    """主函数"""
    print("hellobig应用控件识别测试")
    print("=" * 60)
    print("🎯 目标: 快速准确识别各类控件并进行高亮显示")
    print()
    
    # 检查hellobig应用是否运行
    window_info = find_hellobig_window()
    if not window_info:
        print("❌ 请确保hellobig应用正在运行")
        return 1
    
    print("✅ hellobig应用已就绪，开始测试")
    print()
    
    # 1. 分析界面结构
    print("🔍 步骤1: 分析界面结构")
    analysis_result = analyze_hellobig_structure()
    
    if analysis_result:
        # 2. 测试特定控件
        print("\n🎯 步骤2: 测试特定控件识别和高亮")
        test_specific_controls(analysis_result)
        
        # 3. 性能测试
        print("\n⚡ 步骤3: 性能测试")
        test_performance_on_hellobig()
        
        # 4. 交互式测试
        print("\n🖱️ 步骤4: 交互式测试")
        interactive_control_test()
    
    print("\n" + "=" * 60)
    print("🎉 hellobig控件识别测试完成!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())