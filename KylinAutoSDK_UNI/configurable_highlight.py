#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
可配置的无边框高亮 - 最终解决方案
"""

import subprocess
import time
import os

def highlight_element(x, y, width, height, 
                     duration=2,
                     color='red', 
                     border_width=2,
                     style='border_only'):
    """
    无边框控件高亮函数 - 最终解决方案
    
    Args:
        x, y: 控件位置
        width, height: 控件尺寸  
        duration: 高亮持续时间（秒）
        color: 边框颜色 ('red', 'blue', 'green', 'yellow', 'purple', 'cyan', 'orange')
        border_width: 边框宽度 (1-10)
        style: 高亮样式
            - 'border_only': 仅边框，完全透明背景
            - 'light_fill': 细边框 + 淡色填充
            - 'classic': 经典样式，稍厚边框
    
    Returns:
        bool: 是否成功显示高亮
    """
    
    # 样式配置
    style_config = {
        'border_only': {
            'bg_alpha': 0.0,
            'border_width': border_width
        },
        'light_fill': {
            'bg_alpha': 0.15,
            'border_width': border_width
        },
        'classic': {
            'bg_alpha': 0.2,
            'border_width': max(3, border_width)
        }
    }
    
    config = style_config.get(style, style_config['border_only'])
    
    # 创建X11高亮脚本
    x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    # 连接X11
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取颜色
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 计算背景色
    bg_alpha = {config['bg_alpha']}
    if bg_alpha > 0:
        bg_pixel = color_pixel  # 使用边框色作为背景色
    else:
        bg_pixel = 0  # 完全透明
    
    # 创建高亮窗口
    win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width={config['border_width']},
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=bg_pixel,
        border_pixel=color_pixel,
        override_redirect=True,  # 关键：绕过窗口管理器
        colormap=X.CopyFromParent
    )
    
    # 设置窗口属性（防止窗口管理器干预）
    win.set_wm_name("Element Highlight")
    win.set_wm_class("highlight", "Highlight")
    
    # 设置窗口类型和状态
    try:
        win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
        win.change_property(
            disp.intern_atom("_NET_WM_STATE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
             disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
             disp.intern_atom("_NET_WM_STATE_ABOVE")]
        )
    except:
        pass
    
    # 显示窗口
    win.map()
    disp.sync()
    
    # 等待指定时间
    time.sleep({duration})
    
    # 清理
    win.unmap()
    win.destroy()
    disp.sync()
    disp.close()
    
    # 返回成功标志
    exit(0)
    
except Exception as e:
    print(f"高亮失败: {{e}}")
    exit(1)
'''
    
    try:
        # 执行高亮
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"高亮执行失败: {e}")
        return False

def demo_highlight_styles():
    """演示不同高亮样式"""
    print("🎯 无边框高亮样式演示")
    print("=" * 60)
    
    # 获取鼠标位置作为演示位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    demo_x = mouse_x - 100
    demo_y = mouse_y - 75
    demo_w = 200
    demo_h = 150
    
    styles = [
        ('border_only', 'red', 2, '仅红色细边框'),
        ('light_fill', 'blue', 2, '蓝色边框+淡填充'),
        ('classic', 'green', 3, '绿色经典样式'),
        ('border_only', 'purple', 1, '紫色超细边框'),
    ]
    
    print(f"演示位置: ({demo_x}, {demo_y}) {demo_w}×{demo_h}")
    print()
    
    for style, color, width, desc in styles:
        print(f"🎨 演示: {desc}")
        success = highlight_element(demo_x, demo_y, demo_w, demo_h,
                                  duration=2, color=color, 
                                  border_width=width, style=style)
        
        if success:
            print("✅ 成功")
        else:
            print("❌ 失败")
        print()
    
    print("=" * 60)
    print("🎊 演示完成！")
    print("\n💡 使用说明:")
    print("```python")
    print("# 简单使用")
    print("highlight_element(100, 100, 200, 150)")
    print("")
    print("# 自定义样式")
    print("highlight_element(100, 100, 200, 150,")
    print("                duration=3,")
    print("                color='blue',") 
    print("                border_width=2,")
    print("                style='border_only')")
    print("```")

# 便捷预设函数
def quick_highlight(x, y, width, height, duration=2):
    """快速高亮 - 红色细边框"""
    return highlight_element(x, y, width, height, duration, 'red', 2, 'border_only')

def subtle_highlight(x, y, width, height, duration=2):
    """精细高亮 - 蓝色超细边框"""
    return highlight_element(x, y, width, height, duration, 'blue', 1, 'border_only')

def classic_highlight(x, y, width, height, duration=2):
    """经典高亮 - 绿色填充样式"""
    return highlight_element(x, y, width, height, duration, 'green', 3, 'classic')

if __name__ == "__main__":
    demo_highlight_styles()