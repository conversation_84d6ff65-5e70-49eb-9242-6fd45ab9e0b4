#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
修复的高亮函数
正确处理AT-SPI坐标转换为桌面坐标
"""

import subprocess
import re
import sys
import os

def get_hellobig_window_geometry():
    """获取hellobig窗口的真实几何信息"""
    try:
        # 列出所有窗口找到hellobig
        result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 查找AT-SPI测试界面窗口
        lines = result.stdout.split('\n')
        hellobig_uuid = None
        
        for i, line in enumerate(lines):
            if 'AT-SPI测试界面' in line:
                # 查找前一行的UUID
                for j in range(i-1, -1, -1):
                    if 'toplevel' in lines[j]:
                        uuid_match = re.search(r'"([^"]+)"', lines[j])
                        if uuid_match:
                            hellobig_uuid = uuid_match.group(1)
                            break
                break
        
        if not hellobig_uuid:
            return None
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', hellobig_uuid], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 解析几何信息
        geo_match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', result.stdout)
        if geo_match:
            x, y, width, height = map(int, geo_match.groups())
            return {'x': x, 'y': y, 'width': width, 'height': height}
        
        return None
            
    except Exception:
        return None

def highlight_control_accurately(control_data, target_x, target_y):
    """
    精确高亮控件
    修正AT-SPI坐标到桌面坐标的转换
    """
    if not control_data:
        return False
    
    try:
        # 获取hellobig窗口的真实位置
        window_geo = get_hellobig_window_geometry()
        if not window_geo:
            print("❌ 无法获取hellobig窗口位置")
            return False
        
        print(f"🔧 坐标修正分析:")
        print(f"   hellobig窗口位置: ({window_geo['x']}, {window_geo['y']})")
        print(f"   目标坐标: ({target_x}, {target_y})")
        
        # 获取AT-SPI控件坐标
        coords = control_data.get('Coords', {})
        if not coords:
            print("❌ 控件没有坐标信息")
            return False
        
        atspi_x = coords.get('x', 0)
        atspi_y = coords.get('y', 0)
        atspi_w = coords.get('width', 0)
        atspi_h = coords.get('height', 0)
        
        print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y}) 大小: {atspi_w}×{atspi_h}")
        
        # 关键修正：将AT-SPI相对坐标转换为桌面绝对坐标
        desktop_x = window_geo['x'] + atspi_x
        desktop_y = window_geo['y'] + atspi_y
        
        print(f"   转换后桌面坐标: ({desktop_x}, {desktop_y})")
        
        # 验证转换是否合理
        diff_x = desktop_x - target_x
        diff_y = desktop_y - target_y
        print(f"   与目标坐标偏差: X={diff_x}, Y={diff_y}")
        
        # 使用转换后的坐标进行高亮
        try:
            # 导入高亮函数
            sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
            from ultimate_highlight import ultimate_highlight
            
            print(f"🎨 精确高亮显示:")
            print(f"   位置: ({desktop_x}, {desktop_y})")
            print(f"   大小: {atspi_w} × {atspi_h}")
            
            success = ultimate_highlight(
                x=desktop_x,
                y=desktop_y,
                width=max(atspi_w, 10),  # 确保最小宽度
                height=max(atspi_h, 10), # 确保最小高度
                duration=3,
                color='red',
                border_width=3
            )
            
            if success:
                print("✅ 精确高亮显示成功")
                return True
            else:
                print("❌ 高亮显示失败")
                return False
            
        except Exception as e:
            print(f"❌ 高亮显示异常: {e}")
            return False
    
    except Exception as e:
        print(f"❌ 坐标修正失败: {e}")
        return False

def test_accurate_highlighting():
    """测试精确高亮"""
    print("🎯 测试精确高亮修正")
    print("=" * 50)
    
    # 获取hellobig窗口位置
    window_geo = get_hellobig_window_geometry()
    if not window_geo:
        print("❌ 无法获取窗口位置")
        return
    
    print(f"✅ hellobig窗口: ({window_geo['x']}, {window_geo['y']}) {window_geo['width']}×{window_geo['height']}")
    
    # 测试几个窗口内的位置
    test_positions = [
        (window_geo['x'] + 50, window_geo['y'] + 50, "左上角"),
        (window_geo['x'] + 200, window_geo['y'] + 100, "上方中央"),
        (window_geo['x'] + 400, window_geo['y'] + 200, "中央区域"),
    ]
    
    try:
        # 导入UNI进行控件识别
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        from UNI_new import UNI
        
        uni = UNI()
        
        for abs_x, abs_y, description in test_positions:
            print(f"\n📍 测试 {description} - 位置: ({abs_x}, {abs_y})")
            
            # 先用普通方式识别
            control_data, info = uni.kdk_getElement_Uni(abs_x, abs_y, quick=False, highlight=False)
            
            if control_data:
                name = control_data.get('Name', 'N/A')
                role = control_data.get('Rolename', 'N/A')
                print(f"   ✅ 识别到控件: {name} ({role})")
                
                # 使用修正的高亮函数
                success = highlight_control_accurately(control_data, abs_x, abs_y)
                
                if success:
                    print(f"   🎨 精确高亮成功，请观察红色边框位置")
                else:
                    print(f"   ❌ 精确高亮失败")
            else:
                print(f"   ❌ 未识别到控件: {info}")
            
            import time
            time.sleep(4)  # 等待观察高亮效果
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("修复的精确高亮测试")
    print("=" * 60)
    print("🎯 目标: 修正AT-SPI坐标转换，实现精确高亮")
    print()
    
    test_accurate_highlighting()
    
    print("\n" + "=" * 60)
    print("🎉 精确高亮测试完成!")
    print("💡 观察红色边框是否准确对准了识别的控件")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())