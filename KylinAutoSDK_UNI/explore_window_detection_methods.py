#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
探索窗口检测方法
寻找在Wayland环境中可靠检测窗口最小化状态的方法
"""

import sys
import os
import subprocess
import re
import time


def test_wlcctrl_methods():
    """测试wlcctrl的各种方法"""
    print("🔍 测试wlcctrl的各种方法...")
    
    # 获取hellobig窗口ID
    hellobig_windows = []
    
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            i = 0
            while i < len(lines):
                line = lines[i]
                if 'toplevel' in line:
                    window_id = line.split('"')[1]
                    
                    window_title = ""
                    if i + 1 < len(lines):
                        title_line = lines[i + 1].strip()
                        if title_line.startswith('title: '):
                            window_title = title_line[7:]
                        else:
                            window_title = title_line
                    
                    if ('AT-SPI测试' in window_title or 'hellobig' in window_title.lower()):
                        hellobig_windows.append((window_id, window_title))
                    
                    i += 2
                else:
                    i += 1
    except Exception as e:
        print(f"   ❌ 获取窗口列表失败: {e}")
        return
    
    if not hellobig_windows:
        print("   ❌ 未找到hellobig窗口")
        return
    
    print(f"   找到 {len(hellobig_windows)} 个hellobig窗口")
    
    # 测试各种wlcctrl方法
    wlcctrl_methods = [
        '--getwindowgeometry',
        '--getwindowstate',
        '--getwindowinfo',
        '--getwindowproperties',
        '--iswindowminimized',
        '--iswindowvisible',
        '--iswindowactive',
        '--getwindowworkspace',
        '--getwindowclass',
        '--getwindowpid'
    ]
    
    for window_id, window_title in hellobig_windows:
        print(f"\n   🪟 测试窗口: {window_title}")
        print(f"      ID: {window_id}")
        
        for method in wlcctrl_methods:
            try:
                result = subprocess.run(['wlcctrl', method, window_id], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    output = result.stdout.strip()
                    if output:
                        print(f"      ✅ {method}: {output}")
                    else:
                        print(f"      ✅ {method}: (空输出)")
                else:
                    print(f"      ❌ {method}: {result.stderr.strip()}")
            
            except subprocess.TimeoutExpired:
                print(f"      ⏰ {method}: 超时")
            except Exception as e:
                print(f"      ❌ {method}: {e}")


def test_process_based_detection():
    """测试基于进程的检测方法"""
    print("\n🔍 测试基于进程的检测方法...")
    
    try:
        # 查找hellobig进程
        result = subprocess.run(['pgrep', '-f', 'hellobig'], capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"   找到 {len(pids)} 个hellobig进程: {pids}")
            
            for pid in pids:
                if pid.strip():
                    print(f"\n   📊 分析进程 {pid}:")
                    
                    # 获取进程信息
                    try:
                        ps_result = subprocess.run(['ps', '-p', pid, '-o', 'pid,ppid,cmd,stat'], 
                                                 capture_output=True, text=True)
                        if ps_result.returncode == 0:
                            print(f"      进程信息: {ps_result.stdout.strip()}")
                    except Exception as e:
                        print(f"      ❌ 获取进程信息失败: {e}")
                    
                    # 检查进程的窗口
                    try:
                        # 使用/proc/pid/environ检查环境变量
                        with open(f'/proc/{pid}/environ', 'rb') as f:
                            environ = f.read().decode('utf-8', errors='ignore')
                            if 'DISPLAY' in environ:
                                print(f"      ✅ 进程有DISPLAY环境变量")
                            if 'WAYLAND_DISPLAY' in environ:
                                print(f"      ✅ 进程有WAYLAND_DISPLAY环境变量")
                    except Exception as e:
                        print(f"      ⚠️  无法读取环境变量: {e}")
        else:
            print("   ❌ 未找到hellobig进程")
    
    except Exception as e:
        print(f"   ❌ 进程检测失败: {e}")


def test_focus_based_detection():
    """测试基于焦点的检测方法"""
    print("\n🔍 测试基于焦点的检测方法...")
    
    try:
        # 获取当前活动窗口
        result = subprocess.run(['wlcctrl', '--getactivewindow'], capture_output=True, text=True)
        if result.returncode == 0:
            active_window_id = result.stdout.strip()
            print(f"   当前活动窗口ID: {active_window_id}")
            
            # 获取活动窗口标题
            title_result = subprocess.run(['wlcctrl', '--getwindowtitle', active_window_id], 
                                        capture_output=True, text=True)
            if title_result.returncode == 0:
                active_title = title_result.stdout.strip()
                print(f"   当前活动窗口: {active_title}")
                
                if 'hellobig' in active_title.lower() or 'AT-SPI测试' in active_title:
                    print(f"   ✅ hellobig是当前活动窗口 → 很可能可见")
                else:
                    print(f"   ❌ hellobig不是当前活动窗口 → 可能不可见")
        else:
            print(f"   ❌ 无法获取活动窗口")
    
    except Exception as e:
        print(f"   ❌ 焦点检测失败: {e}")


def test_interaction_based_detection():
    """测试基于交互的检测方法"""
    print("\n🔍 测试基于交互的检测方法...")
    
    # 获取hellobig窗口
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            print("   ❌ 无法获取窗口列表")
            return
        
        hellobig_window_id = None
        lines = result.stdout.strip().split('\n')
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                if 'AT-SPI测试界面' in window_title:
                    hellobig_window_id = window_id
                    print(f"   找到目标窗口: {window_title} ({window_id})")
                    break
                
                i += 2
            else:
                i += 1
        
        if not hellobig_window_id:
            print("   ❌ 未找到AT-SPI测试界面窗口")
            return
        
        # 测试1: 尝试激活窗口
        print(f"\n   🧪 测试1: 尝试激活窗口...")
        try:
            result = subprocess.run(['wlcctrl', '--windowactivate', hellobig_window_id], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"      ✅ 激活命令成功")
                
                # 等待一下，然后检查是否真的成为了活动窗口
                time.sleep(1)
                
                active_result = subprocess.run(['wlcctrl', '--getactivewindow'], 
                                             capture_output=True, text=True)
                if active_result.returncode == 0:
                    current_active = active_result.stdout.strip()
                    if current_active == hellobig_window_id:
                        print(f"      ✅ 窗口成功激活 → 窗口可见")
                        return True
                    else:
                        print(f"      ❌ 窗口未成为活动窗口 → 可能已最小化")
                        print(f"         期望: {hellobig_window_id}")
                        print(f"         实际: {current_active}")
                        return False
            else:
                print(f"      ❌ 激活命令失败: {result.stderr}")
                return False
        
        except Exception as e:
            print(f"      ❌ 激活测试失败: {e}")
            return False
    
    except Exception as e:
        print(f"   ❌ 交互测试失败: {e}")
        return False


def test_workspace_based_detection():
    """测试基于工作区的检测方法"""
    print("\n🔍 测试基于工作区的检测方法...")
    
    try:
        # 获取当前工作区
        result = subprocess.run(['wlcctrl', '--getcurrentworkspace'], capture_output=True, text=True)
        if result.returncode == 0:
            current_workspace = result.stdout.strip()
            print(f"   当前工作区: {current_workspace}")
        else:
            print(f"   ❌ 无法获取当前工作区")
            return
        
        # 获取所有窗口的工作区信息
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            print("   ❌ 无法获取窗口列表")
            return
        
        lines = result.stdout.strip().split('\n')
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                if 'hellobig' in window_title.lower() or 'AT-SPI测试' in window_title:
                    # 获取窗口的工作区
                    ws_result = subprocess.run(['wlcctrl', '--getwindowworkspace', window_id], 
                                             capture_output=True, text=True)
                    if ws_result.returncode == 0:
                        window_workspace = ws_result.stdout.strip()
                        print(f"   窗口 {window_title}:")
                        print(f"      工作区: {window_workspace}")
                        
                        if window_workspace == current_workspace:
                            print(f"      ✅ 在当前工作区 → 可能可见")
                        else:
                            print(f"      ❌ 不在当前工作区 → 可能不可见")
                    else:
                        print(f"   ❌ 无法获取窗口工作区: {ws_result.stderr}")
                
                i += 2
            else:
                i += 1
    
    except Exception as e:
        print(f"   ❌ 工作区检测失败: {e}")


def main():
    """主函数"""
    print("🔍 探索窗口检测方法")
    print("=" * 60)
    print("🎯 寻找在Wayland环境中可靠检测窗口最小化状态的方法")
    print("💡 因为wlcctrl的几何信息不可靠")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        # 测试各种方法
        test_wlcctrl_methods()
        test_process_based_detection()
        test_focus_based_detection()
        
        # 交互测试（可能会改变窗口状态）
        print(f"\n⚠️  以下测试可能会改变窗口状态:")
        interaction_result = test_interaction_based_detection()
        
        test_workspace_based_detection()
        
        print(f"\n💡 总结建议:")
        print("=" * 30)
        print("基于测试结果，最可靠的检测方法可能是:")
        print("1. 检查窗口是否是当前活动窗口")
        print("2. 尝试激活窗口，看是否成功")
        print("3. 检查窗口是否在当前工作区")
        print("4. 结合多种方法进行综合判断")
        
        if interaction_result is not None:
            if interaction_result:
                print(f"\n✅ 交互测试显示窗口可见")
            else:
                print(f"\n❌ 交互测试显示窗口已最小化")
        
    except Exception as e:
        print(f"❌ 探索失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
