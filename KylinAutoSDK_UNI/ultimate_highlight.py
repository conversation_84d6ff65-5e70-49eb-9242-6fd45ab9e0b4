#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
终极高亮解决方案 - 完全透明内部 + 无边框 + 纯边框线条
"""

import subprocess
import os

def ultimate_highlight(x, y, width, height, 
                      duration=2,
                      color='red', 
                      border_width=2):
    """
    终极控件高亮函数
    
    特点：
    - 完全无窗口装饰
    - 中间区域100%透明（无任何窗口覆盖）
    - 只有纯边框线条
    - 不遮挡任何内容
    - 四个独立的边框窗口
    
    Args:
        x, y: 控件位置
        width, height: 控件尺寸  
        duration: 高亮持续时间（秒）
        color: 边框颜色 ('red', 'blue', 'green', 'yellow', 'purple', 'cyan', 'orange')
        border_width: 边框宽度 (1-5推荐)
    
    Returns:
        bool: 是否成功显示高亮
    """
    
    # 创建终极高亮脚本
    x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    # 连接到X11
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取颜色
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 计算边框参数
    border_w = {border_width}
    target_x = {x}
    target_y = {y}
    target_width = {width}
    target_height = {height}
    
    # 创建四个独立的边框窗口
    windows = []
    
    # 上边框线
    top_border = root.create_window(
        target_x, target_y, target_width, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,  # 关键：绕过窗口管理器
        colormap=X.CopyFromParent
    )
    windows.append(top_border)
    
    # 下边框线
    bottom_border = root.create_window(
        target_x, target_y + target_height - border_w, target_width, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(bottom_border)
    
    # 左边框线
    left_border = root.create_window(
        target_x, target_y, border_w, target_height,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(left_border)
    
    # 右边框线
    right_border = root.create_window(
        target_x + target_width - border_w, target_y, border_w, target_height,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(right_border)
    
    # 为所有边框窗口设置属性
    for i, win in enumerate(windows):
        win.set_wm_name(f"Ultimate Border {{i+1}}")
        win.set_wm_class("ultimate_border", "UltimateBorder")
        
        # 设置窗口类型为dock（避免窗口管理器干预）
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_WINDOW_TYPE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
            )
        except:
            pass
        
        # 设置窗口状态
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_STATE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
                 disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
                 disp.intern_atom("_NET_WM_STATE_ABOVE")]
            )
        except:
            pass
    
    # 同时显示所有边框
    for win in windows:
        win.map()
    disp.sync()
    
    # 保持显示指定时间
    time.sleep({duration})
    
    # 清理所有边框窗口
    for win in windows:
        win.unmap()
        win.destroy()
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    exit(1)
'''
    
    try:
        # 静默执行
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

# 便捷预设函数
def red_highlight(x, y, width, height, duration=2):
    """红色高亮"""
    return ultimate_highlight(x, y, width, height, duration, 'red', 2)

def blue_highlight(x, y, width, height, duration=2):
    """蓝色高亮"""
    return ultimate_highlight(x, y, width, height, duration, 'blue', 2)

def green_highlight(x, y, width, height, duration=2):
    """绿色高亮"""
    return ultimate_highlight(x, y, width, height, duration, 'green', 2)

def thin_highlight(x, y, width, height, duration=2, color='red'):
    """超细边框高亮"""
    return ultimate_highlight(x, y, width, height, duration, color, 1)

def thick_highlight(x, y, width, height, duration=2, color='red'):
    """粗边框高亮"""
    return ultimate_highlight(x, y, width, height, duration, color, 3)

def demo_ultimate_highlight():
    """演示终极高亮效果"""
    print("🎯 终极控件高亮演示")
    print("=" * 60)
    
    # 获取鼠标位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    demo_x = mouse_x - 100
    demo_y = mouse_y - 75
    demo_w = 200
    demo_h = 150
    
    print(f"演示位置: ({demo_x}, {demo_y}) {demo_w}×{demo_h}")
    print("\n🏆 终极高亮解决方案特点:")
    print("   ✅ 完全无窗口装饰")
    print("   ✅ 中间区域100%透明")
    print("   ✅ 不遮挡任何内容")
    print("   ✅ 纯边框线条显示")
    print("   ✅ 四个独立边框窗口")
    print("   ✅ 绕过窗口管理器")
    print("   ✅ 可自定义样式")
    print()
    
    # 演示不同样式
    demos = [
        ('red', 2, '红色标准边框'),
        ('blue', 1, '蓝色超细边框'),
        ('green', 3, '绿色粗边框'),
        ('purple', 2, '紫色标准边框'),
        ('yellow', 1, '黄色细边框')
    ]
    
    for color, width, desc in demos:
        print(f"🎨 演示: {desc}")
        success = ultimate_highlight(demo_x, demo_y, demo_w, demo_h,
                                   duration=2, color=color, border_width=width)
        print("✅ 完成" if success else "❌ 失败")
        print()
    
    print("=" * 60)
    print("🎉 终极高亮演示完成！")
    print("\n💡 最终使用方法:")
    print("```python")
    print("from ultimate_highlight import ultimate_highlight")
    print("")
    print("# 基本使用")
    print("ultimate_highlight(x, y, width, height)")
    print("")
    print("# 自定义样式")
    print("ultimate_highlight(x, y, width, height,")
    print("                  duration=3,")
    print("                  color='blue',")
    print("                  border_width=1)")
    print("")
    print("# 便捷函数")
    print("red_highlight(x, y, width, height)")
    print("thin_highlight(x, y, width, height, color='green')")
    print("```")

if __name__ == "__main__":
    demo_ultimate_highlight()