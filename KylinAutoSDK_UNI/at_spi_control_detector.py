#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
通用AT-SPI控件检测和高亮库
基于现有的UNI SDK和高亮功能，提供简洁的API接口

功能特点:
1. 根据桌面坐标获取控件信息
2. 自适应窗口管理和坐标转换
3. 控件高亮显示功能
4. 通用接口，不依赖特定桌面应用
"""

import sys
import os

# 添加src路径到模块搜索路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from uni_sdk.uni import UNI
from ultimate_highlight import ultimate_highlight


class ATSPIControlDetector:
    """
    AT-SPI控件检测器主类
    
    提供简洁的API接口用于:
    - 根据坐标获取控件信息
    - 控件高亮显示
    - 坐标转换和窗口管理
    """
    
    def __init__(self):
        """初始化检测器"""
        self.uni_sdk = UNI()
        self.last_detected_control = None
        
        print("AT-SPI控件检测器已初始化")
        print(f"运行环境: {self.uni_sdk.display_server}")
        if not self.uni_sdk.x11_available and self.uni_sdk.display_server == 'wayland':
            print("注意: 在Wayland环境下，部分X11特定功能可能受限")
    
    def get_control_at_point(self, x, y, include_details=True):
        """
        根据坐标获取控件信息
        
        Args:
            x (int): 横坐标
            y (int): 纵坐标  
            include_details (bool): 是否包含详细信息
            
        Returns:
            dict: 控件信息字典，包含以下字段:
                - success (bool): 是否成功找到控件
                - message (str): 状态信息
                - control_info (dict): 控件详细信息（如果找到）
                - position (tuple): 控件位置信息 (x, y, width, height)
        """
        try:
            if include_details:
                control_data, message = self.uni_sdk.kdk_getElement_Uni(x, y, quick=False)
            else:
                extents, message = self.uni_sdk.kdk_getElement_Uni(x, y, quick=True)
                control_data = {
                    'x': extents.x if extents else 0,
                    'y': extents.y if extents else 0, 
                    'width': extents.width if extents else 0,
                    'height': extents.height if extents else 0
                } if extents else None
            
            if control_data and message == "找到":
                # 保存最后检测到的控件
                self.last_detected_control = control_data
                
                if include_details:
                    position = (
                        control_data.get('Coords', {}).get('x', 0),
                        control_data.get('Coords', {}).get('y', 0),
                        control_data.get('Coords', {}).get('width', 0),
                        control_data.get('Coords', {}).get('height', 0)
                    )
                else:
                    position = (
                        control_data.get('x', 0),
                        control_data.get('y', 0),
                        control_data.get('width', 0),
                        control_data.get('height', 0)
                    )
                
                return {
                    'success': True,
                    'message': message,
                    'control_info': control_data,
                    'position': position
                }
            else:
                return {
                    'success': False,
                    'message': message or "未找到控件",
                    'control_info': None,
                    'position': None
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"检测异常: {e}",
                'control_info': None,
                'position': None
            }
    
    def highlight_control_at_point(self, x, y, duration=2, color='red', border_width=2):
        """
        检测并高亮指定坐标处的控件
        
        Args:
            x (int): 横坐标
            y (int): 纵坐标
            duration (float): 高亮持续时间（秒）
            color (str): 高亮颜色 ('red', 'blue', 'green', 'yellow', 'purple', 'cyan', 'orange')
            border_width (int): 边框宽度
            
        Returns:
            dict: 结果信息
        """
        # 获取控件信息
        result = self.get_control_at_point(x, y, include_details=False)
        
        if not result['success']:
            return {
                'success': False,
                'message': f"未找到控件进行高亮: {result['message']}",
                'highlighted': False
            }
        
        # 获取控件位置
        pos_x, pos_y, width, height = result['position']
        
        if width <= 0 or height <= 0:
            return {
                'success': False,
                'message': "控件尺寸无效，无法高亮",
                'highlighted': False
            }
        
        # 执行高亮
        try:
            highlight_success = ultimate_highlight(
                pos_x, pos_y, width, height,
                duration=duration, 
                color=color, 
                border_width=border_width
            )
            
            return {
                'success': True,
                'message': f"控件高亮{'成功' if highlight_success else '失败'}",
                'highlighted': highlight_success,
                'control_position': (pos_x, pos_y, width, height)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"高亮异常: {e}",
                'highlighted': False
            }
    
    def get_detailed_control_info(self, x, y):
        """
        获取详细的控件信息
        
        Args:
            x (int): 横坐标
            y (int): 纵坐标
            
        Returns:
            dict: 详细的控件信息
        """
        result = self.get_control_at_point(x, y, include_details=True)
        
        if not result['success']:
            return result
        
        control_info = result['control_info']
        
        # 构建详细信息
        detailed_info = {
            'success': True,
            'basic_info': {
                'name': control_info.get('Name', 'N/A'),
                'description': control_info.get('Description', 'N/A'), 
                'role': control_info.get('Rolename', 'N/A'),
                'position': result['position']
            },
            'window_info': {
                'window_name': control_info.get('WindowName', 'N/A'),
                'window_role': control_info.get('WindowRoleName', 'N/A'),
                'process_name': control_info.get('ProcessName', 'N/A')
            },
            'states': control_info.get('States', []),
            'actions': control_info.get('Actions', []),
            'hierarchy_info': {
                'parent_count': control_info.get('ParentCount', 0),
                'child_count': control_info.get('ChildCount', 0),
                'parent_path': control_info.get('ParentPath', [])
            },
            'raw_control_info': control_info
        }
        
        return detailed_info
    
    def highlight_last_detected_control(self, duration=2, color='blue', border_width=2):
        """
        高亮最后检测到的控件
        
        Args:
            duration (float): 高亮持续时间（秒）
            color (str): 高亮颜色
            border_width (int): 边框宽度
            
        Returns:
            dict: 结果信息
        """
        if not self.last_detected_control:
            return {
                'success': False,
                'message': "没有最近检测到的控件",
                'highlighted': False
            }
        
        # 从保存的控件信息中获取位置
        if 'Coords' in self.last_detected_control:
            coords = self.last_detected_control['Coords']
            pos_x = coords.get('x', 0)
            pos_y = coords.get('y', 0)
            width = coords.get('width', 0)
            height = coords.get('height', 0)
        else:
            pos_x = self.last_detected_control.get('x', 0)
            pos_y = self.last_detected_control.get('y', 0)
            width = self.last_detected_control.get('width', 0)
            height = self.last_detected_control.get('height', 0)
        
        if width <= 0 or height <= 0:
            return {
                'success': False,
                'message': "保存的控件尺寸无效",
                'highlighted': False
            }
        
        try:
            highlight_success = ultimate_highlight(
                pos_x, pos_y, width, height,
                duration=duration,
                color=color,
                border_width=border_width
            )
            
            return {
                'success': True,
                'message': f"最后检测控件高亮{'成功' if highlight_success else '失败'}",
                'highlighted': highlight_success,
                'control_position': (pos_x, pos_y, width, height)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"高亮异常: {e}",
                'highlighted': False
            }
    
    def print_control_info(self, x, y):
        """
        打印控件信息到控制台
        
        Args:
            x (int): 横坐标
            y (int): 纵坐标
        """
        print(f"\n🔍 检测坐标 ({x}, {y}) 处的控件:")
        print("=" * 60)
        
        detailed_info = self.get_detailed_control_info(x, y)
        
        if not detailed_info['success']:
            print(f"❌ {detailed_info['message']}")
            return
        
        basic = detailed_info['basic_info']
        window = detailed_info['window_info']
        hierarchy = detailed_info['hierarchy_info']
        
        print(f"📋 基本信息:")
        print(f"   名称: {basic['name']}")
        print(f"   描述: {basic['description']}")
        print(f"   角色: {basic['role']}")
        print(f"   位置: {basic['position']}")
        
        print(f"\n🪟 窗口信息:")
        print(f"   窗口名: {window['window_name']}")
        print(f"   窗口角色: {window['window_role']}")
        print(f"   进程名: {window['process_name']}")
        
        print(f"\n🌳 层级信息:")
        print(f"   父级数量: {hierarchy['parent_count']}")
        print(f"   子级数量: {hierarchy['child_count']}")
        
        if detailed_info['states']:
            print(f"\n🔄 状态: {', '.join(detailed_info['states'])}")
        
        if detailed_info['actions']:
            print(f"\n⚡ 动作: {', '.join(detailed_info['actions'])}")
        
        print("=" * 60)


# 便捷函数
def detect_control_at_point(x, y):
    """
    便捷函数：检测指定坐标的控件
    
    Args:
        x (int): 横坐标
        y (int): 纵坐标
        
    Returns:
        dict: 控件信息
    """
    detector = ATSPIControlDetector()
    return detector.get_control_at_point(x, y)


def highlight_control_at_point(x, y, duration=2, color='red'):
    """
    便捷函数：检测并高亮指定坐标的控件
    
    Args:
        x (int): 横坐标
        y (int): 纵坐标
        duration (float): 高亮持续时间
        color (str): 高亮颜色
        
    Returns:
        dict: 结果信息
    """
    detector = ATSPIControlDetector()
    return detector.highlight_control_at_point(x, y, duration, color)


def print_control_info_at_point(x, y):
    """
    便捷函数：打印指定坐标的控件信息
    
    Args:
        x (int): 横坐标
        y (int): 纵坐标
    """
    detector = ATSPIControlDetector()
    detector.print_control_info(x, y)


# 演示函数
def demo():
    """演示AT-SPI控件检测功能"""
    print("🎯 AT-SPI控件检测器演示")
    print("=" * 60)
    
    detector = ATSPIControlDetector()
    
    # 获取鼠标当前位置进行演示
    try:
        import subprocess
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 500, 300
    except:
        mouse_x, mouse_y = 500, 300
    
    print(f"演示坐标: ({mouse_x}, {mouse_y})")
    
    # 检测控件
    print("\n🔍 检测控件信息...")
    detector.print_control_info(mouse_x, mouse_y)
    
    # 高亮控件
    print(f"\n✨ 高亮控件...")
    result = detector.highlight_control_at_point(mouse_x, mouse_y, duration=3, color='green')
    print(f"高亮结果: {result['message']}")
    
    print("\n🎉 演示完成！")


if __name__ == "__main__":
    demo()