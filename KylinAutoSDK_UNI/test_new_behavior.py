#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
测试新的交互式行为
- 绿色✓按钮：保存控件信息并退出程序
- 红色✗按钮：取消当前捕获，继续监控
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, '/home/<USER>/<PERSON>ylinAutoSDK_UNI')

from universal_offset_detector import UniversalOffsetDetector, get_mouse_position
from interactive_highlight import interactive_highlight


def test_new_behavior():
    """测试新的交互式行为"""
    print("🧪 测试新的交互式行为")
    print("=" * 50)
    print("🎯 行为说明:")
    print("   🟢 绿色✓按钮 → 保存控件信息并退出程序")
    print("   🔴 红色✗按钮 → 取消当前捕获，继续监控")
    print("=" * 50)
    
    # 使用AT-SPI测试界面中的已知控件位置
    test_positions = [
        (1044, 213, "切换按钮"),
        (1064, 264, "单选按钮1"),
        (1014, 403, "广州下拉框"),
    ]
    
    detector = UniversalOffsetDetector()
    
    for i, (x, y, desc) in enumerate(test_positions, 1):
        print(f"\n🎯 测试 {i}: {desc}")
        print(f"📍 测试位置: ({x}, {y})")
        
        try:
            # 计算窗口偏移
            offset, atspi_window_info = detector.calculate_window_offset(x, y)
            if not offset or not atspi_window_info:
                print(f"❌ 无法计算窗口偏移")
                continue
            
            print(f"✅ 找到窗口: {atspi_window_info['app_name']}")
            
            # 收集应用控件
            detector.collect_app_controls(atspi_window_info)
            if not detector.current_app_controls:
                print(f"❌ 未找到应用控件")
                continue
            
            # 查找控件
            ctrl = detector.find_control_with_offset(x, y, offset)
            if not ctrl:
                print(f"❌ 未找到匹配的控件")
                continue
            
            # 获取控件信息
            info = detector.get_control_info(ctrl)
            print(f"🎯 找到控件: {info['name']} ({info['role']})")
            
            # 计算屏幕坐标
            ext = ctrl['extents']
            screen_x = ext.x + offset[0]
            screen_y = ext.y + offset[1]
            
            print(f"📍 屏幕坐标: ({screen_x}, {screen_y}) {ext.width}×{ext.height}")
            
            # 显示交互式高亮
            print("✨ 显示交互式高亮...")
            print("💡 请选择:")
            print("   🟢 点击绿色✓按钮 → 保存并退出")
            print("   🔴 点击红色✗按钮 → 继续下一个测试")
            
            user_choice = interactive_highlight(
                screen_x, screen_y, ext.width, ext.height,
                control_info=info,
                duration=15,  # 15秒超时
                color='orange',
                border_width=2
            )
            
            print(f"👤 用户选择: {user_choice}")
            
            if user_choice == 'confirm':
                print("✅ 用户点击绿色✓按钮")
                print("💾 正在保存控件信息...")
                
                # 保存控件信息
                success = save_and_exit(ctrl, offset, atspi_window_info, info)
                if success:
                    print("🎉 控件信息已保存!")
                    print("🚪 程序即将退出...")
                    time.sleep(2)
                    print("👋 再见!")
                    sys.exit(0)
                else:
                    print("❌ 保存失败")
                    
            elif user_choice == 'cancel':
                print("❌ 用户点击红色✗按钮")
                print("🔄 继续下一个测试...")
                # 继续循环到下一个测试
                
            elif user_choice == 'timeout':
                print("⏰ 用户操作超时")
                print("🔄 继续下一个测试...")
                
            else:
                print("❌ 交互过程出现错误")
                print("🔄 继续下一个测试...")
            
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ 测试 {i} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n🎉 所有测试完成!")
    print("💡 如果您点击了红色✗按钮，程序会继续运行到这里")
    print("💡 如果您点击了绿色✓按钮，程序会在保存后退出")


def save_and_exit(ctrl, offset, atspi_window_info, control_info):
    """保存控件信息并准备退出"""
    try:
        import json
        from datetime import datetime
        
        # 使用真正的原始格式
        mouse_pos = get_mouse_position()
        mouse_x, mouse_y = mouse_pos if mouse_pos != (None, None) else (0, 0)
        
        # 使用检测器的原始格式方法
        detector = UniversalOffsetDetector()
        original_info = detector.get_control_info_original_format(
            ctrl, atspi_window_info, mouse_x, mouse_y, offset
        )
        
        if original_info:
            # 保存到主文件
            with open('detected_control_info.txt', 'w', encoding='utf-8') as f:
                json.dump(original_info, f, ensure_ascii=False)
            
            print(f"📄 详细信息已保存到: detected_control_info.txt")
            
            # 保存带时间戳的备份
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"captured_control_{timestamp}.json"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(original_info, f, ensure_ascii=False, indent=2)
            
            print(f"💾 备份副本: {backup_file}")
            
            # 显示保存的内容摘要
            print("\n📋 保存的控件信息摘要:")
            print(f"   应用程序: {original_info['datamap']['ProcessName']}")
            print(f"   窗口标题: {original_info['datamap']['WindowName']}")
            print(f"   控件名称: {original_info['name']}")
            print(f"   控件类型: {original_info['type']}")
            print(f"   屏幕坐标: ({original_info['coords']['x']}, {original_info['coords']['y']})")
            print(f"   控件尺寸: {original_info['coords']['width']}×{original_info['coords']['height']}")
            
            return True
        else:
            print("❌ 无法生成原始格式信息")
            return False
        
    except Exception as e:
        print(f"❌ 保存控件信息错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        test_new_behavior()
        
    except KeyboardInterrupt:
        print("\n🛑 用户按下 Ctrl+C，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
