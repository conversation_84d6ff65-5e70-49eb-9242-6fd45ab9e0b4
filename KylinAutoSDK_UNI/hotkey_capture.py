#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
系统快捷键控件捕获工具
通过系统快捷键触发控件捕获，适用于Wayland环境
"""

import sys
import os
import time
import subprocess
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')

from universal_offset_detector import UniversalOffsetDetector, get_mouse_position


def setup_system_hotkey():
    """设置系统快捷键"""
    print("🔧 系统快捷键设置指南")
    print("=" * 60)
    print("📋 请按以下步骤设置系统快捷键:")
    print()
    print("1. 打开系统设置 -> 键盘 -> 快捷键")
    print("2. 添加自定义快捷键:")
    print("   名称: 控件捕获")
    print("   命令: /usr/bin/python3 /home/<USER>/KylinAutoSDK_UNI/quick_capture.py --mode quick")
    print("   快捷键: Ctrl+Alt+C (或您喜欢的组合)")
    print()
    print("3. 或者使用以下命令自动设置 (GNOME环境):")
    print("   gsettings set org.gnome.settings-daemon.plugins.media-keys.custom-keybinding:/org/gnome/settings-daemon/plugins/media-keys/custom-keybindings/custom0/ name '控件捕获'")
    print("   gsettings set org.gnome.settings-daemon.plugins.media-keys.custom-keybinding:/org/gnome/settings-daemon/plugins/media-keys/custom-keybindings/custom0/ command '/usr/bin/python3 /home/<USER>/KylinAutoSDK_UNI/quick_capture.py --mode quick'")
    print("   gsettings set org.gnome.settings-daemon.plugins.media-keys.custom-keybinding:/org/gnome/settings-daemon/plugins/media-keys/custom-keybindings/custom0/ binding '<Primary><Alt>c'")
    print()
    print("4. 设置完成后，按 Ctrl+Alt+C 即可捕获控件!")
    print("=" * 60)


def create_desktop_entry():
    """创建桌面快捷方式"""
    desktop_content = """[Desktop Entry]
Version=1.0
Type=Application
Name=控件捕获工具
Comment=快速捕获鼠标位置的控件信息
Exec=/usr/bin/python3 /home/<USER>/KylinAutoSDK_UNI/quick_capture.py --mode quick
Icon=applications-debugging
Terminal=false
Categories=Development;Utility;
Keywords=capture;control;widget;accessibility;
"""
    
    desktop_file = os.path.expanduser("~/.local/share/applications/control-capture.desktop")
    
    try:
        os.makedirs(os.path.dirname(desktop_file), exist_ok=True)
        with open(desktop_file, 'w', encoding='utf-8') as f:
            f.write(desktop_content)
        
        # 设置可执行权限
        os.chmod(desktop_file, 0o755)
        
        print("✅ 桌面快捷方式已创建!")
        print(f"📄 位置: {desktop_file}")
        print("💡 现在可以在应用程序菜单中找到'控件捕获工具'")
        
    except Exception as e:
        print(f"❌ 创建桌面快捷方式失败: {e}")


def create_launcher_script():
    """创建启动脚本"""
    script_content = """#!/bin/bash
# 控件捕获工具启动脚本

# 设置环境变量
export DISPLAY=:0
export PYTHONPATH="/home/<USER>/KylinAutoSDK_UNI:$PYTHONPATH"

# 切换到工作目录
cd /home/<USER>/KylinAutoSDK_UNI

# 执行捕获
python3 quick_capture.py --mode quick --format original

# 显示通知 (如果有通知系统)
if command -v notify-send &> /dev/null; then
    notify-send "控件捕获" "控件信息已保存" --icon=dialog-information
fi
"""
    
    script_file = "/home/<USER>/KylinAutoSDK_UNI/capture_hotkey.sh"
    
    try:
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置可执行权限
        os.chmod(script_file, 0o755)
        
        print("✅ 启动脚本已创建!")
        print(f"📄 位置: {script_file}")
        print("💡 可以将此脚本设置为系统快捷键的命令")
        
        return script_file
        
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return None


def test_capture():
    """测试捕获功能"""
    print("\n🧪 测试控件捕获功能...")
    
    # 导入捕获函数
    sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
    from quick_capture import quick_capture_control
    
    try:
        success = quick_capture_control(output_format='original', highlight_duration=2)
        if success:
            print("✅ 测试成功! 控件捕获功能正常工作")
        else:
            print("❌ 测试失败! 请检查鼠标位置是否在有效控件上")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='系统快捷键控件捕获工具设置')
    parser.add_argument('--setup', action='store_true', help='显示快捷键设置指南')
    parser.add_argument('--desktop', action='store_true', help='创建桌面快捷方式')
    parser.add_argument('--script', action='store_true', help='创建启动脚本')
    parser.add_argument('--test', action='store_true', help='测试捕获功能')
    parser.add_argument('--all', action='store_true', help='执行所有设置步骤')
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        # 如果没有指定参数，显示帮助
        parser.print_help()
        return
    
    print("🚀 Wayland环境控件捕获工具设置")
    print("=" * 60)
    
    if args.setup or args.all:
        setup_system_hotkey()
        print()
    
    if args.script or args.all:
        script_file = create_launcher_script()
        print()
    
    if args.desktop or args.all:
        create_desktop_entry()
        print()
    
    if args.test or args.all:
        test_capture()
        print()
    
    if args.all:
        print("🎉 所有设置完成!")
        print("💡 使用方法:")
        print("   1. 设置系统快捷键 (推荐: Ctrl+Alt+C)")
        print("   2. 或在应用程序菜单中找到'控件捕获工具'")
        print("   3. 将鼠标移动到目标控件上，按快捷键即可捕获")


if __name__ == "__main__":
    main()
