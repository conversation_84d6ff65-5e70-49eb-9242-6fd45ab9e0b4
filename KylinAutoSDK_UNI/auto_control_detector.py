#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
自动控件检测器 - 修复版
专门解决坐标不准确和控件识别问题

功能:
1. 自动检测鼠标位置的控件
2. 使用修正的坐标系统
3. 精确识别label、button等具体控件
4. 准确的边框高亮
"""

import sys
import os
import time
import signal
import subprocess
import pyatspi

from fixed_control_detector import FixedControlDetector


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


class AutoControlDetector:
    """自动控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.detector = FixedControlDetector()
        self.last_mouse_pos = (0, 0)
        self.last_detection_time = 0
        self.detection_cooldown = 1.5  # 检测冷却时间（秒）
        self.position_threshold = 15   # 位置变化阈值（像素）
        self.is_running = True
        
        print("🎯 自动控件检测器 - 修复版")
        print("=" * 60)
        print("📖 功能说明:")
        print("   ✅ 自动检测鼠标位置的控件")
        print("   ✅ 修正坐标系统，确保精度")
        print("   ✅ 识别label、button等具体控件")
        print("   ✅ 准确的边框高亮")
        print("   ✅ 详细的控件信息输出")
        print("=" * 60)
        print("🖱️  移动鼠标到想要检测的控件上...")
        print("🛑 按 Ctrl+C 退出程序")
        print("=" * 60)
    
    def should_detect_at_position(self, x, y):
        """判断是否应该在此位置进行检测"""
        current_time = time.time()
        
        # 检查时间冷却
        if current_time - self.last_detection_time < self.detection_cooldown:
            return False
        
        # 检查位置变化
        last_x, last_y = self.last_mouse_pos
        distance = ((x - last_x) ** 2 + (y - last_y) ** 2) ** 0.5
        
        if distance < self.position_threshold:
            return False
        
        return True
    
    def detect_and_analyze_control(self, x, y):
        """检测和分析控件"""
        print(f"\n🔍 检测坐标 ({x}, {y}) 处的控件...")
        print("-" * 40)
        
        try:
            # 使用修复的检测器
            result = self.detector.get_control_info_at_point(x, y)
            
            if result['success']:
                info = result['control_info']
                pos = result['position']
                
                print(f"✅ 控件检测成功!")
                print(f"   📏 实际位置: ({pos[0]}, {pos[1]})")
                print(f"   📐 实际尺寸: {pos[2]} × {pos[3]}")
                print(f"   🏷️  控件名称: {info['name'] or 'N/A'}")
                print(f"   🎭 控件角色: {info['role']}")
                
                if info.get('description'):
                    print(f"   📝 控件描述: {info['description']}")
                
                if info.get('window_name'):
                    print(f"   🪟 所属窗口: {info['window_name']}")
                
                if info.get('actions'):
                    actions_str = ', '.join(info['actions'][:3])
                    if len(info['actions']) > 3:
                        actions_str += f"... (+{len(info['actions'])-3})"
                    print(f"   ⚡ 可用动作: {actions_str}")
                
                if info.get('states'):
                    states_str = ', '.join(info['states'][:5])
                    if len(info['states']) > 5:
                        states_str += f"... (+{len(info['states'])-5})"
                    print(f"   🔄 控件状态: {states_str}")
                
                # 执行高亮
                print(f"\n✨ 正在高亮控件...")
                highlight_result = self.detector.highlight_control_at_point(
                    x, y, duration=2, color='red', border_width=2
                )
                
                if highlight_result['highlighted']:
                    highlight_pos = highlight_result['control_position']
                    print(f"   ✅ 高亮成功! 高亮位置: ({highlight_pos[0]}, {highlight_pos[1]}) {highlight_pos[2]}×{highlight_pos[3]}")
                    
                    # 验证坐标一致性
                    if (highlight_pos[0] == pos[0] and highlight_pos[1] == pos[1] and 
                        highlight_pos[2] == pos[2] and highlight_pos[3] == pos[3]):
                        print(f"   🎯 坐标验证: 完全一致")
                    else:
                        print(f"   ⚠️  坐标差异: 检测位置与高亮位置不完全一致")
                else:
                    print(f"   ❌ 高亮失败: {highlight_result.get('message', '未知错误')}")
                
                # 分析控件类型
                self._analyze_control_type(info)
                
            else:
                print(f"❌ 未找到控件: {result['message']}")
                
                # 提供诊断信息
                print(f"\n🔧 诊断信息:")
                print(f"   - 尝试调整鼠标位置")
                print(f"   - 确保鼠标位于可见控件上")
                print(f"   - 某些控件可能需要获得焦点才能检测")
                
        except Exception as e:
            print(f"❌ 检测异常: {e}")
        
        print("-" * 40)
    
    def _analyze_control_type(self, info):
        """分析控件类型并给出说明"""
        role = info.get('role', '').lower()
        name = info.get('name', '')
        actions = info.get('actions', [])
        
        print(f"\n📊 控件分析:")
        
        if 'button' in role:
            print(f"   🔘 这是一个按钮控件")
            if 'click' in actions or 'press' in actions:
                print(f"   ✅ 支持点击操作")
        elif 'label' in role or 'static text' in role:
            print(f"   🏷️  这是一个文本标签控件")
            print(f"   ℹ️  通常用于显示静态文本")
        elif 'text' in role or 'entry' in role:
            print(f"   📝 这是一个文本输入控件")
            if 'set text' in actions:
                print(f"   ✅ 支持文本输入")
        elif 'check box' in role:
            print(f"   ☑️  这是一个复选框控件")
            if 'toggle' in actions:
                print(f"   ✅ 支持切换状态")
        elif 'combo box' in role or 'list' in role:
            print(f"   📋 这是一个列表/下拉框控件")
        elif 'menu' in role:
            print(f"   📜 这是一个菜单控件")
        elif 'frame' in role or 'window' in role:
            print(f"   🪟 这是一个窗口/框架控件")
        elif 'panel' in role:
            print(f"   📦 这是一个面板容器控件")
        else:
            print(f"   🔍 控件类型: {role}")
        
        if name:
            print(f"   💬 控件文本: '{name}'")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 接收到信号 {signum}，正在退出...")
        self.is_running = False
    
    def run(self):
        """运行检测器"""
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            print("🚀 开始监控鼠标位置...")
            
            consecutive_failures = 0
            max_failures = 10
            
            while self.is_running:
                # 获取当前鼠标位置
                current_pos = get_mouse_position()
                
                if current_pos == (None, None):
                    consecutive_failures += 1
                    if consecutive_failures <= max_failures:
                        print("⚠️  无法获取鼠标位置，等待中...")
                        time.sleep(1)
                        continue
                    else:
                        print("❌ 连续无法获取鼠标位置，程序退出")
                        break
                else:
                    consecutive_failures = 0
                
                x, y = current_pos
                
                # 检查是否应该检测
                if self.should_detect_at_position(x, y):
                    self.detect_and_analyze_control(x, y)
                    self.last_mouse_pos = (x, y)
                    self.last_detection_time = time.time()
                
                # 短暂休眠避免CPU占用过高
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 接收到中断信号，正在退出...")
        except Exception as e:
            print(f"\n❌ 运行异常: {e}")
        finally:
            self.is_running = False
            print("👋 检测器已停止")


def main():
    """主函数"""
    print("🎯 自动控件检测器 - 修复版")
    print("=" * 60)
    
    # 检查运行环境
    if not sys.platform.startswith('linux'):
        print("❌ 错误: 此程序仅支持Linux系统")
        sys.exit(1)
    
    if not os.getenv('DISPLAY'):
        print("❌ 错误: 未检测到DISPLAY环境变量")
        print("请在图形界面环境中运行此程序")
        sys.exit(1)
    
    # 检查xdotool
    try:
        subprocess.run(['which', 'xdotool'], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("❌ 错误: 未找到xdotool命令")
        print("请安装xdotool: sudo apt-get install xdotool")
        sys.exit(1)
    
    try:
        detector = AutoControlDetector()
        detector.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔧 可能的解决方案:")
        print("1. 确保运行在图形界面环境中")
        print("2. 检查AT-SPI服务是否正常运行")
        print("3. 确保有足够的权限访问辅助功能")
        sys.exit(1)


if __name__ == "__main__":
    main()