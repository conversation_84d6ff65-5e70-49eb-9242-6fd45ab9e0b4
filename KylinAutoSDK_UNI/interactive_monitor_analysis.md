# interactive_monitor.py 依赖分析和执行流程

## 一、文件概述

`interactive_monitor.py` 是一个交互式连续监控工具，用于在 Wayland 环境下实现类似 X11 的控件捕获功能。它结合了连续监控和交互式确认功能，提供了最佳的用户体验。

## 二、依赖关系图

```
interactive_monitor.py
├── 标准库依赖
│   ├── sys
│   ├── os
│   ├── time
│   ├── threading
│   ├── signal
│   └── datetime
│
├── 本地模块依赖
│   ├── universal_offset_detector.py
│   │   ├── UniversalOffsetDetector (类)
│   │   ├── get_mouse_position (函数)
│   │   └── 依赖项：
│   │       ├── pyatspi (外部库)
│   │       ├── subprocess
│   │       ├── ultimate_highlight.py
│   │       └── 系统工具 (xdotool, wlcctrl)
│   │
│   └── interactive_highlight.py
│       ├── interactive_highlight (函数)
│       └── 依赖项：
│           ├── subprocess
│           ├── tempfile
│           └── Xlib (通过子进程)
│
└── 外部系统依赖
    ├── Python 3
    ├── X11 显示服务器
    ├── AT-SPI2 无障碍框架
    ├── xdotool (鼠标位置获取)
    └── wlcctrl (Wayland 窗口管理，可选)
```

## 三、核心类和函数分析

### 1. InteractiveMonitor 类

主要属性：
- `interval`: 监控间隔（默认0.5秒）
- `output_format`: 输出格式（'simple' 或 'original'）
- `running`: 监控状态标志
- `monitor_thread`: 监控线程
- `detector`: UniversalOffsetDetector 实例

主要方法：
- `start_monitoring()`: 启动监控
- `stop_monitoring()`: 停止监控
- `_monitor_loop()`: 监控循环（在独立线程中运行）
- `_detect_and_show_interactive()`: 检测控件并显示交互式高亮
- `_save_control_info()`: 保存控件信息

### 2. 依赖的 UniversalOffsetDetector 类

核心功能：
- 计算窗口偏移量
- 收集应用程序控件
- 查找鼠标位置的控件
- 获取控件详细信息
- 高亮显示控件

### 3. 依赖的 interactive_highlight 函数

功能：
- 显示带确认/取消按钮的高亮边框
- 返回用户选择（'confirm', 'cancel', 'timeout'）

## 四、执行调度流程

### 1. 程序启动流程

```
main()
├── 解析命令行参数
│   ├── --interval: 监控间隔
│   └── --format: 输出格式
│
├── 检查环境变量 DISPLAY
│
├── 创建 InteractiveMonitor 实例
│
├── 设置信号处理器 (SIGINT, SIGTERM)
│
└── 启动监控
    ├── monitor.start_monitoring()
    └── 主线程保持运行
```

### 2. 监控循环流程 (_monitor_loop)

```
监控循环（在独立线程中）
├── 获取鼠标位置 (get_mouse_position)
│   └── 调用 xdotool getmouselocation
│
├── 检查位置是否变化
│
├── 检查冷却时间（2秒）
│
└── 如果满足条件，调用 _detect_and_show_interactive
    ├── 计算窗口偏移 (detector.calculate_window_offset)
    │   ├── 获取鼠标下的窗口 (wlcctrl 或 xdotool)
    │   └── 查找对应的 AT-SPI 窗口
    │
    ├── 收集应用控件 (detector.collect_app_controls)
    │   └── 递归遍历 AT-SPI 树
    │
    ├── 查找控件 (detector.find_control_with_offset)
    │   └── 使用偏移量转换坐标
    │
    ├── 显示控件信息
    │
    └── 显示交互式高亮 (interactive_highlight)
        ├── 创建高亮边框
        ├── 创建确认/取消按钮
        └── 等待用户选择
```

### 3. 用户交互流程

```
用户选择处理
├── 确认 (绿色✓按钮)
│   ├── 保存控件信息
│   │   ├── detected_control_info.txt (主文件)
│   │   └── captured_control_时间戳.json (备份)
│   │
│   ├── 显示确认高亮（绿色，2秒）
│   │
│   └── 退出程序
│
├── 取消 (红色✗按钮)
│   └── 继续监控
│
└── 超时 (15秒)
    └── 继续监控
```

### 4. 控件信息保存格式

原始格式 (original) 包含：
```json
{
    "name": "控件名称",
    "type": "控件类型",
    "coords": {
        "x": 屏幕X坐标,
        "y": 屏幕Y坐标,
        "width": 宽度,
        "height": 高度
    },
    "datamap": {
        "Name": "控件名称",
        "ID": 控件ID,
        "ProcessID": 进程ID,
        "Rolename": "角色名称",
        "Description": "描述",
        "Index_in_parent": 父级索引,
        "ChildrenCount": 子控件数量,
        "ProcessName": "进程名称",
        "Coords": 坐标信息,
        "Text": "文本内容",
        "Actions": ["动作列表"],
        "States": ["状态列表"],
        "ParentPath": [父路径],
        "ParentCount": 父级数量,
        "Key": "唯一键",
        "RecordPosition": [鼠标X, 鼠标Y],
        "WindowRoleName": "frame",
        "WindowChildCount": 窗口子控件数,
        "WindowName": "窗口名称",
        "capture_status": "success"
    },
    "description": "描述",
    "states": ["状态列表"],
    "actions": ["动作列表"],
    "capture_status": "success"
}
```

## 五、关键技术特点

### 1. Wayland 兼容性
- 使用 wlcctrl 获取 Wayland 窗口信息
- 回退到 xdotool 支持 X11 环境
- 通过 AT-SPI 实现跨环境控件访问

### 2. 偏移量计算
- 自动计算真实窗口坐标与 AT-SPI 坐标的偏移
- 考虑标题栏高度等因素
- 确保高亮位置准确

### 3. 交互式设计
- 自动检测鼠标下的控件
- 显示橙色高亮边框
- 提供确认/取消按钮
- 超时自动处理

### 4. 线程安全
- 监控循环在独立线程中运行
- 主线程处理信号和程序退出
- 避免竞态条件

## 六、相关工具和脚本

1. **universal_offset_detector.py**
   - 核心检测引擎
   - 支持单次检测和连续监控模式

2. **interactive_highlight.py**
   - 交互式高亮组件
   - 使用 X11 创建透明窗口和按钮

3. **ultimate_highlight.py**
   - 纯边框高亮实现
   - 被 universal_offset_detector 调用

4. **相关工具**：
   - wayland_control_capture.py: 另一种捕获实现
   - quick_capture.py: 快速捕获工具
   - hotkey_capture.py: 系统快捷键设置工具

## 七、使用建议

1. **推荐场景**：
   - Wayland 环境下的控件捕获
   - 需要精确定位的自动化测试
   - 替代 X11 全局监听功能

2. **性能优化**：
   - 调整监控间隔（--interval 参数）
   - 使用冷却时间避免频繁弹出

3. **最佳实践**：
   - 确保 AT-SPI 服务正常运行
   - 目标应用需要支持无障碍接口
   - 在虚拟桌面中可能需要额外配置