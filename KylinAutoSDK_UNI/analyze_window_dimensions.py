#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
分析hellobig窗口的尺寸差异
对比AT-SPI报告的尺寸与wlcctrl报告的尺寸
"""

import sys
import os
import subprocess
import pyatspi
import re


def get_wlcctrl_window_info():
    """获取wlcctrl报告的窗口信息"""
    try:
        print("🔍 获取wlcctrl窗口信息...")
        
        # 获取窗口列表
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 查找hellobig窗口ID
        window_id = None
        lines = result.stdout.strip().split('\n')
        for i, line in enumerate(lines):
            if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                if i > 0:
                    prev_line = lines[i-1]
                    if 'toplevel' in prev_line:
                        window_id = prev_line.split('"')[1]
                        print(f"   找到窗口ID: {window_id}")
                        print(f"   窗口标题: {line.strip()}")
                        break
        
        if not window_id:
            return None
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 解析几何信息
        for line in result.stdout.strip().split('\n'):
            if 'geometry:' in line:
                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', line)
                if match:
                    x, y, width, height = map(int, match.groups())
                    print(f"   wlcctrl几何信息: 位置({x}, {y}) 尺寸{width}×{height}")
                    return {
                        'position': (x, y),
                        'size': (width, height),
                        'window_id': window_id
                    }
        
        return None
        
    except Exception as e:
        print(f"❌ 获取wlcctrl窗口信息失败: {e}")
        return None


def get_atspi_window_info():
    """获取AT-SPI报告的窗口信息"""
    try:
        print("🔍 获取AT-SPI窗口信息...")
        
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            if 'hellobig' in app_name.lower():
                print(f"   找到应用: {app_name}")
                
                for j in range(app.childCount):
                    window = app.getChildAtIndex(j)
                    window_name = window.name or "unknown"
                    
                    if 'AT-SPI测试' in window_name or '测试主窗口' in window_name:
                        print(f"   找到窗口: {window_name}")
                        
                        # 获取窗口坐标信息
                        if hasattr(window, 'queryComponent'):
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                print(f"   AT-SPI几何信息: 位置({extents.x}, {extents.y}) 尺寸{extents.width}×{extents.height}")
                                
                                return {
                                    'position': (extents.x, extents.y),
                                    'size': (extents.width, extents.height),
                                    'window': window
                                }
        
        return None
        
    except Exception as e:
        print(f"❌ 获取AT-SPI窗口信息失败: {e}")
        return None


def analyze_control_positions(atspi_window):
    """分析控件的实际位置"""
    try:
        print("🔍 分析控件位置...")
        
        controls = []
        
        def collect_controls(element, depth=0, max_depth=3):
            if depth > max_depth:
                return
            
            try:
                name = element.name or "N/A"
                role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                
                # 获取坐标
                extents = None
                try:
                    if hasattr(element, 'queryComponent'):
                        component = element.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                except Exception:
                    pass
                
                # 只收集有名称和坐标的控件
                if (extents and extents.width > 0 and extents.height > 0 and 
                    name != "N/A" and role not in ['filler', 'unknown', 'application', 'frame']):
                    controls.append({
                        'name': name,
                        'role': role,
                        'position': (extents.x, extents.y),
                        'size': (extents.width, extents.height),
                        'depth': depth
                    })
                
                # 递归处理子元素
                try:
                    for i in range(element.childCount):
                        child = element.getChildAtIndex(i)
                        collect_controls(child, depth + 1, max_depth)
                except Exception:
                    pass
                    
            except Exception:
                pass
        
        collect_controls(atspi_window)
        
        print(f"   找到 {len(controls)} 个有效控件:")
        for ctrl in controls[:10]:  # 显示前10个
            print(f"     {ctrl['name']} ({ctrl['role']}) - 位置{ctrl['position']} 尺寸{ctrl['size']}")
        
        return controls
        
    except Exception as e:
        print(f"❌ 分析控件位置失败: {e}")
        return []


def calculate_offsets(wlcctrl_info, atspi_info, controls):
    """计算各种偏移量"""
    print("\n📐 计算偏移量...")
    
    # 窗口位置偏移
    wlc_pos = wlcctrl_info['position']
    atspi_pos = atspi_info['position']
    position_offset = (wlc_pos[0] - atspi_pos[0], wlc_pos[1] - atspi_pos[1])
    
    print(f"   窗口位置偏移: {position_offset}")
    print(f"     wlcctrl位置: {wlc_pos}")
    print(f"     AT-SPI位置: {atspi_pos}")
    
    # 窗口尺寸差异
    wlc_size = wlcctrl_info['size']
    atspi_size = atspi_info['size']
    size_diff = (wlc_size[0] - atspi_size[0], wlc_size[1] - atspi_size[1])
    
    print(f"   窗口尺寸差异: {size_diff}")
    print(f"     wlcctrl尺寸: {wlc_size}")
    print(f"     AT-SPI尺寸: {atspi_size}")
    
    # 分析可能的标题栏/装饰高度
    if size_diff[1] > 0:
        print(f"   可能的标题栏高度: {size_diff[1]}像素")
    
    # 分析控件的相对位置
    if controls:
        print(f"\n🎯 控件位置分析:")
        for ctrl in controls[:5]:  # 分析前5个控件
            ctrl_x, ctrl_y = ctrl['position']
            
            # 相对于AT-SPI窗口的位置
            relative_to_atspi = (ctrl_x - atspi_pos[0], ctrl_y - atspi_pos[1])
            
            # 相对于wlcctrl窗口的位置（假设控件在内容区域）
            relative_to_wlc = (ctrl_x - wlc_pos[0], ctrl_y - wlc_pos[1])
            
            print(f"     {ctrl['name']}:")
            print(f"       绝对位置: {ctrl['position']}")
            print(f"       相对AT-SPI窗口: {relative_to_atspi}")
            print(f"       相对wlcctrl窗口: {relative_to_wlc}")
    
    return {
        'position_offset': position_offset,
        'size_diff': size_diff,
        'title_bar_height': size_diff[1] if size_diff[1] > 0 else 0
    }


def main():
    """主函数"""
    print("📏 hellobig窗口尺寸差异分析器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    # 获取wlcctrl窗口信息
    wlcctrl_info = get_wlcctrl_window_info()
    if not wlcctrl_info:
        print("❌ 无法获取wlcctrl窗口信息")
        sys.exit(1)
    
    # 获取AT-SPI窗口信息
    atspi_info = get_atspi_window_info()
    if not atspi_info:
        print("❌ 无法获取AT-SPI窗口信息")
        sys.exit(1)
    
    # 分析控件位置
    controls = analyze_control_positions(atspi_info['window'])
    
    # 计算偏移量
    offsets = calculate_offsets(wlcctrl_info, atspi_info, controls)
    
    # 总结建议
    print(f"\n💡 修正建议:")
    print("=" * 40)
    print(f"1. 窗口位置偏移: {offsets['position_offset']}")
    print(f"2. 标题栏高度: {offsets['title_bar_height']}像素")
    print(f"3. 建议的坐标修正公式:")
    print(f"   真实屏幕X = AT-SPI_X + {offsets['position_offset'][0]}")
    print(f"   真实屏幕Y = AT-SPI_Y + {offsets['position_offset'][1]}")


if __name__ == "__main__":
    main()
