#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
简单的鼠标窗口检测器
直接显示鼠标位置下的所有窗口，不做复杂的过滤
"""

import sys
import os
import subprocess
import re


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_all_windows_at_mouse(mouse_x, mouse_y):
    """获取鼠标位置下的所有窗口"""
    print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
    print("🔍 查找鼠标位置下的所有窗口...")
    
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ wlcctrl命令失败")
            return []
        
        matching_windows = []
        lines = result.stdout.strip().split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'toplevel' in line:
                window_id = line.split('"')[1]
                
                window_title = ""
                if i + 1 < len(lines):
                    title_line = lines[i + 1].strip()
                    if title_line.startswith('title: '):
                        window_title = title_line[7:]
                    else:
                        window_title = title_line
                
                # 获取几何信息
                geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                                          capture_output=True, text=True)
                if geo_result.returncode == 0:
                    for geo_line in geo_result.stdout.strip().split('\n'):
                        if 'geometry:' in geo_line:
                            match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                            if match:
                                x, y, width, height = map(int, match.groups())
                                
                                # 检查鼠标是否在窗口范围内
                                if (x <= mouse_x < x + width and y <= mouse_y < y + height):
                                    matching_windows.append({
                                        'window_id': window_id,
                                        'title': window_title,
                                        'position': (x, y),
                                        'size': (width, height),
                                        'area': width * height
                                    })
                                break
                
                i += 2
            else:
                i += 1
        
        return matching_windows
        
    except Exception as e:
        print(f"❌ 获取窗口信息失败: {e}")
        return []


def analyze_windows(windows):
    """分析窗口信息"""
    if not windows:
        print("❌ 鼠标位置下没有找到任何窗口")
        return
    
    print(f"\n📋 找到 {len(windows)} 个窗口:")
    print("=" * 60)
    
    for i, window in enumerate(windows):
        title = window['title']
        x, y = window['position']
        width, height = window['size']
        area = window['area']
        
        print(f"{i+1}. 窗口标题: {title}")
        print(f"   窗口ID: {window['window_id']}")
        print(f"   位置: ({x}, {y})")
        print(f"   尺寸: {width}×{height}")
        print(f"   面积: {area}")
        
        # 分析窗口特征
        print(f"   特征分析:")
        
        # 检查是否可能是最小化窗口
        if width >= 2500 or height >= 1500:
            print(f"     ⚠️  尺寸异常大，可能是最小化窗口")
        elif x == 0 and y == 0 and (width > 1500 or height > 1000):
            print(f"     ⚠️  在原点且尺寸大，可能是最小化窗口")
        else:
            print(f"     ✅ 尺寸和位置正常")
        
        # 检查窗口类型
        if title.lower() in ['桌面', 'desktop']:
            print(f"     🖥️  桌面窗口")
        elif 'panel' in title.lower():
            print(f"     📊 面板窗口")
        elif 'terminal' in title.lower():
            print(f"     💻 终端窗口")
        elif '@' in title and ':' in title and ('~' in title or '/' in title):
            print(f"     💻 终端窗口 (mate-terminal/bash提示符)")
        elif 'hellobig' in title.lower() or 'AT-SPI测试' in title:
            print(f"     🎯 hellobig应用窗口")
        elif 'Qt Creator' in title:
            print(f"     🛠️  Qt Creator窗口")
        else:
            print(f"     📄 普通应用窗口")
        
        print()
    
    # 推荐最佳窗口
    print("🎯 推荐选择:")
    print("-" * 30)
    
    # 过滤掉明显的系统窗口和可疑窗口
    valid_windows = []
    for window in windows:
        title = window['title']
        x, y = window['position']
        width, height = window['size']
        
        # 跳过系统窗口
        if title.lower() in ['桌面', 'desktop', 'ukui-panel', 'ukui-sidebar']:
            continue
        
        # 跳过异常大的窗口
        if width >= 2500 or height >= 1500:
            continue
        
        # 跳过在原点且很大的窗口
        if x == 0 and y == 0 and (width > 1500 or height > 1000):
            continue
        
        valid_windows.append(window)
    
    if not valid_windows:
        print("❌ 没有找到有效的应用窗口")
    else:
        # 智能选择窗口
        # 优先选择终端窗口（如果用户说鼠标在mate-terminal上）
        terminal_windows = []
        other_windows = []

        for window in valid_windows:
            title = window['title']
            if ('terminal' in title.lower() or
                ('@' in title and ':' in title and ('~' in title or '/' in title))):
                terminal_windows.append(window)
            else:
                other_windows.append(window)

        if terminal_windows:
            # 如果有终端窗口，选择面积最小的终端窗口
            best_window = min(terminal_windows, key=lambda w: w['area'])
            print(f"✅ 推荐窗口: {best_window['title']}")
            print(f"   位置: {best_window['position']}")
            print(f"   尺寸: {best_window['size'][0]}×{best_window['size'][1]}")
            print(f"   💻 这是终端窗口，符合用户描述")
        else:
            # 否则选择面积最小的其他窗口
            best_window = min(other_windows, key=lambda w: w['area'])
            print(f"✅ 推荐窗口: {best_window['title']}")
            print(f"   位置: {best_window['position']}")
            print(f"   尺寸: {best_window['size'][0]}×{best_window['size'][1]}")

            # 特别提醒
            if 'hellobig' in best_window['title'].lower() or 'AT-SPI测试' in best_window['title']:
                print(f"   ⚠️  这是hellobig窗口")
                print(f"   💡 如果hellobig实际上是最小化的，这个检测就是错误的")


def main():
    """主函数"""
    print("🔍 简单鼠标窗口检测器")
    print("=" * 60)
    print("📖 直接显示鼠标位置下的所有窗口信息")
    print("🎯 帮助诊断窗口检测问题")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        mouse_x, mouse_y = get_mouse_position()
        if not mouse_x:
            print("❌ 无法获取鼠标位置")
            return
        
        # 获取鼠标位置下的所有窗口
        windows = get_all_windows_at_mouse(mouse_x, mouse_y)
        
        # 分析窗口信息
        analyze_windows(windows)
        
        print(f"\n💡 诊断建议:")
        print("=" * 30)
        print("1. 如果推荐的窗口不是你鼠标实际悬停的窗口，")
        print("   说明wlcctrl的窗口信息可能不准确")
        print("2. 如果hellobig窗口被推荐但实际已最小化，")
        print("   说明需要更严格的可见性检查")
        print("3. 如果mate-terminal没有出现在列表中，")
        print("   说明wlcctrl可能无法检测到某些窗口")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
