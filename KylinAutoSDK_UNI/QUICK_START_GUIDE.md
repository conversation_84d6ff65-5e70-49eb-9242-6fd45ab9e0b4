# AT-SPI控件检测库 - 快速开始指南

## 🚀 5分钟快速体验

### 1. 环境检查
```bash
# 确保在Linux图形界面环境中运行
echo $DISPLAY

# 安装必要依赖
sudo apt-get install python3-pyatspi xdotool
pip install pynput
```

### 2. 立即测试（针对hellobig坐标偏差问题）
```bash
# 🎯 如果accerciser能看到控件但坐标不准（推荐）
python3 offset_corrected_detector.py

# 🔍 分析坐标偏移问题
python3 coordinate_offset_analyzer.py

# 🧪 诊断hellobig程序的控件检测问题
python3 hellobig_detector.py

# 🔧 运行修复版自动检测器
python3 auto_control_detector.py

# 🧪 运行精度对比测试
python3 test_control_accuracy.py
```

## 🔧 坐标偏移修正功能

专门解决accerciser能看到控件但坐标不准确的问题：

### 🎯 坐标偏移修正版（最新推荐）
- ✅ **自动偏移检测** - 自动检测11种常见的坐标偏移模式
- ✅ **实时坐标修正** - 实时修正AT-SPI坐标与桌面坐标的偏差
- ✅ **精确高亮定位** - 高亮边框准确覆盖实际控件位置
- ✅ **偏移验证机制** - 自动验证偏移修正的准确性

### 🔧 通用修复版功能
- ✅ **坐标精度问题** - 修正从(0,0)绘制的错误
- ✅ **控件识别问题** - 能够识别label、button等具体控件
- ✅ **高亮准确性** - 边框准确覆盖控件位置
- ✅ **检测算法优化** - 改进的控件筛选和匹配逻辑

### 3. 基本用法代码

#### 🎯 坐标偏移修正版用法（最新推荐）
```python
from offset_corrected_detector import OffsetCorrectedDetector

# 创建坐标偏移修正检测器
detector = OffsetCorrectedDetector()

# 检测指定坐标的控件（自动修正偏移）
result = detector.get_control_at_point_corrected(500, 300)
if result['success']:
    print(f"找到控件: {result['position']}")
    print(f"控件名称: {result['control_info']['name']}")
    print(f"使用偏移: {result['used_offset']}")
    
    # 高亮显示控件（自动应用偏移修正）
    detector.highlight_control_corrected(500, 300, color='green')

# 打印详细信息（含偏移信息）
detector.print_control_info_corrected(500, 300)
```

#### 🔧 通用修复版用法
```python
from fixed_control_detector import FixedControlDetector

# 创建修复版检测器
detector = FixedControlDetector()

# 检测指定坐标的控件
result = detector.get_control_info_at_point(500, 300)
if result['success']:
    print(f"找到控件: {result['position']}")
    print(f"控件名称: {result['control_info']['name']}")
    print(f"控件角色: {result['control_info']['role']}")
    
    # 高亮显示控件
    detector.highlight_control_at_point(500, 300, color='red')

# 打印详细信息
detector.print_control_info(500, 300)
```

#### 📋 原版用法
```python
from at_spi_control_detector import ATSPIControlDetector

# 创建原版检测器
detector = ATSPIControlDetector()

# 检测指定坐标的控件
result = detector.get_control_at_point(500, 300)
if result['success']:
    print(f"找到控件: {result['position']}")
    
    # 高亮显示控件
    detector.highlight_control_at_point(500, 300, color='red')

# 打印详细信息
detector.print_control_info(500, 300)
```

## 📁 主要文件

### 🔧 修复版文件（推荐使用）
- `fixed_control_detector.py` - 修复版核心库
- `auto_control_detector.py` - 修复版自动检测器
- `test_control_accuracy.py` - 精度对比测试工具

### 📋 原版文件
- `at_spi_control_detector.py` - 原版主接口库
- `mouse_hover_control_detector.py` - 原版鼠标悬停检测工具  
- `demo_at_spi_detector.py` - 原版完整功能演示
- `simple_control_test.py` - 原版基础测试工具

### 📚 文档和其他
- `AT_SPI_CONTROL_DETECTOR_README.md` - 详细文档
- `ultimate_highlight.py` - 高亮功能实现

## 🎯 核心功能

1. **坐标检测**: 传入(x,y)坐标，获取该位置的控件信息
2. **控件高亮**: 红框突出显示检测到的控件
3. **详细信息**: 获取控件名称、角色、状态等完整信息
4. **自适应**: 自动处理不同桌面应用的窗口和坐标转换

## 💡 常用快捷操作

```bash
# 快速检测鼠标当前位置的控件
python3 -c "
from at_spi_control_detector import *
import subprocess
result = subprocess.run(['xdotool', 'getmouselocation'], capture_output=True, text=True, env={'DISPLAY': ':0'})
if result.returncode == 0:
    parts = result.stdout.strip().split()
    x = int(parts[0].split(':')[1])
    y = int(parts[1].split(':')[1])
    print(f'鼠标位置: ({x}, {y})')
    highlight_control_at_point(x, y, color='green')
    print_control_info_at_point(x, y)
"
```

## 🔧 故障排除

- **找不到控件**: 确保坐标在可见控件范围内
- **高亮不显示**: 检查X11环境，确认在图形界面中运行
- **导入错误**: 确认安装了pyatspi: `sudo apt-get install python3-pyatspi`

## 📖 详细文档

查看 [AT_SPI_CONTROL_DETECTOR_README.md](AT_SPI_CONTROL_DETECTOR_README.md) 获取完整API文档和高级用法。