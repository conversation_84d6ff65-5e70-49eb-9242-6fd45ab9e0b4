#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
通用AT-SPI坐标转换器
解决窗口标题栏偏移问题，实现真正的通用接口
"""

import time
import subprocess
import re

class UniversalATSPICoordinateConverter:
    """
    通用AT-SPI坐标转换器
    
    功能：
    1. 自动检测窗口位置（支持任意应用）
    2. 考虑窗口标题栏偏移的坐标转换
    3. 无需硬编码特定应用映射
    4. 缓存机制提高性能
    """
    
    def __init__(self, titlebar_height=40):
        """
        初始化坐标转换器
        
        Args:
            titlebar_height: 窗口标题栏高度，默认40px
                           不同桌面环境可能需要调整：
                           - GNOME/MATE: 30-35px
                           - KDE: 35-40px  
                           - XFCE: 25-30px
        """
        self.titlebar_height = titlebar_height
        self._window_cache = {}
        self._cache_time = 0
        self._cache_duration = 5  # 缓存5秒
    
    def detect_titlebar_height(self):
        """
        动态检测标题栏高度
        TODO: 可以通过对比AT-SPI和wlcctrl坐标差异来自动计算
        """
        # 可以基于桌面环境进行自适应
        desktop_env = subprocess.run(['echo', '$XDG_CURRENT_DESKTOP'], 
                                   capture_output=True, text=True).stdout.strip()
        
        if 'GNOME' in desktop_env or 'MATE' in desktop_env:
            return 35
        elif 'KDE' in desktop_env:
            return 40
        elif 'XFCE' in desktop_env:
            return 30
        else:
            return self.titlebar_height  # 默认值
    
    def get_window_by_title_pattern(self, title_pattern):
        """
        通用方法：根据窗口标题模式获取窗口几何信息
        
        Args:
            title_pattern: 窗口标题匹配模式（支持部分匹配）
        
        Returns:
            dict: 窗口信息 {x, y, width, height, title} 或 None
        """
        current_time = time.time()
        
        # 检查缓存
        if current_time - self._cache_time > self._cache_duration:
            self._window_cache.clear()
            self._cache_time = current_time
        
        if title_pattern in self._window_cache:
            return self._window_cache[title_pattern]
        
        try:
            # 获取所有窗口列表
            result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
            if result.returncode != 0:
                return None
            
            lines = result.stdout.split('\n')
            target_uuid = None
            found_title = None
            
            # 通用搜索：查找包含目标标题模式的窗口
            for i, line in enumerate(lines):
                if title_pattern in line:
                    found_title = line.strip()
                    # 向上查找对应的toplevel UUID
                    for j in range(i-1, -1, -1):
                        if 'toplevel' in lines[j]:
                            uuid_match = re.search(r'"([^"]+)"', lines[j])
                            if uuid_match:
                                target_uuid = uuid_match.group(1)
                                break
                    break
            
            if not target_uuid:
                return None
            
            # 获取窗口几何信息
            result = subprocess.run(['wlcctrl', '--getwindowgeometry', target_uuid], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                return None
            
            geo_match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', result.stdout)
            if geo_match:
                x, y, width, height = map(int, geo_match.groups())
                window_info = {
                    'x': x, 
                    'y': y, 
                    'width': width, 
                    'height': height,
                    'title': found_title,
                    'uuid': target_uuid
                }
                
                # 缓存结果
                self._window_cache[title_pattern] = window_info
                return window_info
            
            return None
            
        except Exception as e:
            print(f"获取窗口几何信息失败: {e}")
            return None
    
    def convert_atspi_to_desktop_coords(self, atspi_x, atspi_y, window_title_pattern):
        """
        通用AT-SPI坐标转换方法
        
        Args:
            atspi_x, atspi_y: AT-SPI相对坐标（相对于窗口内容区域）
            window_title_pattern: 窗口标题匹配模式
        
        Returns:
            tuple: (desktop_x, desktop_y, window_info) 桌面绝对坐标和窗口信息
        """
        window_info = self.get_window_by_title_pattern(window_title_pattern)
        if not window_info:
            print(f"⚠️ 无法找到窗口: {window_title_pattern}")
            return atspi_x, atspi_y, None
        
        # 修正的坐标转换公式：考虑标题栏偏移
        desktop_x = window_info['x'] + atspi_x
        desktop_y = window_info['y'] + atspi_y + self.titlebar_height
        
        return desktop_x, desktop_y, window_info
    
    def highlight_control_universal(self, atspi_x, atspi_y, width, height, 
                                  window_title_pattern, duration=3, color='red'):
        """
        通用控件高亮方法
        
        Args:
            atspi_x, atspi_y: AT-SPI坐标
            width, height: 控件尺寸
            window_title_pattern: 窗口标题匹配模式
            duration: 高亮持续时间
            color: 高亮颜色
        
        Returns:
            bool: 高亮是否成功
        """
        desktop_x, desktop_y, window_info = self.convert_atspi_to_desktop_coords(
            atspi_x, atspi_y, window_title_pattern
        )
        
        if not window_info:
            return False
        
        print(f"🎯 通用坐标转换:")
        print(f"   窗口: {window_info['title']}")
        print(f"   窗口位置: ({window_info['x']}, {window_info['y']})")
        print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y})")
        print(f"   标题栏偏移: +{self.titlebar_height}px")
        print(f"   桌面坐标: ({desktop_x}, {desktop_y})")
        
        try:
            import sys
            import os
            sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
            from ultimate_highlight import ultimate_highlight
            
            success = ultimate_highlight(
                desktop_x, desktop_y, 
                max(width, 10), max(height, 10),
                duration, color, 3
            )
            
            if success:
                print(f"   ✅ 通用高亮显示成功")
            else:
                print(f"   ❌ 高亮显示失败")
            
            return success
            
        except Exception as e:
            print(f"   ❌ 高亮失败: {e}")
            return False
    
    def is_coordinate_in_window(self, x, y, window_title_pattern):
        """
        检查坐标是否在指定窗口内
        
        Args:
            x, y: 桌面坐标
            window_title_pattern: 窗口标题匹配模式
        
        Returns:
            bool: 是否在窗口内
        """
        window_info = self.get_window_by_title_pattern(window_title_pattern)
        if not window_info:
            return False
        
        return (window_info['x'] <= x <= window_info['x'] + window_info['width'] and
                window_info['y'] <= y <= window_info['y'] + window_info['height'])
    
    def desktop_to_atspi_coords(self, desktop_x, desktop_y, window_title_pattern):
        """
        桌面坐标转AT-SPI坐标（反向转换）
        
        Args:
            desktop_x, desktop_y: 桌面坐标
            window_title_pattern: 窗口标题匹配模式
        
        Returns:
            tuple: (atspi_x, atspi_y) AT-SPI相对坐标
        """
        window_info = self.get_window_by_title_pattern(window_title_pattern)
        if not window_info:
            return desktop_x, desktop_y
        
        atspi_x = desktop_x - window_info['x']
        atspi_y = desktop_y - window_info['y'] - self.titlebar_height
        
        return atspi_x, atspi_y


# 创建全局实例
_global_converter = None

def get_universal_converter(titlebar_height=40):
    """获取全局通用坐标转换器实例"""
    global _global_converter
    if _global_converter is None:
        _global_converter = UniversalATSPICoordinateConverter(titlebar_height)
    return _global_converter

def convert_atspi_coords_universal(atspi_x, atspi_y, window_title_pattern):
    """
    便捷函数：通用AT-SPI坐标转换
    
    Args:
        atspi_x, atspi_y: AT-SPI坐标
        window_title_pattern: 窗口标题匹配模式
    
    Returns:
        tuple: (desktop_x, desktop_y)
    """
    converter = get_universal_converter()
    desktop_x, desktop_y, _ = converter.convert_atspi_to_desktop_coords(
        atspi_x, atspi_y, window_title_pattern
    )
    return desktop_x, desktop_y

def highlight_control_universal(atspi_x, atspi_y, width, height, 
                               window_title_pattern, duration=3):
    """
    便捷函数：通用控件高亮
    
    Args:
        atspi_x, atspi_y: AT-SPI坐标
        width, height: 控件尺寸
        window_title_pattern: 窗口标题匹配模式
        duration: 高亮持续时间
    
    Returns:
        bool: 高亮是否成功
    """
    converter = get_universal_converter()
    return converter.highlight_control_universal(
        atspi_x, atspi_y, width, height, window_title_pattern, duration
    )