#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
UNI最终版本
集成了精确的坐标转换和高亮功能
专门针对hellobig应用优化
"""

import threading
import time
import subprocess
import os
import sys
import re

# 添加路径并导入基础UNI
try:
    from uni_sdk import UNI as BaseUNI
except ImportError:
    from UNI import UNI as BaseUNI

class UNI_Final(BaseUNI):
    """
    最终优化版本的UNI类
    集成精确的坐标转换和高亮功能
    """
    
    def __init__(self):
        super().__init__()
        self._window_cache = {}
        self._window_cache_time = 0
        
    def kdk_getElement_Uni(self, x, y, quick=False, menuele=None, highlight=False):
        """
        精确的控件定位方法
        集成了修正的坐标转换和高亮功能
        """
        # 处理菜单元素
        if menuele:
            result = super().kdk_getElement_Uni(x, y, quick, menuele)
            if highlight and result[0]:
                self._highlight_control_with_fixed_coords(result[0], x, y)
            return result
        
        # 使用基础识别功能
        try:
            control_data, info = super().kdk_getElement_Uni(x, y, quick, menuele)
            
            if control_data and highlight:
                # 使用修正的高亮功能
                self._highlight_control_with_fixed_coords(control_data, x, y)
            
            return control_data, info
            
        except Exception as e:
            return None, f"识别失败: {e}"
    
    def _get_hellobig_window_geometry(self):
        """获取hellobig窗口的真实几何信息（带缓存）"""
        current_time = time.time()
        
        # 缓存5秒
        if current_time - self._window_cache_time > 5:
            try:
                # 列出所有窗口找到hellobig
                result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
                if result.returncode != 0:
                    return None
                
                # 查找AT-SPI测试界面窗口
                lines = result.stdout.split('\n')
                hellobig_uuid = None
                
                for i, line in enumerate(lines):
                    if 'AT-SPI测试界面' in line or 'hellobig' in line.lower():
                        # 查找前一行的UUID
                        for j in range(i-1, -1, -1):
                            if 'toplevel' in lines[j]:
                                uuid_match = re.search(r'"([^"]+)"', lines[j])
                                if uuid_match:
                                    hellobig_uuid = uuid_match.group(1)
                                    break
                        break
                
                if not hellobig_uuid:
                    return None
                
                # 获取窗口几何信息
                result = subprocess.run(['wlcctrl', '--getwindowgeometry', hellobig_uuid], 
                                      capture_output=True, text=True)
                if result.returncode != 0:
                    return None
                
                # 解析几何信息
                geo_match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', result.stdout)
                if geo_match:
                    x, y, width, height = map(int, geo_match.groups())
                    self._window_cache = {'x': x, 'y': y, 'width': width, 'height': height}
                    self._window_cache_time = current_time
                
            except Exception:
                pass
        
        return self._window_cache if self._window_cache else None
    
    def _highlight_control_with_fixed_coords(self, control_data, target_x, target_y):
        """
        使用修正坐标的高亮函数
        """
        if not control_data:
            return False
        
        try:
            print(f"🎨 开始精确高亮控件...")
            
            # 获取hellobig窗口的真实位置
            window_geo = self._get_hellobig_window_geometry()
            if not window_geo:
                print("⚠️ 无法获取窗口位置，使用默认高亮")
                return self._fallback_highlight(control_data, target_x, target_y)
            
            # 获取AT-SPI控件坐标
            coords = control_data.get('Coords', {})
            if not coords:
                print("⚠️ 控件没有坐标信息，使用目标位置高亮")
                return self._highlight_at_target(target_x, target_y)
            
            atspi_x = coords.get('x', 0)
            atspi_y = coords.get('y', 0)
            atspi_w = coords.get('width', 0)
            atspi_h = coords.get('height', 0)
            
            print(f"📍 坐标转换:")
            print(f"   窗口位置: ({window_geo['x']}, {window_geo['y']})")
            print(f"   AT-SPI相对坐标: ({atspi_x}, {atspi_y})")
            
            # 关键修正：将AT-SPI相对坐标转换为桌面绝对坐标
            desktop_x = window_geo['x'] + atspi_x
            desktop_y = window_geo['y'] + atspi_y
            
            print(f"   转换后桌面坐标: ({desktop_x}, {desktop_y})")
            print(f"   控件大小: {atspi_w} × {atspi_h}")
            
            # 使用转换后的坐标进行高亮
            success = self._ultimate_highlight(
                desktop_x, desktop_y, 
                max(atspi_w, 10), max(atspi_h, 10),
                3, 'red', 3
            )
            
            if success:
                print("✅ 精确高亮显示成功")
                return True
            else:
                print("❌ 高亮显示失败，尝试回退方案")
                return self._fallback_highlight(control_data, target_x, target_y)
            
        except Exception as e:
            print(f"❌ 精确高亮异常: {e}")
            return self._fallback_highlight(control_data, target_x, target_y)
    
    def _ultimate_highlight(self, x, y, width, height, duration, color, border_width):
        """调用终极高亮函数"""
        try:
            sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
            from ultimate_highlight import ultimate_highlight
            
            return ultimate_highlight(
                x=x, y=y, width=width, height=height,
                duration=duration, color=color, border_width=border_width
            )
        except ImportError:
            print("⚠️ 无法导入高亮模块")
            return False
        except Exception as e:
            print(f"❌ 高亮函数异常: {e}")
            return False
    
    def _fallback_highlight(self, control_data, target_x, target_y):
        """回退高亮方案"""
        try:
            coords = control_data.get('Coords', {})
            if coords:
                # 尝试使用原始坐标
                x = coords.get('x', target_x)
                y = coords.get('y', target_y)
                width = coords.get('width', 100)
                height = coords.get('height', 30)
                
                print(f"🔄 回退方案: 使用原始AT-SPI坐标 ({x}, {y})")
                return self._ultimate_highlight(x, y, width, height, 3, 'blue', 2)
            else:
                # 最终回退：在目标位置高亮
                return self._highlight_at_target(target_x, target_y)
                
        except Exception as e:
            print(f"❌ 回退高亮也失败: {e}")
            return self._highlight_at_target(target_x, target_y)
    
    def _highlight_at_target(self, target_x, target_y):
        """在目标位置显示高亮"""
        try:
            print(f"🎯 目标位置高亮: ({target_x}, {target_y})")
            return self._ultimate_highlight(
                target_x - 25, target_y - 12, 50, 25, 3, 'yellow', 2
            )
        except Exception:
            return False
    
    def test_hellobig_controls(self):
        """测试hellobig应用的控件识别和高亮"""
        print("🧪 测试hellobig控件识别和精确高亮")
        print("=" * 50)
        
        # 获取窗口位置
        window_geo = self._get_hellobig_window_geometry()
        if not window_geo:
            print("❌ 无法获取hellobig窗口位置")
            return
        
        print(f"✅ hellobig窗口: ({window_geo['x']}, {window_geo['y']}) {window_geo['width']}×{window_geo['height']}")
        
        # 定义测试点（相对于窗口）
        test_points = [
            (50, 50, "按钮区域"),
            (200, 100, "输入框区域"),
            (400, 150, "滑块区域"),
            (300, 300, "列表区域"),
            (500, 400, "表格区域"),
        ]
        
        success_count = 0
        
        for rel_x, rel_y, description in test_points:
            abs_x = window_geo['x'] + rel_x
            abs_y = window_geo['y'] + rel_y
            
            print(f"\n📍 测试 {description}")
            print(f"   相对位置: ({rel_x}, {rel_y}) -> 绝对位置: ({abs_x}, {abs_y})")
            
            try:
                start_time = time.time()
                control_data, info = self.kdk_getElement_Uni(abs_x, abs_y, quick=False, highlight=True)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    print(f"   ✅ 识别成功 ({elapsed:.2f}s): {name} ({role})")
                    print(f"   🎨 精确高亮已显示")
                    success_count += 1
                else:
                    print(f"   ❌ 识别失败 ({elapsed:.2f}s): {info}")
                
                time.sleep(3)  # 等待观察高亮
                
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_points)} 成功")
        return success_count > 0

# 便捷函数
def create_final_uni():
    """创建最终版本的UNI实例"""
    return UNI_Final()

def test_final_uni():
    """测试最终版本的UNI"""
    uni = UNI_Final()
    return uni.test_hellobig_controls()

# 向后兼容
UNI = UNI_Final

# 导出
__all__ = ["UNI_Final", "UNI", "create_final_uni", "test_final_uni"]