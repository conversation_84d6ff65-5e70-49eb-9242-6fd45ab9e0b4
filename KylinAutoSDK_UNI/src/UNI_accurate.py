#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
UNI精确版本
集成坐标修正功能，确保高亮位置准确
"""

import threading
import time
import subprocess
import os
import sys
from collections import deque
from functools import lru_cache

# 添加路径并导入基础UNI
try:
    from uni_sdk import UNI as BaseUNI
except ImportError:
    from UNI import UNI as BaseUNI

# 导入坐标修正模块
try:
    from coordinate_fix import get_accurate_highlight_coords, smart_coordinate_correction
except ImportError:
    print("⚠️ 坐标修正模块不可用，使用基本修正")
    def get_accurate_highlight_coords(mouse_x, mouse_y, control_data, window_info, target_app_info=None):
        coords = control_data.get('Coords', {}) if control_data else {}
        if coords:
            corrected = coords.copy()
            corrected['y'] = coords.get('y', 0) - 40
            return corrected
        return coords

# 导入精确高亮模块
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
try:
    from ultimate_highlight_accurate import ultimate_highlight_accurate
except ImportError:
    print("⚠️ 精确高亮模块不可用，使用基本高亮")
    def ultimate_highlight_accurate(**kwargs):
        try:
            from ultimate_highlight import ultimate_highlight
            return ultimate_highlight(
                kwargs.get('x', 0),
                kwargs.get('y', 0),
                kwargs.get('width', 100),
                kwargs.get('height', 30),
                kwargs.get('duration', 3),
                kwargs.get('color', 'red'),
                kwargs.get('border_width', 3)
            )
        except:
            return False

class UNI_Accurate(BaseUNI):
    """
    精确版本的UNI类
    集成坐标修正功能
    """
    
    def __init__(self):
        super().__init__()
        self._coordinate_cache = {}
        self._last_mouse_pos = None
        
    def kdk_getElement_Uni(self, x, y, quick=False, menuele=None, highlight=False):
        """
        精确的控件定位方法
        
        Args:
            x, y: 目标坐标
            quick: 快速模式
            menuele: 菜单元素
            highlight: 是否高亮显示
            
        Returns:
            tuple: (控件数据, 信息)
        """
        # 记录当前鼠标位置用于坐标修正
        self._last_mouse_pos = (x, y)
        
        # 处理菜单元素
        if menuele:
            result = super().kdk_getElement_Uni(x, y, quick, menuele)
            if highlight and result[0]:
                self._highlight_control_accurate(result[0], x, y)
            return result
        
        # 使用基础识别功能
        try:
            control_data, info = super().kdk_getElement_Uni(x, y, quick, menuele)
            
            if control_data and highlight:
                # 使用精确高亮
                self._highlight_control_accurate(control_data, x, y)
            
            return control_data, info
            
        except Exception as e:
            return None, f"识别失败: {e}"
    
    def _highlight_control_accurate(self, control_data, mouse_x, mouse_y):
        """
        精确高亮控件
        """
        try:
            print(f"🎨 开始精确高亮控件...")
            
            # 获取窗口信息
            window_info = self._get_current_window_info(mouse_x, mouse_y)
            
            # 获取应用信息
            target_app_info = self._get_target_app_info(control_data)
            
            # 使用精确高亮
            success = ultimate_highlight_accurate(
                control_data=control_data,
                mouse_x=mouse_x,
                mouse_y=mouse_y,
                window_info=window_info,
                target_app_info=target_app_info,
                duration=3,
                color='red',
                border_width=3
            )
            
            if success:
                print(f"✅ 精确高亮成功")
            else:
                print(f"❌ 精确高亮失败，尝试回退方案")
                self._fallback_highlight(control_data, mouse_x, mouse_y)
            
        except Exception as e:
            print(f"❌ 精确高亮异常: {e}")
            self._fallback_highlight(control_data, mouse_x, mouse_y)
    
    def _get_current_window_info(self, mouse_x, mouse_y):
        """获取当前窗口信息"""
        try:
            from uni_sdk.utils.helpers import get_windows_by_wlcctrl
            
            windows = get_windows_by_wlcctrl()
            
            # 查找包含鼠标位置的窗口
            for title, geometry in windows.items():
                if (geometry['x'] <= mouse_x <= geometry['x'] + geometry['width'] and
                    geometry['y'] <= mouse_y <= geometry['y'] + geometry['height']):
                    return {
                        'title': title,
                        'geometry': geometry
                    }
            
            return None
            
        except Exception as e:
            print(f"⚠️ 获取窗口信息失败: {e}")
            return None
    
    def _get_target_app_info(self, control_data):
        """获取目标应用信息"""
        try:
            # 从控件数据推断应用信息
            name = control_data.get('Name', '')
            role = control_data.get('Rolename', '')
            
            # 简单的应用类型推断
            if any(keyword in name.lower() for keyword in ['qt', 'creator', 'hellobig']):
                return {'process': 'qtcreator', 'type': 'qt'}
            elif any(keyword in name.lower() for keyword in ['firefox', 'mozilla']):
                return {'process': 'firefox', 'type': 'browser'}
            elif any(keyword in name.lower() for keyword in ['chrome', 'chromium']):
                return {'process': 'chrome', 'type': 'browser'}
            else:
                return {'process': 'unknown', 'type': 'generic'}
                
        except Exception:
            return {'process': 'unknown', 'type': 'generic'}
    
    def _fallback_highlight(self, control_data, mouse_x, mouse_y):
        """回退高亮方案"""
        try:
            coords = control_data.get('Coords', {})
            if coords:
                # 简单的坐标修正
                x = coords.get('x', mouse_x)
                y = coords.get('y', mouse_y) - 40  # 基本的Y轴修正
                width = coords.get('width', 100)
                height = coords.get('height', 30)
                
                # 使用基本高亮
                ultimate_highlight_accurate(
                    x=x, y=y, width=width, height=height,
                    duration=3, color='blue', border_width=2
                )
                print(f"✅ 回退高亮显示在: ({x}, {y})")
            else:
                # 最终回退：在鼠标位置显示
                ultimate_highlight_accurate(
                    x=mouse_x-25, y=mouse_y-12, width=50, height=25,
                    duration=3, color='yellow', border_width=2
                )
                print(f"✅ 最终回退：鼠标位置高亮")
                
        except Exception as e:
            print(f"❌ 回退高亮也失败: {e}")
    
    def test_accurate_highlight(self, x, y):
        """测试精确高亮功能"""
        print(f"🧪 测试坐标 ({x}, {y}) 的精确高亮")
        
        control_data, info = self.kdk_getElement_Uni(x, y, quick=False, highlight=True)
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            print(f"✅ 找到控件: {name} ({role})")
            return True
        else:
            print(f"❌ 未找到控件: {info}")
            return False

# 便捷函数
def create_accurate_uni():
    """创建精确版本的UNI实例"""
    return UNI_Accurate()

def test_coordinate_accuracy():
    """测试坐标精确性"""
    print("🧪 坐标精确性测试")
    print("=" * 40)
    
    try:
        uni = UNI_Accurate()
        
        # 获取鼠标位置
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            
            print(f"当前鼠标位置: ({mouse_x}, {mouse_y})")
            
            # 测试精确高亮
            success = uni.test_accurate_highlight(mouse_x, mouse_y)
            
            if success:
                print("✅ 精确高亮测试成功")
            else:
                print("❌ 精确高亮测试失败")
        else:
            print("❌ 无法获取鼠标位置")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

# 向后兼容
UNI = UNI_Accurate

# 导出
__all__ = ["UNI_Accurate", "UNI", "create_accurate_uni", "test_coordinate_accuracy"]