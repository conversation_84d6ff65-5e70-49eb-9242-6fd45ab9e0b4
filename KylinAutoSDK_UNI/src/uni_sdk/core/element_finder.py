#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
控件查找模块
负责控件的定位和查找
"""

import pyatspi
from ..utils.helpers import (get_element_extents, is_element_valid,
                           state_list_to_names, get_element_actions,
                           is_point_in_rect, is_point_in_rect_corrected,
                           get_element_extents_corrected, is_wayland_environment)
from .element_extractor import ElementExtractor


class ElementFinder:
    """控件查找器"""
    
    def __init__(self):
        self.child_ele = None
        self.find_data = None
        self.find_extents = None
        self.find_find = None
        self.xxx = None
        self.flag = 0
    
    def find_accessible_at_point(self, element, x, y, activewindow_region):
        """
        在激活窗口中根据(x, y)找到对应的控件信息
        使用新的精确匹配算法，优先返回最小的匹配控件

        Args:
            element: 窗口控件
            x: 横坐标
            y: 纵坐标
            activewindow_region: 窗口的区域位置
        """
        # 收集所有匹配的控件
        matching_elements = []
        self._collect_matching_elements(element, x, y, activewindow_region, matching_elements)

        # 从匹配的控件中选择最精确的一个
        if matching_elements:
            self.child_ele = self._select_best_match(matching_elements, x, y)

    def _collect_matching_elements(self, element, x, y, activewindow_region, matching_elements):
        """
        递归收集所有匹配指定坐标的控件

        Args:
            element: 当前检查的元素
            x, y: 目标坐标
            activewindow_region: 活动窗口区域
            matching_elements: 匹配元素列表
        """
        try:
            extents = get_element_extents(element)
            if not extents or not is_element_valid(extents):
                return

            # 检查坐标是否在当前元素内 - 使用修正后的坐标匹配
            if not is_point_in_rect_corrected(x, y, extents.x, extents.y, extents.width, extents.height):
                return

            # 检查元素状态和属性
            states = element.getState()
            state_names = state_list_to_names(states)
            extents_action = get_element_actions(element)

            # 判断是否为有效的可交互控件 - 放宽条件
            is_interactive = (
                ('showing' in state_names or 'visible' in state_names or 'enabled' in state_names or 'activate' in extents_action) and
                (extents_action or
                 'focusable' in state_names or 'focused' in state_names or
                 'selectable' in state_names or 'sensitive' in state_names or
                 element.name or element.description or  # 有名称或描述的也算
                 element.getRoleName() in ['button', 'menu item', 'text', 'label', 'link', 'entry', 'frame'])  # 常见的可交互角色
            )

            # 排除窗口本身
            is_window = (
                activewindow_region and
                activewindow_region[0] == extents.x and
                activewindow_region[1] == extents.y and
                (activewindow_region[2] - activewindow_region[0]) == extents.width and
                (activewindow_region[3] - activewindow_region[1]) == extents.height
            )

            if is_interactive and not is_window:
                # 计算元素的面积和其他属性用于排序
                area = extents.width * extents.height
                has_name = bool(element.name and element.name.strip())
                has_description = bool(element.description and element.description.strip())
                has_actions = bool(extents_action)
                is_focused = 'focused' in state_names

                matching_elements.append({
                    'element': element,
                    'extents': extents,
                    'area': area,
                    'has_name': has_name,
                    'has_description': has_description,
                    'has_actions': has_actions,
                    'is_focused': is_focused,
                    'role': element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                })

            # 递归检查子元素
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                self._collect_matching_elements(child, x, y, activewindow_region, matching_elements)

        except Exception:
            pass

    def _select_best_match(self, matching_elements, x, y):
        """
        从匹配的元素中选择最佳匹配

        Args:
            matching_elements: 匹配元素列表
            x, y: 目标坐标

        Returns:
            最佳匹配的元素
        """
        if not matching_elements:
            return None

        # 排序规则：优先选择最精确的控件
        # 1. 优先选择有焦点的元素
        # 2. 优先选择面积最小的元素（最精确）
        # 3. 优先选择有名称的元素（更具体）
        # 4. 优先选择有动作的元素（可交互）
        # 5. 优先选择深度更深的元素（更具体）
        def sort_key(item):
            return (
                not item['is_focused'],      # 有焦点的排在前面
                item['area'],                # 面积小的排在前面（最重要）
                not item['has_name'],        # 有名称的排在前面
                not item['has_actions'],     # 有动作的排在前面
                not item['has_description'], # 有描述的排在前面
                -item.get('depth', 0)        # 深度大的排在前面（更具体的子控件）
            )

        # 排序并返回最佳匹配
        matching_elements.sort(key=sort_key)
        best_match = matching_elements[0]

        return best_match['element']
    
    def find_component_by_info(self, window, control_info):
        """
        根据控件信息在窗口中查找控件
        
        Args:
            window: 窗口对象
            control_info: 控件信息字典
        """
        # 策略1: 根据ParentPath查找
        if self._find_by_parent_path(window, control_info):
            return
        
        # 策略2: 去掉第一个节点再试
        if self._find_by_parent_path_skip_first(window, control_info):
            return
        
        # 策略3: 从parent查找
        try:
            element = window
            for i in control_info["ParentPath"][1:]:
                element = element.getChildAtIndex(i)
            if self._find_from_parent(element.parent, control_info):
                return
        except Exception:
            pass
        
        # 策略4: 从application查找
        if self._find_from_application(window, control_info):
            return
        
        # 策略5: 根据名称描述查找
        if control_info["Name"] != "N/A" or control_info["Description"] != "N/A":
            self.xxx = None
            self.flag = 0
            self._recursive_find_by_info(window, control_info)
            if self.flag != 0:
                self.find_data = self.xxx
                extents = get_element_extents(self.find_data)
                self.find_extents = extents
                self.find_find = 1
                return
        
        # 策略6: 适配窗口关闭后控件不消失的情况
        if control_info["Name"] != "N/A" or control_info["Description"] != "N/A":
            if window.getRoleName() != "application":
                window = window.parent
            self.xxx = None
            self.flag = 0
            self._recursive_find_by_name_only(window, control_info)
            if self.flag != 0:
                self.find_data = self.xxx
                extents = get_element_extents(self.find_data)
                self.find_extents = extents
                self.find_find = 1
    
    def _find_by_parent_path(self, window, control_info):
        """根据ParentPath查找控件"""
        try:
            element = window
            for i in control_info["ParentPath"]:
                element = element.getChildAtIndex(i)
            
            path, _ = ElementExtractor.get_element_path(element)
            extents = get_element_extents(element)
            
            if extents and is_element_valid(extents):
                if (path == control_info["ParentPath"] or 
                    (control_info["Name"] != "N/A" and element.name == control_info["Name"]) or
                    (control_info["Description"] != "N/A" and element.description == control_info["Description"]) or
                    (extents.x == control_info['Coords']['x'] and extents.y == control_info['Coords']['y'])):
                    self.find_data, self.find_extents = element, extents
                    self.find_find = 1
                    return True
        except Exception:
            pass
        return False
    
    def _find_by_parent_path_skip_first(self, window, control_info):
        """去掉第一个节点后根据ParentPath查找"""
        try:
            element = window
            for i in control_info["ParentPath"][1:]:
                element = element.getChildAtIndex(i)
            
            path, _ = ElementExtractor.get_element_path(element)
            extents = get_element_extents(element)
            
            if extents and is_element_valid(extents):
                if (path == control_info["ParentPath"] or 
                    (control_info["Name"] != "N/A" and element.name == control_info["Name"]) or
                    (control_info["Description"] != "N/A" and element.description == control_info["Description"]) or
                    (extents.x == control_info['Coords']['x'] and extents.y == control_info['Coords']['y'])):
                    self.find_data, self.find_extents = element, extents
                    self.find_find = 1
                    return True
        except Exception:
            pass
        return False
    
    def _find_from_parent(self, parent_element, control_info):
        """从父元素查找匹配的控件"""
        if not parent_element:
            return None
            
        for i in range(parent_element.childCount):
            child = parent_element.getChildAtIndex(i)
            try:
                if control_info["Name"] != "N/A" and child.name == control_info["Name"]:
                    return child
                if control_info["Description"] != "N/A" and child.description == control_info["Description"]:
                    return child
                extents = get_element_extents(child)
                if (extents and extents.x == control_info['Coords']['x'] and 
                    extents.y == control_info['Coords']['y']):
                    return child
            except Exception:
                pass
        return None
    
    def _find_from_application(self, window, control_info):
        """从application层级查找"""
        try:
            element = window.parent
            for i in control_info["ParentPath"]:
                element = element.getChildAtIndex(i)
            
            path, _ = ElementExtractor.get_element_path(element)
            extents = get_element_extents(element)
            
            if extents and is_element_valid(extents):
                if (path == control_info["ParentPath"] or 
                    (control_info["Name"] != "N/A" and element.name == control_info["Name"]) or
                    (control_info["Description"] != "N/A" and element.description == control_info["Description"]) or
                    (extents.x == control_info['Coords']['x'] and extents.y == control_info['Coords']['y'])):
                    self.find_data, self.find_extents = element, extents
                    self.find_find = 1
                    return True
        except Exception:
            pass
        return False
    
    def _recursive_find_by_info(self, element, control_info):
        """递归查找匹配的控件（根据完整信息）"""
        try:
            if self.flag == 0:
                extents = get_element_extents(element)
                if extents and is_element_valid(extents):
                    if ((control_info["Name"] == "N/A" or control_info["Name"] == element.name) and 
                        (control_info["Description"] == "N/A" or control_info["Description"] == element.description) and 
                        control_info["Rolename"] == element.getRoleName()):
                        _, pc = ElementExtractor.get_element_path(element)
                        if pc == control_info["ParentCount"]:
                            self.xxx = element
                            self.flag = 1
        except Exception:
            pass
            
        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            self._recursive_find_by_info(child, control_info)
    
    def _recursive_find_by_name_only(self, element, control_info):
        """递归查找匹配的控件（仅根据名称）"""
        try:
            if self.flag == 0:
                extents = get_element_extents(element)
                if (extents and is_element_valid(extents) and 
                    control_info["Name"] == element.name and 
                    (control_info["Description"] == "N/A" or control_info["Description"] == element.description) and 
                    control_info["Rolename"] == element.getRoleName()):
                    self.xxx = element
                    self.flag = 1
        except Exception:
            pass
            
        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            self._recursive_find_by_name_only(child, control_info)
    
    def find_tree_elements(self, element, element_list):
        """
        递归遍历元素树，收集所有元素信息
        
        Args:
            element: 起始元素
            element_list: 用于存储元素信息的列表
        """
        # 特殊处理音乐应用
        if element.name == "音乐":
            data = ElementExtractor.extract_element_info(element)
            element_list.append(data)

        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            self.find_tree_elements(child, element_list)

        return element_list 