#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
控件信息提取模块
负责从AT-SPI元素中提取各种信息
"""

import pyatspi
from ..utils.helpers import get_process_name, state_list_to_names, get_element_actions


class ElementExtractor:
    """控件信息提取器"""
    
    @staticmethod
    def get_element_path(element):
        """
        获取元素的父路径
        
        Args:
            element: pyatspi元素对象
            
        Returns:
            tuple: (path列表, 路径长度)
        """
        path = []
        index = element.getIndexInParent()
        
        if index != -1:
            path.insert(0, index)
        else:
            index = None
            parent = element.parent
            if parent:
                for i in range(parent.childCount):
                    if element == parent.getChildAtIndex(i):
                        index = i
                        
                if index is not None:
                    path.insert(0, index)
            else:
                path.insert(0, 0)
                return path, len(path)

        while element:
            if element.getRoleName() == "application":
                break
            parent = element.parent
            if parent:
                index = parent.getIndexInParent()
                if index != -1:
                    path.insert(0, index)
            element = parent

        return path, len(path)
    
    @staticmethod
    def extract_element_info(element):
        """
        提取元素的详细信息
        
        Args:
            element: pyatspi元素对象
            
        Returns:
            dict: 元素信息字典
        """
        # 基础信息
        data = {
            "Name": element.name if element.name else "N/A",
            "ID": element.id if element.id else "N/A",
            "ProcessID": element.get_process_id() if hasattr(element, 'get_process_id') else "N/A",
            "Rolename": element.getRoleName() if element.getRoleName() else "N/A",
            "Description": element.description if element.description else "N/A",
            "Index_in_parent": element.getIndexInParent() if element.getIndexInParent() else "N/A",
            "ChildrenCount": element.childCount if hasattr(element, 'childCount') else "N/A",
        }

        # 进程名称
        data["ProcessName"] = get_process_name(data.get("ProcessID", -1))

        # 坐标信息
        try:
            extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
            data["Coords"] = {
                "x": extents.x,
                "y": extents.y,
                "width": extents.width,
                "height": extents.height
            }
        except Exception:
            data["Coords"] = "Not available"

        # 文本信息
        try:
            if element.queryText() is not None:
                text_interface = element.queryText()
                data["Text"] = text_interface.getText(0, -1)
            else:
                data["Text"] = "Not available"
        except Exception as e:
            data["Text"] = f"Not available: {e}"

        # 动作信息
        actions = get_element_actions(element)
        data["Actions"] = actions if actions is not None else "Not available"

        # 状态信息
        try:
            states = element.getState()
            data["States"] = state_list_to_names(states)
        except Exception as e:
            data["States"] = f"Not available: {e}"

        # 父路径信息
        data["ParentPath"], data["ParentCount"] = ElementExtractor.get_element_path(element)

        # 生成唯一标识
        data = ElementExtractor.generate_key(data)

        return data
    
    @staticmethod
    def generate_key(data):
        """
        为控件生成唯一标识符
        
        Args:
            data: 控件信息字典
            
        Returns:
            dict: 添加了Key字段的控件信息
        """
        pp_str = "P"
        for i in data["ParentPath"]:
            pp_str = pp_str + str(i)

        try:
            keystring = f"N{data['Name']}-D{data['Description']}-{pp_str}"
        except Exception:
            keystring = ""

        keystring = keystring.replace("/", "").replace(" ", "").replace("\n", "").replace("_", "-")
        data["Key"] = keystring
        
        return data
    
    @staticmethod
    def get_document_info(element):
        """获取文档接口信息"""
        try:
            doc_interface = element.queryDocument()
            return {
                "Locale": doc_interface.getLocale(),
                "Attributes": doc_interface.getAttributes()
            }
        except (NotImplementedError, AttributeError):
            return "Not available"
            
    @staticmethod
    def get_hyperlink_info(element):
        """获取超链接接口信息"""
        try:
            hyperlink_interface = element.queryHyperlink()
            if hyperlink_interface:
                return {
                    "URI": hyperlink_interface.getURI(0),
                    "StartIndex": hyperlink_interface.startIndex,
                    "EndIndex": hyperlink_interface.endIndex
                }
            return "Not available"
        except (NotImplementedError, AttributeError):
            return "Not available" 