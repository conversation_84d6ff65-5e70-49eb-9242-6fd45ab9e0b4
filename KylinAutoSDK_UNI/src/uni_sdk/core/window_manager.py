#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
窗口管理模块
负责窗口的获取、查找和管理
"""

import pyatspi
import os
import subprocess
import warnings

from ..utils.constants import TASKBAR_NAME, TASKBAR_Y_THRESHOLD, TASKBAR_REGION, PROCESS_NAME_MAPPING
from ..utils.helpers import get_current_user_info, get_process_name, is_x11_environment, is_wayland_environment

# 尝试导入 X11 相关库，如果失败则在 Wayland 环境下使用替代方案
X11_AVAILABLE = False
try:
    if is_x11_environment():
        import gi
        gi.require_version('Wnck', '3.0')
        from gi.repository import Wnck
        from Xlib import display, X
        X11_AVAILABLE = True
    else:
        # 在 Wayland 环境下，这些库不可用
        warnings.warn("Running in Wayland environment. X11-specific window management features will be limited.",
                     UserWarning)
except Exception as e:
    warnings.warn(f"Failed to import X11 libraries: {e}. Window management features will be limited.",
                 UserWarning)


class WindowManager:
    """窗口管理器"""

    @staticmethod
    def get_window_stack():
        """获取窗口堆栈"""
        if not X11_AVAILABLE:
            # Wayland 环境下返回空列表，依赖 AT-SPI 进行窗口管理
            return []

        try:
            d = display.Display()
            root = d.screen().root

            client_list = root.get_full_property(
                d.intern_atom('_NET_CLIENT_LIST_STACKING'),
                X.AnyPropertyType
            )
            if client_list:
                window_ids = client_list.value
                return [d.create_resource_object('window', win_id)
                       for win_id in reversed(window_ids)]
        except Exception as e:
            print(f"Error getting window stack: {e}")

        return []
    
    @staticmethod
    def get_topmost_window_at_point(x, y):
        """获取指定坐标的最顶层窗口"""
        if not X11_AVAILABLE:
            # Wayland 环境下使用 AT-SPI 替代方案
            return WindowManager._get_window_at_point_atspi(x, y)

        try:
            screen = Wnck.Screen.get_default()
            screen.force_update()

            window_stack = WindowManager.get_window_stack()
            for xwin in window_stack:
                try:
                    wnck_window = Wnck.Window.get(xwin.id)
                    if wnck_window:
                        # 检查窗口是否最小化或隐藏
                        if (wnck_window.is_minimized() or
                            not wnck_window.is_visible_on_workspace(screen.get_active_workspace())):
                            continue

                        geometry = wnck_window.get_geometry()
                        if (geometry.xp <= x < geometry.xp + geometry.widthp and
                            geometry.yp <= y < geometry.yp + geometry.heightp):
                            return wnck_window
                except Exception as e:
                    print(f"Error processing window: {e}")
        except Exception as e:
            print(f"Error in get_topmost_window_at_point: {e}")

        return None

    @staticmethod
    def _get_window_at_point_atspi(x, y):
        """使用 AT-SPI 在指定坐标获取窗口（Wayland 兼容）"""
        try:
            # 在 Wayland 环境下，我们主要依赖 AT-SPI 来获取窗口信息
            # 这个方法返回 None，让调用者使用 AT-SPI 的其他方法
            return None
        except Exception as e:
            print(f"Error in _get_window_at_point_atspi: {e}")
            return None
    
    @staticmethod
    def get_active_window_at_point(x, y):
        """根据坐标获取活动窗口"""
        # 在 Wayland 环境下，直接使用 AT-SPI 方法
        if not X11_AVAILABLE:
            return WindowManager._get_active_window_atspi(x, y)

        window = WindowManager.get_topmost_window_at_point(x, y)
        if not window:
            return WindowManager._get_active_window_atspi(x, y)

        windowlist = []
        try:
            geometry = window.get_geometry()
            x1, y1 = geometry.xp, geometry.yp
            width1, height1 = geometry.widthp, geometry.heightp
            window_name = window.get_name()
            window_pid = window.get_pid()
        except Exception as e:
            print(f"Error getting window properties: {e}")
            return WindowManager._get_active_window_atspi(x, y)
        
        desktop = pyatspi.Registry.getDesktop(0)
        try:
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                for j in range(app.childCount):
                    window_obj = app.getChildAtIndex(j)
                    if window_obj.get_process_id() == window_pid:
                        extents = window_obj.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                        if (extents.x < x < extents.x + extents.width and 
                            extents.y < y < extents.y + extents.height):
                            windowlist.append(window_obj)
            
            # 优先查找ACTIVE窗口
            for window_obj in windowlist:
                if window_obj.getState().contains(pyatspi.STATE_ACTIVE):
                    processid = window_obj.get_process_id()
                    component = window_obj.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        window_region = [extents.x, extents.y, 
                                       extents.x + extents.width,
                                       extents.y + extents.height]
                        return (window_obj, processid, window_region, 
                               window_obj.getRoleName(), window_obj.childCount)

            # 根据窗口名称匹配
            for window_obj in windowlist: 
                if ((window_obj.name == window_name) or 
                    ("ukui-panel" in window_obj.name and window_name == "UKUI Panel") or 
                    (window_obj.name == "")):
                    extents = window_obj.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    if ((extents.x == x1 and extents.y == y1 and 
                         extents.width == width1 and extents.height == height1) or
                        (extents.x == x1 and extents.y == y1+38 and 
                         extents.width == width1 and extents.height == height1-38)):
                        window_region = [extents.x, extents.y,
                                       extents.x + extents.width,
                                       extents.y + extents.height]
                        return (window_obj, window_obj.get_process_id(), 
                               window_region, window_obj.getRoleName(), 
                               window_obj.childCount)
        except Exception as e:
            print(f"找窗口失败咯: {e}")
            
        return None, None, None, None, None
    
    @staticmethod
    def get_active_window(x, y):
        """获取当前激活窗口（兼容旧版本）"""
        # 处理底部任务栏
        if y >= TASKBAR_Y_THRESHOLD:
            try:
                desktop = pyatspi.Registry.getDesktop(0)
                for i in range(desktop.childCount):
                    app = desktop.getChildAtIndex(i)
                    if app.name == TASKBAR_NAME:
                        processid = app.get_process_id()
                        return app, processid, TASKBAR_REGION, app.getRoleName(), app.childCount
            except Exception as e:
                print(f"Error getting active window: {e}")
        else:
            try:
                desktop = pyatspi.Registry.getDesktop(0)
                for i in range(desktop.childCount):
                    app = desktop.getChildAtIndex(i)
                    for j in range(app.childCount):
                        window = app.getChildAtIndex(j)
                        if window.getState().contains(pyatspi.STATE_ACTIVE):
                            processid = window.get_process_id()
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                window_region = [extents.x, extents.y, 
                                               extents.x + extents.width,
                                               extents.y + extents.height]
                                return (window, processid, window_region, 
                                       window.getRoleName(), window.childCount)
            except Exception as e:
                print(f"Error getting active window: {e}")
                
        return None, None, None, None, None
    
    @staticmethod
    def get_window_by_name(window_name):
        """根据窗口名称获取窗口列表"""
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_list = []
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                if app.name == window_name or window_name == "N/A":
                    window_list.append(app)
                else:
                    # 在应用程序的子窗口中查找
                    for j in range(app.childCount):
                        window = app.getChildAtIndex(j)
                        if window.name == window_name or window_name == "N/A":
                            window_list.append(window)
            
            return window_list if window_list else None

        except Exception as e:
            print(f"Error getting matching window by window name: {e}")
            return None
    
    @staticmethod
    def get_window_by_partial_names(window_partial_names):
        """根据部分窗口名称获取窗口"""
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_list = []
            count = 0
            
            for name in window_partial_names:
                found = False
                for i in range(desktop.childCount):
                    app = desktop.getChildAtIndex(i)
                    if app.name == name or name in app.name:
                        window_list.append(app)
                        found = True
                    else:
                        for j in range(app.childCount):
                            window = app.getChildAtIndex(j)
                            if window.name == name or name in window.name:
                                window_list.append(window)
                                found = True
                                
                if found:
                    count += 1
                    
            return (window_list, count) if window_list else (None, 0)

        except Exception as e:
            print(f"Error getting matching window by window partial name: {e}")
            return None, 0
    
    @staticmethod
    def get_window_by_control_info(control_info):
        """根据控件信息获取窗口对象"""
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            matching_windows = []
            username, hostname = get_current_user_info()
            
            # 处理进程名称映射
            process_name = control_info.get("ProcessName", "")
            if process_name in PROCESS_NAME_MAPPING:
                control_info["ProcessName"] = PROCESS_NAME_MAPPING[process_name]
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                if (app.name == control_info["WindowName"] or 
                    app.name == 'N/A' or 
                    control_info["ProcessName"] in app.name):
                    if control_info["WindowName"] == control_info["WindowRoleName"]:
                        return app
                    else: 
                        matching_windows.append(app)
                        
                for j in range(app.childCount):
                    window = app.getChildAtIndex(j)
                    if (control_info['WindowName'] in window.name or 
                        window.name == 'N/A' or 
                        ("@" in control_info["WindowName"] and "@" in window.name) or
                        (("username" in control_info["WindowName"] or 
                          "hostname" in control_info["WindowName"]) and 
                         control_info["WindowName"].replace("username", username)
                                                   .replace("hostname", hostname) in window.name)):
                        matching_windows.append(window)
                        
            if not matching_windows:
                return None
                
            # 根据进程名和角色名称匹配
            for window in matching_windows:
                processid = window.get_process_id() if hasattr(window, 'get_process_id') else "N/A"
                processname = get_process_name(processid)
                
                try:
                    windowRoleName = window.getRoleName()
                except Exception:
                    windowRoleName = "N/A"
                    
                if (processname == control_info['ProcessName'] and 
                    windowRoleName == control_info["WindowRoleName"]):
                    return window

        except Exception as e:
            print(f"Error getting window by info: {e}")

        return None

    @staticmethod
    def _get_active_window_atspi(x, y):
        """使用 AT-SPI 获取活动窗口（Wayland 兼容）"""
        # 处理底部任务栏
        if y >= TASKBAR_Y_THRESHOLD:
            try:
                desktop = pyatspi.Registry.getDesktop(0)
                for i in range(desktop.childCount):
                    app = desktop.getChildAtIndex(i)
                    if app.name == TASKBAR_NAME:
                        processid = app.get_process_id()
                        return app, processid, TASKBAR_REGION, app.getRoleName(), app.childCount
            except Exception as e:
                print(f"Error getting taskbar window: {e}")

        # 查找活动窗口
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                for j in range(app.childCount):
                    window = app.getChildAtIndex(j)
                    try:
                        # 检查窗口是否包含指定坐标
                        component = window.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            if (extents.x <= x <= extents.x + extents.width and
                                extents.y <= y <= extents.y + extents.height):

                                # 优先返回活动窗口
                                if window.getState().contains(pyatspi.STATE_ACTIVE):
                                    processid = window.get_process_id()
                                    window_region = [extents.x, extents.y,
                                                   extents.x + extents.width,
                                                   extents.y + extents.height]
                                    return (window, processid, window_region,
                                           window.getRoleName(), window.childCount)
                    except Exception:
                        continue

            # 如果没找到活动窗口，返回第一个匹配的窗口
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                for j in range(app.childCount):
                    window = app.getChildAtIndex(j)
                    try:
                        component = window.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            if (extents.x <= x <= extents.x + extents.width and
                                extents.y <= y <= extents.y + extents.height):
                                processid = window.get_process_id()
                                window_region = [extents.x, extents.y,
                                               extents.x + extents.width,
                                               extents.y + extents.height]
                                return (window, processid, window_region,
                                       window.getRoleName(), window.childCount)
                    except Exception:
                        continue

        except Exception as e:
            print(f"Error in _get_active_window_atspi: {e}")

        return None, None, None, None, None