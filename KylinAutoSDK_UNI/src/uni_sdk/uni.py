#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
UNI主类
整合所有模块功能，提供统一的API接口
"""

import re
import subprocess
import os
import time
import pyatspi
import json
import warnings

from .core import ElementExtractor, WindowManager, ElementFinder
from .handlers import DataHandler, ScreenshotHandler, VerificationHandler
from .utils.constants import SPECIAL_WINDOWS, FILTER_RULES, DEFAULT_WAIT_COUNT, DEFAULT_WAIT_INTERVAL
from .utils.helpers import (get_current_user_info, get_element_extents,
                          calculate_center_point, is_point_in_rect,
                          is_x11_environment, is_wayland_environment, detect_display_server,
                          is_element_valid, state_list_to_names, get_element_actions,
                          is_point_in_rect_corrected, correct_coordinates_for_wayland)

# 尝试导入 X11 相关库，如果失败则在 Wayland 环境下使用替代方案
X11_AVAILABLE = False
try:
    if is_x11_environment():
        import gi
        gi.require_version('Wnck', '3.0')
        from gi.repository import Wnck
        from Xlib import display, X
        from Xlib.protocol import event
        X11_AVAILABLE = True
    else:
        # 在 Wayland 环境下，这些库不可用
        display_server = detect_display_server()
        print(f"Running in {display_server} environment. X11-specific features will be limited.")
except Exception as e:
    display_server = detect_display_server()
    print(f"Failed to import X11 libraries in {display_server} environment: {e}")
    print("Window management features will be limited to AT-SPI functionality.")


class UNI:
    """KylinAutoSDK_UNI 主类"""
    
    def __init__(self):
        """初始化UNI对象"""
        self.last_desktopEle = None
        self.last_appEle = []
        self.childEle = None
        self.finddata = None
        self.findextents = None
        self.findfind = None

        # 检测显示服务器环境
        self.display_server = detect_display_server()
        self.x11_available = X11_AVAILABLE

        # 初始化各组件
        self.element_finder = ElementFinder()
        self.data_handler = DataHandler()
        self.window_manager = WindowManager()
        self.element_extractor = ElementExtractor()
        self.screenshot_handler = ScreenshotHandler()
        self.verification_handler = VerificationHandler()

        # 兼容旧版本的属性
        self.data_list = self.data_handler.data_list

        # 输出环境信息
        if not self.x11_available and self.display_server == 'wayland':
            print("UNI SDK initialized in Wayland mode. Some X11-specific features may be limited.")
        elif self.x11_available:
            print("UNI SDK initialized in X11 mode. Full functionality available.")
    
    # ========== 公开API方法 ==========
    
    def kdk_getElement_Uni(self, x, y, quick=False, menuele=None):
        """
        描述: 根据(x, y)获取控件信息
        作者: 吴德基
        日期: 2024.10.31
        参数: 
        - x: 横坐标
        - y: 纵坐标
        - quick: 是否快速模式
        - menuele: 菜单元素列表
        返回值: data（dict），文本信息（str）
        """
        # 处理菜单元素
        if menuele:
            for hwcele in menuele:
                extents = get_element_extents(hwcele)
                if extents and is_point_in_rect(x, y, extents.x, extents.y, 
                                               extents.width, extents.height):
                    self.childEle = hwcele
                    data = self.element_extractor.extract_element_info(self.childEle)
                    # 补充信息
                    data["RecordPosition"] = (x, y)
                    try:
                        while hwcele.getRoleName() != "application":
                            hwcele = hwcele.parent
                    except Exception:
                        pass
                    data["WindowName"] = hwcele.name
                    data["WindowRoleName"] = hwcele.getRoleName()
                    data["WindowChildCount"] = hwcele.childCount
                    data["MenuElement"] = "1"
                    return data, "找到"
        
        # 获取当前active窗口控件
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = \
            self.window_manager.get_active_window_at_point(x, y)

        if active_window:
            # 从当前窗口中获取控件信息
            self.element_finder.child_ele = None
            self.element_finder.find_accessible_at_point(active_window, x, y, activewindow_region)

            if self.element_finder.child_ele:
                if quick:
                    extents = get_element_extents(self.element_finder.child_ele)
                    return extents, "找到"
                else:
                    data = self.element_extractor.extract_element_info(self.element_finder.child_ele)
                    # 补充窗口信息
                    data["RecordPosition"] = (x, y)
                    data["WindowRoleName"] = windowRoleName
                    data["WindowChildCount"] = windowChildCount

                    # 处理特殊窗口名称
                    data["WindowName"] = self._process_window_name(active_window, data)

                    if self._checkdata(data, self.element_finder.child_ele):
                        return data, "找到"
                    else:
                        return None, "该控件暂不支持录制"
            else:
                # 在活动窗口中未找到，尝试全局搜索
                return self._find_element_globally(x, y, quick)
        else:
            # 无法获取活动窗口，进行全局搜索
            return self._find_element_globally(x, y, quick)
    
    def kdk_getElement_Uni_onactivewindow(self, x, y):
        """
        描述: 根据(x, y)获取控件信息（仅在活动窗口）
        作者: 吴德基
        日期: 2024.07.09
        参数: 
        - x: 横坐标
        - y: 纵坐标
        返回值: data（dict），文本信息（str）
        """
        # 获取当前active窗口控件
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = \
            self.window_manager.get_active_window(x, y)
            
        if active_window:
            # 从当前窗口中获取控件信息
            self.element_finder.child_ele = None
            self.element_finder.find_accessible_at_point(active_window, x, y, activewindow_region)
            
            if self.element_finder.child_ele:
                data = self.element_extractor.extract_element_info(self.element_finder.child_ele)
                # 补充窗口信息
                data["RecordPosition"] = (x, y)
                data["WindowRoleName"] = windowRoleName
                data["WindowChildCount"] = windowChildCount
                
                # 处理特殊窗口名称
                data["WindowName"] = self._process_window_name(active_window, data)
                
                if self._checkdata(data, self.element_finder.child_ele):
                    return data, "找到"
                else:
                    return None, "该控件暂不支持录制"
            else:
                return None, f"未找到位置为（{x}, {y}）的控件"
        else:
            return None, "获取当前活动窗口失败"
    
    def kdk_getElePos_Uni(self, control_info):
        """
        根据控件信息获取控件位置
        
        Args:
            control_info: 控件信息字典
            
        Returns:
            tuple: (center_x, center_y, width, height) 或 None
        """
        try:
            # 处理菜单元素
            if control_info.get("MenuElement") == "1":
                menu_data = DataHandler.load_menu_record()
                result = DataHandler.check_menu_element(control_info, menu_data)
                if result:
                    return result
                    
            # 获取窗口
            window = self.window_manager.get_window_by_control_info(control_info)
            if window is None:
                return None, None, None, None
            
            # 查找控件
            self.element_finder.find_data = None
            self.element_finder.find_extents = None
            self.element_finder.find_find = None
            self.element_finder.find_component_by_info(window, control_info)
            
            component = self.element_finder.find_data
            extents = self.element_finder.find_extents
            
            if component and extents:
                print(f"component: {component.name}, extents: {extents}")
                
                # 计算位置
                if ('RecordPosition' in control_info and 
                    is_point_in_rect(control_info["RecordPosition"][0], 
                                   control_info["RecordPosition"][1],
                                   extents.x, extents.y, extents.width, extents.height)):
                    center_x = control_info["RecordPosition"][0]
                    center_y = control_info["RecordPosition"][1]
                else:
                    center_x, center_y = calculate_center_point(extents.x, extents.y, 
                                                              extents.width, extents.height)
                
                return center_x, center_y, extents.width, extents.height
            else:
                print("Component or extents not found.")
                return None, None, None, None
                
        except Exception as e:
            print(f"Error in kdk_getElePos_Uni: {e}")
            return None, None, None, None
    
    def kdk_KBToJson_Uni(self, data, filename="data.json"):
        """
        根据数据类型将键值或控件信息添加到列表，并在程序终止时保存到文件
        
        Args:
            data: 键值或控件信息
            filename: 保存的文件名
        """
        self.data_handler.kb_to_json(data, filename)
    
    def kdk_getWinAllEle_Uni(self, window_name):
        """
        获取窗口中的所有控件
        
        Args:
            window_name: 窗口名称
            
        Returns:
            list: 控件信息列表
        """
        try:
            app_list = self.window_manager.get_window_by_name(window_name)
            if not app_list:
                return None
            
            print(app_list[0].name)
            all_controls = []
            
            for app in app_list:
                element_list = []
                self.element_finder.find_tree_elements(app, element_list)
                all_controls.extend(element_list)
            
            return all_controls

        except Exception as e:
            print(f"Error in kdk_getWinAllEle_Uni: {e}")
            return None
    
    def kdk_judgeEleExist_Uni(self, window_name, control_name):
        """
        判断控件是否存在
        
        Args:
            window_name: 窗口名称
            control_name: 控件名称
            
        Returns:
            tuple: (是否存在, 提示信息)
        """
        try:
            windows = self.window_manager.get_window_by_name(window_name)
            if not windows:
                return None, f"未找到名为 {window_name} 的窗口"

            for window in windows:
                element_list = []
                self.element_finder.find_tree_elements(window, element_list)

                # 查找控件
                for control in element_list:
                    if control["Name"] == control_name:
                        if self.verification_handler.is_element_exist(control):
                            return True, "找到"
                            
            return False, f"在 {window_name} 窗口中未找到名为 {control_name} 的控件"
            
        except Exception as e:
            print(f"Error in kdk_judgeEleExist_Uni: {e}")
            return None, "异常"
    
    def kdk_getObjPos_Uni(self, window_name, control_name):
        """
        获取对象位置
        
        Args:
            window_name: 窗口名称
            control_name: 控件名称
            
        Returns:
            tuple: (位置列表, 提示信息)
        """
        try:
            windows = self.window_manager.get_window_by_name(window_name)
            if not windows:
                return None, f"未找到名为 {window_name} 的窗口"

            matching_ctl = []
            for window in windows:
                element_list = []
                self.element_finder.find_tree_elements(window, element_list)

                # 查找控件
                for control in element_list:
                    if control["Name"] == control_name:
                        if self.verification_handler.is_element_visible(control):
                            # 解析控件位置
                            x = control["Coords"]["x"]
                            y = control["Coords"]["y"]
                            width = control["Coords"]["width"]
                            height = control["Coords"]["height"]
                            # 计算中心位置
                            center_x, center_y = calculate_center_point(x, y, width, height)
                            matching_ctl.append([x, y, width, height, center_x, center_y])
                            
            info = "找到" if matching_ctl else f"在 {window_name} 窗口中未找到名为 {control_name} 的控件"
            return matching_ctl, info
            
        except Exception as e:
            print(f"Error in kdk_getObjPos_Uni: {e}")
            return None, "异常"
    
    def kdk_takeEleShot_Uni(self, window_name, control_name):
        """
        为控件截图
        
        Args:
            window_name: 窗口名称
            control_name: 控件名称
            
        Returns:
            tuple: (截图列表, 提示信息)
        """
        try:
            positions, info = self.kdk_getObjPos_Uni(window_name, control_name)
            
            if positions:
                # 截图并标记
                img_path = self.screenshot_handler.take_element_screenshot(control_name, positions)
                for result in positions:
                    print(f"控件{control_name}的位置为:", result)
                info = f"控件 {control_name} 的位置为: {positions}"
                
            return positions, info
            
        except Exception as e:
            print(f"Error in kdk_takeEleShot_Uni: {e}")
            return None, "异常"
    
    def kdk_getWinId_Uni(self, control_name):
        """
        根据控件名称获取窗口ID
        
        Args:
            control_name: 控件名称
            
        Returns:
            dict: 窗口信息字典
        """
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_dict = {}
            
            self.flag = None

            # 在desktop中查找控件
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                self.flag = None
                self._match_window_by_controlname(app, control_name)
                if self.flag:
                    parent_path, _ = self.element_extractor.get_element_path(self.flag)
                    childnum = parent_path[0]
                    window_info = self.element_extractor.extract_element_info(
                        app.getChildAtIndex(childnum))
                    if window_info["Name"] not in window_dict:
                        window_dict[window_info["Name"]] = [window_info]

            return window_dict if window_dict else None

        except Exception as e:
            print(f"Error in kdk_getWinId_Uni: {e}")
            return None
    
    def kdk_getWinTitle_Uni(self):
        """
        获取当前窗口标题
        
        Returns:
            dict: 包含窗口标题的字典
        """
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_title = None
            
            self.flag = None

            # 在desktop中查找控件
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                self.flag = None
                self._match_window_by_state(app)
                if self.flag:
                    window_title = app.name + " : " + self.flag.name

            return {"WindowTitle": window_title} if window_title else None

        except Exception as e:
            print(f"Error in kdk_getWinTitle_Uni: {e}")
            return None
    
    def kdk_waitForEleVisib_Uni(self, window_name, control_name, 
                               count=DEFAULT_WAIT_COUNT, interval=DEFAULT_WAIT_INTERVAL):
        """
        等待控件可见
        
        Args:
            window_name: 窗口名称
            control_name: 控件名称
            count: 重试次数
            interval: 重试间隔
            
        Returns:
            tuple: (是否找到, 提示信息)
        """
        try:
            for i in range(count):
                result, info = self.kdk_judgeEleExist_Uni(window_name, control_name)
                if result:
                    return True, "找到"
                time.sleep(interval)
                
            return False, f"在 {window_name} 窗口中查找{count}次均未找到名为 {control_name} 的控件"
            
        except Exception as e:
            print(f"Error in kdk_waitForEleVisib_Uni: {e}")
            return None, "异常"
    
    def kdk_judgeEleClickable_Uni(self, window_name, control_name):
        """
        判断控件是否可点击
        
        Args:
            window_name: 窗口名称
            control_name: 控件名称
            
        Returns:
            tuple: (是否可点击, 提示信息)
        """
        try:
            windows = self.window_manager.get_window_by_name(window_name)
            if not windows:
                return None, f"未找到名为{window_name}的窗口"

            for window in windows:
                element_list = []
                self.element_finder.find_tree_elements(window, element_list)

                # 查找控件
                for control in element_list:
                    if control["Name"] == control_name:
                        if self.verification_handler.is_element_exist(control):
                            if self.verification_handler.is_element_clickable(control):
                                return True, f"[{window_name}]窗口中的[{control_name}]控件可点"
                                
            return False, f"[{window_name}]窗口中的[{control_name}]控件不可点"
            
        except Exception as e:
            print(f"Error in kdk_judgeEleClickable_Uni: {e}")
            return None, "异常"
    
    def kdk_checkEle(self, control_info, paramdict):
        """
        检查控件
        
        Args:
            control_info: 控件信息
            paramdict: 检查参数字典
            
        Returns:
            检查结果
        """
        try:
            window = self.window_manager.get_window_by_control_info(control_info)
            if window is None:
                return None

            # 查找控件
            self.element_finder.find_data = None
            self.element_finder.find_extents = None
            self.element_finder.find_find = None
            self.element_finder.find_component_by_info(window, control_info)

            component = self.element_finder.find_data
            
            # 验证控件
            result, error = self.verification_handler.check_element(
                component, control_info, paramdict)
            
            if error:
                return False, error
            else:
                return result

        except Exception as e:
            print(f"Error in kdk_checkEle: {e}")
            return None, "异常"
    
    def kdk_judgeEle(self, control_info, judgeinfo):
        """
        控件检查点对比
        
        Args:
            control_info: 控件信息
            judgeinfo: 判断信息
            
        Returns:
            bool: 是否匹配
        """
        try:
            window = self.window_manager.get_window_by_control_info(control_info)
            if window is None:
                return None
            
            self.judgeresult = None
            self._judgeEle_by_info(window, control_info, judgeinfo)
            return self.judgeresult
            
        except Exception as e:
            print(f"Error in kdk_judgeEle: {e}")
            return None
    
    # ========== 特殊方法 ==========
    
    def getkscd_sideblankgeometry(self):
        """获取安全中心侧边栏几何信息"""
        window_list = self.window_manager.get_window_by_name("ksc-defender")
        if not window_list:
            return None
            
        for window in window_list:
            from .utils.helpers import get_process_name
            if get_process_name(window.get_process_id()) == "ksc-defender":
                element = window
                for i in [0, 0, 0, 2, 0, 0]:
                    element = element.getChildAtIndex(i)
                extents = get_element_extents(element)
                print(extents)
                return extents
        return None
    
    def get_iconrange(self, appname, countlist):
        """获取应用中某一块范围"""
        window_list = self.window_manager.get_window_by_name(appname)
        if not window_list:
            return None
            
        for window in window_list:
            from .utils.helpers import get_process_name
            if get_process_name(window.get_process_id()) == appname:
                element = window
                for i in countlist:
                    element = element.getChildAtIndex(i)
                extents = get_element_extents(element)
                print(extents)
                return extents
        return None
    
    def getbtpos_and_pindex(self, window_name, control_name):
        """获取按钮位置和父索引"""
        try:
            positions, info = self.kdk_getObjPos_Uni(window_name, control_name)
            
            if positions:
                # 获取父路径索引
                windows = self.window_manager.get_window_by_name(window_name)
                for window in windows:
                    element_list = []
                    self.element_finder.find_tree_elements(window, element_list)
                    
                    for control in element_list:
                        if control["Name"] == control_name:
                            pindex = control["ParentPath"][:-1]
                            return positions, pindex, "找到"
                            
            return None, None, info
            
        except Exception as e:
            print(f"Error in getbtpos_and_pindex: {e}")
            return None, None, "异常"
    
    def getpeony_moreappgeometry(self, appname, pindex):
        """获取文件管理器更多应用几何信息"""
        window_list = self.window_manager.get_window_by_name(appname)
        if not window_list:
            return None
            
        for window in window_list:
            from .utils.helpers import get_process_name
            if get_process_name(window.get_process_id()) == "peony":
                element = window
                for i in pindex:
                    element = element.getChildAtIndex(i)
                extents = get_element_extents(element)
                print(extents)
                return extents
        return None
    
    # ========== 内部辅助方法 ==========
    
    def _checkdata(self, data, element):
        """检查控件数据是否有效"""
        # 任务栏托盘
        if (data["WindowName"] == "ukui-panel" and 
            data["Coords"]["width"] == 32 and 
            data["Coords"]["height"] == 32 and 
            data["Name"] == "N/A" and 
            data["Description"] == "N/A"):
            try:
                parent_extents = get_element_extents(element.parent)
                if parent_extents and parent_extents.width != 32:
                    return False
            except:
                pass
        
        # 应用过滤规则
        for rule in FILTER_RULES:
            if (data["WindowName"] == rule["window_name"] and
                data["Coords"]["width"] == rule["width"] and
                data["Coords"]["height"] == rule["height"] and
                data["Name"] == rule["name"] and
                data["Description"] == rule["description"]):
                return False
        
        # 开始菜单列表栏
        if ((data["ProcessName"] == "ukui-menu" and 
             data["Coords"]["width"] == 314 and 
             data["Coords"]["height"] == 530 and 
             data["Name"] == "N/A" and 
             data["Description"] == "N/A") or
            (data["ProcessName"] == "ukui-menu" and 
             data["Coords"]["width"] == 298 and 
             data["Coords"]["height"] == 44 and 
             data["Name"] == "N/A" and 
             data["Description"] == "N/A" and 
             data["ParentCount"] == 7 and 
             data["Rolename"] == "list item")):
            return False

        return True
    
    def _process_window_name(self, window, data):
        """处理特殊窗口名称"""
        window_name = window.name
        
        if data["ProcessName"] in SPECIAL_WINDOWS:
            username, hostname = get_current_user_info()
            
            if data["ProcessName"] == "mate-terminal":
                window_name = window_name.replace(hostname, "hostname").replace(username, "username")
            elif data["ProcessName"] == "peony":
                window_name = window_name.replace(username, "username")
            elif "文本编辑器" in window_name:
                window_name = "文本编辑器"
            elif "WPS" in window_name:
                window_name = "WPS"
        
        return window_name
    
    def _match_window_by_controlname(self, element, control_name):
        """根据控件名称匹配窗口"""
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                if child and child.name == control_name:
                    self.flag = element

                if child and child.childCount > 0:
                    self._match_window_by_controlname(child, control_name)

        except Exception as e:
            print(f"Error matching window by controlname: {e}")
    
    def _match_window_by_state(self, element):
        """根据状态匹配窗口"""
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                if child and child.getState() and child.getState().contains(pyatspi.STATE_ACTIVE):
                    self.flag = child
                    
                if child and child.childCount > 0:
                    self._match_window_by_state(child)

        except Exception as e:
            print(f"Error matching window by state: {e}")
    
    def _judgeEle_by_info(self, element, control_info, judgeinfo):
        """递归对比控件检查点"""
        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            try:
                tmpextents = get_element_extents(child)
                if tmpextents and (tmpextents.x + tmpextents.y + 
                                 tmpextents.width + tmpextents.height != 0):
                    self._judgeEle_by_info(child, control_info, judgeinfo)
            except:
                pass
            
            # 检查当前元素
            try:
                if not self.judgeresult:
                    if self.verification_handler.judge_element_match(child, control_info, judgeinfo):
                        self.judgeresult = True
            except TypeError as e:
                print(f"Error in _judgeEle_by_info: {e}")
    
    # ========== 兼容性方法 ==========
    # 以下方法保留以保持向后兼容
    
    def get_window_stack(self):
        """获取窗口堆栈"""
        return self.window_manager.get_window_stack()
    
    def print_topmost_window_info(self, x, y):
        """打印最顶层窗口信息"""
        return self.window_manager.get_topmost_window_at_point(x, y)
    
    def _get_active_window2(self, x, y):
        """获取活动窗口（兼容方法）"""
        return self.window_manager.get_active_window_at_point(x, y)
    
    def _get_active_window(self, x, y):
        """获取活动窗口（兼容方法）"""
        return self.window_manager.get_active_window(x, y)
    
    def _get_window_by_windowname(self, window_name):
        """根据窗口名称获取窗口"""
        return self.window_manager.get_window_by_name(window_name)
    
    def _get_window_by_windowpartialname(self, window_partialnames):
        """根据部分窗口名称获取窗口"""
        return self.window_manager.get_window_by_partial_names(window_partialnames)
    
    def _get_window_by_info(self, control_info):
        """根据控件信息获取窗口"""
        return self.window_manager.get_window_by_control_info(control_info)
    
    def _extract_element_info(self, element):
        """提取元素信息"""
        return self.element_extractor.extract_element_info(element)
    
    def _get_element_path(self, element):
        """获取元素路径"""
        return self.element_extractor.get_element_path(element)
    
    def _generateKey(self, data):
        """生成键值"""
        return self.element_extractor.generate_key(data)
    
    def _find_accessible_at_point(self, element, x, y, activewindow_region):
        """查找指定点的可访问元素"""
        self.element_finder.find_accessible_at_point(element, x, y, activewindow_region)
        self.childEle = self.element_finder.child_ele
    
    def _find_component_by_info(self, window, control_info):
        """根据信息查找组件"""
        self.element_finder.find_component_by_info(window, control_info)
        self.finddata = self.element_finder.find_data
        self.findextents = self.element_finder.find_extents
        self.findfind = self.element_finder.find_find
    
    def _find_treeEle(self, element):
        """查找树形元素"""
        self.element_finder.find_tree_elements(element, self.last_appEle)
        return self.last_appEle
    
    def _find_appallEle(self, window):
        """查找应用所有元素"""
        desktop = pyatspi.Registry.getDesktop(0)
        self.desktopchildcount = desktop.childCount
        self.last_appEle = []
        self._find_treeEle(window)
    
    def _append_key_to_list(self, key_values):
        """添加键值到列表"""
        self.data_handler.append_key_to_list(key_values)
    
    def _append_control_to_list(self, control_data):
        """添加控件到列表"""
        self.data_handler.append_control_to_list(control_data)
    
    def _save_data_to_file(self, filename="data.json"):
        """保存数据到文件"""
        self.data_handler.save_data_to_file(filename)
    
    def _is_real_exist(self, control_info):
        """判断是否真实存在"""
        return self.verification_handler.is_element_exist(control_info)
    
    def _is_visible_button(self, control_info):
        """判断是否可见按钮"""
        return self.verification_handler.is_element_visible(control_info)
    
    def _is_clickable(self, control_info):
        """判断是否可点击"""
        return self.verification_handler.is_element_clickable(control_info)
    
    def _jietu(self, name, info):
        """截图"""
        return self.screenshot_handler.take_screenshot(name, info)
    
    def _markregion(self, img_path, mark_region=None):
        """标记区域"""
        return self.screenshot_handler.mark_region(img_path, mark_region)
    
    # 其他辅助方法
    def _get_lastappEle_at_point(self, nowx, nowy):
        """根据坐标从记录的窗口所有控件信息中获取具体控件信息"""
        matedata = None
        for i in self.last_appEle:
            try:
                x1 = i["Coords"]["x"]
                y1 = i["Coords"]["y"]
                width1 = i["Coords"]["width"]
                height1 = i["Coords"]["height"]
                if x1 <= nowx < x1 + width1 and y1 <= nowy < y1 + height1:
                    states = i["States"]
                    if 'showing' in states and ('focusable' in states or 'focused' in states):
                        if matedata:
                            # 非第一次，则对比两者取小的
                            if width1 + height1 < matedata["Coords"]["width"] + matedata["Coords"]["height"]:
                                matedata = i
                        else:
                            # 说明是第一次，直接赋值
                            matedata = i
            except:
                pass
        return matedata

    def _find_element_globally(self, x, y, quick=False):
        """
        在整个桌面上全局搜索指定坐标的控件

        Args:
            x, y: 目标坐标
            quick: 是否快速模式

        Returns:
            tuple: (控件信息, 状态信息)
        """
        try:
            desktop = pyatspi.Registry.getDesktop(0)

            # 收集所有匹配的控件
            matching_elements = []

            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                try:
                    self._collect_global_matching_elements(app, x, y, matching_elements)
                except Exception:
                    continue

            if matching_elements:
                # 选择最佳匹配
                best_element = self._select_global_best_match(matching_elements, x, y)

                if quick:
                    extents = get_element_extents(best_element)
                    return extents, "找到"
                else:
                    data = self.element_extractor.extract_element_info(best_element)
                    data["RecordPosition"] = (x, y)

                    # 尝试获取窗口信息
                    try:
                        window = best_element
                        while window and window.getRoleName() not in ["application", "frame"]:
                            window = window.parent
                        if window:
                            data["WindowName"] = window.name or ""
                            data["WindowRoleName"] = window.getRoleName()
                            data["WindowChildCount"] = window.childCount
                    except Exception:
                        data["WindowName"] = ""
                        data["WindowRoleName"] = "unknown"
                        data["WindowChildCount"] = 0

                    if self._checkdata(data, best_element):
                        return data, "找到"
                    else:
                        return None, "该控件暂不支持录制"
            else:
                return None, f"未找到位置为（{x}, {y}）的控件"

        except Exception as e:
            print(f"全局搜索失败: {e}")
            return None, f"搜索异常: {e}"

    def _collect_global_matching_elements(self, element, x, y, matching_elements):
        """
        递归收集全局匹配的控件
        """
        try:
            extents = get_element_extents(element)
            if not extents or not is_element_valid(extents):
                return

            # 检查坐标是否在当前元素内 - 使用修正后的坐标匹配
            if not is_point_in_rect_corrected(x, y, extents.x, extents.y, extents.width, extents.height):
                return

            # 检查元素状态和属性
            states = element.getState()
            state_names = state_list_to_names(states)
            extents_action = get_element_actions(element)

            # 判断是否为有效的可交互控件 - 放宽条件
            is_interactive = (
                ('showing' in state_names or 'visible' in state_names or 'enabled' in state_names) and
                (extents_action or
                 'focusable' in state_names or 'focused' in state_names or
                 'selectable' in state_names or 'sensitive' in state_names or
                 'clickable' in state_names or
                 element.name or element.description or  # 有名称或描述的也算
                 element.getRoleName() in ['button', 'menu item', 'text', 'label', 'link', 'entry'])  # 常见的可交互角色
            )

            if is_interactive:
                # 计算元素的面积和其他属性用于排序
                area = extents.width * extents.height
                has_name = bool(element.name and element.name.strip())
                has_description = bool(element.description and element.description.strip())
                has_actions = bool(extents_action)
                is_focused = 'focused' in state_names

                matching_elements.append({
                    'element': element,
                    'extents': extents,
                    'area': area,
                    'has_name': has_name,
                    'has_description': has_description,
                    'has_actions': has_actions,
                    'is_focused': is_focused,
                    'role': element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
                })

            # 递归检查子元素
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                self._collect_global_matching_elements(child, x, y, matching_elements)

        except Exception:
            pass

    def _select_global_best_match(self, matching_elements, x, y):
        """
        从全局匹配的元素中选择最佳匹配
        """
        if not matching_elements:
            return None

        # 排序规则：优先选择最小、最具体的控件
        def sort_key(item):
            return (
                not item['is_focused'],      # 有焦点的排在前面
                item['area'],                # 面积小的排在前面
                not item['has_actions'],     # 有动作的排在前面
                not item['has_name'],        # 有名称的排在前面
                not item['has_description']  # 有描述的排在前面
            )

        # 排序并返回最佳匹配
        matching_elements.sort(key=sort_key)
        return matching_elements[0]['element']


# 测试代码
if __name__ == "__main__":
    a = UNI()
    
    a2, info = a.kdk_getElement_Uni(472, 620, False)
    print(a2)
    print(info)
    x, y, w, h = a.kdk_getElePos_Uni(a2)
    print(x, y, w, h) 