#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
常量定义模块
包含所有的常量配置和默认值
"""

# 默认等待配置
DEFAULT_WAIT_COUNT = 5
DEFAULT_WAIT_INTERVAL = 2

# 截图相关配置
SCREENSHOT_DIR = "screenshots"
SCREENSHOT_FORMAT = "jpg"

# 数据存储配置
DEFAULT_DATA_FILE = "data.json"

# 菜单记录文件
MENU_RECORD_FILE = "./.recordmenu.txt"

# 特殊窗口处理
SPECIAL_WINDOWS = {
    "mate-terminal": ["hostname", "username"],
    "peony": ["username"],
    "文本编辑器": [],
    "WPS": []
}

# 任务栏配置
TASKBAR_NAME = "ukui-panel"
TASKBAR_Y_THRESHOLD = 1034
TASKBAR_REGION = [0, 1034, 1920, 1080]

# 控件过滤规则
FILTER_RULES = [
    # 任务栏托盘
    {
        "window_name": "ukui-panel",
        "width": 32,
        "height": 32,
        "name": "N/A",
        "description": "N/A"
    },
    # 安全中心侧边栏
    {
        "window_name": "安全中心",
        "width": 184,
        "height": 500,
        "name": "N/A",
        "description": "N/A"
    },
    # 系统监视器侧边栏
    {
        "window_name": "系统监视器",
        "width": 194,
        "height": 187,
        "name": "N/A",
        "description": "N/A"
    }
]

# 进程名称特殊处理
PROCESS_NAME_MAPPING = {
    "peony": "文件管理器"
}

# Shift键特殊字符映射
SHIFT_CHAR_MAP = {
    '1': '!', '2': '@', '3': '#', '4': '$', '5': '%', 
    '6': '^', '7': '&', '8': '*', '9': '(', '0': ')',
    '-': '_', '=': '+', '[': '{', ']': '}', '\\': '|', 
    ';': ':', "'": '"', ',': '<', '.': '>', '/': '?'
} 