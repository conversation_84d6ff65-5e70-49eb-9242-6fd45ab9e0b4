#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
辅助工具函数模块
提供通用的工具函数
"""

import os
import subprocess
import pyatspi


def detect_display_server():
    """
    检测当前显示服务器类型

    Returns:
        str: 'wayland', 'x11', 或 'unknown'
    """
    # 检查环境变量
    wayland_display = os.environ.get('WAYLAND_DISPLAY')
    xdg_session_type = os.environ.get('XDG_SESSION_TYPE')
    display = os.environ.get('DISPLAY')

    # 优先检查 XDG_SESSION_TYPE
    if xdg_session_type:
        if xdg_session_type.lower() == 'wayland':
            return 'wayland'
        elif xdg_session_type.lower() == 'x11':
            return 'x11'

    # 检查 WAYLAND_DISPLAY
    if wayland_display:
        return 'wayland'

    # 检查 DISPLAY (X11)
    if display:
        return 'x11'

    # 尝试检查运行的进程
    try:
        # 检查是否有 Wayland compositor 运行
        result = subprocess.run(['pgrep', '-f', 'wayland|weston|sway|gnome-shell'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            return 'wayland'

        # 检查是否有 X server 运行
        result = subprocess.run(['pgrep', '-f', 'Xorg|Xwayland'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            return 'x11'
    except Exception:
        pass

    return 'unknown'


def is_wayland_environment():
    """
    检查是否在 Wayland 环境中运行

    Returns:
        bool: True 如果是 Wayland 环境
    """
    return detect_display_server() == 'wayland'


def is_x11_environment():
    """
    检查是否在 X11 环境中运行

    Returns:
        bool: True 如果是 X11 环境
    """
    return detect_display_server() == 'x11'


def detect_coordinate_offset():
    """
    检测 Wayland 环境下的坐标偏移

    Returns:
        tuple: (x_offset, y_offset) 坐标偏移量
    """
    if not is_wayland_environment():
        return 0, 0

    try:
        import pyatspi

        # 查找一个已知位置的窗口来计算偏移
        desktop = pyatspi.Registry.getDesktop(0)

        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            for j in range(app.childCount):
                try:
                    window = app.getChildAtIndex(j)
                    extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)

                    # 如果窗口坐标为 (0,0) 但窗口名称表明它不应该在左上角
                    if (extents.x == 0 and extents.y == 0 and
                        window.name and len(window.name) > 10):  # 有实际内容的窗口

                        # 尝试通过窗口装饰器估算偏移
                        # 典型的窗口标题栏高度约为 30-40 像素
                        estimated_y_offset = 30
                        estimated_x_offset = 0

                        return estimated_x_offset, estimated_y_offset

                except Exception:
                    continue

        return 0, 0

    except Exception:
        return 0, 0


def create_window_position_cache():
    """
    创建窗口位置缓存，用于记录用户确认的窗口位置

    Returns:
        dict: 窗口位置缓存
    """
    cache_file = os.path.expanduser("~/.uni_sdk_window_cache.json")

    try:
        if os.path.exists(cache_file):
            import json
            with open(cache_file, 'r') as f:
                return json.load(f)
    except Exception:
        pass

    return {}


def save_window_position_cache(cache):
    """
    保存窗口位置缓存

    Args:
        cache: 窗口位置缓存字典
    """
    cache_file = os.path.expanduser("~/.uni_sdk_window_cache.json")

    try:
        import json
        with open(cache_file, 'w') as f:
            json.dump(cache, f, indent=2)
    except Exception:
        pass


def estimate_window_position(window_name, window_size):
    """
    估算窗口的真实位置

    Args:
        window_name: 窗口名称
        window_size: 窗口大小 (width, height)

    Returns:
        tuple: 估算的窗口位置 (x, y)
    """
    if not is_wayland_environment():
        return 0, 0

    # 检查缓存
    cache = create_window_position_cache()
    cache_key = f"{window_name}_{window_size[0]}x{window_size[1]}"

    if cache_key in cache:
        return cache[cache_key]['x'], cache[cache_key]['y']

    # 基于窗口类型的智能估算
    width, height = window_size

    # 获取屏幕尺寸（假设为常见分辨率）
    screen_width = 2560  # 可以通过其他方式获取
    screen_height = 1600

    # 根据窗口名称和大小估算位置
    if 'terminal' in window_name.lower():
        # 终端窗口通常在屏幕左侧或中央
        estimated_x = max(50, (screen_width - width) // 4)
        estimated_y = max(50, (screen_height - height) // 4)
    elif 'browser' in window_name.lower() or 'firefox' in window_name.lower() or 'chrome' in window_name.lower():
        # 浏览器通常最大化或居中
        estimated_x = max(0, (screen_width - width) // 2)
        estimated_y = max(0, (screen_height - height) // 2)
    else:
        # 其他窗口默认居中
        estimated_x = max(0, (screen_width - width) // 2)
        estimated_y = max(0, (screen_height - height) // 2)

    return estimated_x, estimated_y


def get_windows_by_wlcctrl():
    """
    使用 wlcctrl 获取窗口的真实位置

    Returns:
        dict: 窗口标题到位置信息的映射
    """
    if not is_wayland_environment():
        return {}

    try:
        import subprocess
        import re

        # 获取窗口列表
        result = subprocess.run(['wlcctrl', '--list'],
                              capture_output=True, text=True)
        if result.returncode != 0:
            return {}

        windows = {}
        lines = result.stdout.strip().split('\n')

        current_window = None
        for line in lines:
            line = line.strip()
            if line.startswith('toplevel "'):
                # 提取 UUID
                uuid_match = re.search(r'toplevel "([^"]+)"', line)
                if uuid_match:
                    current_window = {'uuid': uuid_match.group(1)}
            elif line.startswith('title:') and current_window:
                current_window['title'] = line.replace('title:', '').strip()
            elif line.startswith('icon:') and current_window:
                current_window['icon'] = line.replace('icon:', '').strip()
                # 获取几何信息
                if current_window and current_window['title']:
                    geometry = get_window_geometry_wlcctrl(current_window['uuid'])
                    if geometry:
                        windows[current_window['title']] = geometry
                current_window = None

        return windows

    except Exception:
        return {}


def get_window_geometry_wlcctrl(uuid):
    """
    使用 wlcctrl 获取指定窗口的几何信息

    Args:
        uuid: 窗口 UUID

    Returns:
        dict: 包含 x, y, width, height 的字典
    """
    try:
        import subprocess
        import re

        result = subprocess.run(['wlcctrl', '--getwindowgeometry', uuid],
                              capture_output=True, text=True)
        if result.returncode == 0:
            # 解析几何信息: geometry: (x, y) width x height
            geometry_line = result.stdout.strip()
            match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', geometry_line)
            if match:
                return {
                    'x': int(match.group(1)),
                    'y': int(match.group(2)),
                    'width': int(match.group(3)),
                    'height': int(match.group(4))
                }
        return None
    except Exception:
        return None


def get_window_real_position(window_element):
    """
    获取窗口的真实位置（尝试多种方法）

    Args:
        window_element: AT-SPI 窗口元素

    Returns:
        tuple: (x, y, width, height) 真实位置，如果无法确定则返回 AT-SPI 报告的位置
    """
    try:
        # 首先获取 AT-SPI 报告的位置
        extents = get_element_extents(window_element)
        if not extents:
            return None

        atspi_x, atspi_y = extents.x, extents.y
        width, height = extents.width, extents.height

        # 如果不是 Wayland 环境，直接返回 AT-SPI 位置
        if not is_wayland_environment():
            return atspi_x, atspi_y, width, height

        # 在 Wayland 环境下，尝试使用 wlcctrl 获取真实位置
        if atspi_x == 0 and atspi_y == 0:
            window_name = getattr(window_element, 'name', '') or ''

            # 尝试使用 wlcctrl 获取真实位置
            wlcctrl_windows = get_windows_by_wlcctrl()

            # 查找匹配的窗口
            for title, geometry in wlcctrl_windows.items():
                if (window_name.lower() in title.lower() or
                    title.lower() in window_name.lower() or
                    any(keyword in window_name.lower() and keyword in title.lower()
                        for keyword in ['terminal', 'browser', 'editor', 'file'])):

                    print(f"找到匹配窗口: {title}")
                    print(f"  AT-SPI 位置: ({atspi_x}, {atspi_y}, {width}, {height})")
                    print(f"  wlcctrl 位置: ({geometry['x']}, {geometry['y']}, {geometry['width']}, {geometry['height']})")

                    # 返回 wlcctrl 的真实位置
                    return geometry['x'], geometry['y'], geometry['width'], geometry['height']

            # 如果没找到匹配的窗口，使用估算位置
            estimated_x, estimated_y = estimate_window_position(window_name, (width, height))
            return estimated_x, estimated_y, width, height

        # 如果 AT-SPI 位置看起来合理，直接使用
        return atspi_x, atspi_y, width, height

    except Exception:
        return None


def calculate_coordinate_offset_with_wlcctrl(window_element):
    """
    使用 wlcctrl 计算坐标偏移

    Args:
        window_element: AT-SPI 窗口元素

    Returns:
        tuple: (offset_x, offset_y) 坐标偏移量
    """
    try:
        # 获取 AT-SPI 位置
        atspi_extents = get_element_extents(window_element)
        if not atspi_extents:
            return 0, 0

        # 获取真实位置
        real_position = get_window_real_position(window_element)
        if not real_position:
            return 0, 0

        real_x, real_y, real_width, real_height = real_position

        # 计算偏移
        offset_x = real_x - atspi_extents.x
        offset_y = real_y - atspi_extents.y

        return offset_x, offset_y

    except Exception:
        return 0, 0


def correct_coordinates_for_wayland(x, y):
    """
    修正 Wayland 环境下的坐标

    Args:
        x, y: 原始坐标

    Returns:
        tuple: (corrected_x, corrected_y) 修正后的坐标
    """
    if not is_wayland_environment():
        return x, y

    # 获取坐标偏移
    x_offset, y_offset = detect_coordinate_offset()

    # 应用偏移修正
    corrected_x = x - x_offset
    corrected_y = y - y_offset

    # 确保坐标不为负数
    corrected_x = max(0, corrected_x)
    corrected_y = max(0, corrected_y)

    return corrected_x, corrected_y


def get_element_extents_corrected(element):
    """
    获取元素范围，并在 Wayland 环境下进行坐标修正

    Args:
        element: AT-SPI 元素

    Returns:
        修正后的元素范围对象
    """
    try:
        extents = get_element_extents(element)
        if not extents:
            return None

        if is_wayland_environment():
            # 在 Wayland 环境下，某些窗口的坐标可能不准确
            # 如果坐标为 (0,0) 但窗口有实际内容，可能需要修正
            if (extents.x == 0 and extents.y == 0 and
                hasattr(element, 'name') and element.name and
                len(element.name) > 5):  # 有实际内容的窗口

                # 创建一个修正后的范围对象
                class CorrectedExtents:
                    def __init__(self, original_extents):
                        self.x = original_extents.x
                        self.y = original_extents.y
                        self.width = original_extents.width
                        self.height = original_extents.height
                        self._original = original_extents

                return CorrectedExtents(extents)

        return extents

    except Exception:
        return None


# 全局缓存多应用坐标偏移
_multi_app_offset_cache = {}

def get_multi_app_coordinate_offsets():
    """
    获取多个应用的坐标偏移信息

    Returns:
        dict: 应用名称到偏移信息的映射
        {
            'app_name': {
                'offset': (offset_x, offset_y),
                'window_title': 'title',
                'real_coords': (x, y, w, h),
                'atspi_coords': (x, y, w, h)
            }
        }
    """
    global _multi_app_offset_cache

    if not is_wayland_environment():
        return {}

    # 如果已经缓存，直接返回
    if _multi_app_offset_cache:
        return _multi_app_offset_cache

    try:
        import pyatspi

        # 获取 wlcctrl 窗口信息
        wlcctrl_windows = get_windows_by_wlcctrl()

        # 获取 AT-SPI 窗口信息
        desktop = pyatspi.Registry.getDesktop(0)
        atspi_windows = {}

        # 收集 AT-SPI 窗口信息
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                app_name = app.name.lower()

                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        if window.name:
                            window_extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                            atspi_windows[window.name] = {
                                'app_name': app_name,
                                'coords': (window_extents.x, window_extents.y, window_extents.width, window_extents.height)
                            }
                    except Exception:
                        continue
            except Exception:
                continue

        # 匹配 wlcctrl 和 AT-SPI 窗口，计算偏移
        offset_info = {}

        for wlc_title, wlc_geometry in wlcctrl_windows.items():
            # 查找匹配的 AT-SPI 窗口
            best_match = None
            best_score = 0

            for atspi_title, atspi_info in atspi_windows.items():
                # 计算标题相似度
                score = calculate_title_similarity(wlc_title, atspi_title)

                # 检查尺寸是否接近
                atspi_w, atspi_h = atspi_info['coords'][2], atspi_info['coords'][3]
                size_diff = abs(wlc_geometry['width'] - atspi_w) + abs(wlc_geometry['height'] - atspi_h)

                if score > 0.5 and size_diff < 100:  # 标题相似且尺寸接近
                    if score > best_score:
                        best_score = score
                        best_match = (atspi_title, atspi_info)

            if best_match:
                atspi_title, atspi_info = best_match
                atspi_coords = atspi_info['coords']

                # 计算偏移
                offset_x = wlc_geometry['x'] - atspi_coords[0]
                offset_y = wlc_geometry['y'] - atspi_coords[1]

                # 识别应用类型
                app_type = identify_app_type(wlc_title, atspi_info['app_name'])

                offset_info[app_type] = {
                    'offset': (offset_x, offset_y),
                    'window_title': wlc_title,
                    'real_coords': (wlc_geometry['x'], wlc_geometry['y'], wlc_geometry['width'], wlc_geometry['height']),
                    'atspi_coords': atspi_coords,
                    'confidence': best_score
                }

        _multi_app_offset_cache = offset_info
        return offset_info

    except Exception as e:
        print(f"获取多应用偏移失败: {e}")
        return {}


def calculate_title_similarity(title1, title2):
    """计算两个标题的相似度"""
    if not title1 or not title2:
        return 0

    title1 = title1.lower()
    title2 = title2.lower()

    # 简单的相似度计算
    common_words = 0
    words1 = set(title1.split())
    words2 = set(title2.split())

    if words1 & words2:
        common_words = len(words1 & words2)
        total_words = len(words1 | words2)
        return common_words / total_words

    # 检查子字符串匹配
    if title1 in title2 or title2 in title1:
        return 0.8

    return 0


def identify_app_type(window_title, app_name):
    """识别应用类型"""
    title_lower = window_title.lower()
    app_lower = app_name.lower()

    # 定义应用类型识别规则
    app_patterns = {
        'terminal': ['terminal', 'kylin@', 'bash', 'shell'],
        'browser': ['firefox', 'chrome', 'chromium', 'browser'],
        'editor': ['code', 'vim', 'emacs', 'gedit', 'editor'],
        'file_manager': ['files', 'nautilus', 'dolphin', 'thunar'],
        'office': ['libreoffice', 'writer', 'calc', 'impress'],
    }

    for app_type, patterns in app_patterns.items():
        if any(pattern in title_lower or pattern in app_lower for pattern in patterns):
            return app_type

    # 如果没有匹配，使用应用名称
    return app_name if app_name else 'unknown'


def get_coordinate_offset_from_wlcctrl():
    """
    从 wlcctrl 获取坐标偏移（向后兼容）
    现在会尝试从多应用偏移中获取终端应用的偏移

    Returns:
        tuple: (offset_x, offset_y) 坐标偏移量
    """
    if not is_wayland_environment():
        return 0, 0

    # 获取多应用偏移信息
    multi_offsets = get_multi_app_coordinate_offsets()

    # 优先查找终端应用
    for app_type in ['terminal', 'mate-terminal', 'gnome-terminal']:
        if app_type in multi_offsets:
            return multi_offsets[app_type]['offset']

    # 如果没有找到终端，返回第一个可用的偏移
    if multi_offsets:
        first_app = next(iter(multi_offsets.values()))
        return first_app['offset']

    return 0, 0


def smart_coordinate_adaptation(target_x, target_y):
    """
    智能坐标适配：根据目标坐标自动选择最合适的应用偏移

    Args:
        target_x, target_y: 目标坐标

    Returns:
        tuple: (adapted_x, adapted_y, app_type, confidence)
    """
    if not is_wayland_environment():
        return target_x, target_y, 'x11', 1.0

    # 获取多应用偏移信息
    multi_offsets = get_multi_app_coordinate_offsets()

    if not multi_offsets:
        return target_x, target_y, 'no_offset', 0.0

    best_match = None
    best_confidence = 0

    # 尝试每个应用的偏移
    for app_type, offset_info in multi_offsets.items():
        offset_x, offset_y = offset_info['offset']
        real_coords = offset_info['real_coords']

        # 检查目标坐标是否在这个应用的窗口范围内
        real_x, real_y, real_w, real_h = real_coords

        # 方法1: 检查是否在真实窗口范围内（绝对坐标）
        if (real_x <= target_x <= real_x + real_w and
            real_y <= target_y <= real_y + real_h):
            confidence = 0.9  # 高置信度
            adapted_x = target_x - offset_x
            adapted_y = target_y - offset_y

            if confidence > best_confidence:
                best_confidence = confidence
                best_match = (adapted_x, adapted_y, app_type, confidence)

        # 方法2: 检查转换后的坐标是否合理（相对坐标）
        adapted_x = target_x - offset_x
        adapted_y = target_y - offset_y

        if (0 <= adapted_x <= real_w and 0 <= adapted_y <= real_h):
            confidence = 0.7  # 中等置信度

            if confidence > best_confidence:
                best_confidence = confidence
                best_match = (adapted_x, adapted_y, app_type, confidence)

    if best_match:
        return best_match

    # 如果没有找到合适的匹配，返回原始坐标
    return target_x, target_y, 'no_match', 0.0


def is_point_in_rect_corrected(x, y, rect_x, rect_y, rect_width, rect_height):
    """
    检查点是否在矩形内，支持多应用 Wayland 坐标修正

    Args:
        x, y: 目标点坐标
        rect_x, rect_y: 矩形左上角坐标
        rect_width, rect_height: 矩形宽高

    Returns:
        bool: 点是否在矩形内
    """
    # 首先检查原始坐标
    original_match = is_point_in_rect(x, y, rect_x, rect_y, rect_width, rect_height)

    if original_match:
        return True

    # 在 Wayland 环境下进行多应用坐标修正
    if is_wayland_environment():
        # 获取多应用偏移信息
        multi_offsets = get_multi_app_coordinate_offsets()

        # 尝试每个应用的偏移
        for app_type, offset_info in multi_offsets.items():
            offset_x, offset_y = offset_info['offset']

            # 如果矩形坐标为 (0,0) 或接近 (0,0)，可能需要修正
            if rect_x <= 10 and rect_y <= 50:
                # 使用偏移后的矩形坐标检查
                corrected_rect_x = rect_x + offset_x
                corrected_rect_y = rect_y + offset_y

                corrected_match = is_point_in_rect(x, y, corrected_rect_x, corrected_rect_y,
                                                  rect_width, rect_height)

                if corrected_match:
                    return True

        # 尝试传统的坐标修正
        corrected_x, corrected_y = correct_coordinates_for_wayland(x, y)
        corrected_match = is_point_in_rect(corrected_x, corrected_y, rect_x, rect_y, rect_width, rect_height)

        return corrected_match

    return False


def get_process_name(process_id):
    """
    根据进程ID获取进程名称
    
    Args:
        process_id: 进程ID
        
    Returns:
        str: 进程名称，获取失败返回"N/A"
    """
    try:
        process_id = int(process_id)
        if process_id == -1:
            raise ValueError("Invalid process ID")
            
        cmd = f'ps -p {process_id} -o comm='
        ret = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, 
                           stderr=subprocess.PIPE, text=True)
        process_name = ret.stdout.strip()
        
        return process_name if process_name else "N/A"
    except Exception as e:
        print(f"Error getting process name for ProcessID {process_id}: {e}")
        return "N/A"


def get_current_user_info():
    """
    获取当前用户信息
    
    Returns:
        tuple: (username, hostname)
    """
    username = os.popen("whoami").readline().strip()
    hostname = os.popen("hostname").readline().strip()
    return username, hostname


def state_list_to_names(states):
    """
    将状态列表转换为状态名称列表
    
    Args:
        states: pyatspi状态列表
        
    Returns:
        list: 状态名称列表
    """
    try:
        state_list = states.getStates()
        return [pyatspi.stateToString(state) for state in state_list]
    except Exception:
        return []


def get_element_actions(element):
    """
    获取元素支持的动作列表
    
    Args:
        element: pyatspi元素对象
        
    Returns:
        list: 动作名称列表
    """
    try:
        action_interface = element.queryAction()
        if action_interface:
            return [action_interface.getName(i) 
                   for i in range(action_interface.nActions)]
    except Exception:
        pass
    return None


def get_element_extents(element):
    """
    获取元素的位置和尺寸信息
    
    Args:
        element: pyatspi元素对象
        
    Returns:
        object: 包含x, y, width, height的对象
    """
    try:
        return element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
    except Exception:
        return None


def is_element_valid(extents):
    """
    检查元素是否有效（是否有有效的位置和尺寸）
    
    Args:
        extents: 元素的位置尺寸对象
        
    Returns:
        bool: 是否有效
    """
    if not extents:
        return False
    return (extents.x + extents.y + extents.width + extents.height) != 0


def calculate_center_point(x, y, width, height):
    """
    计算矩形区域的中心点
    
    Args:
        x, y: 左上角坐标
        width, height: 宽度和高度
        
    Returns:
        tuple: (center_x, center_y)
    """
    return x + width // 2, y + height // 2


def is_point_in_rect(point_x, point_y, rect_x, rect_y, width, height):
    """
    判断点是否在矩形区域内
    
    Args:
        point_x, point_y: 点坐标
        rect_x, rect_y: 矩形左上角坐标
        width, height: 矩形宽高
        
    Returns:
        bool: 是否在区域内
    """
    return (rect_x <= point_x < rect_x + width and 
            rect_y <= point_y < rect_y + height) 