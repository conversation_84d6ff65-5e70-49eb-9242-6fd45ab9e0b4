#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
验证处理模块
负责控件的各种验证操作
"""

from ..utils.helpers import state_list_to_names, get_element_actions


class VerificationHandler:
    """验证处理器"""
    
    @staticmethod
    def is_element_exist(control_info):
        """
        判断控件是否真实存在
        
        Args:
            control_info: 控件信息字典
            
        Returns:
            bool: 是否存在
        """
        if (control_info["Coords"]["x"] != 'N/A' and 
            control_info["Coords"]["x"] != 0 and 
            control_info["Coords"]["y"] != 'N/A' and 
            control_info["Coords"]["y"] != 0):
            if ("enabled" in control_info['States'] or 
                "showing" in control_info['States'] or 
                "visible" in control_info['States'] or 
                control_info['Actions']):
                return True
        return False
    
    @staticmethod
    def is_element_visible(control_info):
        """
        判断控件是否可见
        
        Args:
            control_info: 控件信息字典
            
        Returns:
            bool: 是否可见
        """
        if (control_info["Coords"]["x"] != 'N/A' and 
            control_info["Coords"]["x"] > 0 and 
            control_info["Coords"]["y"] != 'N/A' and 
            control_info["Coords"]["y"] > 0):
            if ("showing" in control_info['States'] or 
                "visible" in control_info['States']):
                return True
        return False
    
    @staticmethod
    def is_element_clickable(control_info):
        """
        判断控件是否可点击
        
        Args:
            control_info: 控件信息字典
            
        Returns:
            bool: 是否可点击
        """
        if (control_info["Coords"]["x"] != 'N/A' and 
            control_info["Coords"]["x"] > 0 and 
            control_info["Coords"]["y"] != 'N/A' and 
            control_info["Coords"]["y"] > 0):
            if ('read only' not in control_info['States'] and 
                ('showing' in control_info['States'] or 'activate' in control_info['Actions']) and 
                ('focusable' in control_info['States'] or 
                 'focused' in control_info['States'] or 
                 'selectable' in control_info['States'] or 
                 'sensitive' in control_info['States']) and 
                control_info['Actions']):
                return True
        return False
    
    @staticmethod
    def check_element(element, control_info, param_dict):
        """
        检查元素是否满足指定条件
        
        Args:
            element: pyatspi元素对象
            control_info: 控件信息
            param_dict: 检查参数字典
            
        Returns:
            tuple: (是否通过, 错误信息)
        """
        # 如果找不到控件，检查是否期望不存在
        if element is None:
            if 'exist' in param_dict and param_dict['exist'] is False:
                return True, None
            else:
                return False, '控件不存在'
        
        # 获取元素信息
        try:
            from ..utils.helpers import get_element_extents
            extents = get_element_extents(element)
            states = element.getState()
            state_names = state_list_to_names(states)
            actions = get_element_actions(element)
        except Exception:
            extents = None
            state_names = []
            actions = None
        
        # 逐项检查
        for key, value in param_dict.items():
            if value is None or value == '' or value == 'None':
                continue
            
            # 检查存在性
            if key == 'exist':
                if extents and extents.x >= 0 and extents.y >= 0 and state_names and (
                    "showing" in state_names or "visible" in state_names or 
                    "enabled" in state_names or actions) and value is True:
                    continue
                elif (not extents or extents.x < 0 or extents.y < 0 or 
                      (not state_names and not actions)) and value is False:
                    continue
                else:
                    return False, f'控件存在性检查失败'
            
            # 检查可见性
            elif key == 'visible':
                if state_names and ("showing" in state_names or 
                                   "visible" in state_names) and value is True:
                    continue
                elif ("showing" not in state_names and 
                      "visible" not in state_names) and value is False:
                    continue
                else:
                    return False, f'控件可见性检查失败'
            
            # 检查选中状态
            elif key == 'selected':
                if state_names and ("selected" in state_names or 
                                   "checked" in state_names) and value is True:
                    continue
                elif ("selected" not in state_names and 
                      "checked" not in state_names) and value is False:
                    continue
                else:
                    return False, f'控件选中状态检查失败'
            
            # 检查文本内容
            elif key == 'text':
                try:
                    if element.queryText() is not None:
                        text_interface = element.queryText()
                        text = text_interface.getText(0, -1)
                    else:
                        text = None
                except Exception:
                    text = None
                
                if text and value and (value == text or value in text):
                    continue
                else:
                    return False, f'控件文本内容不匹配'
            
            # 检查尺寸
            elif key == 'size_width':
                if extents and extents.width == int(value):
                    continue
                else:
                    return False, f'控件宽度不匹配'
            
            elif key == 'size_height':
                if extents and extents.height == int(value):
                    continue
                else:
                    return False, f'控件高度不匹配'
            
            else:
                return False, f'传入检查点参数有误: {key}'
        
        return True, None
    
    @staticmethod
    def judge_element_match(element, control_info, judge_info):
        """
        判断元素是否匹配指定条件
        
        Args:
            element: pyatspi元素对象
            control_info: 控件信息
            judge_info: 判断信息
            
        Returns:
            bool: 是否匹配
        """
        try:
            from ..utils.helpers import get_element_extents
            from ..core.element_extractor import ElementExtractor
            
            if judge_info in element.name and element.name == control_info["Name"]:
                extents = get_element_extents(element)
                if not extents:
                    return False
                
                states = element.getState()
                state_names = state_list_to_names(states)
                _, parent_count = ElementExtractor.get_element_path(element)
                
                if (extents.width == control_info["Coords"]["width"] and 
                    extents.height == control_info["Coords"]["height"] and 
                    state_names == control_info["States"] and 
                    parent_count == control_info["ParentCount"]):
                    return True
        except Exception as e:
            print(f"Error in judge_element_match: {e}")
        
        return False 