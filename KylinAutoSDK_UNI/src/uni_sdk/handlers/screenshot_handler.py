#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
截图处理模块
负责截图的生成和标记
"""

import os
import time
# import cv2
from ..utils.constants import SCREENSHOT_DIR, SCREENSHOT_FORMAT


class ScreenshotHandler:
    """截图处理器"""
    
    @staticmethod
    def take_screenshot(name, info):
        """
        截取屏幕截图
        
        Args:
            name: 截图名称
            info: 截图信息（用于创建目录）
            
        Returns:
            str: 截图文件路径
        """
        # 创建截图目录
        img_result_dir = os.path.join(SCREENSHOT_DIR, info)
        if not os.path.isdir(img_result_dir):
            os.makedirs(img_result_dir)
        
        # 生成截图路径
        img_path = os.path.join(img_result_dir, f'{name}.{SCREENSHOT_FORMAT}')
        
        # 执行截图命令
        os.system(f"scrot '{img_path}'")
        time.sleep(0.2)
        
        return img_path
    
    @staticmethod
    def mark_region(img_path, mark_region=None):
        """
        在截图上标记指定区域
        
        Args:
            img_path: 图片路径
            mark_region: 标记区域 [x1, y1, x2, y2]
            
        Returns:
            str: 处理后的图片路径
        """
        if not mark_region:
            return img_path
        
        try:
            # 读取图片
            tmp_img = cv2.imread(img_path)
            if tmp_img is None:
                print(f"Failed to read image: {img_path}")
                return img_path
            
            # 绘制矩形框
            lt_x, lt_y, rd_x, rd_y = mark_region
            cv2.rectangle(tmp_img, (lt_x, lt_y), (rd_x, rd_y), 
                         color=(0, 0, 255), thickness=2)
            
            # 保存图片
            cv2.imwrite(img_path, tmp_img)
            
        except Exception as e:
            print(f"Error marking region on image: {e}")
        
        return img_path
    
    @staticmethod
    def take_element_screenshot(element_name, positions):
        """
        为元素截图并标记位置
        
        Args:
            element_name: 元素名称
            positions: 元素位置列表，每个元素为 [x, y, width, height, center_x, center_y]
            
        Returns:
            str: 截图路径
        """
        # 生成时间戳
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        
        # 截图
        img_path = ScreenshotHandler.take_screenshot(element_name, timestamp)
        
        # 标记所有位置
        for pos in positions:
            if len(pos) >= 4:
                mark_region = [pos[0], pos[1], pos[0] + pos[2], pos[1] + pos[3]]
                img_path = ScreenshotHandler.mark_region(img_path, mark_region)
        
        return img_path 