#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
数据处理模块
负责数据的存储、读取和处理
"""

import json
from ..utils.constants import DEFAULT_DATA_FILE, MENU_RECORD_FILE


class DataHandler:
    """数据处理器"""
    
    def __init__(self):
        self.data_list = {
            "keys": [],
            "controls": []
        }
    
    def append_key_to_list(self, key_values):
        """
        将键值添加到列表
        
        Args:
            key_values: 键值，可以是字符串或集合
        """
        try:
            if isinstance(key_values, str):
                # 单字符，直接记录
                record = {"type": "single", "value": key_values}
            else:
                key_strs = [self._filter_key_str(k) for k in key_values]
                if len(key_strs) > 1:
                    # 组成组合键字符串，并标记为组合键
                    combined_str = "+".join(key_strs)
                    record = {"type": "combine", "value": combined_str}
                elif len(key_strs) == 1:
                    record = {"type": "single", "value": key_strs[0]}
                else:
                    record = {"type": "single", "value": ""}
            
            self.data_list["keys"].append(record)
            print(f"Key action added to list: {record}")
        except Exception as e:
            print(f"Error adding key action to list: {e}")
    
    def _filter_key_str(self, key):
        """
        过滤键值字符串，去除不需要的部分
        
        Args:
            key: 键值对象
            
        Returns:
            str: 过滤后的键值字符串
        """
        if hasattr(key, 'char') and key.char:
            return key.char
        elif hasattr(key, 'name') and key.name:
            return key.name
        else:
            return str(key).replace("Key.", "")
    
    def append_control_to_list(self, control_data):
        """
        将控件信息添加到列表
        
        Args:
            control_data: 控件信息字典
        """
        try:
            self.data_list["controls"].append(control_data)
            print(f"Control {control_data['Name']} added to list.")
        except Exception as e:
            print(f"Error adding control to list: {e}")
    
    def save_data_to_file(self, filename=DEFAULT_DATA_FILE):
        """
        将所有数据写入JSON文件
        
        Args:
            filename: 保存的文件名
        """
        try:
            with open(filename, 'w', encoding='UTF-8') as json_file:
                json.dump(self.data_list, json_file, indent=4, ensure_ascii=False)
            print(f"Data saved to {filename}")
        except TypeError as e:
            print(f"Error saving data to file: {e}")
            print("Data that caused error:", self.data_list)
        except Exception as e:
            print(f"Unexpected error saving data to file: {e}")
    
    def kb_to_json(self, data, filename=DEFAULT_DATA_FILE):
        """
        根据数据类型将键值或控件信息添加到列表，并保存到文件
        
        Args:
            data: 键值或控件信息
            filename: 保存的文件名
        """
        if isinstance(data, dict):  # 控件信息
            self.append_control_to_list(data)
        else:  # 键值
            if isinstance(data, set):
                self.append_key_to_list(frozenset(data))
            else:
                self.append_key_to_list(data)
        
        self.save_data_to_file(filename)
    
    @staticmethod
    def load_menu_record():
        """
        加载菜单记录文件
        
        Returns:
            dict: 菜单记录数据
        """
        try:
            with open(MENU_RECORD_FILE, 'r') as json_file:
                return json.load(json_file)
        except Exception as e:
            print(f"Error loading menu record: {e}")
            return None
    
    @staticmethod
    def check_menu_element(control_info, menu_data):
        """
        检查控件是否匹配菜单元素
        
        Args:
            control_info: 控件信息
            menu_data: 菜单数据
            
        Returns:
            tuple: (x, y, width, height) 或 None
        """
        if not menu_data:
            return None
            
        for value in menu_data:
            menu_item = menu_data[value]
            if ((control_info["Name"] == "N/A" or control_info["Name"] == menu_item["Name"]) and
                (control_info["Description"] == "N/A" or control_info["Description"] == menu_item["Description"]) and
                control_info["Rolename"] == menu_item["Rolename"] and
                control_info["Coords"]['width'] == menu_item["Coords"]['width'] and
                control_info["Coords"]['height'] == menu_item["Coords"]['height'] and
                control_info["Actions"] == menu_item["Actions"] and
                control_info["States"] == menu_item["States"]):
                
                x = control_info["Coords"]["x"]
                y = control_info["Coords"]["y"]
                width = control_info["Coords"]["width"]
                height = control_info["Coords"]["height"]
                
                if ('RecordPosition' in control_info and 
                    x <= control_info["RecordPosition"][0] <= x + width and 
                    y <= control_info["RecordPosition"][1] <= y + height):
                    center_x = control_info["RecordPosition"][0]
                    center_y = control_info["RecordPosition"][1]
                else:
                    center_x = x + width // 2
                    center_y = y + height // 2
                    
                return center_x, center_y, width, height
                
        return None 