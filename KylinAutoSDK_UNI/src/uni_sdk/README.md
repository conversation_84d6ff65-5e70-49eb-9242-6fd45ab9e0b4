# UNI SDK 模块化架构说明

## 目录结构

```
uni_sdk/
├── __init__.py           # 包初始化，导出UNI类
├── uni.py               # 主类，整合所有功能
├── core/                # 核心功能模块
│   ├── __init__.py
│   ├── element_extractor.py    # 控件信息提取
│   ├── window_manager.py       # 窗口管理
│   └── element_finder.py       # 控件查找定位
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── constants.py     # 常量定义
│   └── helpers.py       # 辅助函数
└── handlers/            # 处理器模块
    ├── __init__.py
    ├── data_handler.py        # 数据处理和持久化
    ├── screenshot_handler.py  # 截图功能
    └── verification_handler.py # 验证功能
```

## 模块说明

### 1. core 核心模块

#### element_extractor.py
- `ElementExtractor`: 负责从AT-SPI元素中提取各种信息
  - `extract_element_info()`: 提取元素详细信息
  - `get_element_path()`: 获取元素父路径
  - `generate_key()`: 生成元素唯一标识

#### window_manager.py
- `WindowManager`: 负责窗口的获取、查找和管理
  - `get_window_stack()`: 获取窗口堆栈
  - `get_active_window_at_point()`: 根据坐标获取活动窗口
  - `get_window_by_name()`: 根据名称获取窗口

#### element_finder.py
- `ElementFinder`: 负责控件的定位和查找
  - `find_accessible_at_point()`: 根据坐标查找控件
  - `find_component_by_info()`: 根据信息查找控件
  - `find_tree_elements()`: 遍历元素树

### 2. utils 工具模块

#### constants.py
- 所有常量定义（窗口名称、过滤规则、默认值等）

#### helpers.py
- 通用辅助函数（进程名获取、状态转换、坐标计算等）

### 3. handlers 处理器模块

#### data_handler.py
- `DataHandler`: 数据存储和处理
  - 键盘操作记录
  - 控件信息保存
  - JSON文件读写

#### screenshot_handler.py
- `ScreenshotHandler`: 截图功能
  - 屏幕截图
  - 区域标记
  - 图片保存

#### verification_handler.py
- `VerificationHandler`: 控件验证
  - 存在性检查
  - 可见性检查
  - 可点击性检查
  - 属性匹配验证

## 使用方式

### 1. 从新模块导入
```python
from uni_sdk import UNI

uni = UNI()
data, info = uni.kdk_getElement_Uni(100, 200)
```

### 2. 向后兼容（使用UNI_new.py）
```python
from UNI_new import UNI

uni = UNI()
data, info = uni.kdk_getElement_Uni(100, 200)
```

### 3. 直接使用子模块
```python
from uni_sdk.core import WindowManager
from uni_sdk.handlers import ScreenshotHandler

# 获取窗口
wm = WindowManager()
windows = wm.get_window_by_name("文件管理器")

# 截图
sh = ScreenshotHandler()
img_path = sh.take_screenshot("test", "2024-01-01")
```

## 优势

1. **模块化设计**: 功能分离，职责明确
2. **易于维护**: 每个模块独立，便于修改和扩展
3. **代码复用**: 子模块可独立使用
4. **向后兼容**: 保持原有API不变
5. **可测试性**: 模块化便于单元测试

## 扩展建议

1. 添加更多验证规则到`verification_handler.py`
2. 在`constants.py`中集中管理配置
3. 扩展`helpers.py`添加更多工具函数
4. 为每个模块编写单元测试
5. 添加类型注解提升代码质量 