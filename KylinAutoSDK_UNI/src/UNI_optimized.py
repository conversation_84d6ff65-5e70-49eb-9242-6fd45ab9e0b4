#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
UNI类的高性能优化版本
目标：将控件识别速度提升至1秒以内
"""

import threading
import time
import subprocess
import os
import sys
from collections import deque
from functools import lru_cache

# 添加路径并导入基础UNI
try:
    from uni_sdk import UNI as BaseUNI
except ImportError:
    # 如果uni_sdk不可用，使用当前目录的UNI
    from UNI import UNI as BaseUNI

# 尝试导入高亮功能
try:
    sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
    from ultimate_highlight_corrected import ultimate_highlight_corrected as ultimate_highlight
except ImportError:
    try:
        from ultimate_highlight import ultimate_highlight
    except ImportError:
        # 如果没有高亮功能，定义一个空函数
        def ultimate_highlight(*args, **kwargs):
            pass

class UNI_Optimized(BaseUNI):
    """
    高性能优化版本的UNI类
    """
    
    def __init__(self):
        super().__init__()
        self._window_cache = {}
        self._window_cache_time = 0
        self._atspi_cache = {}
        self._atspi_cache_time = 0
        self._cache_lock = threading.Lock()
        
    def kdk_getElement_Uni(self, x, y, quick=True, menuele=None, highlight=False):
        """
        高性能控件定位方法
        默认使用快速模式以提高性能
        """
        start_time = time.time()
        
        # 处理菜单元素
        if menuele:
            result = super().kdk_getElement_Uni(x, y, quick, menuele)
            if highlight and result[0]:
                self._highlight_control(result[0])
            return result
        
        # 使用高性能控件定位
        try:
            control_data, location_info = self._locate_control_ultra_fast(x, y)
            
            if control_data:
                elapsed = time.time() - start_time
                print(f"⚡ 控件识别成功 ({elapsed:.2f}s)")
                
                if highlight:
                    self._highlight_control(control_data)
                return control_data, f"高性能定位成功: {location_info}"
                
        except Exception as e:
            print(f"高性能定位失败: {e}")
        
        # 回退到原有逻辑
        result = super().kdk_getElement_Uni(x, y, quick, menuele)
        if highlight and result[0]:
            self._highlight_control(result[0])
        return result
    
    def _locate_control_ultra_fast(self, target_x, target_y):
        """
        超快速控件定位算法
        """
        # 步骤1: 快速获取目标窗口
        target_window = self._get_target_window_cached(target_x, target_y)
        if not target_window:
            return None, "坐标不在任何窗口内"
        
        # 步骤2: 快速获取AT-SPI应用
        atspi_app = self._get_atspi_app_cached(target_window)
        if not atspi_app:
            return None, "未找到对应的AT-SPI应用"
        
        # 步骤3: 超快速控件搜索
        control_data = self._search_control_lightning(atspi_app, target_x, target_y, target_window)
        if control_data:
            return control_data, f"在{target_window['process']}中找到控件"
        
        return None, "未找到匹配的控件"
    
    def _get_target_window_cached(self, target_x, target_y):
        """
        缓存版本的窗口查找
        """
        current_time = time.time()
        
        with self._cache_lock:
            # 缓存1秒钟
            if current_time - self._window_cache_time > 1:
                self._update_window_cache()
                self._window_cache_time = current_time
        
        # 快速查找匹配窗口
        for window_info in self._window_cache.values():
            geometry = window_info['geometry']
            if (geometry['x'] <= target_x <= geometry['x'] + geometry['width'] and
                geometry['y'] <= target_y <= geometry['y'] + geometry['height']):
                return window_info
        
        return None
    
    def _update_window_cache(self):
        """
        更新窗口缓存
        """
        try:
            from uni_sdk.utils.helpers import get_windows_by_wlcctrl
            windows = get_windows_by_wlcctrl()
            
            self._window_cache = {}
            for title, geometry in windows.items():
                process = self._extract_process_from_title(title)
                area = geometry['width'] * geometry['height']
                
                self._window_cache[title] = {
                    'title': title,
                    'geometry': geometry,
                    'process': process,
                    'area': area
                }
        except Exception:
            pass
    
    @lru_cache(maxsize=32)
    def _extract_process_from_title(self, title):
        """
        缓存版本的进程名提取
        """
        title_lower = title.lower()
        
        # 常见应用映射
        process_patterns = {
            'terminal': ['terminal', 'bash', 'shell'],
            'firefox': ['firefox', 'mozilla'],
            'chrome': ['chrome', 'chromium'],
            'code': ['code', 'vscode', 'visual studio'],
            'hellobig': ['hellobig'],
            'gedit': ['gedit', 'text editor'],
            'nautilus': ['files', 'nautilus'],
        }
        
        for process, patterns in process_patterns.items():
            if any(pattern in title_lower for pattern in patterns):
                return process
        
        # 处理终端窗口
        if '@' in title and ':' in title:
            parts = title.split(':')
            if len(parts) >= 2:
                user_host = parts[0].strip()
                path_part = parts[1].strip()
                if '@' in user_host and (path_part.startswith('~') or path_part.startswith('/')):
                    return 'terminal'
        
        # 返回第一个单词
        words = title.split()
        if words:
            first_word = words[0].lower()
            if '@' not in first_word:
                return first_word
        
        return 'unknown'
    
    def _get_atspi_app_cached(self, target_window):
        """
        缓存版本的AT-SPI应用获取
        """
        current_time = time.time()
        process_name = target_window['process']
        
        with self._cache_lock:
            # 缓存2秒钟
            if current_time - self._atspi_cache_time > 2:
                self._atspi_cache.clear()
                self._atspi_cache_time = current_time
            
            # 检查缓存
            if process_name in self._atspi_cache:
                return self._atspi_cache[process_name]
        
        # 查找AT-SPI应用
        atspi_app = self._find_atspi_application_fast(target_window)
        
        if atspi_app:
            with self._cache_lock:
                self._atspi_cache[process_name] = atspi_app
        
        return atspi_app
    
    def _find_atspi_application_fast(self, target_window):
        """
        快速查找AT-SPI应用
        """
        try:
            import pyatspi
            desktop = pyatspi.Registry.getDesktop(0)
            
            # 快速匹配策略
            process_name = target_window['process']
            
            # 限制搜索范围
            for i in range(min(desktop.childCount, 20)):  # 最多检查20个应用
                try:
                    app = desktop.getChildAtIndex(i)
                    app_name = app.name.lower()
                    
                    # 快速匹配
                    if (process_name in app_name or 
                        app_name in process_name or
                        self._is_app_match(app_name, process_name)):
                        return app
                        
                except Exception:
                    continue
            
            return None
            
        except Exception:
            return None
    
    def _is_app_match(self, app_name, process_name):
        """
        快速应用匹配
        """
        # 特殊匹配规则
        matches = {
            'terminal': ['gnome-terminal', 'xterm', 'konsole'],
            'firefox': ['firefox', 'mozilla'],
            'chrome': ['google-chrome', 'chromium'],
            'code': ['code', 'vscode'],
            'hellobig': ['hellobig'],
            'gedit': ['gedit', 'text-editor'],
        }
        
        if process_name in matches:
            return any(pattern in app_name for pattern in matches[process_name])
        
        return False
    
    def _search_control_lightning(self, app_element, target_x, target_y, target_window):
        """
        闪电级控件搜索算法
        限制搜索范围和深度以提高速度
        """
        try:
            from uni_sdk.utils.helpers import get_element_extents
            
            # 超快速搜索配置
            search_queue = deque([(app_element, 0)])
            checked_count = 0
            max_checks = 25  # 大幅减少检查数量
            max_depth = 3    # 减少搜索深度
            
            # 高优先级控件类型（只搜索最常见的）
            priority_roles = {
                'push button', 'button', 'text', 'label', 'entry', 
                'list item', 'menu item', 'check box', 'radio button'
            }
            
            while search_queue and checked_count < max_checks:
                element, depth = search_queue.popleft()
                checked_count += 1
                
                if depth > max_depth:
                    continue
                
                try:
                    role = element.getRoleName().lower()
                    
                    # 只检查高优先级控件
                    if role in priority_roles:
                        extents = get_element_extents(element)
                        if extents and self._is_coordinate_in_control_fast(target_x, target_y, extents, target_window):
                            return self._build_control_data_fast(element, extents, target_window)
                    
                    # 只对容器添加子元素
                    if role in ['frame', 'filler', 'panel'] and depth < 2:
                        child_count = min(element.childCount, 8)  # 最多8个子元素
                        for i in range(child_count):
                            try:
                                child = element.getChildAtIndex(i)
                                search_queue.append((child, depth + 1))
                            except Exception:
                                continue
                
                except Exception:
                    continue
            
            return None
            
        except Exception:
            return None
    
    def _is_coordinate_in_control_fast(self, target_x, target_y, extents, target_window):
        """
        快速坐标检查
        """
        try:
            # 简化坐标转换
            x, y, width, height = extents
            
            # 快速边界检查
            if width <= 0 or height <= 0:
                return False
            
            # 检查坐标是否在控件范围内
            return (x <= target_x <= x + width and 
                   y <= target_y <= y + height)
            
        except Exception:
            return False
    
    def _build_control_data_fast(self, element, extents, target_window):
        """
        快速构建控件数据
        """
        try:
            x, y, width, height = extents
            
            # 简化数据构建
            control_data = {
                'Name': getattr(element, 'name', '') or 'N/A',
                'Rolename': element.getRoleName() or 'N/A',
                'Coords': {
                    'x': x,
                    'y': y,
                    'width': width,
                    'height': height
                },
                'RelativeCoords': {
                    'x': x - target_window['geometry']['x'],
                    'y': y - target_window['geometry']['y'],
                    'width': width,
                    'height': height
                }
            }
            
            return control_data
            
        except Exception:
            return None
    
    def _highlight_control(self, control_data):
        """
        高亮控件
        """
        try:
            coords = control_data.get('Coords', {})
            if coords:
                ultimate_highlight(
                    x=coords.get('x', 0),
                    y=coords.get('y', 0),
                    width=coords.get('width', 1),
                    height=coords.get('height', 1),
                    duration=2,
                    color='red',
                    border_width=2
                )
        except Exception:
            pass

# 便捷函数
def create_optimized_uni():
    """
    创建优化版本的UNI实例
    """
    return UNI_Optimized()

# 向后兼容
UNI = UNI_Optimized

# 导出
__all__ = ["UNI_Optimized", "UNI", "create_optimized_uni"]