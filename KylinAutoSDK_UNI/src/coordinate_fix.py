#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标转换修复模块
修正AT-SPI坐标与实际窗口坐标的差异
"""

import subprocess
import os

def get_window_decorations():
    """获取窗口装饰偏移量"""
    # 在Wayland环境下，窗口装饰通常由compositor处理
    # 常见的偏移量
    decorations = {
        'title_bar_height': 40,  # 标题栏高度
        'border_width': 2,       # 边框宽度
        'menu_bar_height': 25,   # 菜单栏高度（如果有）
    }
    
    # 检测桌面环境
    desktop_env = os.environ.get('XDG_CURRENT_DESKTOP', '').lower()
    
    if 'gnome' in desktop_env:
        decorations['title_bar_height'] = 38
    elif 'kde' in desktop_env or 'plasma' in desktop_env:
        decorations['title_bar_height'] = 35
    elif 'xfce' in desktop_env:
        decorations['title_bar_height'] = 32
    
    return decorations

def detect_coordinate_system():
    """检测当前使用的坐标系统"""
    wayland_display = os.environ.get('WAYLAND_DISPLAY')
    x11_display = os.environ.get('DISPLAY')
    
    if wayland_display and not x11_display:
        return 'wayland'
    elif x11_display:
        return 'x11'
    else:
        return 'unknown'

def calculate_window_offset(window_info, target_app_info=None):
    """计算窗口偏移量"""
    decorations = get_window_decorations()
    coord_system = detect_coordinate_system()
    
    offset_x = 0
    offset_y = 0
    
    # 基本偏移量
    if coord_system == 'wayland':
        # Wayland下通常AT-SPI坐标是相对于窗口内容区域的
        offset_y = decorations['title_bar_height']
    else:
        # X11下可能需要不同的处理
        offset_y = decorations['title_bar_height']
    
    # 如果有应用特定信息，进行微调
    if target_app_info:
        app_name = target_app_info.get('process', '').lower()
        
        if 'qtcreator' in app_name or 'hellobig' in app_name:
            # Qt应用通常有菜单栏
            offset_y += decorations['menu_bar_height']
        elif 'firefox' in app_name or 'chrome' in app_name:
            # 浏览器可能有地址栏
            offset_y += 35
    
    return offset_x, offset_y

def fix_atspi_coordinates(atspi_coords, window_info, target_app_info=None):
    """修正AT-SPI坐标到实际桌面坐标"""
    if not atspi_coords:
        return atspi_coords
    
    # 获取偏移量
    offset_x, offset_y = calculate_window_offset(window_info, target_app_info)
    
    # 复制原始坐标
    fixed_coords = atspi_coords.copy()
    
    # 应用偏移修正
    if 'x' in fixed_coords:
        fixed_coords['x'] = atspi_coords['x'] + offset_x
    
    if 'y' in fixed_coords:
        # 注意：这里我们需要减去偏移量，因为AT-SPI的Y坐标通常包含了标题栏
        fixed_coords['y'] = atspi_coords['y'] - offset_y
    
    return fixed_coords

def smart_coordinate_correction(mouse_x, mouse_y, atspi_coords, window_info, target_app_info=None):
    """智能坐标修正"""
    if not atspi_coords:
        return atspi_coords
    
    atspi_x = atspi_coords.get('x', 0)
    atspi_y = atspi_coords.get('y', 0)
    
    # 计算初始偏差
    diff_x = atspi_x - mouse_x
    diff_y = atspi_y - mouse_y
    
    print(f"🔧 坐标修正分析:")
    print(f"   鼠标位置: ({mouse_x}, {mouse_y})")
    print(f"   AT-SPI位置: ({atspi_x}, {atspi_y})")
    print(f"   初始偏差: X={diff_x}, Y={diff_y}")
    
    # 智能修正策略
    corrected_coords = atspi_coords.copy()
    
    # Y轴修正（最常见的问题）
    if abs(diff_y) > 30:  # 如果Y轴偏差较大
        decorations = get_window_decorations()
        
        # 尝试不同的修正策略
        strategies = [
            decorations['title_bar_height'],                    # 只减去标题栏
            decorations['title_bar_height'] + decorations['menu_bar_height'],  # 标题栏+菜单栏
            40,  # 固定40像素偏移
            diff_y,  # 直接使用计算出的偏差
        ]
        
        best_strategy = 0
        best_diff = abs(diff_y)
        
        for i, offset in enumerate(strategies):
            test_y = atspi_y - offset
            test_diff = abs(test_y - mouse_y)
            
            if test_diff < best_diff:
                best_diff = test_diff
                best_strategy = offset
        
        if best_strategy > 0:
            corrected_coords['y'] = atspi_y - best_strategy
            print(f"   Y轴修正: -{best_strategy} -> 新Y={corrected_coords['y']}")
    
    # X轴修正（较少见）
    if abs(diff_x) > 20:
        # 通常X轴偏差较小，可能是边框影响
        if diff_x > 0:  # AT-SPI坐标偏右
            corrected_coords['x'] = atspi_x - 2  # 减去边框宽度
            print(f"   X轴修正: -2 -> 新X={corrected_coords['x']}")
    
    return corrected_coords

def validate_coordinates(coords, window_info):
    """验证坐标是否在合理范围内"""
    if not coords or not window_info:
        return False
    
    geometry = window_info.get('geometry', {})
    if not geometry:
        return False
    
    x = coords.get('x', 0)
    y = coords.get('y', 0)
    width = coords.get('width', 0)
    height = coords.get('height', 0)
    
    win_x = geometry.get('x', 0)
    win_y = geometry.get('y', 0)
    win_width = geometry.get('width', 0)
    win_height = geometry.get('height', 0)
    
    # 检查坐标是否在窗口范围内（允许一定误差）
    margin = 50
    
    if (x < win_x - margin or 
        y < win_y - margin or 
        x + width > win_x + win_width + margin or 
        y + height > win_y + win_height + margin):
        return False
    
    return True

def get_accurate_highlight_coords(mouse_x, mouse_y, control_data, window_info, target_app_info=None):
    """获取准确的高亮坐标"""
    if not control_data:
        return None
    
    original_coords = control_data.get('Coords', {})
    if not original_coords:
        return None
    
    print(f"🎯 计算准确高亮坐标:")
    print(f"   目标鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 使用智能坐标修正
    corrected_coords = smart_coordinate_correction(
        mouse_x, mouse_y, original_coords, window_info, target_app_info
    )
    
    # 验证修正后的坐标
    if validate_coordinates(corrected_coords, window_info):
        print(f"   ✅ 坐标修正成功")
        return corrected_coords
    else:
        print(f"   ⚠️ 修正后坐标仍超出合理范围，使用原始坐标")
        return original_coords

# 向后兼容的函数
def correct_coordinates_for_highlight(coords, window_info=None, target_app_info=None):
    """修正坐标用于高亮显示（向后兼容）"""
    return fix_atspi_coordinates(coords, window_info, target_app_info)