#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
可靠的窗口可见性检查器
使用wmctrl和xprop来准确检测窗口是否真的可见
"""

import sys
import os
import subprocess
import re


def check_window_in_wmctrl(window_title_pattern):
    """检查窗口是否在wmctrl的窗口列表中"""
    print(f"🔍 检查窗口是否在wmctrl列表中: '{window_title_pattern}'")
    
    try:
        result = subprocess.run(['wmctrl', '-l'], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"   ❌ wmctrl失败: {result.stderr}")
            return False, []
        
        windows = []
        lines = result.stdout.strip().split('\n')
        
        for line in lines:
            parts = line.split(None, 3)
            if len(parts) >= 4:
                window_id = parts[0]
                title = parts[3]
                
                if window_title_pattern.lower() in title.lower():
                    windows.append({
                        'id': window_id,
                        'title': title,
                        'line': line
                    })
        
        if windows:
            print(f"   ✅ 找到 {len(windows)} 个匹配窗口:")
            for window in windows:
                print(f"     - {window['id']}: {window['title']}")
            return True, windows
        else:
            print(f"   ❌ 未找到匹配窗口")
            print(f"   💡 这通常意味着窗口已最小化或隐藏")
            return False, []
    
    except Exception as e:
        print(f"   ❌ wmctrl检查失败: {e}")
        return False, []


def check_window_state_with_xprop(window_id):
    """使用xprop检查窗口的详细状态"""
    print(f"   🔍 使用xprop检查窗口 {window_id} 的状态...")
    
    try:
        result = subprocess.run(['xprop', '-id', window_id, '_NET_WM_STATE'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"     ❌ xprop失败: {result.stderr}")
            return None
        
        state_line = result.stdout.strip()
        print(f"     状态: {state_line}")
        
        # 分析状态
        if '_NET_WM_STATE_HIDDEN' in state_line:
            print(f"     🔴 窗口已隐藏/最小化")
            return 'hidden'
        elif '_NET_WM_STATE_MAXIMIZED' in state_line:
            print(f"     🟢 窗口已最大化")
            return 'maximized'
        elif state_line.strip().endswith('=') or 'no such atom' in state_line.lower():
            print(f"     🟢 窗口状态正常（无特殊状态）")
            return 'normal'
        else:
            print(f"     ❓ 窗口状态未知")
            return 'unknown'
    
    except Exception as e:
        print(f"     ❌ xprop检查失败: {e}")
        return None


def check_window_visibility_comprehensive(window_title_pattern):
    """综合检查窗口可见性"""
    print(f"🎯 综合检查窗口可见性: '{window_title_pattern}'")
    print("=" * 50)
    
    # 步骤1: 检查窗口是否在wmctrl列表中
    in_wmctrl, wmctrl_windows = check_window_in_wmctrl(window_title_pattern)
    
    if not in_wmctrl:
        print(f"\n📊 结论: 窗口不可见")
        print(f"   原因: 窗口不在wmctrl的活动窗口列表中")
        print(f"   状态: 很可能已最小化或隐藏")
        return False, "not_in_wmctrl"
    
    # 步骤2: 如果窗口在列表中，进一步检查状态
    print(f"\n🔍 窗口在wmctrl列表中，进一步检查状态...")
    
    all_visible = True
    states = []
    
    for window in wmctrl_windows:
        state = check_window_state_with_xprop(window['id'])
        states.append(state)
        
        if state == 'hidden':
            all_visible = False
            print(f"     ❌ 窗口 {window['id']} 已隐藏")
        elif state in ['normal', 'maximized']:
            print(f"     ✅ 窗口 {window['id']} 可见")
        else:
            print(f"     ❓ 窗口 {window['id']} 状态不确定")
    
    # 步骤3: 综合判断
    if all_visible and all(s in ['normal', 'maximized'] for s in states if s):
        print(f"\n📊 结论: 窗口可见")
        print(f"   原因: 窗口在活动列表中且状态正常")
        return True, "visible"
    elif not all_visible:
        print(f"\n📊 结论: 窗口不可见")
        print(f"   原因: 至少有一个窗口处于隐藏状态")
        return False, "hidden"
    else:
        print(f"\n📊 结论: 窗口状态不确定")
        print(f"   原因: 无法确定所有窗口的状态")
        return None, "uncertain"


def test_current_hellobig_state():
    """测试当前hellobig窗口的状态"""
    print("🧪 测试当前hellobig窗口状态")
    print("=" * 60)
    
    # 测试不同的窗口标题模式
    patterns = [
        "AT-SPI测试界面",
        "hellobig", 
        "Qt控件集合",
        "AT-SPI测试"
    ]
    
    results = {}
    
    for pattern in patterns:
        print(f"\n🔍 测试模式: '{pattern}'")
        print("-" * 30)
        
        is_visible, reason = check_window_visibility_comprehensive(pattern)
        results[pattern] = (is_visible, reason)
        
        print(f"结果: {'可见' if is_visible else '不可见' if is_visible is False else '不确定'} ({reason})")
    
    # 总结结果
    print(f"\n📊 测试总结:")
    print("=" * 30)
    
    for pattern, (is_visible, reason) in results.items():
        status = "✅ 可见" if is_visible else "❌ 不可见" if is_visible is False else "❓ 不确定"
        print(f"   {pattern}: {status} ({reason})")
    
    # 判断整体状态
    visible_count = sum(1 for is_visible, _ in results.values() if is_visible is True)
    hidden_count = sum(1 for is_visible, _ in results.values() if is_visible is False)
    
    if hidden_count > 0:
        print(f"\n🔴 最终结论: hellobig窗口已最小化/隐藏")
        print(f"   依据: {hidden_count} 个模式检测到窗口不可见")
        return False
    elif visible_count > 0:
        print(f"\n🟢 最终结论: hellobig窗口可见")
        print(f"   依据: {visible_count} 个模式检测到窗口可见")
        return True
    else:
        print(f"\n❓ 最终结论: 无法确定hellobig窗口状态")
        return None


def main():
    """主函数"""
    print("🔍 可靠的窗口可见性检查器")
    print("=" * 60)
    print("🎯 使用wmctrl和xprop准确检测窗口可见性")
    print("💡 解决AT-SPI状态信息不准确的问题")
    print("=" * 60)
    
    # 检查必要工具
    tools = ['wmctrl', 'xprop']
    missing_tools = []
    
    for tool in tools:
        try:
            # 对于xprop，直接测试运行
            if tool == 'xprop':
                result = subprocess.run([tool, '-root', '-f'], capture_output=True, text=True, timeout=5)
                if result.returncode != 0:
                    raise subprocess.CalledProcessError(result.returncode, tool)
            else:
                subprocess.run([tool, '--version'], capture_output=True, check=True)
            print(f"✅ {tool} 可用")
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            try:
                subprocess.run([tool, '--help'], capture_output=True, check=True)
                print(f"✅ {tool} 可用")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print(f"❌ {tool} 不可用")
                missing_tools.append(tool)
    
    if missing_tools:
        print(f"\n❌ 缺少必要工具: {', '.join(missing_tools)}")
        print(f"请安装: sudo apt install wmctrl x11-utils")
        return
    
    # 测试hellobig窗口状态
    result = test_current_hellobig_state()
    
    print(f"\n💡 集成建议:")
    print("=" * 30)
    if result is False:
        print("✅ 检测器正确识别了最小化窗口")
        print("   可以将此方法集成到universal_offset_detector.py中")
        print("   替代不可靠的AT-SPI状态检查")
    elif result is True:
        print("⚠️  检测器认为窗口可见")
        print("   请确认hellobig窗口是否真的可见")
    else:
        print("❓ 检测结果不确定")
        print("   可能需要其他验证方法")


if __name__ == "__main__":
    main()
