#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
控件识别性能优化测试
对比原版与优化版的性能差异
"""

import sys
import os
import time
import statistics
import subprocess

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def test_original_version(test_x, test_y, test_count=5):
    """测试原版性能"""
    print(f"🔍 测试原版性能 (测试{test_count}次)")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        times = []
        success_count = 0
        
        for i in range(test_count):
            print(f"  第{i+1}次测试...")
            start_time = time.time()
            
            try:
                control_data, info = uni.kdk_getElement_Uni(test_x, test_y, quick=False, highlight=False)
                elapsed = time.time() - start_time
                times.append(elapsed)
                
                if control_data:
                    success_count += 1
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    print(f"    ✅ 成功 ({elapsed:.2f}s): {name} ({role})")
                else:
                    print(f"    ❌ 失败 ({elapsed:.2f}s): {info}")
                    
            except Exception as e:
                elapsed = time.time() - start_time
                times.append(elapsed)
                print(f"    ❌ 异常 ({elapsed:.2f}s): {e}")
            
            time.sleep(0.5)  # 短暂休息
        
        # 统计结果
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"\n📊 原版性能统计:")
            print(f"  平均时间: {avg_time:.2f}s")
            print(f"  最快时间: {min_time:.2f}s")
            print(f"  最慢时间: {max_time:.2f}s")
            print(f"  成功率: {success_count}/{test_count} ({success_count/test_count*100:.1f}%)")
            
            return {
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'success_rate': success_count/test_count,
                'times': times
            }
        
        return None
        
    except Exception as e:
        print(f"❌ 原版测试失败: {e}")
        return None

def test_optimized_version(test_x, test_y, test_count=5):
    """测试优化版性能"""
    print(f"\n⚡ 测试优化版性能 (测试{test_count}次)")
    print("=" * 50)
    
    try:
        from UNI_optimized import UNI_Optimized
        
        uni = UNI_Optimized()
        times = []
        success_count = 0
        
        for i in range(test_count):
            print(f"  第{i+1}次测试...")
            start_time = time.time()
            
            try:
                control_data, info = uni.kdk_getElement_Uni(test_x, test_y, quick=True, highlight=False)
                elapsed = time.time() - start_time
                times.append(elapsed)
                
                if control_data:
                    success_count += 1
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    print(f"    ✅ 成功 ({elapsed:.2f}s): {name} ({role})")
                else:
                    print(f"    ❌ 失败 ({elapsed:.2f}s): {info}")
                    
            except Exception as e:
                elapsed = time.time() - start_time
                times.append(elapsed)
                print(f"    ❌ 异常 ({elapsed:.2f}s): {e}")
            
            time.sleep(0.5)  # 短暂休息
        
        # 统计结果
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"\n📊 优化版性能统计:")
            print(f"  平均时间: {avg_time:.2f}s")
            print(f"  最快时间: {min_time:.2f}s")
            print(f"  最慢时间: {max_time:.2f}s")
            print(f"  成功率: {success_count}/{test_count} ({success_count/test_count*100:.1f}%)")
            
            return {
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'success_rate': success_count/test_count,
                'times': times
            }
        
        return None
        
    except Exception as e:
        print(f"❌ 优化版测试失败: {e}")
        return None

def compare_performance(original_stats, optimized_stats):
    """对比性能"""
    print(f"\n🏆 性能对比分析")
    print("=" * 60)
    
    if not original_stats or not optimized_stats:
        print("❌ 无法进行性能对比，缺少测试数据")
        return
    
    # 时间改进
    time_improvement = (original_stats['avg_time'] - optimized_stats['avg_time']) / original_stats['avg_time'] * 100
    speed_ratio = original_stats['avg_time'] / optimized_stats['avg_time']
    
    print(f"📈 时间性能:")
    print(f"  原版平均时间: {original_stats['avg_time']:.2f}s")
    print(f"  优化版平均时间: {optimized_stats['avg_time']:.2f}s")
    print(f"  性能提升: {time_improvement:.1f}%")
    print(f"  速度比: {speed_ratio:.1f}x")
    
    # 稳定性对比
    original_std = statistics.stdev(original_stats['times']) if len(original_stats['times']) > 1 else 0
    optimized_std = statistics.stdev(optimized_stats['times']) if len(optimized_stats['times']) > 1 else 0
    
    print(f"\n📊 稳定性:")
    print(f"  原版标准差: {original_std:.3f}s")
    print(f"  优化版标准差: {optimized_std:.3f}s")
    
    # 成功率对比
    print(f"\n✅ 成功率:")
    print(f"  原版成功率: {original_stats['success_rate']*100:.1f}%")
    print(f"  优化版成功率: {optimized_stats['success_rate']*100:.1f}%")
    
    # 目标达成情况
    print(f"\n🎯 目标达成情况:")
    target_time = 1.0
    if optimized_stats['avg_time'] <= target_time:
        print(f"  ✅ 目标达成! 平均时间 {optimized_stats['avg_time']:.2f}s ≤ {target_time}s")
    else:
        print(f"  ❌ 目标未达成! 平均时间 {optimized_stats['avg_time']:.2f}s > {target_time}s")
        print(f"  还需优化: {optimized_stats['avg_time'] - target_time:.2f}s")
    
    # 推荐
    print(f"\n💡 优化建议:")
    if optimized_stats['avg_time'] > target_time:
        print("  - 进一步减少搜索深度")
        print("  - 增加缓存时间")
        print("  - 优化AT-SPI查找算法")
        print("  - 使用预测式缓存")
    else:
        print("  - 优化已达到目标，可考虑部署")
        print("  - 可继续优化稳定性")

def test_cache_effectiveness():
    """测试缓存效果"""
    print(f"\n🔄 测试缓存效果")
    print("=" * 50)
    
    try:
        from UNI_optimized import UNI_Optimized
        
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
        
        uni = UNI_Optimized()
        
        # 第一次调用（冷启动）
        print("  第1次调用（冷启动）...")
        start_time = time.time()
        control_data1, info1 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=True, highlight=False)
        cold_time = time.time() - start_time
        
        # 第二次调用（热启动）
        print("  第2次调用（热启动）...")
        start_time = time.time()
        control_data2, info2 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=True, highlight=False)
        hot_time = time.time() - start_time
        
        print(f"\n📊 缓存效果:")
        print(f"  冷启动时间: {cold_time:.2f}s")
        print(f"  热启动时间: {hot_time:.2f}s")
        
        if cold_time > 0:
            cache_improvement = (cold_time - hot_time) / cold_time * 100
            print(f"  缓存提升: {cache_improvement:.1f}%")
        
        # 验证结果一致性
        if control_data1 and control_data2:
            name1 = control_data1.get('Name', 'N/A')
            name2 = control_data2.get('Name', 'N/A')
            if name1 == name2:
                print(f"  ✅ 缓存一致性验证通过")
            else:
                print(f"  ⚠️ 缓存一致性异常: {name1} vs {name2}")
        
    except Exception as e:
        print(f"❌ 缓存测试失败: {e}")

def main():
    """主函数"""
    print("控件识别性能优化测试")
    print("=" * 60)
    print("🎯 目标: 将控件识别速度优化至1秒以内")
    print("📊 对比: 原版 vs 优化版性能")
    print()
    
    # 获取测试坐标
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        print(f"🖱️ 当前鼠标位置: ({mouse_x}, {mouse_y})")
        test_x, test_y = mouse_x, mouse_y
    else:
        print("⚠️ 无法获取鼠标位置，使用默认位置")
        test_x, test_y = 500, 300
    
    print(f"🎯 测试坐标: ({test_x}, {test_y})")
    print()
    
    # 执行测试
    original_stats = test_original_version(test_x, test_y, test_count=5)
    optimized_stats = test_optimized_version(test_x, test_y, test_count=5)
    
    # 对比分析
    compare_performance(original_stats, optimized_stats)
    
    # 测试缓存效果
    test_cache_effectiveness()
    
    print("\n" + "=" * 60)
    print("🎉 性能测试完成!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())