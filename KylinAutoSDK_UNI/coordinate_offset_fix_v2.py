#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标偏移诊断和修复工具 V2
修正版本：正确分析偏移方向
"""

import sys
import os
import subprocess
import time
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>in<PERSON>utoSDK_UNI/src')

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return 1000, 500
    except:
        return 1000, 500

def diagnose_coordinate_offset_v2():
    """修正版坐标偏移诊断"""
    print("🔍 坐标偏移诊断工具 V2 (修正版)")
    print("=" * 70)
    
    # 获取鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    print(f"📍 当前鼠标位置: ({mouse_x}, {mouse_y})")
    print()
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        print("🔍 正在检测控件...")
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        
        if control_data:
            print("✅ 检测到控件")
            print(f"   控件名称: {control_data.get('Name', 'N/A')}")
            print(f"   控件类型: {control_data.get('Rolename', 'N/A')}")
            print()
            
            # 获取各种坐标数据
            coords = control_data.get('Coords', {})
            rel_coords = control_data.get('RelativeCoords', {})
            
            print("📊 坐标数据分析:")
            if coords:
                coord_x = coords.get('x')
                coord_y = coords.get('y') 
                coord_w = coords.get('width')
                coord_h = coords.get('height')
                print(f"   屏幕坐标: x={coord_x}, y={coord_y}, w={coord_w}, h={coord_h}")
            
            if rel_coords:
                rel_x = rel_coords.get('x')
                rel_y = rel_coords.get('y')
                rel_w = rel_coords.get('width') 
                rel_h = rel_coords.get('height')
                print(f"   相对坐标: x={rel_x}, y={rel_y}, w={rel_w}, h={rel_h}")
            
            # AT-SPI坐标
            atspi_element = control_data.get('_atspi_element')
            atspi_x = atspi_y = atspi_w = atspi_h = None
            if atspi_element:
                try:
                    import pyatspi
                    extents = atspi_element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    atspi_x = extents.x
                    atspi_y = extents.y 
                    atspi_w = extents.width
                    atspi_h = extents.height
                    print(f"   AT-SPI坐标: x={atspi_x}, y={atspi_y}, w={atspi_w}, h={atspi_h}")
                except Exception as e:
                    print(f"   AT-SPI坐标获取失败: {e}")
            print()
            
            # 现在进行逐步偏移测试
            print("🧪 偏移测试 - 请观察哪个边框最准确对齐控件")
            print("-" * 50)
            
            # 使用最可靠的坐标数据
            test_x = coord_x if coord_x is not None else (atspi_x if atspi_x is not None else mouse_x - 50)
            test_y = coord_y if coord_y is not None else (atspi_y if atspi_y is not None else mouse_y - 25)
            test_w = coord_w if coord_w is not None else (atspi_w if atspi_w is not None else 100)
            test_h = coord_h if coord_h is not None else (atspi_h if atspi_h is not None else 50)
            
            from ultimate_highlight import ultimate_highlight
            
            # 测试1: 原始坐标
            print("1️⃣ 红色边框 = 原始坐标")
            ultimate_highlight(test_x, test_y, test_w, test_h, 3, 'red', 2)
            print(f"   位置: ({test_x}, {test_y}, {test_w}, {test_h})")
            time.sleep(3)
            
            # 测试2: Y坐标 + 不同偏移量
            offsets_to_test = [10, 20, 30, 40, 50]
            colors = ['blue', 'green', 'yellow', 'purple', 'cyan']
            
            for i, offset in enumerate(offsets_to_test):
                color = colors[i % len(colors)]
                adjusted_y = test_y + offset  # 往下偏移
                print(f"{i+2}️⃣ {color.title()}色边框 = Y坐标 + {offset}像素")
                ultimate_highlight(test_x, adjusted_y, test_w, test_h, 2, color, 2)
                print(f"   位置: ({test_x}, {adjusted_y}, {test_w}, {test_h})")
                time.sleep(2)
            
            print()
            print("🎯 测试完成！请告诉我哪个颜色的边框最准确对齐控件")
            print()
            print("颜色对应的偏移量:")
            print("  🔴 红色 = 原始坐标（无偏移）")
            for i, offset in enumerate(offsets_to_test):
                color = colors[i % len(colors)]
                emoji = {'blue': '🔵', 'green': '🟢', 'yellow': '🟡', 'purple': '🟣', 'cyan': '🔵'}
                print(f"  {emoji.get(color, '⚫')} {color.title()}色 = Y坐标 + {offset}像素")
            
            print()
            print("📝 接下来:")
            print("   1. 观察哪个颜色的边框最准确")
            print("   2. 告诉我颜色，我将应用相应的修正")
            print("   3. 例如：'绿色最准确' 或 '蓝色对齐得最好'")
            
        else:
            print("❌ 未检测到控件")
            print(f"   信息: {info}")
            print()
            print("💡 建议:")
            print("   • 将鼠标移动到按钮、文本框等控件上")
            print("   • 确保鼠标在应用窗口内")
            print("   • 重新运行此工具")
            
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()

def apply_offset_correction(offset_amount):
    """应用指定的偏移修正"""
    print(f"🔧 应用偏移修正: Y坐标 + {offset_amount}像素")
    
    # 创建修正版的高亮函数
    correction_code = f'''#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
修正坐标偏移的终极高亮函数
"""

def ultimate_highlight_corrected(x, y, width, height, duration=2, color='red', border_width=2):
    """
    修正坐标偏移的终极高亮函数
    """
    # 应用Y坐标修正
    corrected_y = y + {offset_amount}  # 往下偏移{offset_amount}像素
    
    print(f"🔧 坐标修正: 原始Y={{y}} -> 修正Y={{corrected_y}} (+{offset_amount})")
    
    # 调用原始ultimate_highlight
    import sys
    import os
    sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
    from ultimate_highlight import ultimate_highlight
    return ultimate_highlight(x, corrected_y, width, height, duration, color, border_width)

if __name__ == "__main__":
    # 测试修正效果
    import subprocess
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={{'DISPLAY': ':0'}})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            
            print(f"测试修正效果，鼠标位置: ({{mouse_x}}, {{mouse_y}})")
            ultimate_highlight_corrected(mouse_x-50, mouse_y-25, 100, 50, 3, 'orange', 2)
        else:
            ultimate_highlight_corrected(1000, 500, 200, 100, 3, 'orange', 2)
    except:
        ultimate_highlight_corrected(1000, 500, 200, 100, 3, 'orange', 2)
'''
    
    # 写入修正版函数
    with open('/home/<USER>/KylinAutoSDK_UNI/ultimate_highlight_corrected.py', 'w', encoding='utf-8') as f:
        f.write(correction_code)
    
    print("✅ 创建了修正版高亮函数: ultimate_highlight_corrected.py")
    
    # 更新UNI_new.py
    uni_file = '/home/<USER>/KylinAutoSDK_UNI/src/UNI_new.py'
    
    try:
        with open(uni_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换导入
        if 'from ultimate_highlight import ultimate_highlight' in content:
            content = content.replace(
                'from ultimate_highlight import ultimate_highlight',
                f'''# 使用修正版高亮函数解决坐标偏移问题 (Y+{offset_amount})
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
from ultimate_highlight_corrected import ultimate_highlight_corrected as ultimate_highlight'''
            )
            
            with open(uni_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
            print("✅ 已更新UNI_new.py使用修正版高亮函数")
            print(f"   修正方式: Y坐标 + {offset_amount}像素")
            
        else:
            print("⚠️ 未找到需要替换的导入语句")
            
    except Exception as e:
        print(f"❌ 修改UNI_new.py失败: {e}")
    
    print()
    print("🎉 修正完成！现在可以测试最终效果")

if __name__ == "__main__":
    print()
    print("🎯 坐标偏移诊断工具 V2")
    print()
    print("📖 使用说明:")
    print("   1. 将鼠标移动到要检测的控件上")
    print("   2. 运行此工具，观察不同颜色边框的对齐效果")
    print("   3. 告诉我哪个颜色最准确对齐")
    print("   4. 我将应用对应的修正方案")
    print()
    
    input("按回车键开始诊断...")
    print()
    
    diagnose_coordinate_offset_v2()
    
    print()
    print("请告诉我哪个颜色的边框最准确对齐控件:")
    user_choice = input("输入颜色 (红/蓝/绿/黄/紫/青): ").strip().lower()
    
    color_to_offset = {
        '红': 0, 'red': 0, '红色': 0,
        '蓝': 10, 'blue': 10, '蓝色': 10,
        '绿': 20, 'green': 20, '绿色': 20, 
        '黄': 30, 'yellow': 30, '黄色': 30,
        '紫': 40, 'purple': 40, '紫色': 40,
        '青': 50, 'cyan': 50, '青色': 50
    }
    
    if user_choice in color_to_offset:
        offset = color_to_offset[user_choice]
        print(f"✅ 您选择了偏移量: +{offset}像素")
        apply_offset_correction(offset)
    else:
        print("⚠️ 未识别的颜色选择，请重新运行工具")