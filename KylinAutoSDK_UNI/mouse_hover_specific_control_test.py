#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
基于鼠标悬停位置的具体控件检测测试
实时检测鼠标位置下的具体控件并高亮显示
"""

import sys
import os
import subprocess
import time
import signal
from specific_control_detector import SpecificControlDetector


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


class MouseHoverControlTester:
    """基于鼠标悬停的控件测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.detector = SpecificControlDetector()
        self.last_mouse_pos = (0, 0)
        self.last_detection_time = 0
        self.detection_cooldown = 1.0  # 1秒检测间隔
        self.is_running = True
        self.last_detected_control = None
        
        print("🖱️  鼠标悬停控件检测器")
        print("=" * 60)
        print("📖 功能说明:")
        print("   ✅ 实时检测鼠标位置下的具体控件")
        print("   ✅ 自动高亮检测到的控件")
        print("   ✅ 显示详细的控件信息")
        print("   ✅ 排除filler等容器控件")
        print("=" * 60)
        
        if self.detector.window_offset:
            print(f"🪟 窗口偏移: {self.detector.window_offset}")
        else:
            print("❌ 无法获取窗口偏移")
            
        print("🖱️  将鼠标移动到hellobig程序的控件上...")
        print("🛑 按 Ctrl+C 退出程序")
        print("=" * 60)
    
    def should_detect_at_position(self, x, y):
        """判断是否应该在此位置进行检测"""
        current_time = time.time()
        
        # 检查时间间隔
        if current_time - self.last_detection_time < self.detection_cooldown:
            return False
        
        # 检查鼠标移动距离
        last_x, last_y = self.last_mouse_pos
        distance = ((x - last_x) ** 2 + (y - last_y) ** 2) ** 0.5
        
        return distance >= 10  # 移动超过10像素才检测
    
    def detect_and_highlight_control(self, x, y):
        """检测并高亮鼠标位置的控件"""
        print(f"\n🔍 检测鼠标位置: ({x}, {y})")
        print("-" * 40)
        
        try:
            # 查找最具体的控件
            ctrl = self.detector.find_most_specific_control_at_point(x, y)
            
            if ctrl:
                info = self.detector.get_control_info(ctrl)
                
                # 检查是否与上次检测到的控件相同
                if (self.last_detected_control and 
                    self.last_detected_control['name'] == info['name'] and
                    self.last_detected_control['role'] == info['role'] and
                    self.last_detected_control['position'] == info['position']):
                    print(f"🔄 相同控件，跳过重复显示")
                    return
                
                self.last_detected_control = info
                
                print(f"✅ 检测成功!")
                print(f"📋 控件信息:")
                print(f"   名称: {info['name']}")
                print(f"   类型: {info['role']}")
                print(f"   位置: {info['position']}")
                print(f"   尺寸: {info['size']}")
                print(f"   深度: {info['depth']}")
                
                if info['states']:
                    print(f"   状态: {', '.join(info['states'][:3])}")
                
                if info['actions']:
                    print(f"   动作: {', '.join(info['actions'])}")
                
                # 执行高亮
                print(f"\n✨ 执行高亮...")
                highlight_success = self.detector.highlight_specific_control(
                    x, y, duration=2, color='cyan', border_width=2
                )
                
                if highlight_success:
                    print(f"✅ 高亮成功!")
                else:
                    print(f"❌ 高亮失败")
                
                # 验证控件类型
                self._analyze_control_type(info)
                
            else:
                print(f"❌ 未找到具体控件")
                self.last_detected_control = None
                
        except Exception as e:
            print(f"❌ 检测异常: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 40)
    
    def _analyze_control_type(self, info):
        """分析控件类型"""
        role = info['role']
        
        # 控件类型分析
        if role == 'push button':
            print(f"🔘 这是一个按钮控件，可以点击执行操作")
        elif role == 'check box':
            print(f"☑️  这是一个复选框控件，可以切换选中状态")
        elif role == 'radio button':
            print(f"🔘 这是一个单选按钮控件，与其他单选按钮互斥")
        elif role == 'text':
            print(f"📝 这是一个文本输入框，可以输入文字")
        elif role == 'password text':
            print(f"🔒 这是一个密码输入框，输入内容会被隐藏")
        elif role == 'slider':
            print(f"🎚️  这是一个滑块控件，可以拖动调整数值")
        elif role == 'spin button':
            print(f"🔢 这是一个数字输入框，可以输入或调整数字")
        elif role == 'combo box':
            print(f"📋 这是一个下拉框控件，可以选择选项")
        elif role == 'list item':
            print(f"📄 这是一个列表项，可以选择")
        elif role == 'label':
            print(f"🏷️  这是一个标签控件，用于显示文本")
        else:
            print(f"🔍 这是一个 {role} 控件")
        
        # 动作分析
        actions = info.get('actions', [])
        if 'Press' in actions:
            print(f"   💡 可以执行点击操作")
        if 'Toggle' in actions:
            print(f"   💡 可以执行切换操作")
        if 'SetFocus' in actions:
            print(f"   💡 可以获得焦点")
        if 'Increase' in actions or 'Decrease' in actions:
            print(f"   💡 可以调整数值")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 接收到信号 {signum}，正在退出...")
        self.is_running = False
    
    def run(self):
        """运行检测器"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            print("🚀 开始监控鼠标位置...")
            
            while self.is_running:
                current_pos = get_mouse_position()
                
                if current_pos == (None, None):
                    time.sleep(0.5)
                    continue
                
                x, y = current_pos
                
                if self.should_detect_at_position(x, y):
                    self.detect_and_highlight_control(x, y)
                    self.last_mouse_pos = (x, y)
                    self.last_detection_time = time.time()
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 接收到中断信号，正在退出...")
        except Exception as e:
            print(f"\n❌ 运行异常: {e}")
        finally:
            self.is_running = False
            print("👋 检测器已停止")


class InteractiveControlTester:
    """交互式控件测试器"""
    
    def __init__(self):
        """初始化"""
        self.detector = SpecificControlDetector()
        
        print("🎮 交互式控件检测器")
        print("=" * 60)
        
        if not self.detector.window_offset:
            print("❌ 无法获取窗口偏移")
            return
        
        print(f"🪟 窗口偏移: {self.detector.window_offset}")
    
    def test_current_mouse_position(self):
        """测试当前鼠标位置"""
        pos = get_mouse_position()
        if pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return
        
        x, y = pos
        print(f"\n🖱️  当前鼠标位置: ({x}, {y})")
        
        # 检测控件
        ctrl = self.detector.find_most_specific_control_at_point(x, y)
        
        if ctrl:
            info = self.detector.get_control_info(ctrl)
            
            print(f"✅ 检测到控件:")
            print(f"   名称: {info['name']}")
            print(f"   类型: {info['role']}")
            print(f"   位置: {info['position']}")
            print(f"   尺寸: {info['size']}")
            
            # 询问是否高亮
            try:
                choice = input("\n是否高亮此控件? (y/n): ").strip().lower()
                if choice == 'y':
                    self.detector.highlight_specific_control(x, y, duration=3, color='yellow', border_width=3)
            except KeyboardInterrupt:
                pass
        else:
            print("❌ 未检测到具体控件")
    
    def interactive_mode(self):
        """交互模式"""
        if not self.detector.window_offset:
            return
        
        print("\n🎮 交互模式:")
        print("1. 测试当前鼠标位置")
        print("2. 启动实时监控模式")
        print("3. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-3): ").strip()
                
                if choice == '1':
                    self.test_current_mouse_position()
                elif choice == '2':
                    hover_tester = MouseHoverControlTester()
                    hover_tester.run()
                elif choice == '3':
                    print("👋 退出")
                    break
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n👋 退出")
                break


def main():
    """主函数"""
    print("🖱️  鼠标悬停控件检测测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        tester = InteractiveControlTester()
        tester.interactive_mode()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
