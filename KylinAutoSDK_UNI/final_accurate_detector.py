#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
最终准确的控件检测器
使用验证过的正确偏移: (567, 192)
"""

import sys
import os
import subprocess
import time
import pyatspi


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


class FinalAccurateDetector:
    """最终准确的控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        # 使用验证过的正确偏移
        self.correct_offset = (567, 192)
        self.all_controls = []
        
        # 具体控件类型
        self.specific_control_types = {
            'push button', 'button', 'toggle button',
            'check box', 'radio button', 
            'text', 'password text', 'entry',
            'combo box', 'list', 'list item',
            'slider', 'spin button', 'scroll bar',
            'menu item', 'tab', 'tree item',
            'label', 'image', 'progress bar'
        }
        
        print("🎯 最终准确的控件检测器")
        print("=" * 60)
        print(f"✅ 使用验证过的偏移: {self.correct_offset}")
    
    def refresh_controls(self):
        """刷新控件缓存"""
        print("🔄 刷新控件缓存...")
        
        self.all_controls = []
        
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"
                
                if 'hellobig' in app_name.lower():
                    app_controls = []
                    self._collect_controls_recursive(app, app_controls, max_depth=6)
                    
                    if app_controls:
                        print(f"   📱 {app_name}: {len(app_controls)} 个控件")
                        self.all_controls.extend(app_controls)
                    break
            
            print(f"✅ 缓存完成，总共 {len(self.all_controls)} 个控件")
            
        except Exception as e:
            print(f"❌ 刷新缓存失败: {e}")
    
    def _collect_controls_recursive(self, element, control_list, depth=0, max_depth=6):
        """递归收集控件"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 收集所有有坐标的控件
            if extents and extents.width > 0 and extents.height > 0:
                control_list.append({
                    'element': element,
                    'name': name,
                    'role': role,
                    'extents': extents,
                    'depth': depth
                })
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_controls_recursive(child, control_list, depth + 1, max_depth)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def find_control_at_mouse_position(self, mouse_x, mouse_y):
        """在鼠标位置查找控件"""
        print(f"\n🔍 在鼠标位置 ({mouse_x}, {mouse_y}) 查找控件")
        
        if not self.all_controls:
            self.refresh_controls()
        
        # 转换为AT-SPI坐标
        atspi_x = mouse_x - self.correct_offset[0]
        atspi_y = mouse_y - self.correct_offset[1]
        
        print(f"   转换为AT-SPI坐标: ({atspi_x}, {atspi_y})")
        print(f"   使用偏移: {self.correct_offset}")
        
        # 查找匹配的控件
        matches = []
        for ctrl in self.all_controls:
            ext = ctrl['extents']
            
            # 检查点是否在控件范围内
            if (ext.x <= atspi_x < ext.x + ext.width and
                ext.y <= atspi_y < ext.y + ext.height):
                matches.append(ctrl)
        
        if not matches:
            print("❌ 未找到匹配的控件")
            return None
        
        print(f"   找到 {len(matches)} 个匹配的控件:")
        for i, match in enumerate(matches):
            print(f"     {i+1}. {match['name']} ({match['role']}) - 面积: {match['extents'].width * match['extents'].height}")
        
        # 选择最具体的控件（优先选择具体控件类型，然后按面积最小）
        specific_matches = [m for m in matches if m['role'] in self.specific_control_types]
        
        if specific_matches:
            best_match = min(specific_matches, key=lambda x: x['extents'].width * x['extents'].height)
            print(f"   ✅ 选择最具体的控件: {best_match['name']} ({best_match['role']})")
            return best_match
        else:
            # 如果没有具体控件，选择面积最小的
            best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
            print(f"   ⚠️  选择面积最小的控件: {best_match['name']} ({best_match['role']})")
            return best_match
    
    def get_control_info(self, ctrl):
        """获取控件详细信息"""
        try:
            # 获取状态
            states = []
            try:
                state = ctrl['element'].getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass
            
            # 获取动作
            actions = []
            try:
                if hasattr(ctrl['element'], 'queryAction'):
                    action = ctrl['element'].queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass
            
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': states,
                'actions': actions,
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
        except Exception:
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': [],
                'actions': [],
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
    
    def highlight_control_accurately(self, ctrl, duration=3, color='green', border_width=3):
        """准确高亮控件"""
        if not ctrl:
            return False
        
        ext = ctrl['extents']
        
        # 计算控件在屏幕上的真实位置
        screen_x = ext.x + self.correct_offset[0]
        screen_y = ext.y + self.correct_offset[1]
        width = ext.width
        height = ext.height
        
        print(f"\n✨ 准确高亮控件:")
        print(f"   控件: {ctrl['name']} ({ctrl['role']})")
        print(f"   AT-SPI坐标: ({ext.x}, {ext.y}) {width}×{height}")
        print(f"   屏幕坐标: ({screen_x}, {screen_y}) {width}×{height}")
        
        try:
            from ultimate_highlight import ultimate_highlight
            highlight_success = ultimate_highlight(
                screen_x, screen_y, width, height,
                duration=duration, color=color, border_width=border_width
            )
            
            if highlight_success:
                print(f"✅ 高亮成功!")
                return True
            else:
                print(f"❌ 高亮失败")
                return False
                
        except Exception as e:
            print(f"❌ 高亮异常: {e}")
            return False
    
    def test_mouse_detection_and_highlight(self):
        """测试鼠标检测和高亮"""
        mouse_pos = get_mouse_position()
        if mouse_pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return False
        
        mouse_x, mouse_y = mouse_pos
        
        print(f"🖱️  当前鼠标位置: ({mouse_x}, {mouse_y})")
        
        # 查找控件
        ctrl = self.find_control_at_mouse_position(mouse_x, mouse_y)
        
        if not ctrl:
            print("❌ 未找到控件")
            return False
        
        # 显示控件信息
        info = self.get_control_info(ctrl)
        
        print(f"\n📋 控件信息:")
        print(f"   名称: {info['name']}")
        print(f"   类型: {info['role']}")
        print(f"   位置: {info['position']}")
        print(f"   尺寸: {info['size']}")
        print(f"   深度: {info['depth']}")
        
        if info['states']:
            print(f"   状态: {', '.join(info['states'][:3])}")
        
        if info['actions']:
            print(f"   动作: {', '.join(info['actions'])}")
        
        # 执行准确的高亮
        return self.highlight_control_accurately(ctrl, duration=3, color='cyan', border_width=3)
    
    def interactive_test(self):
        """交互式测试"""
        print(f"\n🎮 交互式测试模式:")
        print("=" * 40)
        print("1. 测试当前鼠标位置")
        print("2. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-2): ").strip()
                
                if choice == '1':
                    self.test_mouse_detection_and_highlight()
                elif choice == '2':
                    print("👋 退出测试")
                    break
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n👋 退出测试")
                break


def main():
    """主函数"""
    print("🎯 最终准确的控件检测器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        detector = FinalAccurateDetector()
        detector.interactive_test()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
