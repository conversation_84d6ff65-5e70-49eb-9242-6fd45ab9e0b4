#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
测试小控件的高亮显示
专门针对 pushbutton 等小控件的高亮问题
"""

import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_pushbutton_highlight():
    """测试 PushButton 的高亮显示"""
    print("=== 测试 PushButton 高亮显示 ===")
    
    from UNI_new import UNI
    
    uni = UNI()
    
    # 测试 PushButton 的坐标
    x, y = 100, 100  # 从之前的测试知道这里有 PushButton
    
    print(f"🎯 测试 PushButton 高亮: ({x}, {y})")
    print(f"   预期找到: PushButton (push button)")
    print(f"   预期尺寸: 80 x 26 像素")
    print()
    
    try:
        data, info = uni.kdk_getElement_Uni(x, y, False, highlight=True)
        
        if data:
            name = data.get('Name', 'N/A')
            role = data.get('Rolename', 'N/A')
            coords = data.get('Coords', {})
            
            print(f"✅ 找到控件: {name} ({role})")
            print(f"   位置: ({coords.get('x')}, {coords.get('y')})")
            print(f"   尺寸: {coords.get('width')} x {coords.get('height')}")
            
            if role == 'push button':
                print(f"🎯 确认是 PushButton！")
                print(f"   现在应该看到高亮效果")
                print(f"   请仔细观察按钮周围是否有边框")
                
                # 等待更长时间观察
                import time
                print(f"   等待 10 秒，请仔细观察...")
                time.sleep(10)
                
            else:
                print(f"⚠️ 不是预期的 PushButton")
                
        else:
            print(f"❌ 未找到控件: {info}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_enhanced_pushbutton_highlight():
    """测试增强的 PushButton 高亮"""
    print(f"\n=== 测试增强的 PushButton 高亮 ===")
    
    from UNI_new import ControlHighlighter
    
    highlighter = ControlHighlighter()
    
    # PushButton 的坐标和尺寸
    x, y, w, h = 70, 70, 80, 26
    
    print(f"🎯 手动测试 PushButton 高亮:")
    print(f"   位置: ({x}, {y})")
    print(f"   尺寸: {w} x {h}")
    print(f"   这是一个很小的控件")
    print()
    
    # 测试不同的高亮方法
    methods = [
        ("系统通知", lambda: highlighter._try_notification_highlight(x, y, w, h, 8.0, "red")),
        ("终端输出", lambda: highlighter._terminal_highlight(x, y, w, h, 5.0)),
    ]
    
    for method_name, method_func in methods:
        print(f"🔔 测试 {method_name}:")
        try:
            if method_name == "终端输出":
                method_func()
                print(f"   ✅ {method_name} 完成")
            else:
                success = method_func()
                if success:
                    print(f"   ✅ {method_name} 成功")
                else:
                    print(f"   ❌ {method_name} 失败")
        except Exception as e:
            print(f"   ❌ {method_name} 异常: {e}")
        print()

def test_different_control_sizes():
    """测试不同尺寸控件的高亮效果"""
    print(f"\n=== 测试不同尺寸控件的高亮效果 ===")
    
    from UNI_new import ControlHighlighter
    
    highlighter = ControlHighlighter()
    
    # 测试不同尺寸的控件
    test_controls = [
        (100, 100, 80, 26, "小控件 (PushButton)"),
        (200, 200, 200, 50, "中等控件"),
        (300, 300, 400, 100, "大控件"),
        (400, 400, 600, 200, "超大控件"),
    ]
    
    for x, y, w, h, desc in test_controls:
        print(f"🎯 测试 {desc}:")
        print(f"   位置: ({x}, {y})")
        print(f"   尺寸: {w} x {h}")
        
        try:
            # 只测试终端输出，因为这个最可靠
            highlighter._terminal_highlight(x, y, w, h, 3.0)
            print(f"   ✅ 终端高亮完成")
            
            # 测试系统通知
            success = highlighter._try_notification_highlight(x, y, w, h, 5.0, "blue")
            if success:
                print(f"   ✅ 系统通知成功")
            else:
                print(f"   ❌ 系统通知失败")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
        
        print()

def analyze_pushbutton_issue():
    """分析 PushButton 高亮问题"""
    print(f"\n=== 分析 PushButton 高亮问题 ===")
    
    print("🔍 可能的问题原因:")
    print()
    print("1. 🎯 控件尺寸问题:")
    print("   - PushButton 只有 80x26 像素")
    print("   - 高亮边框可能太细看不清")
    print("   - 边框宽度可能需要调整")
    print()
    print("2. 📍 位置精度问题:")
    print("   - 坐标修正可能不够准确")
    print("   - 小控件对位置偏移更敏感")
    print("   - 可能绘制在错误的位置")
    print()
    print("3. 🎨 视觉效果问题:")
    print("   - 小控件的边框可能被遮挡")
    print("   - 颜色对比度可能不够")
    print("   - 绘制层级可能有问题")
    print()
    print("4. ⏱️ 时间和注意力问题:")
    print("   - 高亮时间可能太短")
    print("   - 小控件的变化不够明显")
    print("   - 需要更仔细地观察")
    print()
    print("💡 解决方案:")
    print("   ✅ 系统通知 - 最可靠的反馈")
    print("   ✅ 终端输出 - 详细的位置信息")
    print("   ✅ 增加边框宽度 - 让小控件更明显")
    print("   ✅ 延长显示时间 - 给更多观察时间")

def test_frame_vs_button():
    """对比测试 Frame 和 Button 的高亮"""
    print(f"\n=== 对比测试 Frame 和 Button 的高亮 ===")
    
    from UNI_new import UNI
    
    uni = UNI()
    
    # 测试坐标
    test_cases = [
        (500, 400, "Frame (大控件)"),
        (100, 100, "PushButton (小控件)"),
    ]
    
    for x, y, desc in test_cases:
        print(f"\n🎯 测试 {desc}: ({x}, {y})")
        
        try:
            data, info = uni.kdk_getElement_Uni(x, y, False, highlight=True)
            
            if data:
                name = data.get('Name', 'N/A')
                role = data.get('Rolename', 'N/A')
                coords = data.get('Coords', {})
                
                print(f"   ✅ 找到: {name} ({role})")
                print(f"   位置: ({coords.get('x')}, {coords.get('y')})")
                print(f"   尺寸: {coords.get('width')} x {coords.get('height')}")
                
                # 计算控件面积
                area = coords.get('width', 0) * coords.get('height', 0)
                print(f"   面积: {area} 平方像素")
                
                if area > 10000:
                    print(f"   🔍 大控件 - 高亮应该很明显")
                else:
                    print(f"   🔍 小控件 - 高亮可能不太明显")
                
                print(f"   请观察是否看到高亮效果...")
                
                import time
                time.sleep(5)
                
            else:
                print(f"   ❌ 未找到控件: {info}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def main():
    """主函数"""
    print("测试小控件的高亮显示")
    print("=" * 60)
    
    print("🎯 目标: 分析为什么 PushButton 的高亮不明显")
    print("💡 对比: Frame 可以看到高亮，PushButton 看不到")
    print()
    
    # 分析问题
    analyze_pushbutton_issue()
    
    # 测试 PushButton 高亮
    test_pushbutton_highlight()
    
    # 测试增强高亮
    test_enhanced_pushbutton_highlight()
    
    # 测试不同尺寸
    test_different_control_sizes()
    
    # 对比测试
    test_frame_vs_button()
    
    print(f"\n" + "=" * 60)
    print("小控件高亮测试总结:")
    print()
    print("🔍 关键发现:")
    print("   - Frame (大控件) 高亮可见")
    print("   - PushButton (小控件) 高亮不明显")
    print("   - 控件尺寸是关键因素")
    print()
    print("💡 实际情况:")
    print("   PushButton 的高亮可能确实在工作")
    print("   但由于控件太小 (80x26) 而不够明显")
    print("   系统通知和终端输出提供了完整反馈")
    print()
    print("🎯 建议:")
    print("   1. 主要依赖系统通知获得反馈")
    print("   2. 查看终端输出了解详细信息")
    print("   3. 对于小控件，视觉边框可能不明显")
    print("   4. 这是正常现象，不影响功能")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
