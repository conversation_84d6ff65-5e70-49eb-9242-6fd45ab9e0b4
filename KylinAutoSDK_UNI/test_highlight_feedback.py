#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
专门测试高亮反馈功能
确认系统通知和终端输出是否正常工作
"""

import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_highlight_feedback():
    """测试高亮反馈功能"""
    print("=== 测试高亮反馈功能 ===")
    
    from UNI_new import UNI
    
    uni = UNI()
    
    # 使用一个已知的有效坐标进行测试
    test_coords = [
        (1067, 621, "hellobig 按钮"),
        (500, 400, "屏幕中央"),
        (100, 100, "屏幕左上角"),
    ]
    
    for x, y, desc in test_coords:
        print(f"\n{'='*60}")
        print(f"测试坐标: ({x}, {y}) - {desc}")
        print(f"{'='*60}")
        
        print(f"🎯 启动带高亮的控件识别...")
        print(f"   坐标: ({x}, {y})")
        print(f"   描述: {desc}")
        print()
        print(f"📱 请注意观察:")
        print(f"   1. 系统通知是否弹出")
        print(f"   2. 下方是否有红色边框的终端输出")
        print(f"   3. 是否有详细的控件信息")
        print()
        
        try:
            # 调用带高亮的控件识别
            data, info = uni.kdk_getElement_Uni(x, y, False, highlight=True)
            
            if data:
                name = data.get('Name', 'N/A')
                role = data.get('Rolename', 'N/A')
                
                print(f"✅ 控件识别成功:")
                print(f"   名称: {name}")
                print(f"   类型: {role}")
                print(f"   坐标: ({x}, {y})")
                
                coords = data.get('Coords', {})
                print(f"   位置: ({coords.get('x')}, {coords.get('y')})")
                print(f"   尺寸: {coords.get('width')} x {coords.get('height')}")
                
                print(f"\n🎯 高亮反馈应该包括:")
                print(f"   📱 系统通知: 2个通知窗口")
                print(f"   📝 终端输出: 上方的红色边框显示")
                print(f"   ✅ 状态确认: 明确的成功标识")
                
            else:
                print(f"❌ 未找到控件: {info}")
                print(f"   但仍然应该看到终端输出的高亮反馈")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 等待用户观察
        print(f"\n⏳ 等待 3 秒，请观察高亮效果...")
        import time
        time.sleep(3)

def test_individual_highlight_methods():
    """测试各种高亮方法"""
    print(f"\n{'='*60}")
    print(f"测试各种高亮方法")
    print(f"{'='*60}")
    
    from UNI_new import ControlHighlighter
    
    highlighter = ControlHighlighter()
    
    # 测试坐标
    x, y, w, h = 500, 300, 120, 40
    
    print(f"🔔 测试1: 增强系统通知")
    print(f"   应该看到2个系统通知弹出")
    try:
        success = highlighter._try_notification_highlight(x, y, w, h, 5.0, "red")
        if success:
            print(f"   ✅ 系统通知发送成功")
        else:
            print(f"   ❌ 系统通知发送失败")
    except Exception as e:
        print(f"   ❌ 系统通知异常: {e}")
    
    print(f"\n📝 测试2: 增强终端输出")
    print(f"   应该看到下方的红色边框显示")
    try:
        highlighter._terminal_highlight(x, y, w, h, 3.0)
        print(f"   ✅ 终端输出完成")
    except Exception as e:
        print(f"   ❌ 终端输出异常: {e}")

def explain_highlight_system():
    """解释高亮系统"""
    print(f"\n{'='*60}")
    print(f"高亮反馈系统说明")
    print(f"{'='*60}")
    
    print("🎯 当前的高亮反馈系统:")
    print()
    print("📱 系统通知 (主要视觉反馈):")
    print("   ✅ 第1个通知: 控件基本信息")
    print("   ✅ 第2个通知: 详细坐标范围")
    print("   ✅ 彩色图标: 根据颜色选择")
    print("   ✅ 持续显示: 5-8秒")
    print()
    print("📝 终端输出 (详细信息):")
    print("   ✅ 红色边框: 醒目的🔴标识")
    print("   ✅ 详细信息: 坐标、尺寸、面积")
    print("   ✅ ASCII艺术: 可视化边框")
    print("   ✅ 状态确认: 明确的成功标识")
    print()
    print("❌ 不可用的方法:")
    print("   ❌ GTK 窗口: 在您的环境下无法显示")
    print("   ❌ 视觉边框: 被窗口管理器阻止")
    print("   ❌ 覆盖显示: Wayland 环境限制")
    print()
    print("💡 为什么这样设计:")
    print("   - 系统通知最可靠，所有环境都支持")
    print("   - 终端输出提供详细技术信息")
    print("   - 不依赖复杂的图形窗口系统")
    print("   - 确保用户能获得完整反馈")

def check_notification_system():
    """检查通知系统"""
    print(f"\n{'='*60}")
    print(f"检查通知系统")
    print(f"{'='*60}")
    
    import subprocess
    
    # 检查 notify-send 是否可用
    try:
        result = subprocess.run(['which', 'notify-send'], capture_output=True)
        if result.returncode == 0:
            print("✅ notify-send 可用")
            
            # 发送测试通知
            print("📱 发送测试通知...")
            result = subprocess.run([
                'notify-send', 
                '--icon', 'dialog-information',
                '--expire-time', '3000',
                '🔔 高亮测试通知', 
                '如果您看到这个通知，说明通知系统正常工作'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 测试通知发送成功")
                print("   您应该看到一个测试通知")
            else:
                print(f"❌ 测试通知发送失败: {result.stderr}")
        else:
            print("❌ notify-send 不可用")
            
    except Exception as e:
        print(f"❌ 通知系统检查失败: {e}")

def main():
    """主函数"""
    print("测试高亮反馈功能")
    print("=" * 60)
    
    print("🎯 目标: 确认高亮反馈是否正常工作")
    print("💡 重点: 系统通知 + 终端输出")
    print()
    
    # 解释高亮系统
    explain_highlight_system()
    
    # 检查通知系统
    check_notification_system()
    
    # 测试各种高亮方法
    test_individual_highlight_methods()
    
    # 测试高亮反馈
    test_highlight_feedback()
    
    print(f"\n" + "=" * 60)
    print("高亮反馈测试总结:")
    print()
    print("🔍 如果您看到了:")
    print("   ✅ 系统通知弹出 - 高亮系统正常")
    print("   ✅ 红色终端输出 - 反馈系统正常")
    print("   ✅ 详细控件信息 - 识别系统正常")
    print()
    print("🔍 如果您没有看到:")
    print("   📱 系统通知 - 可能被禁用或阻止")
    print("   📝 终端输出 - 应该总是可见的")
    print("   🔧 需要检查系统设置")
    print()
    print("💡 重要提醒:")
    print("   在您的环境下，GTK 窗口无法显示")
    print("   这是正常的，不影响功能")
    print("   系统通知和终端输出已经提供了")
    print("   完整的控件识别和高亮反馈")
    
    print(f"\n🎯 使用建议:")
    print(f"   现在使用 test_enhanced_control_detection.py")
    print(f"   将鼠标悬停在项目树节点上")
    print(f"   应该能看到系统通知和终端输出")
    print(f"   这就是完整的高亮反馈效果")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
