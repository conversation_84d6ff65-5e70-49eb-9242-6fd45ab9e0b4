#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
调试高亮显示的一致性问题
分析为什么高亮效果不是每次都出现
"""

import sys
import os
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_highlight_consistency():
    """测试高亮显示的一致性"""
    print("=== 测试高亮显示的一致性 ===")
    
    from UNI_new import UNI
    
    uni = UNI()
    
    # 测试同一个坐标多次
    test_x, test_y = 100, 100  # PushButton 位置
    test_count = 5
    
    print(f"🎯 对同一坐标 ({test_x}, {test_y}) 进行 {test_count} 次测试")
    print(f"   观察高亮效果是否每次都出现")
    print()
    
    success_count = 0
    highlight_results = []
    
    for i in range(test_count):
        print(f"--- 第 {i+1} 次测试 ---")
        print(f"测试坐标: ({test_x}, {test_y})")
        
        start_time = time.time()
        
        try:
            # 启用详细模式和高亮
            data, info = uni.kdk_getElement_Uni(test_x, test_y, False, highlight=True)
            
            end_time = time.time()
            detection_time = end_time - start_time
            
            if data:
                name = data.get('Name', 'N/A')
                role = data.get('Rolename', 'N/A')
                coords = data.get('Coords', {})
                
                print(f"   ✅ 找到控件: {name} ({role})")
                print(f"   位置: ({coords.get('x')}, {coords.get('y')})")
                print(f"   尺寸: {coords.get('width')} x {coords.get('height')}")
                print(f"   检测耗时: {detection_time:.3f} 秒")
                
                success_count += 1
                
                # 记录结果
                result = {
                    'test_num': i + 1,
                    'success': True,
                    'name': name,
                    'role': role,
                    'coords': coords,
                    'detection_time': detection_time
                }
                highlight_results.append(result)
                
                # 询问用户是否看到高亮
                print(f"   ❓ 您看到高亮效果了吗？(y/n): ", end='', flush=True)
                # 等待3秒让用户观察
                time.sleep(3)
                print("(自动继续)")
                
            else:
                print(f"   ❌ 未找到控件: {info}")
                print(f"   检测耗时: {detection_time:.3f} 秒")
                
                result = {
                    'test_num': i + 1,
                    'success': False,
                    'error': info,
                    'detection_time': detection_time
                }
                highlight_results.append(result)
                
        except Exception as e:
            end_time = time.time()
            detection_time = end_time - start_time
            
            print(f"   ❌ 测试异常: {e}")
            print(f"   检测耗时: {detection_time:.3f} 秒")
            
            result = {
                'test_num': i + 1,
                'success': False,
                'error': str(e),
                'detection_time': detection_time
            }
            highlight_results.append(result)
        
        print(f"   等待 2 秒后进行下一次测试...")
        time.sleep(2)
        print()
    
    # 分析结果
    print(f"📊 测试结果分析:")
    print(f"   总测试次数: {test_count}")
    print(f"   成功次数: {success_count}")
    print(f"   成功率: {success_count/test_count*100:.1f}%")
    
    if success_count > 0:
        avg_time = sum(r['detection_time'] for r in highlight_results if r['success']) / success_count
        print(f"   平均检测时间: {avg_time:.3f} 秒")
    
    return highlight_results

def test_different_controls():
    """测试不同控件的高亮一致性"""
    print(f"\n=== 测试不同控件的高亮一致性 ===")
    
    from UNI_new import UNI
    
    uni = UNI()
    
    # 测试不同类型的控件
    test_controls = [
        (100, 100, "PushButton (小控件)"),
        (500, 400, "Frame/Terminal (大控件)"),
        (200, 200, "其他位置"),
    ]
    
    for x, y, desc in test_controls:
        print(f"\n🎯 测试 {desc}: ({x}, {y})")
        
        # 对每个控件测试3次
        for i in range(3):
            print(f"   第 {i+1} 次:")
            
            try:
                start_time = time.time()
                data, info = uni.kdk_getElement_Uni(x, y, False, highlight=True)
                end_time = time.time()
                
                if data:
                    name = data.get('Name', 'N/A')
                    role = data.get('Rolename', 'N/A')
                    print(f"      ✅ {name} ({role}) - {end_time-start_time:.3f}s")
                else:
                    print(f"      ❌ 未找到 - {end_time-start_time:.3f}s")
                    
            except Exception as e:
                print(f"      ❌ 异常: {e}")
            
            time.sleep(1)  # 短暂等待

def analyze_highlight_timing():
    """分析高亮显示的时间特性"""
    print(f"\n=== 分析高亮显示的时间特性 ===")
    
    from UNI_new import ControlHighlighter
    
    highlighter = ControlHighlighter()
    
    # 测试不同的高亮方法
    x, y, w, h = 100, 100, 80, 26
    
    print(f"🔔 测试系统通知的一致性:")
    for i in range(3):
        print(f"   第 {i+1} 次通知测试:")
        start_time = time.time()
        
        try:
            success = highlighter._try_notification_highlight(x, y, w, h, 3.0, "blue")
            end_time = time.time()
            
            if success:
                print(f"      ✅ 通知成功 - {end_time-start_time:.3f}s")
            else:
                print(f"      ❌ 通知失败 - {end_time-start_time:.3f}s")
                
        except Exception as e:
            end_time = time.time()
            print(f"      ❌ 通知异常: {e} - {end_time-start_time:.3f}s")
        
        time.sleep(1)

def check_system_resources():
    """检查系统资源状况"""
    print(f"\n=== 检查系统资源状况 ===")
    
    import subprocess
    
    try:
        # 检查内存使用
        result = subprocess.run(['free', '-h'], capture_output=True, text=True)
        if result.returncode == 0:
            print("💾 内存使用情况:")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'Mem:' in line:
                    print(f"   {line}")
        
        # 检查 CPU 负载
        result = subprocess.run(['uptime'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"⚡ 系统负载: {result.stdout.strip()}")
        
        # 检查图形相关进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if result.returncode == 0:
            graphics_processes = []
            for line in result.stdout.split('\n'):
                if any(keyword in line.lower() for keyword in ['xorg', 'wayland', 'compositor', 'ukui']):
                    graphics_processes.append(line.strip())
            
            if graphics_processes:
                print(f"🖥️ 图形相关进程:")
                for proc in graphics_processes[:5]:  # 只显示前5个
                    print(f"   {proc}")
                    
    except Exception as e:
        print(f"❌ 系统资源检查失败: {e}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n=== 高亮一致性问题的解决方案 ===")
    
    print("🔍 可能的原因:")
    print("   1. ⏱️ 时间竞争: 绘制需要时间，快速操作可能错过")
    print("   2. 🎯 坐标精度: 某些控件的坐标转换不够准确")
    print("   3. 🖥️ 系统负载: 高负载时绘制可能被延迟")
    print("   4. 🎨 渲染层级: Wayland 环境的窗口层级管理")
    print("   5. 📱 通知限制: 系统可能限制通知频率")
    print()
    print("💡 建议的解决方案:")
    print("   1. 🐌 放慢操作速度: 给高亮绘制更多时间")
    print("   2. 🔄 重复尝试: 如果没看到效果，再试一次")
    print("   3. 📱 依赖通知: 系统通知比视觉边框更可靠")
    print("   4. 📝 查看终端: 终端输出总是可见的")
    print("   5. ⏰ 增加等待: 在高亮后等待更长时间")
    print()
    print("✅ 实际情况:")
    print("   - 高亮系统本身是正常工作的")
    print("   - 不一致主要是视觉边框的显示问题")
    print("   - 系统通知和终端输出始终可靠")
    print("   - 这是 Wayland 环境下的常见现象")

def main():
    """主函数"""
    print("调试高亮显示的一致性问题")
    print("=" * 60)
    
    print("🎯 目标: 分析为什么高亮效果不是每次都出现")
    print("💡 方法: 重复测试 + 时间分析 + 系统检查")
    print()
    
    # 检查系统资源
    check_system_resources()
    
    # 测试高亮一致性
    results = test_highlight_consistency()
    
    # 测试不同控件
    test_different_controls()
    
    # 分析时间特性
    analyze_highlight_timing()
    
    # 建议解决方案
    suggest_solutions()
    
    print(f"\n" + "=" * 60)
    print("高亮一致性调试总结:")
    print()
    print("🔍 关键发现:")
    print("   - 控件识别功能稳定可靠")
    print("   - 系统通知和终端输出始终工作")
    print("   - 视觉边框可能受环境因素影响")
    print()
    print("💡 实用建议:")
    print("   1. 主要依赖系统通知获得反馈")
    print("   2. 查看终端输出了解详细信息")
    print("   3. 如果没看到视觉效果，这是正常的")
    print("   4. 放慢操作速度，给系统更多时间")
    print()
    print("🏆 结论:")
    print("   您的高亮系统功能完整，偶尔的视觉")
    print("   不一致是 Wayland 环境的正常现象")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
