#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
交互式连续监控工具
结合连续监控和交互式确认功能，提供最佳的用户体验
"""

import sys
import os
import time
import threading
import signal
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/home/<USER>/<PERSON>ylinAutoSDK_UNI')

from universal_offset_detector import UniversalOffsetDetector, get_mouse_position
from interactive_highlight import interactive_highlight


class InteractiveMonitor:
    """交互式连续监控器"""
    
    def __init__(self, interval=0.5, output_format='original'):
        """
        初始化交互式监控器
        
        Args:
            interval: 监控间隔（秒）
            output_format: 输出格式
        """
        self.interval = interval
        self.output_format = output_format
        self.running = False
        self.monitor_thread = None
        self.last_position = (None, None)
        self.last_control_id = None
        self.capture_count = 0
        self.detector = UniversalOffsetDetector()
        
        print("🎯 交互式连续监控工具")
        print("=" * 60)
        print("🌟 功能特点:")
        print("   - 实时监控鼠标位置的控件")
        print("   - 自动显示控件高亮和信息")
        print("   - 绿色✓按钮：确认捕获并退出")
        print("   - 红色✗按钮：取消捕获，继续监控")
        print("   - 完美替代X11的Ctrl+点击体验")
        print("=" * 60)
    
    def start_monitoring(self):
        """开始监控"""
        if self.running:
            print("⚠️  监控已在运行中")
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        print("🚀 交互式监控已启动...")
        print("💡 使用说明:")
        print("   1. 将鼠标悬停在目标控件上")
        print("   2. 系统会自动显示高亮和确认按钮")
        print("   3. 点击绿色✓按钮 → 保存控件信息并退出程序")
        print("   4. 点击红色✗按钮 → 取消当前捕获，继续监控")
        print("   5. 按 Ctrl+C 随时退出监控")
        print()
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        print(f"🛑 监控已停止，共捕获 {self.capture_count} 个控件")
    
    def _monitor_loop(self):
        """监控循环"""
        last_interaction_time = 0
        interaction_cooldown = 2  # 2秒冷却时间，避免频繁弹出
        
        while self.running:
            try:
                current_time = time.time()
                
                # 获取当前鼠标位置
                mouse_pos = get_mouse_position()
                if mouse_pos == (None, None):
                    time.sleep(self.interval)
                    continue
                
                mouse_x, mouse_y = mouse_pos
                
                # 检查位置是否变化
                if (mouse_x, mouse_y) != self.last_position:
                    self.last_position = (mouse_x, mouse_y)
                    
                    # 检查冷却时间
                    if current_time - last_interaction_time > interaction_cooldown:
                        # 检测并显示交互式高亮
                        if self._detect_and_show_interactive(mouse_x, mouse_y):
                            last_interaction_time = current_time
                
                time.sleep(self.interval)
                
            except Exception as e:
                print(f"❌ 监控循环错误: {e}")
                time.sleep(1)
    
    def _detect_and_show_interactive(self, mouse_x, mouse_y):
        """检测控件并显示交互式高亮"""
        try:
            # 计算窗口偏移
            offset, atspi_window_info = self.detector.calculate_window_offset(mouse_x, mouse_y)
            if not offset or not atspi_window_info:
                return False
            
            # 收集应用控件
            self.detector.collect_app_controls(atspi_window_info)
            if not self.detector.current_app_controls:
                return False
            
            # 查找控件
            ctrl = self.detector.find_control_with_offset(mouse_x, mouse_y, offset)
            if not ctrl:
                return False
            
            # 避免重复显示同一个控件
            ctrl_id = f"{ctrl['name']}_{ctrl['extents'].x}_{ctrl['extents'].y}"
            if ctrl_id == self.last_control_id:
                return False
            
            self.last_control_id = ctrl_id
            
            # 获取控件信息
            info = self.detector.get_control_info(ctrl)
            
            # 计算屏幕坐标
            ext = ctrl['extents']
            screen_x = ext.x + offset[0]
            screen_y = ext.y + offset[1]
            
            # 显示控件信息
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"\n[{timestamp}] 🎯 检测到控件:")
            print(f"   📱 应用: {atspi_window_info['app_name']}")
            print(f"   📋 控件: {info['name']} ({info['role']})")
            print(f"   📍 位置: {info['position']}")
            print(f"   📏 尺寸: {info['size']}")
            print("   ✨ 显示交互式确认...")
            
            # 显示交互式高亮
            user_choice = interactive_highlight(
                screen_x, screen_y, ext.width, ext.height,
                control_info=info,
                duration=15,  # 15秒超时
                color='orange',
                border_width=3
            )
            
            # 处理用户选择
            if user_choice == 'confirm':
                print("✅ 用户确认捕获，正在保存...")
                success = self._save_control_info(ctrl, offset, atspi_window_info)
                if success:
                    self.capture_count += 1
                    print(f"🎉 控件信息已保存! (第 {self.capture_count} 个)")

                    # 显示确认高亮
                    self.detector.highlight_control(ctrl, offset,
                                                  duration=2,
                                                  color='lime', border_width=4)

                    # 绿色按钮：保存后退出程序
                    print("\n🎯 捕获完成，程序即将退出...")
                    time.sleep(2)  # 等待高亮显示完成
                    self.stop_monitoring()
                    print("👋 再见!")
                    sys.exit(0)
                else:
                    print("❌ 保存失败，继续监控...")

            elif user_choice == 'cancel':
                print("❌ 用户取消捕获，继续监控...")
                # 红色按钮：继续监控，不退出

            elif user_choice == 'timeout':
                print("⏰ 用户操作超时，继续监控...")

            else:
                print("❌ 交互过程出现错误，继续监控...")
            
            return True
            
        except Exception as e:
            print(f"❌ 检测交互错误: {e}")
            return False
    
    def _save_control_info(self, ctrl, offset, atspi_window_info):
        """保存控件信息"""
        try:
            if self.output_format == 'original':
                # 使用真正的原始格式
                mouse_pos = get_mouse_position()
                mouse_x, mouse_y = mouse_pos if mouse_pos != (None, None) else (0, 0)

                # 使用检测器的原始格式方法
                original_info = self.detector.get_control_info_original_format(
                    ctrl, atspi_window_info, mouse_x, mouse_y, offset
                )

                if original_info:
                    # 保存到主文件（与universal_offset_detector.py完全一致）
                    import json
                    with open('detected_control_info.txt', 'w', encoding='utf-8') as f:
                        json.dump(original_info, f, ensure_ascii=False)

                    print(f"📄 详细信息已保存到: detected_control_info.txt")

                    # 保存带时间戳的备份
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_file = f"captured_control_{timestamp}.json"
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(original_info, f, ensure_ascii=False, indent=2)

                    print(f"💾 备份副本: {backup_file}")

                    # 显示原始格式的JSON内容
                    print("\n📋 保存的控件信息 (原始格式):")
                    print("=" * 60)
                    print(json.dumps(original_info, indent=2, ensure_ascii=False))
                    print("=" * 60)

                else:
                    print("❌ 无法生成原始格式信息")
                    return False

            else:
                # 简单格式
                info = self.detector.get_control_info(ctrl)
                print(f"📋 控件信息: {info}")

            return True

        except Exception as e:
            print(f"❌ 保存控件信息错误: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='交互式连续监控工具')
    parser.add_argument('--interval', type=float, default=0.5,
                       help='监控间隔（秒），默认0.5秒')
    parser.add_argument('--format', choices=['simple', 'original'], default='original',
                       help='输出格式: simple (简单) 或 original (完整)')
    
    args = parser.parse_args()
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        # 创建监控器
        monitor = InteractiveMonitor(
            interval=args.interval,
            output_format=args.format
        )
        
        # 设置信号处理
        def signal_handler(sig, frame):
            print(f"\n🛑 收到信号 {sig}，正在停止监控...")
            monitor.stop_monitoring()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 开始监控
        monitor.start_monitoring()
        
        try:
            # 保持主线程运行
            while monitor.running:
                time.sleep(1)
        except KeyboardInterrupt:
            monitor.stop_monitoring()
            
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
