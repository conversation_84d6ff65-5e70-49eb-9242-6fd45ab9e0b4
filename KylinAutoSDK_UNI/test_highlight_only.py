#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
专门测试高亮功能的脚本
直接调用高亮相关的方法，不涉及控件识别
"""

import sys
import os
import subprocess
import time

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def test_ultimate_highlight():
    """测试 ultimate_highlight 函数"""
    print("🎯 测试1: ultimate_highlight 函数")
    print("=" * 50)
    
    try:
        from ultimate_highlight import ultimate_highlight
        
        # 获取鼠标位置作为测试区域
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        # 在鼠标位置周围创建测试区域
        test_x = mouse_x - 100
        test_y = mouse_y - 50
        test_w = 200
        test_h = 100
        
        print(f"📍 测试区域: ({test_x}, {test_y}) 尺寸: {test_w}×{test_h}")
        print(f"🖱️ 基于鼠标位置: ({mouse_x}, {mouse_y})")
        print()
        
        # 测试不同的高亮样式
        test_cases = [
            ("红色标准", "red", 2, 3),
            ("蓝色细边框", "blue", 1, 3),
            ("绿色粗边框", "green", 3, 3),
            ("黄色超细", "yellow", 1, 2),
            ("紫色中等", "purple", 2, 2),
        ]
        
        for desc, color, border_width, duration in test_cases:
            print(f"🎨 测试 {desc} 高亮:")
            print(f"   颜色: {color}")
            print(f"   边框宽度: {border_width}px")
            print(f"   持续时间: {duration}s")
            
            success = ultimate_highlight(
                x=test_x, 
                y=test_y, 
                width=test_w, 
                height=test_h,
                duration=duration,
                color=color,
                border_width=border_width
            )
            
            if success:
                print(f"   ✅ 高亮显示成功")
            else:
                print(f"   ❌ 高亮显示失败")
            
            print(f"   ⏳ 等待 {duration} 秒...")
            time.sleep(duration + 0.5)  # 额外等待0.5秒
            print()
            
    except ImportError as e:
        print(f"❌ 无法导入 ultimate_highlight: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_control_highlighter():
    """测试 ControlHighlighter 类"""
    print("🎯 测试2: ControlHighlighter 类")
    print("=" * 50)
    
    try:
        from UNI_new import ControlHighlighter
        
        highlighter = ControlHighlighter()
        
        # 获取鼠标位置作为测试区域
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        # 在鼠标位置周围创建测试区域
        test_x = mouse_x - 75
        test_y = mouse_y - 75
        test_w = 150
        test_h = 150
        
        print(f"📍 测试区域: ({test_x}, {test_y}) 尺寸: {test_w}×{test_h}")
        print(f"🖱️ 基于鼠标位置: ({mouse_x}, {mouse_y})")
        print()
        
        # 测试 ControlHighlighter 的 highlight_control 方法
        test_cases = [
            ("默认红色", 3, "red", 3),
            ("蓝色细边框", 1, "blue", 3),
            ("绿色粗边框", 4, "green", 2),
        ]
        
        for desc, thickness, color, duration in test_cases:
            print(f"🎨 测试 {desc}:")
            print(f"   颜色: {color}")
            print(f"   边框厚度: {thickness}px")
            print(f"   持续时间: {duration}s")
            
            try:
                highlighter.highlight_control(
                    x=test_x,
                    y=test_y,
                    width=test_w,
                    height=test_h,
                    duration=duration,
                    color=color,
                    thickness=thickness
                )
                print(f"   ✅ ControlHighlighter 调用成功")
                print(f"   ⏳ 等待 {duration} 秒...")
                time.sleep(duration + 0.5)
                
            except Exception as e:
                print(f"   ❌ ControlHighlighter 调用失败: {e}")
            
            print()
            
    except ImportError as e:
        print(f"❌ 无法导入 ControlHighlighter: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_highlight_found_control():
    """测试 highlight_found_control 函数"""
    print("🎯 测试3: highlight_found_control 函数")
    print("=" * 50)
    
    try:
        from UNI_new import highlight_found_control
        
        # 获取鼠标位置作为测试区域
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        # 创建模拟的控件数据
        mock_control_data = {
            'Name': '测试控件',
            'Rolename': 'push button',
            'Coords': {
                'x': mouse_x - 60,
                'y': mouse_y - 30,
                'width': 120,
                'height': 60
            },
            'RelativeCoords': {
                'x': mouse_x - 60,
                'y': mouse_y - 30,
                'width': 120,
                'height': 60
            }
        }
        
        print(f"📍 模拟控件位置: ({mock_control_data['Coords']['x']}, {mock_control_data['Coords']['y']})")
        print(f"📏 模拟控件尺寸: {mock_control_data['Coords']['width']}×{mock_control_data['Coords']['height']}")
        print(f"🖱️ 基于鼠标位置: ({mouse_x}, {mouse_y})")
        print()
        
        # 测试不同的高亮参数
        test_cases = [
            ("默认参数", 3.0, "red", 3),
            ("蓝色长时间", 5.0, "blue", 2),
            ("绿色细边框", 2.0, "green", 1),
        ]
        
        for desc, duration, color, thickness in test_cases:
            print(f"🎨 测试 {desc}:")
            print(f"   持续时间: {duration}s")
            print(f"   颜色: {color}")
            print(f"   边框厚度: {thickness}px")
            
            try:
                success = highlight_found_control(
                    control_data=mock_control_data,
                    duration=duration,
                    color=color,
                    thickness=thickness
                )
                
                if success:
                    print(f"   ✅ highlight_found_control 调用成功")
                else:
                    print(f"   ❌ highlight_found_control 返回失败")
                    
                print(f"   ⏳ 等待 {duration} 秒...")
                time.sleep(duration + 0.5)
                
            except Exception as e:
                print(f"   ❌ highlight_found_control 调用异常: {e}")
            
            print()
            
    except ImportError as e:
        print(f"❌ 无法导入 highlight_found_control: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_preset_highlight_functions():
    """测试预设的高亮函数"""
    print("🎯 测试4: 预设高亮函数")
    print("=" * 50)
    
    try:
        from ultimate_highlight import (
            red_highlight, blue_highlight, green_highlight,
            thin_highlight, thick_highlight
        )
        
        # 获取鼠标位置作为测试区域
        mouse_x, mouse_y = get_mouse_position()
        if mouse_x is None:
            mouse_x, mouse_y = 500, 300
            
        test_x = mouse_x - 50
        test_y = mouse_y - 25
        test_w = 100
        test_h = 50
        
        print(f"📍 测试区域: ({test_x}, {test_y}) 尺寸: {test_w}×{test_h}")
        print(f"🖱️ 基于鼠标位置: ({mouse_x}, {mouse_y})")
        print()
        
        # 测试预设函数
        preset_tests = [
            ("红色高亮", lambda: red_highlight(test_x, test_y, test_w, test_h, 2)),
            ("蓝色高亮", lambda: blue_highlight(test_x, test_y, test_w, test_h, 2)),
            ("绿色高亮", lambda: green_highlight(test_x, test_y, test_w, test_h, 2)),
            ("超细边框", lambda: thin_highlight(test_x, test_y, test_w, test_h, 2, 'purple')),
            ("粗边框", lambda: thick_highlight(test_x, test_y, test_w, test_h, 2, 'orange')),
        ]
        
        for desc, func in preset_tests:
            print(f"🎨 测试 {desc}:")
            
            try:
                success = func()
                if success:
                    print(f"   ✅ {desc} 成功")
                else:
                    print(f"   ❌ {desc} 失败")
                    
                print(f"   ⏳ 等待 2 秒...")
                time.sleep(2.5)
                
            except Exception as e:
                print(f"   ❌ {desc} 异常: {e}")
            
            print()
            
    except ImportError as e:
        print(f"❌ 无法导入预设高亮函数: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("专门测试高亮功能")
    print("=" * 60)
    print("🎯 目标: 单独测试各种高亮方法")
    print("💡 说明: 不涉及控件识别，只测试高亮显示")
    print()
    
    # 检查鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        print(f"🖱️ 当前鼠标位置: ({mouse_x}, {mouse_y})")
        print(f"📍 高亮测试将在鼠标周围区域进行")
    else:
        print(f"⚠️ 无法获取鼠标位置，使用默认位置 (500, 300)")
    print()
    
    # 依次测试各种高亮方法
    test_ultimate_highlight()
    test_control_highlighter()
    test_highlight_found_control()
    test_preset_highlight_functions()
    
    print("=" * 60)
    print("🎉 高亮功能测试完成!")
    print()
    print("📊 测试总结:")
    print("   1. ultimate_highlight - 底层高亮函数")
    print("   2. ControlHighlighter - 高亮器类")
    print("   3. highlight_found_control - 控件高亮函数")
    print("   4. 预设高亮函数 - 便捷调用")
    print()
    print("💡 如果看到高亮效果，说明高亮功能正常工作")
    print("💡 如果没有看到，请检查X11环境和权限设置")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
