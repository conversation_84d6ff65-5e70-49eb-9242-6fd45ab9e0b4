#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
通用偏移检测器 - Wayland环境优化版
基于鼠标位置自动计算任何应用窗口的AT-SPI坐标偏移
支持连续监控模式，替代X11的全局监听功能
"""

import sys
import os
import subprocess
import pyatspi
import re
import time
import threading
import signal
from datetime import datetime


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_window_at_mouse_position(mouse_x, mouse_y):
    """获取鼠标位置下的窗口信息"""
    print(f"   🔍 在位置 ({mouse_x}, {mouse_y}) 查找窗口...")

    # 方法1: 尝试使用wlcctrl (Wayland环境)
    try:
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode == 0:
            print("   🌊 检测到Wayland环境，使用wlcctrl...")

            # 获取所有窗口信息
            all_windows = []
            lines = result.stdout.strip().split('\n')

            i = 0
            while i < len(lines):
                line = lines[i]
                if 'toplevel' in line:
                    # 提取窗口ID
                    window_id = line.split('"')[1]

                    # 获取窗口标题（下一行）
                    window_title = ""
                    if i + 1 < len(lines):
                        title_line = lines[i + 1].strip()
                        # 移除 "title: " 前缀
                        if title_line.startswith('title: '):
                            window_title = title_line[7:]  # 移除 "title: "
                        else:
                            window_title = title_line

                    # 获取窗口几何信息和状态
                    geo_result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id],
                                              capture_output=True, text=True)
                    if geo_result.returncode == 0:
                        geometry_info = {}
                        window_visible = True

                        for geo_line in geo_result.stdout.strip().split('\n'):
                            if 'geometry:' in geo_line:
                                # 解析几何信息: "geometry: (x, y) width x height"
                                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)', geo_line)
                                if match:
                                    x, y, width, height = map(int, match.groups())
                                    geometry_info = {'x': x, 'y': y, 'width': width, 'height': height}
                            elif 'minimized:' in geo_line:
                                # 检查是否最小化
                                if 'true' in geo_line.lower():
                                    window_visible = False
                                    print(f"     ⚠️  窗口 {window_title} 已最小化，跳过")
                            elif 'visible:' in geo_line:
                                # 检查是否可见
                                if 'false' in geo_line.lower():
                                    window_visible = False
                                    print(f"     ⚠️  窗口 {window_title} 不可见，跳过")

                        # 额外的可见性检查：窗口是否在合理的屏幕范围内
                        if window_visible and geometry_info:
                            x, y = geometry_info['x'], geometry_info['y']
                            width, height = geometry_info['width'], geometry_info['height']

                            # 检查窗口是否在屏幕范围内（简单检查）
                            # 如果窗口完全在屏幕外或尺寸异常，可能是最小化或隐藏的
                            if (x < -width or y < -height or  # 完全在屏幕左上方外
                                x > 5000 or y > 5000 or       # 完全在屏幕右下方外（假设屏幕不超过5000px）
                                width <= 0 or height <= 0):   # 尺寸异常
                                print(f"     ⚠️  窗口 {window_title} 位置异常 ({x}, {y}) {width}×{height}，可能已隐藏")
                                window_visible = False

                        # 只添加可见的窗口
                        if window_visible and geometry_info:
                            all_windows.append({
                                'window_id': window_id,
                                'title': window_title,
                                'position': (geometry_info['x'], geometry_info['y']),
                                'size': (geometry_info['width'], geometry_info['height']),
                                'area': geometry_info['width'] * geometry_info['height'],
                                'visible': True
                            })

                    i += 2  # 跳过标题行
                else:
                    i += 1

            print(f"   📋 找到 {len(all_windows)} 个窗口:")
            for window in all_windows:
                x, y = window['position']
                width, height = window['size']
                print(f"     - {window['title'][:30]}: ({x}, {y}) {width}×{height}")

            # 查找鼠标位置所在的窗口
            matching_windows = []
            for window in all_windows:
                x, y = window['position']
                width, height = window['size']

                # 检查鼠标是否在窗口范围内
                if (x <= mouse_x < x + width and y <= mouse_y < y + height):
                    matching_windows.append(window)

            if matching_windows:
                print(f"   🎯 找到 {len(matching_windows)} 个匹配窗口:")
                for window in matching_windows:
                    print(f"     - {window['title']}: {window['position']} {window['size'][0]}×{window['size'][1]}")

                # 智能窗口选择逻辑
                print(f"   🔍 智能窗口选择...")
                valid_windows = []

                for window in matching_windows:
                    title = window['title']
                    x, y = window['position']
                    width, height = window['size']

                    print(f"     检查窗口: {title} ({x},{y}) {width}×{height}")

                    # 跳过系统窗口
                    if title.lower() in ['桌面', 'desktop', 'ukui-panel', 'ukui-sidebar']:
                        print(f"       ⏭️  跳过系统窗口")
                        continue

                    # 跳过异常大的窗口（可能是最小化窗口）
                    if width >= 2500 or height >= 1500:
                        print(f"       ❌ 跳过异常大窗口 (可能已最小化)")
                        continue

                    # 跳过在原点且很大的窗口
                    if x == 0 and y == 0 and (width > 1500 or height > 1000):
                        print(f"       ❌ 跳过原点大窗口 (可能已最小化)")
                        continue

                    print(f"       ✅ 窗口通过过滤")
                    valid_windows.append(window)

                if not valid_windows:
                    print(f"   ❌ 没有找到有效的应用窗口")
                    return None

                # 智能选择：选择面积最小的窗口（通常是最上层的）
                for window in valid_windows:
                    title = window['title']
                    if ('terminal' in title.lower() or
                        ('@' in title and ':' in title and ('~' in title or '/' in title))):
                        print(f"     💻 识别为终端窗口: {title}")
                    else:
                        print(f"     📄 识别为应用窗口: {title}")

                # 选择面积最小的窗口（通常是最上层的，最可能是用户当前交互的）
                best_window = min(valid_windows, key=lambda w: w['area'])

                window_type = "终端窗口" if ('terminal' in best_window['title'].lower() or
                                        ('@' in best_window['title'] and ':' in best_window['title'] and
                                         ('~' in best_window['title'] or '/' in best_window['title']))) else "应用窗口"

                print(f"   ✅ 选择最小{window_type}: {best_window['title']}")
                print(f"      面积: {best_window['area']} (最可能是最上层窗口)")

                # 用户确认机制
                print(f"\n🤔 检测到窗口: {best_window['title']}")
                print(f"   位置: {best_window['position']}")
                print(f"   尺寸: {best_window['size']}")
                print(f"   💡 这个窗口是否真的可见且是你鼠标悬停的窗口？")

                # 简单的自动验证：检查窗口是否可能被遮挡
                x, y = best_window['position']
                width, height = best_window['size']

                # 如果窗口完全被其他窗口覆盖，可能不可见
                potentially_hidden = False
                for other_window in valid_windows:
                    if other_window == best_window:
                        continue

                    other_x, other_y = other_window['position']
                    other_width, other_height = other_window['size']

                    # 检查是否完全覆盖
                    if (other_x <= x and other_y <= y and
                        other_x + other_width >= x + width and
                        other_y + other_height >= y + height):
                        potentially_hidden = True
                        print(f"   ⚠️  可能被窗口遮挡: {other_window['title']}")
                        break

                if potentially_hidden:
                    print(f"   ❌ 窗口可能被遮挡，跳过")
                    return None

                return {
                    'method': 'wlcctrl',
                    'window_id': best_window['window_id'],
                    'position': best_window['position'],
                    'size': best_window['size'],
                    'title': best_window['title']
                }
            else:
                print(f"   ❌ 鼠标不在任何窗口内")
                return None

    except Exception as e:
        print(f"   ❌ wlcctrl方法失败: {e}")

    # 方法2: 回退到xdotool (X11环境)
    try:
        result = subprocess.run(['xdotool', 'getmouselocation', '--shell'],
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            print("   🖥️  回退到X11环境，使用xdotool...")

            lines = result.stdout.strip().split('\n')
            window_id = None
            for line in lines:
                if line.startswith('WINDOW='):
                    window_id = line.split('=')[1]
                    break

            if window_id and window_id != '0':
                # 获取窗口几何信息
                result = subprocess.run(['xdotool', 'getwindowgeometry', window_id],
                                      capture_output=True, text=True, env={'DISPLAY': ':0'})
                if result.returncode == 0:
                    # 解析几何信息
                    for line in result.stdout.strip().split('\n'):
                        if 'Position:' in line:
                            pos_part = line.split('Position:')[1].strip()
                            coords = pos_part.split(',')
                            if len(coords) == 2:
                                x = int(coords[0].strip())
                                y = int(coords[1].strip().split()[0])

                                # 获取窗口标题
                                title_result = subprocess.run(['xdotool', 'getwindowname', window_id],
                                                            capture_output=True, text=True, env={'DISPLAY': ':0'})
                                window_title = title_result.stdout.strip() if title_result.returncode == 0 else "Unknown"

                                print(f"   ✅ 找到X11窗口: {window_title}")
                                print(f"      位置: ({x}, {y})")

                                return {
                                    'method': 'xdotool',
                                    'window_id': window_id,
                                    'position': (x, y),
                                    'title': window_title
                                }
    except Exception as e:
        print(f"   ❌ xdotool方法失败: {e}")

    return None


def check_atspi_window_visibility(window):
    """检查AT-SPI窗口的可见性状态"""
    try:
        if hasattr(window, 'getState'):
            state = window.getState()

            # 检查关键的可见性状态
            has_visible = hasattr(pyatspi, 'STATE_VISIBLE') and state.contains(pyatspi.STATE_VISIBLE)
            has_showing = hasattr(pyatspi, 'STATE_SHOWING') and state.contains(pyatspi.STATE_SHOWING)
            is_iconified = hasattr(pyatspi, 'STATE_ICONIFIED') and state.contains(pyatspi.STATE_ICONIFIED)

            print(f"       AT-SPI状态检查:")
            print(f"         VISIBLE: {'✅' if has_visible else '❌'}")
            print(f"         SHOWING: {'✅' if has_showing else '❌'}")
            print(f"         ICONIFIED: {'✅' if is_iconified else '❌'}")

            # 如果窗口被最小化，肯定不可见
            if is_iconified:
                print(f"       ❌ 窗口已最小化")
                return False

            # 如果没有VISIBLE或SHOWING状态，很可能不在正常显示的桌面中
            if not has_visible:
                print(f"       ❌ 窗口缺少VISIBLE状态，可能不在正常显示的桌面中")
                return False

            if not has_showing:
                print(f"       ❌ 窗口缺少SHOWING状态，可能不在正常显示的桌面中")
                return False

            print(f"       ✅ 窗口状态正常，应该是可见的")
            return True

    except Exception as e:
        print(f"       ⚠️  检查AT-SPI状态失败: {e}")
        # 如果无法检查状态，保守地假设可见
        return True

    return True


def find_atspi_window_for_real_window(window_info):
    """根据真实窗口信息找到对应的AT-SPI窗口"""
    if not window_info:
        return None

    window_title = window_info['title']

    try:
        desktop = pyatspi.Registry.getDesktop(0)

        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"

            # 跳过系统应用
            if app_name.lower() in ['at-spi-bus-launcher', 'gnome-shell', 'ibus-daemon']:
                continue

            for j in range(app.childCount):
                try:
                    window = app.getChildAtIndex(j)
                    window_name = window.name or ""

                    # 尝试匹配窗口标题
                    print(f"     比较窗口: '{window_title}' vs AT-SPI: '{window_name}'")

                    # 多种匹配策略
                    title_match = False
                    if window_title and window_name:
                        # 直接匹配
                        if (window_title.lower() in window_name.lower() or
                            window_name.lower() in window_title.lower()):
                            title_match = True
                        # 关键词匹配
                        elif any(keyword in window_name.lower() for keyword in window_title.lower().split() if len(keyword) > 3):
                            title_match = True
                        # 特殊匹配：hellobig应用
                        elif ('AT-SPI测试界面' in window_title and '测试主窗口' in window_name):
                            title_match = True
                        elif ('Qt控件集合' in window_title and 'hellobig' in app_name.lower()):
                            title_match = True

                    if title_match:
                        print(f"     🎯 找到标题匹配的AT-SPI窗口: {window_name}")

                        # 检查AT-SPI窗口的可见性状态
                        if not check_atspi_window_visibility(window):
                            print(f"     ❌ AT-SPI窗口不可见，跳过")
                            continue

                        # 获取AT-SPI窗口坐标
                        if hasattr(window, 'queryComponent'):
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                                print(f"     ✅ AT-SPI窗口通过可见性验证")
                                return {
                                    'app': app,
                                    'app_name': app_name,
                                    'window': window,
                                    'window_name': window_name,
                                    'atspi_position': (extents.x, extents.y),
                                    'atspi_size': (extents.width, extents.height)
                                }
                except Exception:
                    continue

        # 如果通过标题匹配失败，尝试通过应用名称匹配
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"

            # 检查应用名称是否与窗口标题相关
            if (window_title and app_name and
                any(keyword in window_title.lower() for keyword in app_name.lower().split()) and
                app_name.lower() not in ['at-spi-bus-launcher', 'gnome-shell', 'ibus-daemon']):

                # 取第一个窗口
                if app.childCount > 0:
                    try:
                        window = app.getChildAtIndex(0)

                        # 检查AT-SPI窗口的可见性状态
                        if not check_atspi_window_visibility(window):
                            print(f"     ❌ 应用名称匹配的AT-SPI窗口不可见，跳过")
                            continue

                        if hasattr(window, 'queryComponent'):
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                                print(f"     ✅ 应用名称匹配的AT-SPI窗口通过可见性验证")
                                return {
                                    'app': app,
                                    'app_name': app_name,
                                    'window': window,
                                    'window_name': window.name or "Unknown",
                                    'atspi_position': (extents.x, extents.y),
                                    'atspi_size': (extents.width, extents.height)
                                }
                    except Exception:
                        continue

    except Exception as e:
        print(f"❌ 查找AT-SPI窗口失败: {e}")

    return None


class WaylandControlMonitor:
    """Wayland环境下的控件监控器 - 替代X11全局监听"""

    def __init__(self, interval=0.5, highlight_duration=2):
        """
        初始化监控器

        Args:
            interval: 监控间隔（秒）
            highlight_duration: 高亮持续时间（秒）
        """
        self.interval = interval
        self.highlight_duration = highlight_duration
        self.running = False
        self.monitor_thread = None
        self.last_position = (None, None)
        self.last_control = None
        self.detector = UniversalOffsetDetector()

        print("🌊 Wayland控件监控器")
        print("=" * 60)
        print("🎯 替代X11全局监听，实现连续控件检测")
        print(f"⏱️  监控间隔: {interval}秒")
        print(f"✨ 高亮时长: {highlight_duration}秒")
        print("=" * 60)

    def start_monitoring(self):
        """开始监控"""
        if self.running:
            print("⚠️  监控已在运行中")
            return

        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

        print("🚀 开始监控鼠标位置...")
        print("💡 提示：")
        print("   - 将鼠标悬停在目标控件上")
        print("   - 系统会自动检测并高亮控件")
        print("   - 按 Ctrl+C 停止监控")
        print()

    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        print("🛑 监控已停止")

    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 获取当前鼠标位置
                mouse_pos = get_mouse_position()
                if mouse_pos == (None, None):
                    time.sleep(self.interval)
                    continue

                mouse_x, mouse_y = mouse_pos

                # 检查位置是否变化
                if (mouse_x, mouse_y) != self.last_position:
                    self.last_position = (mouse_x, mouse_y)

                    # 检测控件并高亮
                    self._detect_and_highlight(mouse_x, mouse_y)

                time.sleep(self.interval)

            except Exception as e:
                print(f"❌ 监控循环错误: {e}")
                time.sleep(1)

    def _detect_and_highlight(self, mouse_x, mouse_y):
        """检测并高亮控件"""
        try:
            # 计算窗口偏移
            offset, atspi_window_info = self.detector.calculate_window_offset(mouse_x, mouse_y)
            if not offset or not atspi_window_info:
                return

            # 收集应用控件
            self.detector.collect_app_controls(atspi_window_info)
            if not self.detector.current_app_controls:
                return

            # 查找控件
            ctrl = self.detector.find_control_with_offset(mouse_x, mouse_y, offset)
            if not ctrl:
                return

            # 避免重复高亮同一个控件
            ctrl_id = f"{ctrl['name']}_{ctrl['extents'].x}_{ctrl['extents'].y}"
            if ctrl_id == self.last_control:
                return

            self.last_control = ctrl_id

            # 显示控件信息
            info = self.detector.get_control_info(ctrl)
            timestamp = datetime.now().strftime("%H:%M:%S")

            print(f"\n[{timestamp}] 🎯 检测到控件:")
            print(f"   📱 应用: {atspi_window_info['app_name']}")
            print(f"   📋 控件: {info['name']} ({info['role']})")
            print(f"   📍 位置: {info['position']}")
            print(f"   📏 尺寸: {info['size']}")

            # 执行高亮
            self.detector.highlight_control(ctrl, offset,
                                          duration=self.highlight_duration,
                                          color='cyan', border_width=2)

        except Exception as e:
            print(f"❌ 检测高亮错误: {e}")


class UniversalOffsetDetector:
    """通用偏移检测器"""

    def __init__(self):
        """初始化检测器"""
        self.specific_control_types = {
            'push button', 'button', 'toggle button',
            'check box', 'radio button',
            'text', 'password text', 'entry',
            'combo box', 'list', 'list item',
            'slider', 'spin button', 'scroll bar',
            'menu item', 'tab', 'tree item',
            'label', 'image', 'progress bar'
        }

        self.current_app_controls = []

        print("🌍 通用偏移检测器")
        print("=" * 60)
        print("🎯 基于鼠标位置自动计算窗口偏移")
        print("🔄 适用于任何支持AT-SPI的应用")
        print("🔍 包含窗口可见性验证")
        print("=" * 60)

def verify_window_visibility_via_atspi(window_info, mouse_x, mouse_y):
        """通过AT-SPI验证窗口是否真的可见和可交互"""
        try:
            window_title = window_info['title']

            # 查找对应的AT-SPI窗口
            desktop = pyatspi.Registry.getDesktop(0)

            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                app_name = app.name or f"应用{i}"

                # 跳过系统应用
                if app_name.lower() in ['at-spi-bus-launcher', 'gnome-shell', 'ibus-daemon']:
                    continue

                for j in range(app.childCount):
                    try:
                        atspi_window = app.getChildAtIndex(j)
                        window_name = atspi_window.name or ""

                        # 检查是否匹配
                        if window_titles_match(window_title, window_name, app_name):
                            # 检查窗口状态
                            try:
                                if hasattr(atspi_window, 'getState'):
                                    state = atspi_window.getState()

                                    # 检查是否最小化
                                    if state.contains(pyatspi.STATE_ICONIFIED):
                                        print(f"       🔍 AT-SPI检测到窗口已最小化: {window_name}")
                                        return False

                                    # 检查是否可见
                                    if not state.contains(pyatspi.STATE_VISIBLE):
                                        print(f"       🔍 AT-SPI检测到窗口不可见: {window_name}")
                                        return False

                                    # 检查是否显示中
                                    if not state.contains(pyatspi.STATE_SHOWING):
                                        print(f"       🔍 AT-SPI检测到窗口未显示: {window_name}")
                                        return False

                                # 额外检查：尝试获取窗口的组件信息
                                if hasattr(atspi_window, 'queryComponent'):
                                    component = atspi_window.queryComponent()
                                    if component:
                                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                                        # 如果AT-SPI报告的窗口尺寸为0或负数，可能是隐藏的
                                        if extents.width <= 0 or extents.height <= 0:
                                            print(f"       🔍 AT-SPI检测到窗口尺寸异常: {extents.width}×{extents.height}")
                                            return False

                                print(f"       ✅ AT-SPI确认窗口可见: {window_name}")
                                return True

                            except Exception as e:
                                print(f"       ⚠️  检查窗口状态时出错: {e}")
                                # 如果无法检查状态，假设可见
                                return True

                    except Exception:
                        continue

            # 如果找不到对应的AT-SPI窗口，可能是不支持AT-SPI的应用
            print(f"       ⚠️  未找到对应的AT-SPI窗口: {window_title}")
            return False

        except Exception as e:
            print(f"       ❌ 验证窗口可见性失败: {e}")
            return False

def window_titles_match(real_title, atspi_title, app_name):
        """检查窗口标题是否匹配"""
        if not real_title or not atspi_title:
            return False

        # 直接匹配
        if (real_title.lower() in atspi_title.lower() or
            atspi_title.lower() in real_title.lower()):
            return True

        # 关键词匹配
        if any(keyword in atspi_title.lower() for keyword in real_title.lower().split() if len(keyword) > 3):
            return True

        # 特殊匹配：hellobig应用
        if ('AT-SPI测试界面' in real_title and '测试主窗口' in atspi_title):
            return True
        elif ('Qt控件集合' in real_title and 'hellobig' in app_name.lower()):
            return True

        return False


class UniversalOffsetDetector:
    """通用偏移检测器"""

    def __init__(self):
        """初始化检测器"""
        self.specific_control_types = {
            'push button', 'button', 'toggle button',
            'check box', 'radio button',
            'text', 'password text', 'entry',
            'combo box', 'list', 'list item',
            'slider', 'spin button', 'scroll bar',
            'menu item', 'tab', 'tree item',
            'label', 'image', 'progress bar'
        }

        self.current_app_controls = []

        print("🌍 通用偏移检测器")
        print("=" * 60)
        print("🎯 基于鼠标位置自动计算窗口偏移")
        print("🔄 适用于任何支持AT-SPI的应用")
        print("🔍 包含窗口可见性验证")
        print("=" * 60)

    def calculate_window_offset(self, mouse_x, mouse_y):
        """计算窗口偏移"""
        print(f"\n🔍 步骤1: 获取鼠标下的窗口信息")
        
        # 获取真实窗口信息
        window_info = get_window_at_mouse_position(mouse_x, mouse_y)
        if not window_info:
            print("❌ 无法获取窗口信息")
            return None, None
        
        print(f"   方法: {window_info['method']}")
        print(f"   窗口标题: {window_info['title']}")
        print(f"   真实位置: {window_info['position']}")
        
        print(f"\n🔍 步骤2: 查找对应的AT-SPI窗口")
        
        # 查找对应的AT-SPI窗口
        atspi_window_info = find_atspi_window_for_real_window(window_info)
        if not atspi_window_info:
            print("❌ 无法找到对应的AT-SPI窗口")
            return None, None
        
        print(f"   应用名称: {atspi_window_info['app_name']}")
        print(f"   窗口名称: {atspi_window_info['window_name']}")
        print(f"   AT-SPI位置: {atspi_window_info['atspi_position']}")
        print(f"   AT-SPI尺寸: {atspi_window_info['atspi_size']}")
        
        print(f"\n🔍 步骤3: 计算坐标偏移")

        # 获取尺寸信息
        real_x, real_y = window_info['position']
        real_width, real_height = window_info['size']
        atspi_x, atspi_y = atspi_window_info['atspi_position']
        atspi_width, atspi_height = atspi_window_info['atspi_size']

        # 计算位置偏移
        offset_x = real_x - atspi_x
        offset_y = real_y - atspi_y

        # 计算尺寸差异
        width_diff = real_width - atspi_width
        height_diff = real_height - atspi_height

        print(f"   真实窗口位置: ({real_x}, {real_y}) 尺寸: {real_width}×{real_height}")
        print(f"   AT-SPI窗口位置: ({atspi_x}, {atspi_y}) 尺寸: {atspi_width}×{atspi_height}")
        print(f"   位置偏移: ({offset_x}, {offset_y})")
        print(f"   尺寸差异: ({width_diff}, {height_diff})")

        # 分析尺寸差异
        if height_diff > 0:
            print(f"   🔍 检测到标题栏高度: {height_diff}像素")
            # 考虑标题栏高度的偏移修正
            # 如果有标题栏，内容区域的Y偏移需要加上标题栏高度
            corrected_offset_y = offset_y + height_diff
            calculated_offset = (offset_x, corrected_offset_y)
            print(f"   📐 修正后偏移 (考虑标题栏): {calculated_offset}")
        else:
            calculated_offset = (offset_x, offset_y)
            print(f"   📐 最终偏移: {calculated_offset}")
        
        return calculated_offset, atspi_window_info
    
    def collect_app_controls(self, atspi_window_info):
        """收集应用的所有控件"""
        print(f"\n🔍 步骤4: 收集应用控件")
        
        self.current_app_controls = []
        
        try:
            app = atspi_window_info['app']
            app_name = atspi_window_info['app_name']
            
            self._collect_controls_recursive(app, self.current_app_controls, max_depth=6)
            
            print(f"   收集到 {len(self.current_app_controls)} 个控件")
            
            # 显示控件类型统计
            role_counts = {}
            for ctrl in self.current_app_controls:
                role = ctrl['role']
                role_counts[role] = role_counts.get(role, 0) + 1
            
            print(f"   控件类型分布:")
            for role, count in sorted(role_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"     {role}: {count}")
            
        except Exception as e:
            print(f"❌ 收集控件失败: {e}")
    
    def _collect_controls_recursive(self, element, control_list, depth=0, max_depth=6):
        """递归收集控件"""
        if depth > max_depth:
            return
        
        try:
            name = element.name or "N/A"
            role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
            
            # 获取坐标
            extents = None
            try:
                if hasattr(element, 'queryComponent'):
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            except Exception:
                pass
            
            # 收集所有有坐标的控件
            if extents and extents.width > 0 and extents.height > 0:
                control_list.append({
                    'element': element,
                    'name': name,
                    'role': role,
                    'extents': extents,
                    'depth': depth
                })
            
            # 递归处理子元素
            try:
                for i in range(element.childCount):
                    child = element.getChildAtIndex(i)
                    self._collect_controls_recursive(child, control_list, depth + 1, max_depth)
            except Exception:
                pass
                
        except Exception:
            pass
    
    def find_control_with_offset(self, mouse_x, mouse_y, offset):
        """使用偏移查找控件"""
        print(f"\n🔍 步骤5: 使用偏移查找控件")
        
        # 转换为AT-SPI坐标
        atspi_x = mouse_x - offset[0]
        atspi_y = mouse_y - offset[1]
        
        print(f"   鼠标坐标: ({mouse_x}, {mouse_y})")
        print(f"   AT-SPI坐标: ({atspi_x}, {atspi_y})")
        print(f"   使用偏移: {offset}")
        
        # 查找匹配的控件
        matches = []
        for ctrl in self.current_app_controls:
            ext = ctrl['extents']
            
            if (ext.x <= atspi_x < ext.x + ext.width and
                ext.y <= atspi_y < ext.y + ext.height):
                matches.append(ctrl)
        
        if not matches:
            print("❌ 未找到匹配的控件")
            return None
        
        print(f"   找到 {len(matches)} 个匹配控件:")
        for i, match in enumerate(matches):
            print(f"     {i+1}. {match['name']} ({match['role']}) - 面积: {match['extents'].width * match['extents'].height}")
        
        # 选择最具体的控件
        specific_matches = [m for m in matches if m['role'] in self.specific_control_types]
        
        if specific_matches:
            best_match = min(specific_matches, key=lambda x: x['extents'].width * x['extents'].height)
            print(f"   ✅ 选择最具体控件: {best_match['name']} ({best_match['role']})")
            return best_match
        else:
            best_match = min(matches, key=lambda x: x['extents'].width * x['extents'].height)
            print(f"   ⚠️  选择最小控件: {best_match['name']} ({best_match['role']})")
            return best_match
    
    def get_control_info(self, ctrl):
        """获取控件详细信息"""
        try:
            # 获取状态
            states = []
            try:
                state = ctrl['element'].getState()
                for i in range(len(state)):
                    if state.contains(i):
                        states.append(pyatspi.stateToString(i))
            except Exception:
                pass

            # 获取动作
            actions = []
            try:
                if hasattr(ctrl['element'], 'queryAction'):
                    action = ctrl['element'].queryAction()
                    if action:
                        for i in range(action.nActions):
                            actions.append(action.getName(i))
            except Exception:
                pass

            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': states,
                'actions': actions,
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }
        except Exception:
            return {
                'name': ctrl['name'],
                'role': ctrl['role'],
                'states': [],
                'actions': [],
                'depth': ctrl['depth'],
                'position': (ctrl['extents'].x, ctrl['extents'].y),
                'size': (ctrl['extents'].width, ctrl['extents'].height)
            }

    def get_control_info_original_format(self, ctrl, atspi_window_info, mouse_x, mouse_y, offset):
        """获取与原始 UNI 输出完全相同格式的控件信息"""
        try:
            element = ctrl['element']
            extents = ctrl['extents']

            # 获取进程ID
            try:
                process_id = element.get_process_id() if hasattr(element, 'get_process_id') else -1
            except Exception:
                process_id = -1

            # 获取进程名称
            try:
                if process_id != -1:
                    import subprocess
                    cmd = f'ps -p {process_id} -o comm='
                    ret = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                    process_name = ret.stdout.strip() if ret.stdout.strip() else atspi_window_info['app_name']
                else:
                    process_name = atspi_window_info['app_name']
            except Exception:
                process_name = atspi_window_info['app_name']

            # 获取元素的基本信息
            try:
                description = element.description or ''
            except Exception:
                description = ''

            try:
                element_id = element.id if hasattr(element, 'id') else -1
            except Exception:
                element_id = -1

            try:
                index_in_parent = element.getIndexInParent()
            except Exception:
                index_in_parent = 0

            try:
                children_count = element.childCount if hasattr(element, 'childCount') else 0
            except Exception:
                children_count = 0

            # 获取控件名称和角色
            control_name = element.name if element.name else "N/A"
            control_role = element.getRoleName() if element.getRoleName() else "N/A"

            # 获取文本信息
            try:
                if element.queryText() is not None:
                    text_interface = element.queryText()
                    text_content = text_interface.getText(0, -1)
                else:
                    text_content = "Not available: "
            except Exception:
                text_content = "Not available: "

            # 获取动作信息
            try:
                if element.queryAction() is not None:
                    action_interface = element.queryAction()
                    actions = [action_interface.getName(i) for i in range(action_interface.nActions)]
                else:
                    actions = []
            except Exception:
                actions = []

            # 获取状态信息
            try:
                states = element.getState().getStates()
                state_names = [pyatspi.stateToString(state) for state in states]
            except Exception:
                state_names = []

            # 计算真实坐标（加上偏移）
            real_x = extents.x + offset[0]
            real_y = extents.y + offset[1]

            # 获取父路径信息
            try:
                parent_path, parent_count = self._get_element_path_compatible(element)
            except Exception:
                parent_path, parent_count = [], 0

            # 生成Key（按照原始格式）
            try:
                pp_str = "P"
                for i in parent_path:
                    pp_str = pp_str + str(i)
                keystring = f"N{control_name}-D{description}-{pp_str}"
                keystring = keystring.replace("/", "").replace(" ", "").replace("\n", "").replace("_", "-")
            except Exception:
                keystring = ""

            # 构建坐标字典
            coords_dict = {
                "x": real_x,
                "y": real_y,
                "width": extents.width,
                "height": extents.height
            }

            # 构建 datamap（与原始格式完全一致）
            datamap = {
                "Name": control_name,
                "ID": element_id,
                "ProcessID": process_id,
                "Rolename": control_role,
                "Description": description,
                "Index_in_parent": index_in_parent,
                "ChildrenCount": children_count,
                "ProcessName": process_name,
                "Coords": coords_dict.copy(),
                "Text": text_content,
                "Actions": actions,
                "States": state_names,
                "ParentPath": parent_path,
                "ParentCount": parent_count,
                "Key": keystring,
                "RecordPosition": [mouse_x, mouse_y],  # 注意：原始格式使用列表
                "WindowRoleName": "frame",
                "WindowChildCount": 1,
                "WindowName": atspi_window_info['window_name'],
                "capture_status": "success"
            }

            # 构建完整的原始格式数据结构
            original_format_data = {
                "name": control_name,
                "type": control_role,
                "coords": coords_dict.copy(),
                "datamap": datamap,
                "description": description,
                "states": state_names,
                "actions": actions,
                "capture_status": "success"
            }

            return original_format_data

        except Exception as e:
            print(f"构建原始格式控件数据失败: {e}")
            return None

    def _get_element_path_compatible(self, element):
        """获取元素的父路径（与原始格式兼容）"""
        try:
            path = []
            index = element.getIndexInParent()

            if index != -1:
                path.insert(0, index)
            else:
                index = None
                parent = element.parent
                if parent:
                    for i in range(parent.childCount):
                        if element == parent.getChildAtIndex(i):
                            index = i

                    if index is not None:
                        path.insert(0, index)
                else:
                    path.insert(0, 0)
                    return path, len(path)

            while element:
                if element.getRoleName() == "application":
                    break
                parent = element.parent
                if parent:
                    index = parent.getIndexInParent()
                    if index != -1:
                        path.insert(0, index)
                element = parent

            return path, len(path)
        except Exception:
            return [], 0
    
    def highlight_control(self, ctrl, offset, duration=3, color='cyan', border_width=3):
        """高亮控件"""
        if not ctrl:
            return False
        
        ext = ctrl['extents']
        
        # 计算屏幕坐标
        screen_x = ext.x + offset[0]
        screen_y = ext.y + offset[1]
        width = ext.width
        height = ext.height
        
        print(f"\n✨ 步骤6: 执行高亮")
        print(f"   AT-SPI坐标: ({ext.x}, {ext.y}) {width}×{height}")
        print(f"   屏幕坐标: ({screen_x}, {screen_y}) {width}×{height}")
        print(f"   使用偏移: {offset}")
        
        try:
            from ultimate_highlight import ultimate_highlight
            highlight_success = ultimate_highlight(
                screen_x, screen_y, width, height,
                duration=duration, color=color, border_width=border_width
            )
            
            if highlight_success:
                print(f"✅ 高亮成功!")
                return True
            else:
                print(f"❌ 高亮失败")
                return False
                
        except Exception as e:
            print(f"❌ 高亮异常: {e}")
            return False
    
    def detect_control_at_mouse(self, output_format='simple'):
        """
        检测鼠标位置的控件

        Args:
            output_format: 输出格式，'simple' 或 'original'
        """
        # 获取鼠标位置
        mouse_pos = get_mouse_position()
        if mouse_pos == (None, None):
            print("❌ 无法获取鼠标位置")
            return False

        mouse_x, mouse_y = mouse_pos

        print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")

        # 计算窗口偏移
        offset, atspi_window_info = self.calculate_window_offset(mouse_x, mouse_y)
        if not offset or not atspi_window_info:
            print("❌ 无法计算窗口偏移")
            return False

        # 收集应用控件
        self.collect_app_controls(atspi_window_info)
        if not self.current_app_controls:
            print("❌ 未找到应用控件")
            return False

        # 查找控件
        ctrl = self.find_control_with_offset(mouse_x, mouse_y, offset)
        if not ctrl:
            print("❌ 未找到匹配的控件")
            return False

        # 根据输出格式获取控件信息
        if output_format == 'original':
            # 获取原始格式的控件信息
            original_info = self.get_control_info_original_format(ctrl, atspi_window_info, mouse_x, mouse_y, offset)

            if original_info:
                print(f"\n📋 检测结果 (原始格式):")
                print("=" * 60)

                # 输出原始格式的 JSON
                import json
                print(json.dumps(original_info, indent=2, ensure_ascii=False))

                # 保存到文件
                try:
                    with open('detected_control_info.txt', 'w', encoding='utf-8') as f:
                        json.dump(original_info, f, ensure_ascii=False)
                    print(f"\n💾 控件信息已保存到 detected_control_info.txt")
                except Exception as e:
                    print(f"⚠️  保存文件失败: {e}")
            else:
                print("❌ 无法生成原始格式信息")
                return False
        else:
            # 获取简单格式的控件信息
            info = self.get_control_info(ctrl)

            print(f"\n📋 检测结果:")
            print("=" * 60)
            print(f"📱 应用程序: {atspi_window_info['app_name']}")
            print(f"🪟 窗口名称: {atspi_window_info['window_name']}")
            print(f"📋 控件名称: {info['name']}")
            print(f"🏷️  控件类型: {info['role']}")
            print(f"📍 AT-SPI位置: {info['position']}")
            print(f"📏 尺寸: {info['size']}")
            print(f"🔄 计算偏移: {offset}")

            if info['states']:
                print(f"🔧 状态: {', '.join(info['states'][:3])}")

            if info['actions']:
                print(f"⚡ 动作: {', '.join(info['actions'])}")

        # 执行高亮
        highlight_success = self.highlight_control(ctrl, offset, duration=3, color='lime', border_width=3)

        return highlight_success


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='通用偏移检测器 - Wayland环境优化版')
    parser.add_argument('--format', choices=['simple', 'original'], default='simple',
                       help='输出格式: simple (简单格式) 或 original (与control_info.txt相同的格式)')
    parser.add_argument('--mode', choices=['single', 'monitor'], default='single',
                       help='运行模式: single (单次检测) 或 monitor (连续监控)')
    parser.add_argument('--interval', type=float, default=0.5,
                       help='监控模式下的检测间隔（秒），默认0.5秒')
    parser.add_argument('--duration', type=float, default=2,
                       help='高亮持续时间（秒），默认2秒')

    args = parser.parse_args()

    print("� 通用偏移检测器 - Wayland环境优化版")
    print("=" * 60)
    print(f"🎮 运行模式: {args.mode}")
    print(f"📋 输出格式: {args.format}")
    if args.mode == 'monitor':
        print(f"⏱️  检测间隔: {args.interval}秒")
        print(f"✨ 高亮时长: {args.duration}秒")
    if args.format == 'original':
        print("📄 将输出与 control_info.txt 相同格式的 JSON 数据")
    print("=" * 60)

    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)

    try:
        if args.mode == 'single':
            # 单次检测模式（原有功能）
            detector = UniversalOffsetDetector()
            print(f"\n🚀 开始检测当前鼠标位置的控件...")
            success = detector.detect_control_at_mouse(output_format=args.format)

            if success:
                print(f"\n🎉 检测完成!")
                if args.format == 'original':
                    print(f"📄 原始格式数据已输出，与 control_info.txt 格式完全一致")
            else:
                print(f"\n❌ 检测失败")

        elif args.mode == 'monitor':
            # 连续监控模式（新功能，替代X11全局监听）
            monitor = WaylandControlMonitor(
                interval=args.interval,
                highlight_duration=args.duration
            )

            # 设置信号处理
            def signal_handler(sig, frame):
                print(f"\n🛑 收到信号 {sig}，正在停止监控...")
                monitor.stop_monitoring()
                sys.exit(0)

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # 开始监控
            monitor.start_monitoring()

            try:
                # 保持主线程运行
                while monitor.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                monitor.stop_monitoring()

    except Exception as e:
        print(f"❌ 运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
