#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
完美的控件高亮解决方案 - 细边框+完全透明内部+无窗口装饰
"""

import subprocess
import os

def highlight_control(x, y, width, height, 
                     duration=2,
                     color='red', 
                     border_width=2):
    """
    完美的控件高亮函数
    
    特点:
    - 完全无窗口装饰
    - 边框内部完全透明
    - 细边框样式
    - 不遮挡控件内容
    
    Args:
        x, y: 控件位置
        width, height: 控件尺寸  
        duration: 高亮持续时间（秒）
        color: 边框颜色 ('red', 'blue', 'green', 'yellow', 'purple', 'cyan')
        border_width: 边框宽度 (1-5推荐)
    
    Returns:
        bool: 是否成功显示高亮
    """
    
    # 创建完美高亮的X11脚本
    x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    # 连接到X11
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取边框颜色
    colormap = screen.default_colormap
    border_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 创建完美高亮窗口
    win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width={border_width},
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,        # 关键：完全透明内部
        border_pixel=border_pixel, # 彩色边框
        override_redirect=True,    # 关键：绕过窗口管理器
        colormap=X.CopyFromParent
    )
    
    # 设置窗口属性（防止窗口管理器干预）
    win.set_wm_name("Perfect Control Highlight")
    win.set_wm_class("highlight", "ControlHighlight")
    
    # 设置窗口类型为dock（避免窗口装饰）
    try:
        win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
    except:
        pass
    
    # 设置窗口状态
    try:
        win.change_property(
            disp.intern_atom("_NET_WM_STATE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
             disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
             disp.intern_atom("_NET_WM_STATE_ABOVE")]
        )
    except:
        pass
    
    # 显示高亮
    win.map()
    disp.sync()
    
    # 保持显示指定时间
    time.sleep({duration})
    
    # 清理资源
    win.unmap()
    win.destroy()
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    exit(1)
'''
    
    try:
        # 静默执行高亮
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

# 便捷预设函数
def quick_red_highlight(x, y, width, height, duration=2):
    """快速红色高亮"""
    return highlight_control(x, y, width, height, duration, 'red', 2)

def subtle_blue_highlight(x, y, width, height, duration=2):
    """精细蓝色高亮"""
    return highlight_control(x, y, width, height, duration, 'blue', 1)

def green_highlight(x, y, width, height, duration=2):
    """绿色高亮"""
    return highlight_control(x, y, width, height, duration, 'green', 2)

def demo_perfect_highlight():
    """演示完美高亮效果"""
    print("🎯 完美控件高亮演示")
    print("=" * 50)
    
    # 获取鼠标位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    demo_x = mouse_x - 100
    demo_y = mouse_y - 75
    demo_w = 200
    demo_h = 150
    
    print(f"演示位置: ({demo_x}, {demo_y}) {demo_w}×{demo_h}")
    print("\n✅ 完美高亮特点:")
    print("   • 完全无窗口装饰")
    print("   • 边框内部完全透明")
    print("   • 不遮挡控件内容")
    print("   • 细边框，简洁美观")
    print("   • 可自定义颜色和粗细")
    print()
    
    # 演示不同样式
    styles = [
        ('red', 2, '红色标准边框'),
        ('blue', 1, '蓝色细边框'),
        ('green', 3, '绿色粗边框'),
        ('purple', 2, '紫色标准边框')
    ]
    
    for color, width, desc in styles:
        print(f"🎨 演示: {desc}")
        success = highlight_control(demo_x, demo_y, demo_w, demo_h,
                                  duration=2, color=color, border_width=width)
        print("✅ 完成" if success else "❌ 失败")
        print()
    
    print("=" * 50)
    print("🎊 完美高亮演示完成！")
    print("\n💡 使用方法:")
    print("```python")
    print("from perfect_highlight import highlight_control")
    print("")
    print("# 基本使用")
    print("highlight_control(x, y, width, height)")
    print("")
    print("# 自定义样式")
    print("highlight_control(x, y, width, height,")
    print("                duration=3,")
    print("                color='blue',")
    print("                border_width=1)")
    print("```")

if __name__ == "__main__":
    demo_perfect_highlight()