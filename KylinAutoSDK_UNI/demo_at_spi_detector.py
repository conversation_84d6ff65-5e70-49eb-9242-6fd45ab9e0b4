#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
AT-SPI控件检测器完整演示脚本
展示所有主要功能和用法示例
"""

import time
import subprocess
import sys
from at_spi_control_detector import ATSPIControlDetector


def print_banner():
    """打印欢迎横幅"""
    print("🎯" + "=" * 78 + "🎯")
    print("🚀                    AT-SPI通用控件检测和高亮库                     🚀")
    print("🎯" + "=" * 78 + "🎯")
    print()
    print("📋 主要特性:")
    print("   ✅ 通用坐标检测 - 传入桌面坐标，获取具体控件信息")
    print("   ✅ 自适应窗口管理 - 自动根据鼠标位置获取对应应用窗口")  
    print("   ✅ 坐标转换 - 自动处理AT-SPI坐标转换，确保准确性")
    print("   ✅ 控件高亮 - 支持多种样式的控件边框高亮显示")
    print("   ✅ 跨桌面兼容 - 支持X11和Wayland环境")
    print("   ✅ 详细信息获取 - 提供控件的完整属性信息")
    print()


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None


def demo_basic_detection():
    """演示基本检测功能"""
    print("🔍 【演示 1】基本控件检测功能")
    print("-" * 60)
    
    # 获取演示坐标
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        demo_x, demo_y = mouse_x, mouse_y
        print(f"📍 使用当前鼠标位置: ({demo_x}, {demo_y})")
    else:
        demo_x, demo_y = 500, 300
        print(f"📍 使用默认坐标: ({demo_x}, {demo_y})")
    
    # 创建检测器
    detector = ATSPIControlDetector()
    
    # 执行检测
    print("\n🔍 正在检测控件...")
    result = detector.get_control_at_point(demo_x, demo_y)
    
    if result['success']:
        pos = result['position']
        print(f"✅ 检测成功！")
        print(f"   📏 控件位置: ({pos[0]}, {pos[1]})")
        print(f"   📐 控件尺寸: {pos[2]} × {pos[3]}")
        print(f"   💬 状态信息: {result['message']}")
        return detector, demo_x, demo_y
    else:
        print(f"❌ 检测失败: {result['message']}")
        return detector, demo_x, demo_y


def demo_highlight_features(detector, x, y):
    """演示高亮功能"""
    print("\n✨ 【演示 2】控件高亮功能")
    print("-" * 60)
    
    # 演示不同颜色的高亮
    colors = [
        ('red', '红色'),
        ('blue', '蓝色'), 
        ('green', '绿色'),
        ('yellow', '黄色'),
        ('purple', '紫色')
    ]
    
    for color, name in colors:
        print(f"🎨 {name}高亮演示...")
        result = detector.highlight_control_at_point(x, y, duration=1.5, color=color)
        
        if result['success'] and result['highlighted']:
            print(f"   ✅ {name}高亮成功")
        else:
            print(f"   ❌ {name}高亮失败: {result['message']}")
        
        time.sleep(0.5)
    
    # 演示不同边框宽度
    print("\n🖼️  不同边框宽度演示...")
    widths = [(1, '超细'), (2, '标准'), (3, '粗'), (4, '超粗')]
    
    for width, desc in widths:
        print(f"   📏 {desc}边框 (宽度{width})...")
        detector.highlight_control_at_point(x, y, duration=1, color='cyan', border_width=width)
        time.sleep(0.8)


def demo_detailed_info(detector, x, y):
    """演示详细信息获取"""
    print("\n📋 【演示 3】详细控件信息获取")
    print("-" * 60)
    
    print("🔍 获取并显示控件详细信息...")
    detector.print_control_info(x, y)
    
    # 获取结构化信息
    detailed_info = detector.get_detailed_control_info(x, y)
    
    if detailed_info['success']:
        print("\n📊 结构化信息摘要:")
        basic = detailed_info['basic_info']
        window = detailed_info['window_info']
        hierarchy = detailed_info['hierarchy_info']
        
        print(f"   🏷️  控件名称: {basic['name']}")
        print(f"   🎭 控件角色: {basic['role']}")
        print(f"   🪟 所属窗口: {window['window_name']}")
        print(f"   🔧 进程名称: {window['process_name']}")
        print(f"   🌳 层级深度: {hierarchy['parent_count']} 级")
        
        if detailed_info['states']:
            states_str = ', '.join(detailed_info['states'][:5])  # 只显示前5个状态
            if len(detailed_info['states']) > 5:
                states_str += f"... (+{len(detailed_info['states'])-5})"
            print(f"   🔄 控件状态: {states_str}")
        
        if detailed_info['actions']:
            actions_str = ', '.join(detailed_info['actions'][:3])  # 只显示前3个动作
            if len(detailed_info['actions']) > 3:
                actions_str += f"... (+{len(detailed_info['actions'])-3})"
            print(f"   ⚡ 可用动作: {actions_str}")


def demo_multiple_positions():
    """演示多位置检测"""
    print("\n🎯 【演示 4】多位置控件检测")
    print("-" * 60)
    
    # 获取基准位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        base_x, base_y = mouse_x, mouse_y
    else:
        base_x, base_y = 500, 300
    
    # 计算测试点（以当前位置为中心的十字形）
    offset = 100
    test_positions = [
        (base_x, base_y, '中心'),
        (base_x - offset, base_y, '左侧'),
        (base_x + offset, base_y, '右侧'),
        (base_x, base_y - offset, '上方'),
        (base_x, base_y + offset, '下方')
    ]
    
    detector = ATSPIControlDetector()
    colors = ['red', 'blue', 'green', 'yellow', 'purple']
    
    print(f"📍 基准位置: ({base_x}, {base_y})")
    print(f"🔍 检测 {len(test_positions)} 个位置...")
    
    found_count = 0
    for i, (x, y, desc) in enumerate(test_positions):
        color = colors[i % len(colors)]
        
        print(f"\n   📌 检测点 {i+1}: {desc} ({x}, {y})")
        result = detector.get_control_at_point(x, y, include_details=False)
        
        if result['success']:
            print(f"      ✅ 找到控件，{color}高亮")
            detector.highlight_control_at_point(x, y, duration=1, color=color, border_width=1)
            found_count += 1
        else:
            print(f"      ❌ 未找到控件")
        
        time.sleep(0.5)
    
    print(f"\n📊 检测结果: {found_count}/{len(test_positions)} 个位置找到控件")


def demo_advanced_features(detector):
    """演示高级功能"""
    print("\n🚀 【演示 5】高级功能")
    print("-" * 60)
    
    # 演示最后控件高亮
    print("🔄 高亮最后检测到的控件...")
    result = detector.highlight_last_detected_control(duration=2, color='orange', border_width=3)
    
    if result['success']:
        print(f"   ✅ 最后控件高亮成功")
        if result['highlighted']:
            pos = result['control_position']
            print(f"   📏 控件位置: ({pos[0]}, {pos[1]}) {pos[2]}×{pos[3]}")
    else:
        print(f"   ❌ 最后控件高亮失败: {result['message']}")
    
    time.sleep(2)
    
    # 演示便捷函数
    print("\n🔧 便捷函数演示...")
    from at_spi_control_detector import highlight_control_at_point
    
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        print(f"   🎯 使用便捷函数高亮鼠标位置 ({mouse_x}, {mouse_y})")
        highlight_control_at_point(mouse_x, mouse_y, duration=2, color='cyan')
    else:
        print("   ⚠️  无法获取鼠标位置，跳过便捷函数演示")


def demo_performance_test():
    """演示性能测试"""
    print("\n⚡ 【演示 6】性能测试")
    print("-" * 60)
    
    detector = ATSPIControlDetector()
    
    # 获取测试位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is not None:
        test_x, test_y = mouse_x, mouse_y
    else:
        test_x, test_y = 500, 300
    
    # 速度测试
    print(f"📊 执行 10 次检测，测试速度...")
    start_time = time.time()
    
    success_count = 0
    for i in range(10):
        result = detector.get_control_at_point(test_x + i*2, test_y + i*2, include_details=False)
        if result['success']:
            success_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / 10
    
    print(f"   ✅ 完成! 总用时: {total_time:.2f}秒")
    print(f"   📈 平均用时: {avg_time:.3f}秒/次")
    print(f"   🎯 成功率: {success_count}/10 ({success_count*10}%)")
    
    # 快速高亮测试
    if success_count > 0:
        print(f"\n⚡ 快速高亮测试...")
        for i in range(3):
            detector.highlight_control_at_point(test_x, test_y, duration=0.5, color='red', border_width=1)
            time.sleep(0.2)


def interactive_demo():
    """交互式演示"""
    print("\n🎮 【演示 7】交互式功能")
    print("-" * 60)
    
    print("💡 移动鼠标到想要检测的控件上，然后按 Enter 继续...")
    input("   按 Enter 键开始交互式演示...")
    
    # 获取当前鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is None:
        print("❌ 无法获取鼠标位置，跳过交互式演示")
        return
    
    detector = ATSPIControlDetector()
    
    print(f"🎯 检测鼠标当前位置: ({mouse_x}, {mouse_y})")
    
    # 检测控件
    result = detector.get_control_at_point(mouse_x, mouse_y)
    
    if result['success']:
        print("✅ 发现控件!")
        
        # 提供交互选项
        while True:
            print("\n选择操作:")
            print("1. 🔴 红色高亮")
            print("2. 🔵 蓝色高亮") 
            print("3. 🟢 绿色高亮")
            print("4. 📋 显示详细信息")
            print("5. 🔄 重新检测当前位置")
            print("6. ➡️  继续下个演示")
            
            choice = input("请选择 (1-6): ").strip()
            
            if choice == '1':
                detector.highlight_control_at_point(mouse_x, mouse_y, color='red', duration=2)
            elif choice == '2':
                detector.highlight_control_at_point(mouse_x, mouse_y, color='blue', duration=2)
            elif choice == '3':
                detector.highlight_control_at_point(mouse_x, mouse_y, color='green', duration=2)
            elif choice == '4':
                detector.print_control_info(mouse_x, mouse_y)
            elif choice == '5':
                new_mouse_x, new_mouse_y = get_mouse_position()
                if new_mouse_x is not None:
                    mouse_x, mouse_y = new_mouse_x, new_mouse_y
                    print(f"🔄 重新检测位置: ({mouse_x}, {mouse_y})")
                    result = detector.get_control_at_point(mouse_x, mouse_y)
                    if not result['success']:
                        print(f"❌ 检测失败: {result['message']}")
                        break
                else:
                    print("❌ 无法获取鼠标位置")
            elif choice == '6':
                break
            else:
                print("❌ 无效选择，请重新输入")
    else:
        print(f"❌ 未发现控件: {result['message']}")


def print_summary():
    """打印演示总结"""
    print("\n🎉 【演示完成】功能总结")
    print("=" * 60)
    print("✅ 已演示的功能:")
    print("   1. ✅ 基本控件检测 - 根据坐标获取控件信息")
    print("   2. ✅ 控件高亮显示 - 多种颜色和边框样式")
    print("   3. ✅ 详细信息获取 - 完整的控件属性信息")
    print("   4. ✅ 多位置检测 - 批量检测多个坐标")
    print("   5. ✅ 高级功能 - 最后控件高亮、便捷函数")
    print("   6. ✅ 性能测试 - 检测速度和成功率")
    print("   7. ✅ 交互式功能 - 实时鼠标位置检测")
    
    print("\n🚀 可用的工具:")
    print("   📁 at_spi_control_detector.py - 主接口库")
    print("   🖱️  mouse_hover_control_detector.py - 鼠标悬停检测工具")
    print("   🧪 simple_control_test.py - 简单测试工具")
    print("   📚 AT_SPI_CONTROL_DETECTOR_README.md - 详细文档")
    
    print("\n💡 下一步:")
    print("   1. 阅读 README.md 了解详细用法")
    print("   2. 运行 mouse_hover_control_detector.py 进行实时测试")
    print("   3. 在您的项目中集成 at_spi_control_detector.py")
    print("\n🎯 AT-SPI控件检测库演示完成! 感谢体验!")
    print("=" * 60)


def main():
    """主演示函数"""
    # 检查环境
    if not sys.platform.startswith('linux'):
        print("❌ 错误: 此程序仅支持Linux系统")
        sys.exit(1)
    
    import os
    if not os.getenv('DISPLAY'):
        print("❌ 错误: 未检测到DISPLAY环境变量")
        print("请在图形界面环境中运行此程序")
        sys.exit(1)
    
    # 开始演示
    print_banner()
    
    try:
        # 基础演示
        detector, demo_x, demo_y = demo_basic_detection()
        
        # 等待用户确认
        input("\n按 Enter 键继续高亮功能演示...")
        demo_highlight_features(detector, demo_x, demo_y)
        
        input("\n按 Enter 键继续详细信息演示...")
        demo_detailed_info(detector, demo_x, demo_y)
        
        input("\n按 Enter 键继续多位置检测演示...")
        demo_multiple_positions()
        
        input("\n按 Enter 键继续高级功能演示...")
        demo_advanced_features(detector)
        
        input("\n按 Enter 键继续性能测试...")
        demo_performance_test()
        
        input("\n按 Enter 键开始交互式演示...")
        interactive_demo()
        
        # 总结
        print_summary()
        
    except KeyboardInterrupt:
        print("\n\n🛑 演示被用户中断")
        print("👋 感谢体验AT-SPI控件检测库!")
    except Exception as e:
        print(f"\n❌ 演示过程中发生异常: {e}")
        print("请检查环境配置或查看README.md获取帮助")


if __name__ == "__main__":
    main()