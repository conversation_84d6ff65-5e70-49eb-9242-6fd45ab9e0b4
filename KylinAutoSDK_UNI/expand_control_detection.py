#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
扩展UNI控件识别范围的修复工具
解决只能识别pushbutton的问题
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>in<PERSON>utoSDK_UNI/src')

def expand_uni_control_detection():
    """扩展UNI控件识别范围"""
    print("🔧 扩展UNI控件识别范围")
    print("=" * 70)
    
    # 1. 读取当前的UNI_new.py
    uni_file = '/home/<USER>/KylinAutoSDK_UNI/src/UNI_new.py'
    
    try:
        with open(uni_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ 读取UNI_new.py成功")
        
        # 2. 定义扩展的控件类型
        print("\n📋 扩展控件类型支持")
        print("-" * 40)
        
        # 原始的极限制类型
        original_priority = "{'push button', 'button', 'radio button', 'check box'}"
        original_interactive = """{'push button', 'button', 'radio button', 'check box',
        'toggle button', 'menu item', 'combo box', 'text'}"""
        
        # 扩展的控件类型
        expanded_priority = """{
        # 基础交互控件
        'push button', 'button', 'radio button', 'check box', 'toggle button',
        
        # 文本和输入控件  
        'text', 'label', 'static text', 'editable text', 'entry', 'text box',
        'password text', 'paragraph',
        
        # 选择和列表控件
        'combo box', 'list', 'list item', 'menu item', 'tree item', 'table cell'
    }"""
        
        expanded_interactive = """{
        # 基础交互控件
        'push button', 'button', 'radio button', 'check box', 'toggle button',
        
        # 文本控件
        'text', 'label', 'static text', 'editable text', 'entry', 'text box',
        'password text', 'paragraph', 'heading',
        
        # 输入控件
        'combo box', 'list box', 'spin button', 'password field',
        
        # 选择控件
        'menu item', 'menu', 'list', 'list item', 'tree', 'tree item',
        'table', 'table cell', 'table row', 'table column header',
        
        # 容器控件
        'panel', 'pane', 'tab list', 'tab', 'frame', 'filler',
        
        # 图形控件
        'image', 'icon', 'canvas',
        
        # 其他常用控件
        'link', 'separator', 'group box'
    }"""
        
        print("🎯 原始支持的控件类型:")
        print("   priority_roles: 4种")
        print("   interactive_roles: 8种")
        print()
        
        print("🚀 扩展后支持的控件类型:")
        print("   priority_roles: ~15种")
        print("   interactive_roles: ~35种")
        print()
        
        # 3. 替换控件类型定义
        print("📝 正在修改UNI_new.py...")
        
        # 替换 search_control_ultra_fast 中的 priority_roles
        if "priority_roles = {'push button', 'button', 'radio button', 'check box'}" in content:
            content = content.replace(
                "priority_roles = {'push button', 'button', 'radio button', 'check box'}",
                f"priority_roles = {expanded_priority}"
            )
            print("✅ 已扩展 search_control_ultra_fast 的控件类型")
        
        # 替换 search_control_optimized 中的 interactive_roles
        interactive_pattern = """interactive_roles = {
        'push button', 'button', 'radio button', 'check box',
        'toggle button', 'menu item', 'combo box', 'text'
    }"""
        
        if "interactive_roles = {" in content and "'push button', 'button', 'radio button', 'check box'," in content:
            # 找到并替换整个interactive_roles定义
            import re
            pattern = r"interactive_roles = \{[^}]+\}"
            replacement = f"interactive_roles = {expanded_interactive}"
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            print("✅ 已扩展 search_control_optimized 的控件类型")
        
        # 4. 增加搜索深度和范围
        print("\n📈 优化搜索参数")
        print("-" * 20)
        
        # 增加最大搜索深度
        if "max_depth = 4  # 减少最大深度" in content:
            content = content.replace(
                "max_depth = 4  # 减少最大深度",
                "max_depth = 6  # 增加搜索深度以找到更多控件"
            )
            print("✅ 增加搜索深度: 4 → 6")
        
        # 增加搜索数量限制
        if "len(search_queue) < 50" in content:
            content = content.replace(
                "len(search_queue) < 50",
                "len(search_queue) < 100"
            )
            print("✅ 增加搜索数量限制: 50 → 100")
        
        # 增加子元素搜索数量
        if "min(element.childCount, 5)" in content:
            content = content.replace(
                "min(element.childCount, 5)",
                "min(element.childCount, 10)"
            )
            print("✅ 增加子元素搜索数量: 5 → 10")
        
        # 5. 保存修改后的文件
        with open(uni_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("\n✅ UNI_new.py修改完成!")
        
        # 6. 创建测试脚本
        create_enhanced_test_script()
        
        print("\n" + "=" * 70)
        print("🎉 控件识别范围扩展完成!")
        print()
        print("🚀 改进效果:")
        print("   • 支持的控件类型从8种扩展到35+种")
        print("   • 包含Qt应用中的常用控件类型")
        print("   • 增加了搜索深度和范围")
        print("   • 保持了性能优化")
        print()
        print("💡 现在可以识别的控件类型包括:")
        print("   • 文本: label, static text, editable text")
        print("   • 列表: list, list item, tree, tree item")
        print("   • 表格: table, table cell, table row")
        print("   • 容器: panel, pane, frame, filler")
        print("   • 图形: image, icon, canvas")
        print("   • 其他: link, separator, group box等")
        
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        import traceback
        traceback.print_exc()

def create_enhanced_test_script():
    """创建增强测试脚本"""
    test_script = '''#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
增强控件识别测试脚本
测试扩展后的控件识别范围
"""

import sys
import os
import subprocess
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

def test_enhanced_control_detection():
    """测试增强后的控件识别"""
    print("🧪 增强控件识别测试")
    print("=" * 50)
    
    try:
        from UNI_new import UNI
        
        # 获取鼠标位置
        try:
            result = subprocess.run(['xdotool', 'getmouselocation'], 
                                  capture_output=True, text=True, env={'DISPLAY': ':0'})
            if result.returncode == 0:
                parts = result.stdout.strip().split()
                mouse_x = int(parts[0].split(':')[1])
                mouse_y = int(parts[1].split(':')[1])
            else:
                mouse_x, mouse_y = 1000, 500
        except:
            mouse_x, mouse_y = 1000, 500
        
        print(f"📍 测试位置: ({mouse_x}, {mouse_y})")
        print()
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试不同的搜索模式
        print("🔍 测试1: 快速搜索模式")
        control_data1, info1 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=True, highlight=False)
        
        if control_data1:
            print(f"   ✅ 找到控件: {control_data1.get('Name', 'N/A')} ({control_data1.get('Rolename', 'N/A')})")
        else:
            print(f"   ❌ 未找到控件: {info1}")
        
        print()
        print("🔍 测试2: 完整搜索模式")
        control_data2, info2 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        
        if control_data2:
            print(f"   ✅ 找到控件: {control_data2.get('Name', 'N/A')} ({control_data2.get('Rolename', 'N/A')})")
            
            # 显示详细信息
            print("   📊 控件详细信息:")
            print(f"      类型: {control_data2.get('Rolename', 'N/A')}")
            print(f"      名称: {control_data2.get('Name', 'N/A')}")
            print(f"      描述: {control_data2.get('Description', 'N/A')}")
            print(f"      状态: {', '.join(control_data2.get('States', [])[:3])}")
            print(f"      动作: {', '.join(control_data2.get('Actions', [])[:3])}")
            
        else:
            print(f"   ❌ 未找到控件: {info2}")
        
        print()
        print("🔍 测试3: 带高亮的控件识别")
        control_data3, info3 = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
        
        if control_data3:
            print(f"   ✅ 找到控件并高亮: {control_data3.get('Name', 'N/A')} ({control_data3.get('Rolename', 'N/A')})")
            print("   🎨 高亮效果: 40像素偏移修正 + 无边框显示")
        else:
            print(f"   ❌ 未找到控件: {info3}")
        
        print()
        print("=" * 50)
        print("🎉 增强控件识别测试完成!")
        
        if control_data2:
            print("✅ 控件识别增强成功!")
            print(f"   识别到控件类型: {control_data2.get('Rolename', 'N/A')}")
            if control_data2.get('Rolename', '').lower() not in ['push button', 'button']:
                print("🎯 成功! 识别到了pushbutton以外的控件类型!")
        else:
            print("⚠️ 在当前位置未找到控件，请移动鼠标到其他控件上重试")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_enhanced_control_detection()
'''
    
    with open('/home/<USER>/KylinAutoSDK_UNI/test_enhanced_control_detection.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 创建了增强测试脚本: test_enhanced_control_detection.py")

if __name__ == "__main__":
    expand_uni_control_detection()