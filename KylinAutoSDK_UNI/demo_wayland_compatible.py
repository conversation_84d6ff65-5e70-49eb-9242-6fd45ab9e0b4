#!/usr/bin/env python3
"""
演示 Wayland 环境下与 kdk_getElement_Uni 兼容的控件检测
"""

import sys
import time

# 添加项目路径
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

def demo_compatible_usage():
    """演示兼容用法"""
    print("🎯 Wayland 环境下的 kdk_getElement_Uni 兼容演示")
    print("=" * 60)
    
    try:
        # 导入 Wayland 兼容函数
        from UNI_new import kdk_getElement_Uni_wayland
        
        # 使用您在代码中选择的坐标
        x, y = 472, 620
        
        print(f"📍 测试坐标: ({x}, {y})")
        print(f"🔍 调用 kdk_getElement_Uni_wayland({x}, {y}, False)")
        print()
        
        # 调用函数 - 与原始 kdk_getElement_Uni 完全相同的调用方式
        start_time = time.time()
        a2, info = kdk_getElement_Uni_wayland(x, y, False)
        elapsed = time.time() - start_time
        
        print(f"⏱️  执行时间: {elapsed:.3f}s")
        print(f"📄 返回信息: {info}")
        print()
        
        if a2:
            print("✅ 成功找到控件！")
            print("📊 控件信息 (与原始 kdk_getElement_Uni 返回格式完全一致):")
            print("-" * 50)
            
            # 显示关键信息
            print(f"控件名称: {a2.get('Name', 'N/A')}")
            print(f"控件角色: {a2.get('Rolename', 'N/A')}")
            print(f"所属进程: {a2.get('ProcessName', 'N/A')}")
            print(f"窗口名称: {a2.get('WindowName', 'N/A')}")
            print(f"控件描述: {a2.get('Description', 'N/A')}")
            print(f"记录位置: {a2.get('RecordPosition', 'N/A')}")
            
            # 显示坐标信息
            coords = a2.get('Coords', {})
            if coords:
                print(f"控件坐标: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                print(f"控件大小: {coords.get('width', 'N/A')} × {coords.get('height', 'N/A')}")
            
            # 显示状态和动作
            states = a2.get('States', [])
            if isinstance(states, list) and states:
                print(f"控件状态: {', '.join(states[:3])}{'...' if len(states) > 3 else ''}")
            
            actions = a2.get('Actions', [])
            if isinstance(actions, list) and actions:
                print(f"可用动作: {', '.join(actions[:3])}{'...' if len(actions) > 3 else ''}")
            
            # 显示其他重要字段
            print(f"进程ID: {a2.get('ProcessID', 'N/A')}")
            print(f"元素ID: {a2.get('ID', 'N/A')}")
            print(f"父级索引: {a2.get('Index_in_parent', 'N/A')}")
            print(f"子元素数: {a2.get('ChildrenCount', 'N/A')}")
            print(f"唯一标识: {a2.get('Key', 'N/A')[:50]}{'...' if len(str(a2.get('Key', ''))) > 50 else ''}")
            
            print()
            print("🔧 完整的数据结构字段:")
            for key in sorted(a2.keys()):
                if not key.startswith('_'):  # 跳过内部字段
                    value = a2[key]
                    if isinstance(value, (dict, list)) and len(str(value)) > 50:
                        print(f"   {key}: {type(value).__name__} (长度: {len(value) if hasattr(value, '__len__') else 'N/A'})")
                    else:
                        print(f"   {key}: {value}")
        else:
            print("❌ 未找到控件")
        
        print()
        print("🚀 测试快速模式:")
        print(f"🔍 调用 kdk_getElement_Uni_wayland({x}, {y}, True)")
        
        # 测试快速模式
        start_time = time.time()
        extents, info_quick = kdk_getElement_Uni_wayland(x, y, True)
        elapsed_quick = time.time() - start_time
        
        print(f"⏱️  执行时间: {elapsed_quick:.3f}s")
        print(f"📄 返回信息: {info_quick}")
        
        if extents:
            print("✅ 快速模式成功！")
            print(f"📐 控件范围 (extents 对象):")
            print(f"   x: {extents.x}")
            print(f"   y: {extents.y}")
            print(f"   width: {extents.width}")
            print(f"   height: {extents.height}")
        else:
            print("❌ 快速模式失败")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

def demo_original_style_usage():
    """演示原始风格的用法"""
    print(f"\n🎨 原始 kdk_getElement_Uni 风格的用法演示")
    print("=" * 60)
    
    try:
        from UNI_new import kdk_getElement_Uni_wayland
        
        # 模拟您的原始代码风格
        print("# 您的原始代码:")
        print("# a2, info = a.kdk_getElement_Uni(472,620,False)")
        print()
        print("# 现在可以这样使用:")
        print("# a2, info = kdk_getElement_Uni_wayland(472, 620, False)")
        print()
        
        # 实际执行
        a2, info = kdk_getElement_Uni_wayland(472, 620, False)
        
        print(f"返回结果:")
        print(f"  a2 = {type(a2).__name__} ({'有数据' if a2 else '无数据'})")
        print(f"  info = '{info}'")
        
        if a2:
            print(f"\n控件基本信息:")
            print(f"  名称: {a2.get('Name', 'N/A')}")
            print(f"  类型: {a2.get('Rolename', 'N/A')}")
            print(f"  进程: {a2.get('ProcessName', 'N/A')}")
            
            # 演示如何访问坐标信息
            coords = a2.get('Coords', {})
            if coords:
                print(f"  位置: ({coords['x']}, {coords['y']})")
                print(f"  大小: {coords['width']} × {coords['height']}")
        
        print(f"\n✅ 完全兼容原始 kdk_getElement_Uni 的返回格式！")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def show_compatibility_summary():
    """显示兼容性总结"""
    print(f"\n📋 兼容性总结")
    print("=" * 40)
    
    print("✅ 函数签名兼容:")
    print("   原始: kdk_getElement_Uni(x, y, quick=False, menuele=None)")
    print("   新版: kdk_getElement_Uni_wayland(x, y, quick=False, menuele=None)")
    print()
    
    print("✅ 返回值兼容:")
    print("   普通模式: (控件数据dict, 信息字符串)")
    print("   快速模式: (extents对象, 信息字符串)")
    print()
    
    print("✅ 数据结构兼容:")
    print("   包含所有原始字段: Name, Rolename, ProcessName, Coords, 等")
    print("   保持相同的数据类型和结构")
    print()
    
    print("✅ 使用方式兼容:")
    print("   可以直接替换原始函数调用")
    print("   无需修改现有代码逻辑")
    print()
    
    print("🎯 建议用法:")
    print("   在 Wayland 环境下，使用 kdk_getElement_Uni_wayland")
    print("   在 X11 环境下，继续使用原始 kdk_getElement_Uni")
    print("   或者创建一个自动检测环境的包装函数")

if __name__ == "__main__":
    demo_compatible_usage()
    demo_original_style_usage()
    show_compatibility_summary()
