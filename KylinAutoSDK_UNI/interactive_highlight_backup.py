#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
交互式高亮模块 - 在高亮边框旁边添加确认/取消按钮
"""

import subprocess
import os
import time
import threading
import tempfile
import json
from datetime import datetime


class InteractiveHighlight:
    """交互式高亮类 - 支持确认/取消按钮"""
    
    def __init__(self):
        """初始化交互式高亮"""
        self.result_file = None
        self.button_windows = []
        self.highlight_windows = []
        self.user_choice = None
        self.waiting = False
        
    def show_interactive_highlight(self, x, y, width, height, 
                                 control_info=None,
                                 duration=30,  # 默认30秒超时
                                 color='cyan', 
                                 border_width=2):
        """
        显示带交互按钮的高亮
        
        Args:
            x, y: 控件位置
            width, height: 控件尺寸
            control_info: 控件信息字典
            duration: 超时时间（秒）
            color: 边框颜色
            border_width: 边框宽度
            
        Returns:
            str: 'confirm', 'cancel', 'timeout'
        """
        
        # 创建临时结果文件
        self.result_file = tempfile.mktemp(suffix='.json')
        
        # 计算按钮位置（在控件右下角）
        button_y = y + height + 10  # 控件下方10像素
        confirm_x = x + width - 120  # 右对齐
        cancel_x = x + width - 60   # 确认按钮右侧
        
        # 确保按钮不超出屏幕
        if confirm_x < 0:
            confirm_x = x
            cancel_x = x + 60
        
        print(f"🎯 显示交互式高亮")
        print(f"   控件位置: ({x}, {y}) {width}×{height}")
        print(f"   确认按钮: ({confirm_x}, {button_y})")
        print(f"   取消按钮: ({cancel_x}, {button_y})")
        
        # 创建X11脚本
        x11_script = self._create_interactive_script(
            x, y, width, height,
            confirm_x, cancel_x, button_y,
            color, border_width, duration,
            control_info
        )
        
        try:
            # 启动交互式高亮
            process = subprocess.Popen(['python3', '-c', x11_script], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
            
            # 等待用户选择或超时
            start_time = time.time()
            while time.time() - start_time < duration:
                if os.path.exists(self.result_file):
                    try:
                        with open(self.result_file, 'r') as f:
                            result = f.read().strip()
                        os.remove(self.result_file)
                        process.terminate()
                        return result
                    except:
                        pass
                time.sleep(0.1)
            
            # 超时
            process.terminate()
            if os.path.exists(self.result_file):
                os.remove(self.result_file)
            return 'timeout'
            
        except Exception as e:
            print(f"❌ 交互式高亮失败: {e}")
            return 'error'
    
    def _create_interactive_script(self, x, y, width, height,
                                 confirm_x, cancel_x, button_y,
                                 color, border_width, duration, control_info):
        """创建交互式X11脚本"""
        
        # 控件信息文本 - 简化处理，移除特殊字符
        info_text = ""
        if control_info:
            name = control_info.get('name', 'N/A')
            role = control_info.get('role', 'N/A')
            # 只保留ASCII字符，移除可能的特殊字符
            name = ''.join(c if ord(c) < 128 else '?' for c in str(name))
            role = ''.join(c if ord(c) < 128 else '?' for c in str(role))
            info_text = f"{name} [{role}]"
        
        script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display, Xutil
    from Xlib.protocol import event
    import time
    import threading
    
    # 颜色映射
    color_map = {{
        'red': 0xFF0000, 'green': 0x00FF00, 'blue': 0x0000FF,
        'yellow': 0xFFFF00, 'purple': 0xFF00FF, 'cyan': 0x00FFFF,
        'orange': 0xFF8000, 'lime': 0x80FF00, 'pink': 0xFF8080
    }}
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 创建颜色
    colormap = screen.default_colormap
    border_color = color_map.get('{color}', 0xFF0000)
    
    # 创建高亮边框窗口
    border_windows = []
    
    # 上边框
    top_win = root.create_window(
        {x}, {y}, {width}, {border_width}, 0,
        screen.root_depth, X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=border_color,
        override_redirect=True,
        event_mask=X.ExposureMask
    )
    
    # 下边框  
    bottom_win = root.create_window(
        {x}, {y + height - border_width}, {width}, {border_width}, 0,
        screen.root_depth, X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=border_color,
        override_redirect=True,
        event_mask=X.ExposureMask
    )
    
    # 左边框
    left_win = root.create_window(
        {x}, {y}, {border_width}, {height}, 0,
        screen.root_depth, X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=border_color,
        override_redirect=True,
        event_mask=X.ExposureMask
    )
    
    # 右边框
    right_win = root.create_window(
        {x + width - border_width}, {y}, {border_width}, {height}, 0,
        screen.root_depth, X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=border_color,
        override_redirect=True,
        event_mask=X.ExposureMask
    )
    
    border_windows = [top_win, bottom_win, left_win, right_win]
    
    # 创建确认按钮（深绿色，提高对比度）
    confirm_btn = root.create_window(
        {confirm_x}, {button_y}, 50, 30, 2,
        screen.root_depth, X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0x006600,  # 深绿色（原来是0x00AA00）
        border_pixel=0x004400,     # 更深绿色边框
        override_redirect=True,
        event_mask=X.ButtonPressMask | X.ExposureMask | X.EnterWindowMask | X.LeaveWindowMask
    )
    
    # 创建取消按钮（深红色，提高对比度）
    cancel_btn = root.create_window(
        {cancel_x}, {button_y}, 50, 30, 2,
        screen.root_depth, X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0x660000,  # 深红色（原来是0xAA0000）
        border_pixel=0x440000,     # 更深红色边框
        override_redirect=True,
        event_mask=X.ButtonPressMask | X.ExposureMask | X.EnterWindowMask | X.LeaveWindowMask
    )
    
    # 创建信息显示窗口（如果有控件信息）
    info_win = None
    if "{info_text}":
        # 使用深蓝色背景，提高对比度
        info_win = root.create_window(
            {x}, {y - 30}, max(250, len("{info_text}") * 8), 25, 1,
            screen.root_depth, X.InputOutput,
            visual=X.CopyFromParent,
            background_pixel=0x003366,  # 深蓝色背景
            border_pixel=0x0066CC,     # 蓝色边框
            override_redirect=True,
            event_mask=X.ExposureMask
        )
    
    # 显示所有窗口
    for win in border_windows:
        win.map()
    
    confirm_btn.map()
    cancel_btn.map()
    
    if info_win:
        info_win.map()
    
    disp.sync()
    
    # 创建字体和GC用于绘制文本
    try:
        # 尝试使用更大的字体
        font = disp.open_font('-*-*-bold-r-*-*-*-180-*-*-*-*-*-*')
        gc = confirm_btn.create_gc(foreground=0xFFFFFF, background=0x006600, font=font, line_width=2)
        gc_cancel = cancel_btn.create_gc(foreground=0xFFFFFF, background=0x660000, font=font, line_width=2)
        if info_win:
            gc_info = info_win.create_gc(foreground=0xFFFFFF, font=font)
    except:
        try:
            font = disp.open_font('fixed')
            gc = confirm_btn.create_gc(foreground=0xFFFFFF, background=0x006600, font=font, line_width=2)
            gc_cancel = cancel_btn.create_gc(foreground=0xFFFFFF, background=0x660000, font=font, line_width=2)
            if info_win:
                gc_info = info_win.create_gc(foreground=0xFFFFFF, font=font)
        except:
            gc = confirm_btn.create_gc(foreground=0xFFFFFF, background=0x006600, line_width=2)
            gc_cancel = cancel_btn.create_gc(foreground=0xFFFFFF, background=0x660000, line_width=2)
            if info_win:
                gc_info = info_win.create_gc(foreground=0xFFFFFF)
    
    # 绘制按钮文本和图形
    def draw_buttons():
        try:
            # 使用简单的文本，避免编码问题
            gc_white = confirm_btn.create_gc(foreground=0xFFFFFF, line_width=2)
            gc_white_cancel = cancel_btn.create_gc(foreground=0xFFFFFF, line_width=2)
            
            # 使用简单的ASCII文本
            try:
                # 尝试使用较大的字体
                font_large = disp.open_font('-*-*-bold-r-*-*-*-140-*-*-*-*-*-*')
                gc_text = confirm_btn.create_gc(foreground=0xFFFFFF, font=font_large)
                gc_text_cancel = cancel_btn.create_gc(foreground=0xFFFFFF, font=font_large)
                confirm_btn.draw_text(gc_text, 10, 20, "YES")
                cancel_btn.draw_text(gc_text_cancel, 15, 20, "NO")
            except:
                # 如果大字体失败，使用默认字体
                confirm_btn.draw_text(gc, 10, 20, "YES")
                cancel_btn.draw_text(gc_cancel, 15, 20, "NO")
            
            # 备选方案：绘制简单图形
            # 确认按钮 - 绘制勾号
            # confirm_btn.draw_line(gc_white, 15, 18, 20, 23)  # 左斜线
            # confirm_btn.draw_line(gc_white, 20, 23, 35, 8)   # 右斜线
            
            # 取消按钮 - 绘制X号
            # cancel_btn.draw_line(gc_white_cancel, 15, 10, 35, 20)  # 左上到右下
            # cancel_btn.draw_line(gc_white_cancel, 15, 20, 35, 10)  # 左下到右上
            
            if info_win and "{info_text}":
                # 绘制信息文本，确保使用ASCII字符
                try:
                    info_win.draw_text(gc_info, 5, 18, "{info_text}"[:30])
                except:
                    # 如果失败，尝试使用更简单的文本
                    info_win.draw_text(gc_info, 5, 18, "Control Info")
        except:
            pass
    
    draw_buttons()
    disp.sync()
    
    # 事件处理
    result_written = False
    start_time = time.time()
    
    while time.time() - start_time < {duration} and not result_written:
        if disp.pending_events():
            e = disp.next_event()
            
            if e.type == X.ButtonPress:
                if e.window == confirm_btn:
                    # 确认按钮被点击
                    with open('{self.result_file}', 'w') as f:
                        f.write('confirm')
                    result_written = True
                    break
                elif e.window == cancel_btn:
                    # 取消按钮被点击
                    with open('{self.result_file}', 'w') as f:
                        f.write('cancel')
                    result_written = True
                    break
            
            elif e.type == X.Expose:
                # 重绘按钮
                draw_buttons()
                disp.sync()
            
            elif e.type == X.EnterNotify:
                # 鼠标悬停效果
                if e.window == confirm_btn:
                    confirm_btn.change_attributes(background_pixel=0x008800)  # 中等绿色
                elif e.window == cancel_btn:
                    cancel_btn.change_attributes(background_pixel=0x880000)   # 中等红色
                disp.sync()
            
            elif e.type == X.LeaveNotify:
                # 恢复原色
                if e.window == confirm_btn:
                    confirm_btn.change_attributes(background_pixel=0x006600)  # 深绿色
                elif e.window == cancel_btn:
                    cancel_btn.change_attributes(background_pixel=0x660000)   # 深红色
                disp.sync()
        
        time.sleep(0.01)
    
    # 清理窗口
    for win in border_windows:
        win.unmap()
        win.destroy()
    
    confirm_btn.unmap()
    confirm_btn.destroy()
    cancel_btn.unmap() 
    cancel_btn.destroy()
    
    if info_win:
        info_win.unmap()
        info_win.destroy()
    
    disp.sync()
    disp.close()
    
    # 如果没有写入结果，说明超时
    if not result_written:
        with open('{self.result_file}', 'w') as f:
            f.write('timeout')

except Exception as e:
    # 错误处理
    try:
        with open('{self.result_file}', 'w') as f:
            f.write('error')
    except:
        pass
    exit(1)
'''
        return script


def interactive_highlight(x, y, width, height, control_info=None, **kwargs):
    """
    便捷的交互式高亮函数
    
    Args:
        x, y: 控件位置
        width, height: 控件尺寸  
        control_info: 控件信息
        **kwargs: 其他参数
        
    Returns:
        str: 用户选择结果
    """
    highlighter = InteractiveHighlight()
    return highlighter.show_interactive_highlight(
        x, y, width, height, control_info, **kwargs
    )


# 测试函数
def test_interactive_highlight():
    """测试交互式高亮"""
    print("🧪 测试交互式高亮功能")
    
    # 获取鼠标位置作为测试区域
    try:
        import subprocess
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            output = result.stdout.strip()
            # 解析 "x:123 y:456 screen:0 window:789"
            parts = output.split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 500, 300
    except:
        mouse_x, mouse_y = 500, 300
    
    # 测试控件信息
    test_control = {
        'name': '测试按钮',
        'role': 'push button',
        'position': (mouse_x, mouse_y),
        'size': (120, 40)
    }
    
    print(f"📍 测试位置: ({mouse_x}, {mouse_y})")
    print("💡 将显示交互式高亮，请点击绿色✓确认或红色✗取消")
    
    result = interactive_highlight(
        mouse_x - 60, mouse_y - 20, 120, 40,
        control_info=test_control,
        duration=15,
        color='blue',
        border_width=3
    )
    
    print(f"🎯 用户选择: {result}")
    
    if result == 'confirm':
        print("✅ 用户确认捕获")
    elif result == 'cancel':
        print("❌ 用户取消捕获")
    elif result == 'timeout':
        print("⏰ 操作超时")
    else:
        print("❌ 发生错误")


if __name__ == "__main__":
    test_interactive_highlight()
