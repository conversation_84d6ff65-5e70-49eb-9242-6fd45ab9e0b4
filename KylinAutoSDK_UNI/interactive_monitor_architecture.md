# Interactive Monitor 架构文档

## 概述

`interactive_monitor.py` 是一个交互式控件捕获工具，专为 Wayland 环境设计，通过连续监控鼠标位置和交互式确认来实现控件信息的精确捕获。

## 依赖关系图

```
interactive_monitor.py (主程序)
    │
    ├─> universal_offset_detector.py (核心检测引擎)
    │   │
    │   └─> ultimate_highlight.py (基础高亮功能)
    │
    └─> interactive_highlight.py (交互式高亮模块)
```

## 各模块详细说明

### 1. interactive_monitor.py (主程序)
**作用**：
- 提供主程序入口和用户界面
- 管理监控线程的生命周期
- 处理信号和异常
- 保存捕获的控件信息

**主要类**：
- `InteractiveMonitor`: 主控制类，负责整体流程调度

### 2. universal_offset_detector.py (核心检测引擎)
**作用**：
- 计算真实窗口坐标与 AT-SPI 坐标之间的偏移量
- 收集应用程序的所有控件信息
- 根据鼠标位置查找对应的控件
- 提供控件信息的格式化输出

**主要功能**：
- `calculate_window_offset()`: 计算坐标偏移
- `collect_app_controls()`: 递归收集控件树
- `find_control_with_offset()`: 精确定位控件
- `get_control_info()`: 格式化控件信息
- `highlight_control()`: 调用高亮显示

### 3. interactive_highlight.py (交互式高亮模块)
**作用**：
- 创建带确认/取消按钮的交互式界面
- 在控件周围显示彩色边框
- 处理用户点击事件
- 返回用户选择结果

**主要功能**：
- `show_interactive_highlight()`: 显示交互界面
- `_create_interactive_script()`: 生成 X11 显示脚本
- 支持超时自动关闭
- 鼠标悬停效果

### 4. ultimate_highlight.py (基础高亮功能)
**作用**：
- 提供简单的边框高亮显示
- 支持自定义颜色和边框宽度
- 自动定时消失

**主要功能**：
- `highlight()`: 显示高亮边框
- 创建四个独立的边框窗口
- 支持多种预定义颜色

## 执行调度流程

### 1. 程序启动阶段
```python
1. 解析命令行参数 (--interval, --format)
2. 检查 DISPLAY 环境变量
3. 创建 InteractiveMonitor 实例
4. 设置信号处理器 (SIGINT, SIGTERM)
5. 启动监控线程
```

### 2. 监控循环流程
```python
while running:
    1. 获取当前鼠标位置 (get_mouse_position)
    2. 检查位置是否变化
    3. 检查冷却时间 (避免频繁弹出)
    4. 如果满足条件:
        a. 计算窗口偏移 (calculate_window_offset)
        b. 收集应用控件 (collect_app_controls)
        c. 查找目标控件 (find_control_with_offset)
        d. 显示交互式高亮 (interactive_highlight)
    5. 等待指定间隔时间
```

### 3. 交互确认流程
```python
1. 创建高亮边框 (橙色)
2. 创建确认按钮 (绿色 YES)
3. 创建取消按钮 (红色 NO)
4. 显示控件信息窗口
5. 等待用户操作:
    - 点击 YES → 保存信息并退出
    - 点击 NO → 继续监控
    - 超时 → 继续监控
```

### 4. 信息保存流程
```python
1. 生成原始格式的控件信息
2. 保存到 detected_control_info.txt
3. 创建带时间戳的备份文件
4. 显示确认高亮 (绿色, 2秒)
5. 退出程序
```

## 关键技术特性

### 1. 坐标偏移计算
- 使用 xdotool 获取真实窗口位置
- 通过 AT-SPI 获取逻辑窗口位置
- 计算偏移量以实现精确定位

### 2. 递归控件遍历
- 深度优先遍历控件树
- 收集所有可见且有效的控件
- 过滤掉无效或不可见的控件

### 3. X11 窗口创建
- 使用 python-xlib 创建透明窗口
- override_redirect 避免窗口管理器干扰
- 事件处理实现用户交互

### 4. 多线程设计
- 主线程处理用户界面
- 监控线程持续检测鼠标位置
- 避免阻塞和提高响应性

## 使用示例

### 基本使用
```bash
DISPLAY=:0 python3 interactive_monitor.py
```

### 自定义参数
```bash
# 设置监控间隔为 1 秒
DISPLAY=:0 python3 interactive_monitor.py --interval 1.0

# 使用简单输出格式
DISPLAY=:0 python3 interactive_monitor.py --format simple
```

## 输出文件格式

### 主文件 (detected_control_info.txt)
```json
{
    "app_name": "应用程序名称",
    "window_position": [x, y],
    "window_size": [width, height],
    "control_info": {
        "name": "控件名称",
        "role": "控件角色",
        "position": [x, y],
        "size": [width, height],
        "states": ["状态列表"]
    },
    "mouse_position": [x, y],
    "offset": [offset_x, offset_y]
}
```

### 备份文件
- 文件名格式: `captured_control_YYYYMMDD_HHMMSS.json`
- 内容格式与主文件相同

## 注意事项

1. **环境要求**
   - 必须设置 DISPLAY 环境变量
   - 需要 X11 服务器支持
   - 需要安装 xdotool 工具

2. **权限要求**
   - 需要访问 AT-SPI 服务
   - 需要创建 X11 窗口权限

3. **性能考虑**
   - 监控间隔不宜设置过小
   - 冷却时间避免频繁弹窗
   - 及时清理监控线程

4. **兼容性**
   - 主要针对 Wayland 环境设计
   - 在纯 X11 环境下也可使用
   - 部分应用可能不支持 AT-SPI

## 故障排查

1. **无法显示高亮**
   - 检查 DISPLAY 环境变量
   - 确认 X11 服务正在运行
   - 验证窗口创建权限

2. **找不到控件**
   - 确认应用支持 AT-SPI
   - 检查控件是否可见
   - 验证坐标偏移计算

3. **文字显示异常**
   - 使用 ASCII 字符替代 Unicode
   - 调整字体和颜色对比度
   - 检查字体文件是否存在