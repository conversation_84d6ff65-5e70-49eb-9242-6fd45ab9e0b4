#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
快速坐标修正方案
基于UKUI环境的典型偏移进行快速修正
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>inAutoSDK_UNI/src')

def get_ukui_coordinate_offset():
    """获取UKUI环境的典型坐标偏移"""
    try:
        # 检查顶部面板高度
        import subprocess
        result = subprocess.run(['xprop', '-root', '_NET_WORKAREA'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and '_NET_WORKAREA' in result.stdout:
            workarea_line = result.stdout.strip()
            if '=' in workarea_line:
                coords_part = workarea_line.split('=')[1].strip()
                coords = [int(x.strip()) for x in coords_part.split(',')]
                if len(coords) >= 4:
                    panel_height = coords[1]  # Y偏移就是面板高度
                    return {'panel_offset': panel_height}
    except:
        pass
    
    # 默认UKUI偏移值（经验值）
    return {'panel_offset': 30}  # UKUI顶部面板通常30像素

def apply_coordinate_fix():
    """应用坐标修正到UNI_new.py"""
    
    print("🔧 UKUI坐标偏移快速修正")
    print("=" * 50)
    
    # 获取偏移量
    offset_info = get_ukui_coordinate_offset()
    panel_offset = offset_info['panel_offset']
    
    print(f"检测到面板偏移: {panel_offset}像素")
    
    # 创建修正版的ultimate_highlight函数
    fixed_highlight_code = f'''
def ultimate_highlight_fixed(x, y, width, height, duration=2, color='red', border_width=2):
    """
    修正UKUI坐标偏移的终极高亮函数
    """
    # UKUI环境坐标修正
    corrected_y = y - {panel_offset}  # 减去面板高度偏移
    
    print(f"🔧 坐标修正: 原始Y={{y}} -> 修正Y={{corrected_y}}")
    
    # 调用原始ultimate_highlight
    from ultimate_highlight import ultimate_highlight
    return ultimate_highlight(x, corrected_y, width, height, duration, color, border_width)
'''
    
    # 写入修正版函数
    with open('/home/<USER>/KylinAutoSDK_UNI/ultimate_highlight_fixed.py', 'w', encoding='utf-8') as f:
        f.write(f'''#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
修正UKUI坐标偏移的终极高亮函数
"""

{fixed_highlight_code}

if __name__ == "__main__":
    # 测试修正效果
    import subprocess
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={{'DISPLAY': ':0'}})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            
            print(f"测试修正效果，鼠标位置: ({{mouse_x}}, {{mouse_y}})")
            ultimate_highlight_fixed(mouse_x-50, mouse_y-25, 100, 50, 3, 'green', 2)
        else:
            ultimate_highlight_fixed(1000, 500, 200, 100, 3, 'green', 2)
    except:
        ultimate_highlight_fixed(1000, 500, 200, 100, 3, 'green', 2)
''')
    
    print("✅ 创建了修正版高亮函数: ultimate_highlight_fixed.py")
    
    # 现在修改UNI_new.py使用修正版
    uni_file = '/home/<USER>/KylinAutoSDK_UNI/src/UNI_new.py'
    
    try:
        with open(uni_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入
        if 'from ultimate_highlight import ultimate_highlight' in content:
            content = content.replace(
                'from ultimate_highlight import ultimate_highlight',
                f'''# 使用修正版高亮函数解决坐标偏移问题
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI')
from ultimate_highlight_fixed import ultimate_highlight_fixed as ultimate_highlight'''
            )
            
            with open(uni_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
            print("✅ 已更新UNI_new.py使用修正版高亮函数")
            print(f"   修正偏移量: Y坐标减去{panel_offset}像素")
            
        else:
            print("⚠️ 未找到需要替换的导入语句")
            
    except Exception as e:
        print(f"❌ 修改UNI_new.py失败: {e}")
    
    print()
    print("🎯 修正完成！现在可以测试修正效果")
    print("   运行: python3 demo_uni_control_detection_with_highlight.py")

if __name__ == "__main__":
    apply_coordinate_fix()