#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
修复的真正透明高亮 - 只绘制边框线条
"""

import subprocess
import os

def pure_border_highlight(x, y, width, height, 
                         duration=2,
                         color='red', 
                         border_width=2):
    """
    纯边框高亮 - 使用四个独立的线条窗口
    """
    
    # 创建四条边框线的脚本
    x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    border_w = {border_width}
    target_x = {x}
    target_y = {y}
    target_width = {width}
    target_height = {height}
    
    # 创建四个边框窗口
    windows = []
    
    # 上边框
    top_win = root.create_window(
        target_x, target_y, target_width, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(top_win)
    
    # 下边框
    bottom_win = root.create_window(
        target_x, target_y + target_height - border_w, target_width, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(bottom_win)
    
    # 左边框
    left_win = root.create_window(
        target_x, target_y, border_w, target_height,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(left_win)
    
    # 右边框
    right_win = root.create_window(
        target_x + target_width - border_w, target_y, border_w, target_height,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(right_win)
    
    # 设置窗口属性
    for win in windows:
        win.set_wm_name("Pure Border")
        win.set_wm_class("border", "PureBorder")
        
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_WINDOW_TYPE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
            )
        except:
            pass
    
    # 显示所有边框
    for win in windows:
        win.map()
    disp.sync()
    
    time.sleep({duration})
    
    # 清理
    for win in windows:
        win.unmap()
        win.destroy()
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    print(f"错误: {{e}}")
    exit(1)
'''
    
    try:
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def outline_highlight(x, y, width, height, 
                     duration=2,
                     color='red', 
                     border_width=1):
    """
    轮廓高亮 - 使用XDrawRectangle直接绘制到根窗口
    """
    
    outline_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 直接在根窗口上绘制
    gc = root.create_gc(
        foreground=color_pixel,
        background=screen.black_pixel,
        line_width={border_width}
    )
    
    # 绘制矩形轮廓
    root.rectangle(gc, {x}, {y}, {width}, {height})
    disp.sync()
    
    time.sleep({duration})
    
    # 清除绘制（用背景色重绘）
    clear_gc = root.create_gc(
        foreground=screen.black_pixel,
        background=screen.black_pixel,
        line_width={border_width + 2}
    )
    
    root.rectangle(clear_gc, {x} - 1, {y} - 1, {width} + 2, {height} + 2)
    disp.sync()
    disp.close()
    
    exit(0)
    
except Exception as e:
    print(f"轮廓绘制错误: {{e}}")
    exit(1)
'''
    
    try:
        result = subprocess.run(['python3', '-c', outline_script], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def test_pure_border():
    """测试纯边框高亮"""
    print("🎯 纯边框高亮测试")
    print("=" * 60)
    
    # 获取鼠标位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    test_x = mouse_x - 100
    test_y = mouse_y - 75
    test_w = 200
    test_h = 150
    
    print(f"测试位置: ({test_x}, {test_y}) {test_w}×{test_h}")
    print()
    
    print("✅ 纯边框高亮特点:")
    print("   • 四个独立的线条窗口")
    print("   • 中间完全无窗口覆盖")
    print("   • 100%透明内部区域")
    print("   • 完全无窗口装饰")
    print()
    
    # 测试1: 四个独立边框
    print("🔍 测试1: 四个独立边框窗口")
    print("   • 红色边框，2像素宽")
    success1 = pure_border_highlight(test_x, test_y, test_w, test_h,
                                   duration=3, color='red', border_width=2)
    if success1:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    # 测试2: 蓝色细边框
    print("🔍 测试2: 蓝色超细边框")
    print("   • 蓝色边框，1像素宽")
    success2 = pure_border_highlight(test_x, test_y, test_w, test_h,
                                   duration=3, color='blue', border_width=1)
    if success2:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    # 测试3: 绿色粗边框
    print("🔍 测试3: 绿色稍粗边框")
    print("   • 绿色边框，3像素宽")
    success3 = pure_border_highlight(test_x, test_y, test_w, test_h,
                                   duration=3, color='green', border_width=3)
    if success3:
        print("✅ 成功")
    else:
        print("❌ 失败")
    print()
    
    print("=" * 60)
    print("🎊 纯边框测试完成！")
    print("\n💡 这种方法的优势:")
    print("   • 中间完全没有任何窗口")
    print("   • 只有四条边框线")
    print("   • 绝对不会遮挡内容")
    print("   • 无任何背景色问题")

# 便捷函数
def quick_pure_highlight(x, y, width, height, duration=2):
    """快速纯边框高亮"""
    return pure_border_highlight(x, y, width, height, duration, 'red', 2)

if __name__ == "__main__":
    test_pure_border()