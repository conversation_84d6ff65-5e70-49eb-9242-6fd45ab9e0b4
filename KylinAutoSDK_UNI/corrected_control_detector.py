#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
使用正确偏移的控件检测器
偏移量: (567, 192)
"""

import sys
import os
import subprocess
import time
import re
import pyatspi


class CorrectedControlDetector:
    """使用正确偏移的控件检测器"""
    
    def __init__(self):
        """初始化检测器"""
        # 使用验证过的正确偏移
        self.correct_offset = (567, 192)
        self.all_controls = []
        
        print("✅ 使用正确偏移的控件检测器")
        print(f"🎯 偏移量: {self.correct_offset}")
    
    def get_mouse_position(self):
        """获取当前鼠标位置"""
        try:
            result = subprocess.run(['xdotool', 'getmouselocation'], 
                                  capture_output=True, text=True, env={'DISPLAY': ':0'})
            if result.returncode == 0:
                parts = result.stdout.strip().split()
                mouse_x = int(parts[0].split(':')[1])
                mouse_y = int(parts[1].split(':')[1])
                return mouse_x, mouse_y
        except Exception:
            pass
        return None, None
    
    def find_control_at_mouse(self):
        """在鼠标位置查找控件"""
        mouse_pos = self.get_mouse_position()
        if mouse_pos == (None, None):
            return None
        
        mouse_x, mouse_y = mouse_pos
        
        # 转换为AT-SPI坐标
        atspi_x = mouse_x - self.correct_offset[0]
        atspi_y = mouse_y - self.correct_offset[1]
        
        print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        print(f"🔄 AT-SPI坐标: ({atspi_x}, {atspi_y})")
        
        # 在这里添加控件查找逻辑...
        # (使用之前的控件查找代码)
        
        return None
    
    def highlight_control_correctly(self, control):
        """正确高亮控件"""
        if not control:
            return False
        
        atspi_x, atspi_y = control['atspi_position']
        width, height = control['size']
        
        # 计算正确的屏幕坐标
        screen_x = atspi_x + self.correct_offset[0]
        screen_y = atspi_y + self.correct_offset[1]
        
        try:
            from ultimate_highlight import ultimate_highlight
            return ultimate_highlight(screen_x, screen_y, width, height, 
                                    duration=2, color='green', border_width=2)
        except Exception:
            return False


def main():
    """主函数"""
    detector = CorrectedControlDetector()
    # 添加你的测试代码...


if __name__ == "__main__":
    main()
