#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
精确坐标调试工具
深入分析坐标转换问题的根源
"""

import sys
import os
import time
import subprocess
import re

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_hellobig_window_info():
    """获取hellobig窗口详细信息"""
    try:
        # 获取窗口列表
        result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        lines = result.stdout.split('\n')
        hellobig_uuid = None
        
        for i, line in enumerate(lines):
            if 'AT-SPI测试界面' in line:
                for j in range(i-1, -1, -1):
                    if 'toplevel' in lines[j]:
                        uuid_match = re.search(r'"([^"]+)"', lines[j])
                        if uuid_match:
                            hellobig_uuid = uuid_match.group(1)
                            break
                break
        
        if not hellobig_uuid:
            return None
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', hellobig_uuid], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        geo_match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', result.stdout)
        if geo_match:
            x, y, width, height = map(int, geo_match.groups())
            return {
                'uuid': hellobig_uuid,
                'x': x, 'y': y, 'width': width, 'height': height,
                'title': 'AT-SPI测试界面 - Qt控件集合'
            }
        
        return None
    except Exception as e:
        print(f"获取窗口信息失败: {e}")
        return None

def get_mouse_position():
    """获取鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        return None, None
    except:
        return None, None

def analyze_coordinate_problem():
    """深入分析坐标问题"""
    print("🔍 深入分析坐标转换问题")
    print("=" * 60)
    
    # 1. 获取窗口信息
    window_info = get_hellobig_window_info()
    if not window_info:
        print("❌ 无法获取hellobig窗口信息")
        return
    
    print(f"✅ hellobig窗口信息:")
    print(f"   UUID: {window_info['uuid']}")
    print(f"   位置: ({window_info['x']}, {window_info['y']})")
    print(f"   大小: {window_info['width']} × {window_info['height']}")
    print()
    
    # 2. 获取鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is None:
        print("❌ 无法获取鼠标位置")
        return
    
    print(f"🖱️ 当前鼠标位置: ({mouse_x}, {mouse_y})")
    
    # 检查鼠标是否在窗口内
    if (window_info['x'] <= mouse_x <= window_info['x'] + window_info['width'] and
        window_info['y'] <= mouse_y <= window_info['y'] + window_info['height']):
        rel_x = mouse_x - window_info['x']
        rel_y = mouse_y - window_info['y']
        print(f"✅ 鼠标在hellobig窗口内，相对位置: ({rel_x}, {rel_y})")
    else:
        print("⚠️ 鼠标不在hellobig窗口内")
        # 移动到窗口中心进行测试
        center_x = window_info['x'] + window_info['width'] // 2
        center_y = window_info['y'] + window_info['height'] // 2
        mouse_x, mouse_y = center_x, center_y
        rel_x = mouse_x - window_info['x']
        rel_y = mouse_y - window_info['y']
        print(f"🎯 使用窗口中心位置进行测试: ({mouse_x}, {mouse_y})")
        print(f"   相对位置: ({rel_x}, {rel_y})")
    
    print()
    
    # 3. 进行控件识别
    try:
        from UNI_new import UNI
        
        uni = UNI()
        
        print(f"🔍 在位置 ({mouse_x}, {mouse_y}) 进行控件识别...")
        
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=False)
        elapsed = time.time() - start_time
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            rel_coords = control_data.get('RelativeCoords', {})
            
            print(f"✅ 识别成功 ({elapsed:.2f}s): {name} ({role})")
            print()
            
            print(f"📊 详细坐标分析:")
            print(f"   目标鼠标位置: ({mouse_x}, {mouse_y})")
            
            if coords:
                atspi_x = coords.get('x', 0)
                atspi_y = coords.get('y', 0)
                atspi_w = coords.get('width', 0)
                atspi_h = coords.get('height', 0)
                
                print(f"   AT-SPI Coords: ({atspi_x}, {atspi_y}) 大小: {atspi_w}×{atspi_h}")
                
                # 分析不同的坐标转换策略
                print(f"\n🧪 坐标转换策略测试:")
                
                # 策略1: 直接使用AT-SPI坐标
                print(f"   策略1 - 直接AT-SPI: ({atspi_x}, {atspi_y})")
                diff1_x = atspi_x - mouse_x
                diff1_y = atspi_y - mouse_y
                print(f"            偏差: X={diff1_x}, Y={diff1_y}")
                
                # 策略2: AT-SPI + 窗口偏移
                converted_x = window_info['x'] + atspi_x
                converted_y = window_info['y'] + atspi_y
                print(f"   策略2 - 窗口偏移: ({converted_x}, {converted_y})")
                diff2_x = converted_x - mouse_x
                diff2_y = converted_y - mouse_y
                print(f"            偏差: X={diff2_x}, Y={diff2_y}")
                
                # 策略3: 检查是否AT-SPI坐标已经是绝对坐标
                if abs(atspi_x - mouse_x) < abs(converted_x - mouse_x):
                    print(f"   💡 AT-SPI坐标可能已经是绝对坐标！")
                else:
                    print(f"   💡 需要窗口偏移转换")
                
                # 策略4: 使用RelativeCoords
                if rel_coords:
                    rel_x = rel_coords.get('x', 0)
                    rel_y = rel_coords.get('y', 0)
                    rel_w = rel_coords.get('width', 0)
                    rel_h = rel_coords.get('height', 0)
                    print(f"   RelativeCoords: ({rel_x}, {rel_y}) 大小: {rel_w}×{rel_h}")
                    
                    rel_abs_x = window_info['x'] + rel_x
                    rel_abs_y = window_info['y'] + rel_y
                    print(f"   策略3 - 相对转换: ({rel_abs_x}, {rel_abs_y})")
                    diff3_x = rel_abs_x - mouse_x
                    diff3_y = rel_abs_y - mouse_y
                    print(f"            偏差: X={diff3_x}, Y={diff3_y}")
                
                # 找出最佳策略
                strategies = [
                    ("直接AT-SPI", atspi_x, atspi_y, abs(diff1_x) + abs(diff1_y)),
                    ("窗口偏移", converted_x, converted_y, abs(diff2_x) + abs(diff2_y))
                ]
                
                if rel_coords:
                    strategies.append(("相对转换", rel_abs_x, rel_abs_y, abs(diff3_x) + abs(diff3_y)))
                
                best_strategy = min(strategies, key=lambda x: x[3])
                print(f"\n🎯 最佳策略: {best_strategy[0]} - 位置({best_strategy[1]}, {best_strategy[2]}) 总偏差: {best_strategy[3]:.0f}")
                
                # 使用最佳策略进行高亮测试
                test_highlight_accuracy(best_strategy[1], best_strategy[2], atspi_w, atspi_h, best_strategy[0])
                
            else:
                print("❌ 没有坐标信息")
        else:
            print(f"❌ 识别失败 ({elapsed:.2f}s): {info}")
    
    except Exception as e:
        print(f"❌ 控件识别失败: {e}")
        import traceback
        traceback.print_exc()

def test_highlight_accuracy(x, y, width, height, strategy_name):
    """测试高亮精度"""
    print(f"\n🎨 测试高亮精度 - {strategy_name}")
    print(f"   高亮位置: ({x}, {y}) 大小: {width}×{height}")
    
    try:
        from ultimate_highlight import ultimate_highlight
        
        # 先显示一个小的绿色标记在计算出的位置
        ultimate_highlight(x-2, y-2, 4, 4, 1, 'green', 1)
        print(f"   🟢 绿色标记显示在计算位置")
        time.sleep(1)
        
        # 再显示控件的完整高亮
        success = ultimate_highlight(
            x=x, y=y, width=max(width, 10), height=max(height, 10),
            duration=3, color='red', border_width=3
        )
        
        if success:
            print(f"   🔴 红色边框显示控件范围")
            print(f"   💡 观察绿色点是否在红色框内，框是否对准控件")
        else:
            print(f"   ❌ 高亮显示失败")
        
        time.sleep(4)
        
    except Exception as e:
        print(f"   ❌ 高亮测试失败: {e}")

def main():
    """主函数"""
    print("精确坐标调试工具")
    print("=" * 80)
    print("🎯 目标: 找出坐标转换的根本问题并修复")
    print("🔍 方法: 对比不同的坐标转换策略")
    print()
    
    try:
        analyze_coordinate_problem()
    except KeyboardInterrupt:
        print("\n用户中断调试")
    except Exception as e:
        print(f"❌ 调试失败: {e}")
    
    print("\n" + "=" * 80)
    print("🎉 坐标调试完成!")
    print("💡 根据测试结果选择最佳的坐标转换策略")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())