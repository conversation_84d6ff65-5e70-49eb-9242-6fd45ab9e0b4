#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标偏差分析器
专门分析和修正AT-SPI坐标与实际桌面坐标的偏差

功能:
1. 对比accerciser显示的坐标与实际桌面坐标
2. 计算精确的坐标偏移量
3. 提供自动坐标修正功能
4. 验证修正后的坐标准确性
"""

import sys
import os
import subprocess
import pyatspi
import time


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_screen_info():
    """获取屏幕信息"""
    try:
        result = subprocess.run(['xdpyinfo'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'dimensions:' in line:
                    # 提取屏幕尺寸
                    parts = line.split()
                    dimensions = parts[1]  # 例如: 1920x1080
                    width, height = map(int, dimensions.split('x'))
                    return width, height
    except Exception:
        pass
    return None, None


def get_panel_info():
    """获取面板/任务栏信息"""
    print("🔍 检测面板/任务栏信息...")
    
    panel_info = {
        'top': 0,
        'bottom': 0,
        'left': 0,
        'right': 0
    }
    
    try:
        # 查找面板相关的窗口
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = (app.name or "").lower()
            
            # 检查是否是面板应用
            if any(panel_name in app_name for panel_name in ['panel', 'bar', 'dock', 'taskbar']):
                print(f"   找到面板应用: {app.name}")
                
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        if hasattr(window, 'queryComponent'):
                            component = window.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                print(f"     面板位置: ({extents.x}, {extents.y}) 尺寸: {extents.width}×{extents.height}")
                                
                                # 判断面板位置
                                if extents.y == 0 and extents.height < 100:
                                    panel_info['top'] = max(panel_info['top'], extents.height)
                                elif extents.x == 0 and extents.width < 100:
                                    panel_info['left'] = max(panel_info['left'], extents.width)
                                
                    except Exception:
                        continue
    except Exception as e:
        print(f"   ❌ 检测面板失败: {e}")
    
    print(f"   面板偏移: 上={panel_info['top']}, 下={panel_info['bottom']}, 左={panel_info['left']}, 右={panel_info['right']}")
    return panel_info


def find_hellobig_elements():
    """查找hellobig程序的所有元素"""
    print("🔍 查找hellobig程序的控件...")
    
    hellobig_elements = []
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = (app.name or "").lower()
            
            # 检查是否是hellobig或相关应用
            if any(keyword in app_name for keyword in ['hello', 'big', 'test']) or not app_name:
                print(f"📱 检查应用: {app.name or '无名应用'}")
                
                app_elements = []
                collect_elements_with_coords(app, app_elements, max_depth=5)
                
                if app_elements:
                    print(f"   找到 {len(app_elements)} 个有坐标的元素")
                    hellobig_elements.extend(app_elements)
                    
                    # 显示前几个元素的信息
                    for j, elem in enumerate(app_elements[:3]):
                        ext = elem['extents']
                        print(f"     {j+1}. {elem['name']} ({elem['role']}) - "
                              f"AT-SPI坐标: ({ext.x}, {ext.y}) {ext.width}×{ext.height}")
    
    except Exception as e:
        print(f"❌ 查找hellobig元素失败: {e}")
    
    return hellobig_elements


def collect_elements_with_coords(element, element_list, depth=0, max_depth=5):
    """收集有坐标信息的元素"""
    if depth > max_depth:
        return
    
    try:
        name = element.name or "N/A"
        role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
        
        # 获取坐标
        extents = None
        try:
            if hasattr(element, 'queryComponent'):
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
        except Exception:
            pass
        
        # 只收集有有效坐标的元素
        if extents and extents.width > 0 and extents.height > 0:
            element_list.append({
                'element': element,
                'name': name,
                'role': role,
                'extents': extents,
                'depth': depth
            })
        
        # 递归处理子元素
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                collect_elements_with_coords(child, element_list, depth + 1, max_depth)
        except Exception:
            pass
            
    except Exception:
        pass


def analyze_coordinate_offset():
    """分析坐标偏移"""
    print("🎯 坐标偏移分析")
    print("=" * 60)
    
    # 获取屏幕信息
    screen_info = get_screen_info()
    if screen_info:
        print(f"📺 屏幕尺寸: {screen_info[0]}×{screen_info[1]}")
    
    # 获取面板信息
    panel_info = get_panel_info()
    
    # 获取当前鼠标位置
    mouse_pos = get_mouse_position()
    if mouse_pos:
        print(f"🖱️  当前鼠标位置: ({mouse_pos[0]}, {mouse_pos[1]})")
    
    # 查找hellobig元素
    hellobig_elements = find_hellobig_elements()
    
    if not hellobig_elements:
        print("❌ 未找到hellobig程序的控件")
        print("💡 请确保:")
        print("   1. hellobig程序正在运行")
        print("   2. hellobig窗口可见")
        print("   3. 程序名称包含'hello'、'big'或'test'")
        return None
    
    print(f"\n📊 找到 {len(hellobig_elements)} 个hellobig控件")
    
    # 分析坐标模式
    print("\n🔍 坐标模式分析:")
    
    # 统计坐标分布
    x_coords = [elem['extents'].x for elem in hellobig_elements]
    y_coords = [elem['extents'].y for elem in hellobig_elements]
    
    min_x, max_x = min(x_coords), max(x_coords)
    min_y, max_y = min(y_coords), max(y_coords)
    
    print(f"   X坐标范围: {min_x} ~ {max_x}")
    print(f"   Y坐标范围: {min_y} ~ {max_y}")
    
    # 检测可能的偏移模式
    possible_offsets = []
    
    # 模式1: Y轴面板偏移
    if panel_info['top'] > 0:
        y_offset = panel_info['top']
        possible_offsets.append((0, y_offset, f"顶部面板偏移: +{y_offset}"))
    
    # 模式2: 窗口装饰偏移
    if min_y > 20:  # 如果最小Y坐标大于20，可能有窗口装饰
        y_offset = min_y
        possible_offsets.append((0, -y_offset, f"窗口装饰偏移: -{y_offset}"))
    
    # 模式3: 固定偏移检测
    common_offsets = [(0, 0), (0, 30), (0, -30), (0, 24), (0, -24)]
    for x_off, y_off in common_offsets:
        possible_offsets.append((x_off, y_off, f"常见偏移: ({x_off}, {y_off})"))
    
    print(f"\n🎯 可能的坐标偏移:")
    for i, (x_off, y_off, desc) in enumerate(possible_offsets):
        print(f"   {i+1}. {desc}")
    
    return {
        'hellobig_elements': hellobig_elements,
        'possible_offsets': possible_offsets,
        'panel_info': panel_info,
        'screen_info': screen_info
    }


def test_coordinate_correction(analysis_result):
    """测试坐标修正"""
    if not analysis_result:
        return
    
    print("\n🧪 测试坐标修正")
    print("=" * 60)
    
    hellobig_elements = analysis_result['hellobig_elements']
    possible_offsets = analysis_result['possible_offsets']
    
    # 获取当前鼠标位置
    mouse_x, mouse_y = get_mouse_position()
    if not mouse_x:
        print("❌ 无法获取鼠标位置")
        return
    
    print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
    print(f"🔍 在{len(hellobig_elements)}个控件中查找匹配...")
    
    best_matches = []
    
    # 测试每种偏移
    for x_offset, y_offset, desc in possible_offsets:
        corrected_mouse_x = mouse_x + x_offset
        corrected_mouse_y = mouse_y + y_offset
        
        print(f"\n📐 测试偏移: {desc}")
        print(f"   修正后鼠标位置: ({corrected_mouse_x}, {corrected_mouse_y})")
        
        matches = []
        for elem in hellobig_elements:
            ext = elem['extents']
            
            # 检查修正后的坐标是否在控件内
            if (ext.x <= corrected_mouse_x < ext.x + ext.width and
                ext.y <= corrected_mouse_y < ext.y + ext.height):
                
                matches.append({
                    'element': elem,
                    'offset': (x_offset, y_offset),
                    'desc': desc
                })
                
                print(f"   ✅ 匹配: {elem['name']} ({elem['role']}) - "
                      f"AT-SPI坐标: ({ext.x}, {ext.y}) {ext.width}×{ext.height}")
        
        if matches:
            best_matches.extend(matches)
            print(f"   找到 {len(matches)} 个匹配控件")
        else:
            print(f"   ❌ 无匹配控件")
    
    # 选择最佳匹配
    if best_matches:
        print(f"\n🏆 找到 {len(best_matches)} 个可能的匹配")
        
        # 选择面积最小的控件（通常是最具体的）
        best_match = min(best_matches, key=lambda x: x['element']['extents'].width * x['element']['extents'].height)
        
        elem = best_match['element']
        offset = best_match['offset']
        
        print(f"🎯 最佳匹配:")
        print(f"   控件: {elem['name']} ({elem['role']})")
        print(f"   AT-SPI坐标: ({elem['extents'].x}, {elem['extents'].y}) {elem['extents'].width}×{elem['extents'].height}")
        print(f"   建议偏移: ({offset[0]}, {offset[1]})")
        
        # 测试高亮
        test_highlight_with_offset(elem['extents'], offset)
        
        return offset
    else:
        print(f"\n❌ 未找到任何匹配控件")
        print(f"💡 可能需要:")
        print(f"   1. 调整鼠标位置到明确的控件上")
        print(f"   2. 手动测试其他偏移值")
        print(f"   3. 检查hellobig窗口是否获得焦点")
        
    return None


def test_highlight_with_offset(extents, offset):
    """测试带偏移的高亮"""
    print(f"\n✨ 测试高亮 (偏移: {offset})")
    
    # 应用偏移
    highlight_x = extents.x - offset[0]
    highlight_y = extents.y - offset[1]
    
    print(f"   AT-SPI坐标: ({extents.x}, {extents.y})")
    print(f"   高亮坐标: ({highlight_x}, {highlight_y})")
    
    try:
        from ultimate_highlight import ultimate_highlight
        result = ultimate_highlight(highlight_x, highlight_y, extents.width, extents.height,
                                  duration=3, color='green', border_width=2)
        print(f"   高亮结果: {'成功' if result else '失败'}")
        
        if result:
            print(f"   🎉 如果高亮位置准确，则偏移值正确!")
        
    except Exception as e:
        print(f"   ❌ 高亮异常: {e}")


def interactive_offset_test():
    """交互式偏移测试"""
    print("\n🎮 交互式偏移测试")
    print("=" * 60)
    
    while True:
        print(f"\n请将鼠标移动到hellobig程序的一个明确控件上")
        input("按Enter键开始检测...")
        
        mouse_pos = get_mouse_position()
        if not mouse_pos:
            print("❌ 无法获取鼠标位置")
            continue
        
        mouse_x, mouse_y = mouse_pos
        print(f"🖱️  鼠标位置: ({mouse_x}, {mouse_y})")
        
        # 获取hellobig元素
        hellobig_elements = find_hellobig_elements()
        if not hellobig_elements:
            print("❌ 未找到hellobig控件")
            continue
        
        print(f"请手动输入偏移值:")
        try:
            x_offset = int(input("X偏移 (默认0): ") or "0")
            y_offset = int(input("Y偏移 (默认0): ") or "0")
            
            # 测试这个偏移
            corrected_x = mouse_x + x_offset
            corrected_y = mouse_y + y_offset
            
            print(f"修正后坐标: ({corrected_x}, {corrected_y})")
            
            # 查找匹配
            for elem in hellobig_elements:
                ext = elem['extents']
                if (ext.x <= corrected_x < ext.x + ext.width and
                    ext.y <= corrected_y < ext.y + ext.height):
                    
                    print(f"✅ 找到匹配控件: {elem['name']} ({elem['role']})")
                    print(f"   AT-SPI坐标: ({ext.x}, {ext.y}) {ext.width}×{ext.height}")
                    
                    # 测试高亮
                    test_highlight_with_offset(ext, (x_offset, y_offset))
                    break
            else:
                print("❌ 没有找到匹配的控件")
        
        except ValueError:
            print("❌ 请输入有效的数字")
        
        if input("\n继续测试? (y/n): ").lower() != 'y':
            break


def main():
    """主函数"""
    print("🎯 坐标偏差分析器")
    print("=" * 60)
    print("专门分析AT-SPI坐标与实际桌面坐标的偏差")
    print("适用于accerciser能看到控件但坐标不准确的情况")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    while True:
        print(f"\n选择功能:")
        print(f"1. 自动分析坐标偏移")
        print(f"2. 测试坐标修正")
        print(f"3. 交互式偏移测试")
        print(f"4. 退出")
        
        choice = input(f"\n请选择 (1-4): ").strip()
        
        if choice == '1':
            analysis_result = analyze_coordinate_offset()
            
        elif choice == '2':
            print("先进行分析...")
            analysis_result = analyze_coordinate_offset()
            if analysis_result:
                offset = test_coordinate_correction(analysis_result)
                if offset:
                    print(f"\n🎉 建议的坐标偏移: ({offset[0]}, {offset[1]})")
                    print(f"💡 在你的代码中使用这个偏移来修正坐标")
            
        elif choice == '3':
            interactive_offset_test()
            
        elif choice == '4':
            print("👋 退出")
            break
            
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()