#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
树形控件识别问题分析脚本
分析为什么鼠标悬停在树形控件子节点上，但识别结果总是返回根节点
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

from UNI_new import UNI
import pyatspi
from uni_sdk.utils.helpers import get_element_extents

def analyze_tree_control_issue():
    """分析树形控件识别问题"""
    print("🔍 分析树形控件识别问题")
    print("=" * 80)
    
    # 问题分析
    print("\n📋 问题描述")
    print("-" * 40)
    print("• 鼠标悬停在树形控件的子节点上")
    print("• 但识别结果总是返回根节点")
    print("• 需要找出为什么子节点没有被正确识别")
    
    print("\n🔍 关键代码路径分析")
    print("-" * 40)
    
    # 分析kdk_getElement_Uni的工作流程
    print("1. kdk_getElement_Uni 函数流程:")
    print("   ├─ locate_control_by_coordinate_fast (快速模式)")
    print("   │  ├─ find_target_application_fast")
    print("   │  └─ search_control_in_application_fast")
    print("   │     └─ search_control_ultra_fast")
    print("   └─ 回退到原始 UNI.kdk_getElement_Uni")
    print("      └─ _find_accessible_at_point (递归搜索)")
    
    print("\n2. 搜索算法分析:")
    print("   • search_control_ultra_fast: 最多检查20个元素，深度限制2层")
    print("   • search_control_optimized: 广度优先搜索，深度限制6层")
    print("   • search_control_recursive: 递归搜索，深度限制8层")
    
    print("\n3. 控件优先级设置:")
    print("   • tree item: 70分 (中等优先级)")
    print("   • table cell: 85分 (较高优先级)")
    print("   • push button: 100分 (最高优先级)")
    
    print("\n🎯 可能的问题原因")
    print("-" * 40)
    
    problems = [
        {
            "问题": "搜索深度限制",
            "描述": "search_control_ultra_fast 深度限制为2层，可能无法到达树形控件的子节点",
            "影响": "子节点在深度3层或更深时无法被发现"
        },
        {
            "问题": "搜索数量限制", 
            "描述": "search_control_ultra_fast 最多检查20个元素，在复杂界面中可能不够",
            "影响": "在遍历完容器控件后，可能还没到达目标子节点就停止搜索"
        },
        {
            "问题": "容器控件优先级",
            "描述": "容器控件(frame, filler)的优先级很低，但它们包含了子节点",
            "影响": "可能父容器被选中，而不是其中的具体子节点"
        },
        {
            "问题": "坐标匹配范围",
            "描述": "父节点和子节点可能都包含目标坐标，但算法优先选择了父节点",
            "影响": "当子节点完全在父节点范围内时，父节点被错误选择"
        },
        {
            "问题": "控件类型识别",
            "描述": "树形控件的子节点可能被识别为 table cell 而不是 tree item",
            "影响": "如果角色名称不匹配预期，可能导致优先级计算错误"
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"\n{i}. {problem['问题']}")
        print(f"   描述: {problem['描述']}")
        print(f"   影响: {problem['影响']}")
    
    print("\n💡 解决方案建议")
    print("-" * 40)
    
    solutions = [
        {
            "方案": "增加搜索深度",
            "实现": "将 search_control_ultra_fast 的深度限制从2增加到4-5层",
            "代码位置": "UNI_new.py 第198行: if depth > 2"
        },
        {
            "方案": "增加搜索数量限制",
            "实现": "将最大检查数量从20增加到50-100",
            "代码位置": "UNI_new.py 第179行: max_checks = 20"
        },
        {
            "方案": "优化控件选择逻辑",
            "实现": "在多个匹配控件中，优先选择面积更小、层次更深的控件",
            "代码位置": "calculate_control_priority 函数"
        },
        {
            "方案": "改进坐标匹配算法",
            "实现": "当多个控件都包含目标坐标时，选择最小的那个",
            "代码位置": "is_coordinate_in_control 函数"
        },
        {
            "方案": "添加树形控件特殊处理",
            "实现": "为 tree item 角色添加特殊的优先级提升逻辑",
            "代码位置": "calculate_control_priority 函数中的 role_priorities"
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n{i}. {solution['方案']}")
        print(f"   实现: {solution['实现']}")
        print(f"   位置: {solution['代码位置']}")
    
    print("\n🔧 立即可行的修复")
    print("-" * 40)
    print("最简单的修复方法:")
    print("1. 增加 search_control_ultra_fast 的深度限制 (2 → 4)")
    print("2. 增加最大检查数量限制 (20 → 50)")
    print("3. 提高 tree item 的优先级分数 (70 → 85)")
    print("4. 在多个匹配控件中优先选择面积最小的")
    
    print("\n📊 需要进一步调试的信息")
    print("-" * 40)
    print("要彻底解决这个问题，需要获取以下调试信息:")
    print("• 目标树形控件的实际层次结构")
    print("• 各层控件的角色名称(role name)")
    print("• 各层控件的坐标和尺寸信息")
    print("• 控件的状态信息(states)")
    print("• 当前搜索算法实际遍历到的控件列表")
    
    return True

def create_tree_control_debugger():
    """创建树形控件调试器"""
    debugger_code = '''#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
树形控件调试器
用于调试特定坐标下的控件识别问题
"""

import sys
sys.path.insert(0, '/home/<USER>/KylinAutoSDK_UNI/src')

from UNI_new import UNI
import pyatspi

def debug_tree_control_at_position(x, y):
    """调试指定位置的树形控件识别"""
    print(f"🔍 调试位置 ({x}, {y}) 的控件识别")
    print("=" * 60)
    
    uni = UNI()
    
    # 获取控件信息
    result, info = uni.kdk_getElement_Uni(x, y, quick=False, highlight=True)
    
    print(f"识别结果: {info}")
    if result:
        print(f"控件名称: {result.get('Name', 'N/A')}")
        print(f"控件角色: {result.get('Rolename', 'N/A')}")
        print(f"控件坐标: {result.get('Coords', 'N/A')}")
        print(f"控件状态: {result.get('States', 'N/A')}")
        print(f"控件动作: {result.get('Actions', 'N/A')}")
    
    return result

if __name__ == "__main__":
    # 使用示例 - 请替换为实际的问题坐标
    # debug_tree_control_at_position(400, 300)
    print("请调用 debug_tree_control_at_position(x, y) 来调试特定位置")
'''
    
    with open('/home/<USER>/KylinAutoSDK_UNI/tree_control_debugger.py', 'w', encoding='utf-8') as f:
        f.write(debugger_code)
    
    print(f"\n✅ 已创建树形控件调试器: /home/<USER>/KylinAutoSDK_UNI/tree_control_debugger.py")

if __name__ == "__main__":
    analyze_tree_control_issue()
    create_tree_control_debugger()
    print("\n🎯 分析完成！")
    print("建议先尝试简单的修复方法，然后使用调试器获取更多信息。")