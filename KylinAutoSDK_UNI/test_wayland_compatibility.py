#!/usr/bin/env python3
"""
测试 Wayland 环境下的控件检测兼容性
验证新的 kdk_getElement_Uni_wayland 函数是否返回与原始 kdk_getElement_Uni 相同格式的数据
"""

import sys
import time
import subprocess

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            output = result.stdout.strip()
            # 解析输出: x:123 y:456 screen:0 window:789
            parts = output.split()
            x = int(parts[0].split(':')[1])
            y = int(parts[1].split(':')[1])
            return x, y
    except Exception:
        pass
    
    # 如果 xdotool 失败，尝试其他方法
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        x = root.winfo_pointerx()
        y = root.winfo_pointery()
        root.destroy()
        return x, y
    except Exception:
        pass
    
    return None, None

def compare_control_data_formats(data1, data2, source1="原始", source2="Wayland"):
    """比较两个控件数据的格式是否一致"""
    print(f"\n📊 数据格式对比: {source1} vs {source2}")
    print("=" * 60)
    
    if not data1 and not data2:
        print("✅ 两者都未找到控件")
        return True
    
    if not data1:
        print(f"❌ {source1} 未找到控件，{source2} 找到了控件")
        return False
    
    if not data2:
        print(f"❌ {source2} 未找到控件，{source1} 找到了控件")
        return False
    
    # 检查关键字段
    key_fields = [
        'Name', 'Rolename', 'ProcessName', 'Description', 
        'Coords', 'Actions', 'States', 'RecordPosition',
        'WindowName', 'WindowRoleName', 'MenuElement'
    ]
    
    compatible = True
    
    for field in key_fields:
        val1 = data1.get(field, 'MISSING')
        val2 = data2.get(field, 'MISSING')
        
        if field in data1 and field in data2:
            print(f"✅ {field}: 都存在")
            if field == 'Coords':
                # 特殊处理坐标字段
                if isinstance(val1, dict) and isinstance(val2, dict):
                    coord_fields = ['x', 'y', 'width', 'height']
                    for coord_field in coord_fields:
                        if coord_field in val1 and coord_field in val2:
                            print(f"   ✅ {coord_field}: {val1[coord_field]} vs {val2[coord_field]}")
                        else:
                            print(f"   ❌ {coord_field}: 缺失")
                            compatible = False
        elif field in data1:
            print(f"❌ {field}: 只在{source1}中存在")
            compatible = False
        elif field in data2:
            print(f"❌ {field}: 只在{source2}中存在")
            compatible = False
        else:
            print(f"⚠️  {field}: 两者都缺失")
    
    # 检查数据类型一致性
    print(f"\n📋 数据类型对比:")
    for field in key_fields:
        if field in data1 and field in data2:
            type1 = type(data1[field]).__name__
            type2 = type(data2[field]).__name__
            if type1 == type2:
                print(f"✅ {field}: {type1}")
            else:
                print(f"❌ {field}: {type1} vs {type2}")
                compatible = False
    
    return compatible

def test_wayland_compatibility():
    """测试 Wayland 兼容性"""
    print("🧪 Wayland 控件检测兼容性测试")
    print("=" * 50)

    # 使用您之前测试过的坐标
    test_coordinates = [
        (472, 620),  # 您在代码中选择的坐标
        (276, 164),  # 之前测试成功的坐标
        (49, 206),   # 桌面图标坐标
        (600, 445),  # 另一个测试坐标
    ]

    print("🎯 使用预设测试坐标进行兼容性测试")

    for i, (mouse_x, mouse_y) in enumerate(test_coordinates):
        print(f"\n📍 测试坐标 {i+1}: ({mouse_x}, {mouse_y})")
        print("-" * 40)

        try:
            # 测试原始的 kdk_getElement_Uni
            print(f"🔍 测试原始 kdk_getElement_Uni...")
            from src.UNI import UNI

            uni_original = UNI()
            start_time = time.time()
            data_original, info_original = uni_original.kdk_getElement_Uni(mouse_x, mouse_y, False)
            time_original = time.time() - start_time

            print(f"   耗时: {time_original:.3f}s")
            print(f"   结果: {info_original}")
            if data_original:
                print(f"   控件: {data_original.get('Name', 'N/A')} ({data_original.get('Rolename', 'N/A')})")

        except Exception as e:
            print(f"   ❌ 原始方法失败: {e}")
            data_original, info_original = None, str(e)

        try:
            # 测试新的 Wayland 兼容方法
            print(f"🔍 测试 Wayland 兼容方法...")
            from src.UNI_new import kdk_getElement_Uni_wayland

            start_time = time.time()
            data_wayland, info_wayland = kdk_getElement_Uni_wayland(mouse_x, mouse_y, False)
            time_wayland = time.time() - start_time

            print(f"   耗时: {time_wayland:.3f}s")
            print(f"   结果: {info_wayland}")
            if data_wayland:
                print(f"   控件: {data_wayland.get('Name', 'N/A')} ({data_wayland.get('Rolename', 'N/A')})")

        except Exception as e:
            print(f"   ❌ Wayland方法失败: {e}")
            data_wayland, info_wayland = None, str(e)

        # 比较数据格式
        is_compatible = compare_control_data_formats(data_original, data_wayland)

        print(f"🎯 坐标 ({mouse_x}, {mouse_y}) 兼容性结果:")
        if is_compatible:
            print("✅ 数据格式完全兼容！")
        else:
            print("❌ 数据格式存在差异")

        # 只显示第一个测试的详细信息
        if i == 0:
            if data_original:
                print(f"\n📋 原始方法详细信息:")
                for key, value in data_original.items():
                    if not key.startswith('_'):  # 跳过内部字段
                        print(f"   {key}: {value}")

            if data_wayland:
                print(f"\n📋 Wayland方法详细信息:")
                for key, value in data_wayland.items():
                    if not key.startswith('_'):  # 跳过内部字段
                        print(f"   {key}: {value}")

def test_quick_mode():
    """测试快速模式"""
    print(f"\n🚀 测试快速模式兼容性")
    print("=" * 40)

    # 使用固定坐标
    mouse_x, mouse_y = 472, 620
    
    try:
        # 测试原始快速模式
        from src.UNI import UNI
        uni_original = UNI()
        extents_original, info_original = uni_original.kdk_getElement_Uni(mouse_x, mouse_y, True)
        
        print(f"原始快速模式: {info_original}")
        if extents_original:
            print(f"   坐标: ({extents_original.x}, {extents_original.y}, {extents_original.width}, {extents_original.height})")
        
    except Exception as e:
        print(f"原始快速模式失败: {e}")
        extents_original = None
    
    try:
        # 测试 Wayland 快速模式
        from src.UNI_new import kdk_getElement_Uni_wayland
        extents_wayland, info_wayland = kdk_getElement_Uni_wayland(mouse_x, mouse_y, True)
        
        print(f"Wayland快速模式: {info_wayland}")
        if extents_wayland:
            print(f"   坐标: ({extents_wayland.x}, {extents_wayland.y}, {extents_wayland.width}, {extents_wayland.height})")
        
    except Exception as e:
        print(f"Wayland快速模式失败: {e}")
        extents_wayland = None
    
    # 比较快速模式结果
    if extents_original and extents_wayland:
        print("✅ 快速模式格式兼容")
    elif not extents_original and not extents_wayland:
        print("⚠️  两种快速模式都未找到控件")
    else:
        print("❌ 快速模式结果不一致")

if __name__ == "__main__":
    test_wayland_compatibility()
    test_quick_mode()
