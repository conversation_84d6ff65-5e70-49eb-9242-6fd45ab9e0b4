#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
hellobig应用精确控件识别测试
使用真实窗口位置进行准确的控件识别和高亮
"""

import sys
import os
import time
import subprocess
import re

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_hellobig_window_geometry():
    """获取hellobig窗口的真实几何信息"""
    try:
        # 首先列出所有窗口找到hellobig
        result = subprocess.run(['wlcctrl', '-l'], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 无法列出窗口: {result.stderr}")
            return None
        
        # 查找AT-SPI测试界面窗口
        lines = result.stdout.split('\n')
        hellobig_uuid = None
        
        for i, line in enumerate(lines):
            if 'AT-SPI测试界面' in line:
                # 查找前一行的UUID
                for j in range(i-1, -1, -1):
                    if 'toplevel' in lines[j]:
                        uuid_match = re.search(r'"([^"]+)"', lines[j])
                        if uuid_match:
                            hellobig_uuid = uuid_match.group(1)
                            break
                break
        
        if not hellobig_uuid:
            print("❌ 未找到hellobig窗口UUID")
            return None
        
        print(f"✅ 找到hellobig窗口UUID: {hellobig_uuid}")
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', hellobig_uuid], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 无法获取窗口几何信息: {result.stderr}")
            return None
        
        # 解析几何信息 "geometry: (564, 155) 1200 x 838"
        geo_match = re.search(r'geometry: \((\d+), (\d+)\) (\d+) x (\d+)', result.stdout)
        if geo_match:
            x, y, width, height = map(int, geo_match.groups())
            geometry = {
                'x': x,
                'y': y, 
                'width': width,
                'height': height
            }
            print(f"✅ hellobig窗口位置: ({x}, {y}) 大小: {width} × {height}")
            return geometry
        else:
            print(f"❌ 无法解析几何信息: {result.stdout}")
            return None
            
    except Exception as e:
        print(f"❌ 获取窗口几何信息失败: {e}")
        return None

def test_hellobig_controls_accurately():
    """精确测试hellobig应用中的控件"""
    print("🎯 精确测试hellobig应用控件识别")
    print("=" * 50)
    
    # 获取真实窗口位置
    geometry = get_hellobig_window_geometry()
    if not geometry:
        return
    
    win_x, win_y = geometry['x'], geometry['y']
    win_width, win_height = geometry['width'], geometry['height']
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        
        # 定义hellobig窗口内的测试点（相对于窗口左上角）
        relative_test_points = [
            # 左侧控件区域
            (50, 50, "普通按钮"),
            (50, 80, "切换按钮"),
            (50, 110, "复选框"),
            (50, 140, "单选按钮1"),
            (50, 170, "单选按钮2"),
            
            # 输入控件区域
            (150, 50, "用户名标签"),
            (250, 50, "用户名输入框"),
            (150, 80, "密码标签"),
            (250, 80, "密码输入框"),
            
            # 滑块和数字控件
            (400, 50, "音量滑块"),
            (400, 80, "数字输入框"),
            (400, 110, "进度条"),
            
            # 下拉框和选择器
            (550, 50, "城市下拉框"),
            (550, 80, "日期选择器"),
            (550, 110, "时间选择器"),
            
            # 文本区域
            (150, 200, "多行文本输入框"),
            
            # 列表和表格
            (50, 350, "列表控件"),
            (300, 350, "树形控件"),
            (550, 350, "表格控件"),
            
            # 高级控件
            (50, 600, "标签页控件"),
            (300, 600, "垂直滑块"),
            (450, 600, "拨号控件"),
            (550, 600, "字体选择器"),
        ]
        
        print(f"📋 测试 {len(relative_test_points)} 个窗口内控件位置")
        print(f"窗口范围: ({win_x}, {win_y}) 到 ({win_x + win_width}, {win_y + win_height})")
        print()
        
        successful_tests = []
        failed_tests = []
        
        for i, (rel_x, rel_y, description) in enumerate(relative_test_points):
            # 转换为绝对坐标
            abs_x = win_x + rel_x
            abs_y = win_y + rel_y
            
            # 检查坐标是否在窗口范围内
            if (abs_x < win_x or abs_x > win_x + win_width or
                abs_y < win_y or abs_y > win_y + win_height):
                print(f"📍 {i+1:2d}/{len(relative_test_points)} {description} - 坐标超出窗口范围，跳过")
                continue
            
            print(f"📍 {i+1:2d}/{len(relative_test_points)} {description}")
            print(f"     相对位置: ({rel_x}, {rel_y}) -> 绝对位置: ({abs_x}, {abs_y})")
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(abs_x, abs_y, quick=False, highlight=True)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    print(f"     ✅ 识别成功 ({elapsed:.2f}s): {name} ({role})")
                    
                    if coords:
                        ctrl_x = coords.get('x', 0)
                        ctrl_y = coords.get('y', 0)
                        ctrl_w = coords.get('width', 0)
                        ctrl_h = coords.get('height', 0)
                        print(f"     控件位置: ({ctrl_x}, {ctrl_y}) 大小: {ctrl_w} × {ctrl_h}")
                        
                        # 检查坐标偏差
                        diff_x = ctrl_x - abs_x
                        diff_y = ctrl_y - abs_y
                        print(f"     坐标偏差: X={diff_x}, Y={diff_y}")
                    
                    print(f"     🎨 红色高亮已显示")
                    
                    successful_tests.append({
                        'description': description,
                        'name': name,
                        'role': role,
                        'abs_coords': (abs_x, abs_y),
                        'ctrl_coords': coords,
                        'time': elapsed
                    })
                    
                else:
                    print(f"     ❌ 识别失败 ({elapsed:.2f}s): {info}")
                    failed_tests.append({
                        'description': description,
                        'abs_coords': (abs_x, abs_y),
                        'reason': info,
                        'time': elapsed
                    })
                
                time.sleep(2)  # 等待观察高亮效果
                
            except Exception as e:
                print(f"     ❌ 测试异常: {e}")
                failed_tests.append({
                    'description': description,
                    'abs_coords': (abs_x, abs_y),
                    'reason': f"异常: {e}",
                    'time': 0
                })
        
        # 统计结果
        print(f"\n📊 hellobig控件识别结果统计")
        print("=" * 50)
        print(f"总测试点数: {len(relative_test_points)}")
        print(f"识别成功: {len(successful_tests)}")
        print(f"识别失败: {len(failed_tests)}")
        print(f"成功率: {len(successful_tests)/len(relative_test_points)*100:.1f}%")
        
        if successful_tests:
            avg_time = sum(t['time'] for t in successful_tests) / len(successful_tests)
            print(f"平均识别时间: {avg_time:.2f}s")
            
            # 控件类型统计
            from collections import defaultdict
            type_count = defaultdict(int)
            for test in successful_tests:
                type_count[test['role']] += 1
            
            print(f"\n成功识别的控件类型:")
            for role, count in sorted(type_count.items()):
                print(f"  - {role}: {count} 个")
        
        if failed_tests:
            print(f"\n识别失败的控件:")
            for test in failed_tests:
                print(f"  - {test['description']}: {test['reason']}")
        
        return successful_tests, failed_tests
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return [], []

def test_hellobig_interactive():
    """交互式测试hellobig控件识别"""
    print(f"\n🖱️ hellobig交互式控件识别测试")
    print("=" * 50)
    
    geometry = get_hellobig_window_geometry()
    if not geometry:
        return
    
    win_x, win_y = geometry['x'], geometry['y']
    win_width, win_height = geometry['width'], geometry['height']
    
    print(f"hellobig窗口范围: ({win_x}, {win_y}) 到 ({win_x + win_width}, {win_y + win_height})")
    print("每2秒自动测试一次当前鼠标位置（如果在hellobig窗口内）")
    print("按Ctrl+C结束测试")
    print()
    
    try:
        from UNI_new import UNI
        
        uni = UNI()
        test_count = 0
        
        while test_count < 20:  # 最多测试20次
            # 获取鼠标位置
            try:
                result = subprocess.run(['xdotool', 'getmouselocation'], 
                                      capture_output=True, text=True, env={'DISPLAY': ':0'})
                if result.returncode == 0:
                    parts = result.stdout.strip().split()
                    mouse_x = int(parts[0].split(':')[1])
                    mouse_y = int(parts[1].split(':')[1])
                else:
                    time.sleep(2)
                    continue
            except:
                time.sleep(2)
                continue
            
            # 检查鼠标是否在hellobig窗口内
            if (win_x <= mouse_x <= win_x + win_width and 
                win_y <= mouse_y <= win_y + win_height):
                
                test_count += 1
                rel_x = mouse_x - win_x
                rel_y = mouse_y - win_y
                
                print(f"📍 测试 {test_count}/20: 鼠标在hellobig窗口内")
                print(f"   绝对位置: ({mouse_x}, {mouse_y})")
                print(f"   相对位置: ({rel_x}, {rel_y})")
                
                try:
                    control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
                    
                    if control_data:
                        name = control_data.get('Name', 'N/A')
                        role = control_data.get('Rolename', 'N/A')
                        coords = control_data.get('Coords', {})
                        
                        print(f"   ✅ 控件: {name} ({role})")
                        if coords:
                            print(f"   控件坐标: ({coords.get('x')}, {coords.get('y')})")
                        print(f"   🎨 红色高亮已显示")
                    else:
                        print(f"   ❌ 未找到控件: {info}")
                    
                except Exception as e:
                    print(f"   ❌ 识别异常: {e}")
            
            time.sleep(2)
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 交互测试失败: {e}")

def validate_highlight_accuracy():
    """验证高亮精度"""
    print(f"\n🎯 验证高亮精度")
    print("=" * 50)
    
    geometry = get_hellobig_window_geometry()
    if not geometry:
        return
    
    win_x, win_y = geometry['x'], geometry['y']
    
    # 测试几个固定位置的高亮精度
    test_positions = [
        (win_x + 50, win_y + 50, "左上角按钮"),
        (win_x + 200, win_y + 100, "中上输入框"),
        (win_x + 400, win_y + 200, "中央区域"),
        (win_x + 600, win_y + 300, "右侧控件"),
    ]
    
    try:
        from UNI_new import UNI
        uni = UNI()
        
        for abs_x, abs_y, description in test_positions:
            print(f"\n📍 测试 {description} - 位置: ({abs_x}, {abs_y})")
            
            # 先显示一个绿色标记在测试坐标
            try:
                from ultimate_highlight import ultimate_highlight
                ultimate_highlight(abs_x-5, abs_y-5, 10, 10, 1, 'green', 1)
                print(f"   🟢 绿色标记显示在测试坐标")
                time.sleep(1)
            except:
                pass
            
            # 进行控件识别和高亮
            try:
                control_data, info = uni.kdk_getElement_Uni(abs_x, abs_y, quick=False, highlight=True)
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    coords = control_data.get('Coords', {})
                    
                    print(f"   ✅ 识别成功: {name} ({role})")
                    
                    if coords:
                        ctrl_x = coords.get('x', 0)
                        ctrl_y = coords.get('y', 0)
                        diff_x = ctrl_x - abs_x
                        diff_y = ctrl_y - abs_y
                        
                        print(f"   测试坐标: ({abs_x}, {abs_y})")
                        print(f"   控件坐标: ({ctrl_x}, {ctrl_y})")
                        print(f"   偏差: X={diff_x}, Y={diff_y}")
                        
                        if abs(diff_x) < 20 and abs(diff_y) < 20:
                            print(f"   ✅ 坐标偏差较小，高亮应该准确")
                        else:
                            print(f"   ⚠️ 坐标偏差较大，高亮可能不准确")
                    
                    print(f"   🎨 红色高亮已显示，请观察是否对准控件")
                    
                else:
                    print(f"   ❌ 识别失败: {info}")
                
                time.sleep(4)  # 等待观察
                
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
    
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    print("hellobig应用精确控件识别测试")
    print("=" * 60)
    print("🎯 目标: 使用真实窗口位置进行准确的控件识别和高亮")
    print("🔧 使用wlcctrl获取真实窗口几何信息")
    print()
    
    # 获取窗口信息
    geometry = get_hellobig_window_geometry()
    if not geometry:
        print("❌ 无法获取hellobig窗口信息，请确保应用正在运行")
        return 1
    
    try:
        # 1. 精确控件测试
        print("🎯 步骤1: 精确控件识别测试")
        successful_tests, failed_tests = test_hellobig_controls_accurately()
        
        # 2. 高亮精度验证
        print("\n🎯 步骤2: 高亮精度验证")
        validate_highlight_accuracy()
        
        # 3. 交互式测试
        print("\n🖱️ 步骤3: 交互式测试")
        test_hellobig_interactive()
        
        print(f"\n📊 测试完成总结")
        print("=" * 40)
        if successful_tests:
            print(f"✅ 成功识别 {len(successful_tests)} 个控件")
            print(f"🎨 所有成功识别的控件都显示了红色高亮边框")
            print(f"📍 高亮位置基于AT-SPI返回的控件坐标")
        
        if failed_tests:
            print(f"❌ {len(failed_tests)} 个位置未找到控件")
        
        print(f"\n💡 观察要点:")
        print(f"   - 红色边框应该准确对准识别到的控件")
        print(f"   - 如果边框位置偏移，说明坐标转换需要进一步调整")
        print(f"   - 不同类型的控件可能有不同的坐标特征")
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 hellobig精确控件识别测试完成!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())