#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
坐标修正测试脚本
验证AT-SPI坐标修正功能
"""

import sys
import os
import time
import subprocess

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
        else:
            return None, None
    except:
        return None, None

def compare_highlighting_methods():
    """对比不同高亮方法的精确度"""
    print("🎯 对比高亮方法精确度")
    print("=" * 50)
    
    # 获取测试坐标
    mouse_x, mouse_y = get_mouse_position()
    if mouse_x is None:
        print("❌ 无法获取鼠标位置")
        return
    
    print(f"测试坐标: ({mouse_x}, {mouse_y})")
    print("将依次显示3种高亮方法，请观察哪种最准确")
    print()
    
    try:
        # 方法1: 原版UNI
        print("🔍 方法1: 原版UNI高亮")
        test_original_uni(mouse_x, mouse_y)
        
        # 方法2: 精确版UNI
        print("\n🎯 方法2: 精确版UNI高亮")
        test_accurate_uni(mouse_x, mouse_y)
        
        # 方法3: 直接坐标高亮
        print("\n📍 方法3: 鼠标位置直接高亮")
        test_direct_highlight(mouse_x, mouse_y)
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")

def test_original_uni(mouse_x, mouse_y):
    """测试原版UNI"""
    try:
        from UNI_new import UNI
        
        uni = UNI()
        
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
        elapsed = time.time() - start_time
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            
            print(f"   ✅ 识别成功 ({elapsed:.2f}s): {name} ({role})")
            if coords:
                print(f"   原始坐标: ({coords.get('x')}, {coords.get('y')})")
            print("   🎨 红色高亮已显示（原版）")
        else:
            print(f"   ❌ 识别失败 ({elapsed:.2f}s): {info}")
        
        time.sleep(4)  # 等待观察
        
    except Exception as e:
        print(f"   ❌ 原版测试失败: {e}")

def test_accurate_uni(mouse_x, mouse_y):
    """测试精确版UNI"""
    try:
        from UNI_accurate import UNI_Accurate
        
        uni = UNI_Accurate()
        
        start_time = time.time()
        control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
        elapsed = time.time() - start_time
        
        if control_data:
            name = control_data.get('Name', 'N/A')
            role = control_data.get('Rolename', 'N/A')
            coords = control_data.get('Coords', {})
            
            print(f"   ✅ 识别成功 ({elapsed:.2f}s): {name} ({role})")
            if coords:
                print(f"   修正坐标: 将自动进行智能修正")
            print("   🎨 红色高亮已显示（精确版）")
        else:
            print(f"   ❌ 识别失败 ({elapsed:.2f}s): {info}")
        
        time.sleep(4)  # 等待观察
        
    except Exception as e:
        print(f"   ❌ 精确版测试失败: {e}")

def test_direct_highlight(mouse_x, mouse_y):
    """测试直接坐标高亮"""
    try:
        from ultimate_highlight_accurate import highlight_at_mouse
        
        print(f"   📍 直接在鼠标位置高亮")
        
        success = highlight_at_mouse(
            mouse_x, mouse_y, 
            width=60, height=30,
            duration=4, color='green', border_width=3
        )
        
        if success:
            print("   ✅ 绿色高亮已显示（鼠标位置）")
        else:
            print("   ❌ 直接高亮失败")
        
        time.sleep(4)  # 等待观察
        
    except Exception as e:
        print(f"   ❌ 直接高亮测试失败: {e}")

def test_multiple_positions():
    """测试多个位置的坐标修正"""
    print("\n🔄 多位置坐标修正测试")
    print("=" * 50)
    
    # 预定义一些测试点（基于hellobig应用的典型位置）
    test_points = [
        (50, 100, "左上角控件"),
        (200, 150, "上方控件"),
        (400, 300, "中央控件"),
        (600, 450, "右侧控件"),
        (100, 600, "下方控件"),
    ]
    
    try:
        from UNI_accurate import UNI_Accurate
        
        uni = UNI_Accurate()
        
        for i, (x, y, description) in enumerate(test_points):
            print(f"\n📍 测试点 {i+1}/5: {description} - ({x}, {y})")
            
            try:
                start_time = time.time()
                control_data, info = uni.kdk_getElement_Uni(x, y, quick=False, highlight=True)
                elapsed = time.time() - start_time
                
                if control_data:
                    name = control_data.get('Name', 'N/A')
                    role = control_data.get('Rolename', 'N/A')
                    print(f"   ✅ 找到控件 ({elapsed:.2f}s): {name} ({role})")
                    print("   🎨 精确高亮已显示")
                else:
                    print(f"   ❌ 未找到控件 ({elapsed:.2f}s): {info}")
                
                time.sleep(3)  # 等待观察每个高亮
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
        
    except Exception as e:
        print(f"❌ 多位置测试失败: {e}")

def interactive_coordinate_test():
    """交互式坐标测试"""
    print("\n🖱️ 交互式坐标测试")
    print("=" * 50)
    print("移动鼠标到不同控件位置")
    print("每3秒自动测试一次精确高亮")
    print("按Ctrl+C结束测试")
    print()
    
    try:
        from UNI_accurate import UNI_Accurate
        
        uni = UNI_Accurate()
        test_count = 0
        
        while test_count < 10:  # 最多测试10次
            current_pos = get_mouse_position()
            
            if current_pos[0] is not None:
                mouse_x, mouse_y = current_pos
                test_count += 1
                
                print(f"📍 测试 {test_count}/10: 鼠标位置 ({mouse_x}, {mouse_y})")
                
                try:
                    control_data, info = uni.kdk_getElement_Uni(mouse_x, mouse_y, quick=False, highlight=True)
                    
                    if control_data:
                        name = control_data.get('Name', 'N/A')
                        role = control_data.get('Rolename', 'N/A')
                        print(f"   ✅ 控件: {name} ({role})")
                        print("   🎨 精确高亮已显示")
                    else:
                        print(f"   ❌ 未找到控件")
                    
                except Exception as e:
                    print(f"   ❌ 测试失败: {e}")
            
            time.sleep(3)
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 交互测试失败: {e}")

def main():
    """主函数"""
    print("坐标修正测试脚本")
    print("=" * 60)
    print("🎯 目标: 验证AT-SPI坐标修正功能的效果")
    print("🎨 观察高亮边框是否准确对准控件")
    print()
    
    try:
        # 检查依赖
        print("🔧 检查依赖模块...")
        
        try:
            from UNI_accurate import UNI_Accurate
            print("   ✅ UNI_Accurate 可用")
        except ImportError as e:
            print(f"   ❌ UNI_Accurate 不可用: {e}")
            return 1
        
        try:
            from ultimate_highlight_accurate import ultimate_highlight_accurate
            print("   ✅ ultimate_highlight_accurate 可用")
        except ImportError as e:
            print(f"   ❌ ultimate_highlight_accurate 不可用: {e}")
            return 1
        
        print("   ✅ 所有依赖模块正常")
        print()
        
        # 1. 对比测试
        compare_highlighting_methods()
        
        # 2. 多位置测试
        test_multiple_positions()
        
        # 3. 交互式测试
        interactive_coordinate_test()
        
        print(f"\n📊 测试总结")
        print("=" * 40)
        print("✅ 坐标修正测试完成")
        print()
        print("🎯 效果评估:")
        print("   - 红色边框应该准确对准控件边界")
        print("   - 相比原版，精确版的高亮位置更准确")
        print("   - 智能坐标修正能适应不同类型的应用")
        print()
        print("💡 如果高亮仍然不准确，可能需要:")
        print("   - 调整坐标修正算法的偏移量")
        print("   - 针对特定应用类型进行微调")
        print("   - 考虑不同桌面环境的差异")
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 坐标修正测试结束!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())