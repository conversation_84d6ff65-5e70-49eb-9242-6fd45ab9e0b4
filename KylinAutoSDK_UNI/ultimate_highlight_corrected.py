#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
修正坐标偏移的终极高亮函数
"""

def ultimate_highlight_corrected(x, y, width, height, duration=2, color='red', border_width=2):
    """
    修正坐标偏移的终极高亮函数
    """
    # 应用Y坐标修正
    corrected_y = y + 40  # 往下偏移40像素
    
    print(f"🔧 坐标修正: 原始Y={y} -> 修正Y={corrected_y} (+40)")
    
    # 调用原始ultimate_highlight
    import sys
    import os
    sys.path.insert(0, '/home/<USER>/<PERSON><PERSON>inAutoSDK_UNI')
    from ultimate_highlight import ultimate_highlight
    return ultimate_highlight(x, corrected_y, width, height, duration, color, border_width)

if __name__ == "__main__":
    # 测试修正效果
    import subprocess
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            
            print(f"测试修正效果，鼠标位置: ({mouse_x}, {mouse_y})")
            ultimate_highlight_corrected(mouse_x-50, mouse_y-25, 100, 50, 3, 'orange', 2)
        else:
            ultimate_highlight_corrected(1000, 500, 200, 100, 3, 'orange', 2)
    except:
        ultimate_highlight_corrected(1000, 500, 200, 100, 3, 'orange', 2)
