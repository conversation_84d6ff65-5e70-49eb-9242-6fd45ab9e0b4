#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
简洁无边框高亮 - 细边框样式
"""

import subprocess
import time
import os

class CleanBorderlessHighlight:
    """简洁无边框高亮类"""
    
    def __init__(self):
        self.display = ":0"
        os.environ["DISPLAY"] = self.display
    
    def create_clean_highlight(self, x, y, width, height, duration=3, 
                             border_color='red', border_width=3, 
                             background_alpha=0.1):
        """创建简洁的无边框高亮
        
        Args:
            x, y: 位置
            width, height: 尺寸
            duration: 持续时间
            border_color: 边框颜色 ('red', 'blue', 'green', 'yellow', 'purple', 'cyan')
            border_width: 边框宽度 (1-10)
            background_alpha: 背景透明度 (0.0-1.0, 0为完全透明)
        """
        print("🎯 简洁无边框高亮")
        print("=" * 50)
        
        print(f"目标区域: ({x}, {y}) {width}×{height}")
        print(f"边框颜色: {border_color}, 宽度: {border_width}")
        print(f"持续时间: {duration}秒")
        
        # 创建简洁X11脚本
        x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    print("✅ 连接到X11...")
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    print("✅ 获取颜色...")
    colormap = screen.default_colormap
    
    # 支持的颜色
    color_map = {{
        'red': 'red',
        'blue': 'blue', 
        'green': 'green',
        'yellow': 'yellow',
        'purple': 'purple',
        'cyan': 'cyan',
        'orange': 'orange',
        'pink': 'pink'
    }}
    
    color_name = color_map.get('{border_color}', 'red')
    border_pixel = colormap.alloc_named_color(color_name).pixel
    
    # 如果需要背景色（半透明效果）
    bg_alpha = {background_alpha}
    if bg_alpha > 0:
        bg_pixel = border_pixel  # 使用相同颜色作为背景
    else:
        bg_pixel = 0  # 完全透明背景
    
    print("✅ 创建简洁高亮窗口...")
    # 创建主窗口
    highlight_win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width={border_width},
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=bg_pixel,
        border_pixel=border_pixel,
        override_redirect=True,  # 关键：绕过窗口管理器
        colormap=X.CopyFromParent
    )
    
    print("✅ 设置窗口属性...")
    # 设置窗口属性
    highlight_win.set_wm_name("Clean Highlight")
    highlight_win.set_wm_class("clean_highlight", "CleanHighlight")
    
    # 设置为dock类型
    try:
        highlight_win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
    except:
        pass
    
    # 设置状态
    try:
        highlight_win.change_property(
            disp.intern_atom("_NET_WM_STATE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
             disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
             disp.intern_atom("_NET_WM_STATE_ABOVE")]
        )
    except:
        pass
    
    print("✅ 映射窗口...")
    highlight_win.map()
    disp.sync()
    
    print(f"✅ 显示简洁高亮 {duration} 秒...")
    time.sleep({duration})
    
    print("✅ 清理窗口...")
    highlight_win.unmap()
    highlight_win.destroy()
    disp.sync()
    disp.close()
    
    print("✅ 简洁无边框高亮完成！")
    
except Exception as e:
    print(f"❌ 简洁高亮失败: {{e}}")
    import traceback
    traceback.print_exc()
'''
        
        try:
            print("🚀 启动简洁高亮...")
            result = subprocess.run(['python3', '-c', x11_script], 
                                  capture_output=True, text=True)
            
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print("🔴 错误:", result.stderr)
                
            if result.returncode == 0:
                print("✅ 简洁高亮执行成功！")
                return True
            else:
                print(f"⚠️ 返回码: {result.returncode}")
                return False
                
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
    
    def create_minimal_highlight(self, x, y, width, height, duration=3, color='red'):
        """创建最简洁的高亮 - 只有边框线条"""
        print("\n🎯 最简洁高亮（仅边框）")
        print("=" * 50)
        
        minimal_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel
    
    # 创建最小化窗口 - 只有边框，完全透明背景
    minimal_win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width=2,  # 细边框
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,  # 完全透明
        border_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    
    # 设置属性
    minimal_win.set_wm_name("Minimal Highlight")
    try:
        minimal_win.change_property(
            disp.intern_atom("_NET_WM_WINDOW_TYPE"),
            disp.intern_atom("ATOM"),
            32,
            [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
        )
    except:
        pass
    
    minimal_win.map()
    disp.sync()
    
    time.sleep({duration})
    
    minimal_win.unmap()
    minimal_win.destroy()
    disp.sync()
    disp.close()
    
    print("✅ 最简洁高亮完成！")
    
except Exception as e:
    print(f"❌ 最简洁高亮失败: {{e}}")
'''
        
        try:
            result = subprocess.run(['python3', '-c', minimal_script], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 最简洁高亮执行成功！")
                return True
            else:
                print(f"⚠️ 失败，返回码: {result.returncode}")
                if result.stderr:
                    print("错误:", result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False

def test_clean_styles():
    """测试各种简洁样式"""
    print("🎯 简洁无边框高亮样式测试")
    print("=" * 60)
    
    # 获取鼠标位置
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
        else:
            mouse_x, mouse_y = 1000, 500
    except:
        mouse_x, mouse_y = 1000, 500
    
    test_x = mouse_x - 150
    test_y = mouse_y - 100
    test_w = 300
    test_h = 200
    
    print("✅ 可用样式:")
    print("   • 细边框 + 透明背景")
    print("   • 细边框 + 淡色背景")  
    print("   • 最简洁样式（仅边框）")
    print("   • 多种颜色选择")
    print()
    
    # 创建高亮器
    highlighter = CleanBorderlessHighlight()
    
    # 测试1: 红色细边框，透明背景
    print("🔥 测试1: 红色细边框，透明背景")
    highlighter.create_clean_highlight(test_x, test_y, test_w, test_h, 
                                     duration=3, border_color='red', 
                                     border_width=2, background_alpha=0)
    
    # 测试2: 蓝色细边框，淡背景
    print("🔥 测试2: 蓝色细边框，淡背景")
    highlighter.create_clean_highlight(test_x, test_y, test_w, test_h, 
                                     duration=3, border_color='blue', 
                                     border_width=3, background_alpha=0.1)
    
    # 测试3: 最简洁样式
    print("🔥 测试3: 最简洁绿色边框")
    highlighter.create_minimal_highlight(test_x, test_y, test_w, test_h, 
                                       duration=3, color='green')
    
    print(f"\n" + "=" * 60)
    print("🎊 简洁样式测试完成！")
    print("\n💡 样式说明:")
    print("   • border_width: 1-5推荐（细边框）")
    print("   • background_alpha: 0（透明）到0.3（淡色）")
    print("   • 颜色: red, blue, green, yellow, purple, cyan")
    print("   • 完全无窗口装饰")

# 便捷函数
def highlight_control(x, y, width, height, duration=2, color='red', style='clean'):
    """便捷的控件高亮函数
    
    Args:
        x, y: 控件位置
        width, height: 控件尺寸
        duration: 高亮时间（秒）
        color: 边框颜色
        style: 样式类型 ('clean', 'minimal')
    """
    highlighter = CleanBorderlessHighlight()
    
    if style == 'minimal':
        return highlighter.create_minimal_highlight(x, y, width, height, duration, color)
    else:
        return highlighter.create_clean_highlight(x, y, width, height, duration, 
                                                color, border_width=2, background_alpha=0)

if __name__ == "__main__":
    test_clean_styles()