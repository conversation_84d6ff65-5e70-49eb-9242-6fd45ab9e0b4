#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
找出真正的坐标偏移
通过已知控件位置反推正确的偏移量
"""

import sys
import os
import subprocess
import time
import re
import pyatspi


def get_mouse_position():
    """获取当前鼠标位置"""
    try:
        result = subprocess.run(['xdotool', 'getmouselocation'], 
                              capture_output=True, text=True, env={'DISPLAY': ':0'})
        if result.returncode == 0:
            parts = result.stdout.strip().split()
            mouse_x = int(parts[0].split(':')[1])
            mouse_y = int(parts[1].split(':')[1])
            return mouse_x, mouse_y
    except Exception:
        pass
    return None, None


def get_wlcctrl_offset():
    """获取wlcctrl报告的窗口偏移"""
    try:
        # 获取窗口列表
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 查找hellobig窗口ID
        window_id = None
        lines = result.stdout.strip().split('\n')
        for i, line in enumerate(lines):
            if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                if i > 0:
                    prev_line = lines[i-1]
                    if 'toplevel' in prev_line:
                        window_id = prev_line.split('"')[1]
                        break
        
        if not window_id:
            return None
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 解析几何信息
        for line in result.stdout.strip().split('\n'):
            if 'geometry:' in line:
                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)', line)
                if match:
                    x, y = int(match.group(1)), int(match.group(2))
                    return (x, y)
        
        return None
        
    except Exception as e:
        print(f"❌ 获取wlcctrl偏移失败: {e}")
        return None


def find_known_controls():
    """查找已知的控件"""
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            app_name = app.name or f"应用{i}"
            
            if 'hellobig' in app_name.lower():
                print(f"📱 找到应用: {app_name}")
                
                controls = []
                collect_known_controls(app, controls)
                
                return controls
        
        return []
        
    except Exception as e:
        print(f"❌ 查找控件失败: {e}")
        return []


def collect_known_controls(element, control_list, depth=0, max_depth=4):
    """收集已知的控件"""
    if depth > max_depth:
        return
    
    try:
        name = element.name or "N/A"
        role = element.getRoleName() if hasattr(element, 'getRoleName') else 'unknown'
        
        # 获取坐标
        extents = None
        try:
            if hasattr(element, 'queryComponent'):
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
        except Exception:
            pass
        
        # 收集已知的具体控件
        known_controls = {
            '普通按钮': 'push button',
            '切换按钮': 'check box', 
            '复选框': 'check box',
            '单选按钮1': 'radio button',
            '单选按钮2': 'radio button',
            '用户名输入框': 'text',
            '密码输入框': 'password text',
            '音量滑块': 'slider',
            '数字输入框': 'spin button'
        }
        
        if (extents and extents.width > 0 and extents.height > 0 and 
            name in known_controls and role == known_controls[name]):
            
            control_list.append({
                'name': name,
                'role': role,
                'atspi_position': (extents.x, extents.y),
                'size': (extents.width, extents.height),
                'element': element
            })
        
        # 递归处理子元素
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                collect_known_controls(child, control_list, depth + 1, max_depth)
        except Exception:
            pass
            
    except Exception:
        pass


def test_offset_with_known_control(control, test_offset):
    """使用已知控件测试偏移"""
    atspi_x, atspi_y = control['atspi_position']
    width, height = control['size']
    
    # 计算控件在屏幕上的预期位置
    expected_screen_x = atspi_x + test_offset[0]
    expected_screen_y = atspi_y + test_offset[1]
    
    print(f"\n🧪 测试控件: {control['name']}")
    print(f"   AT-SPI位置: ({atspi_x}, {atspi_y}) {width}×{height}")
    print(f"   测试偏移: {test_offset}")
    print(f"   预期屏幕位置: ({expected_screen_x}, {expected_screen_y}) {width}×{height}")
    
    # 执行高亮测试
    try:
        from ultimate_highlight import ultimate_highlight
        
        print(f"   执行高亮测试...")
        highlight_success = ultimate_highlight(
            expected_screen_x, expected_screen_y, width, height,
            duration=2, color='yellow', border_width=2
        )
        
        if highlight_success:
            print(f"   ✅ 高亮成功")
            
            # 询问用户高亮是否准确
            try:
                feedback = input(f"   高亮是否准确覆盖了 {control['name']}? (y/n): ").strip().lower()
                return feedback == 'y'
            except KeyboardInterrupt:
                return False
        else:
            print(f"   ❌ 高亮失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 高亮异常: {e}")
        return False


def find_correct_offset():
    """找出正确的偏移"""
    print("🔍 寻找正确的坐标偏移")
    print("=" * 60)
    
    # 获取wlcctrl偏移作为起始点
    wlc_offset = get_wlcctrl_offset()
    if wlc_offset:
        print(f"🪟 wlcctrl报告的偏移: {wlc_offset}")
    else:
        print("❌ 无法获取wlcctrl偏移")
        wlc_offset = (0, 0)
    
    # 查找已知控件
    controls = find_known_controls()
    if not controls:
        print("❌ 未找到已知控件")
        return
    
    print(f"📋 找到 {len(controls)} 个已知控件:")
    for i, ctrl in enumerate(controls):
        print(f"   {i+1}. {ctrl['name']} ({ctrl['role']}) - AT-SPI位置: {ctrl['atspi_position']}")
    
    # 选择一个控件进行测试
    if not controls:
        return
    
    test_control = controls[0]  # 使用第一个控件
    print(f"\n🎯 使用 '{test_control['name']}' 进行偏移测试")
    
    # 测试不同的偏移值
    test_offsets = [
        wlc_offset,  # wlcctrl报告的偏移
        (wlc_offset[0], wlc_offset[1] - 38),  # 减去可能的标题栏高度
        (wlc_offset[0] - 8, wlc_offset[1] - 30),  # 减去可能的边框
        (wlc_offset[0] + 8, wlc_offset[1] + 30),  # 加上可能的边框
        (wlc_offset[0], wlc_offset[1] - 50),  # 更大的Y偏移
        (wlc_offset[0], wlc_offset[1] + 50),  # 反向Y偏移
    ]
    
    print(f"\n🧪 开始测试不同偏移值...")
    
    correct_offset = None
    
    for i, offset in enumerate(test_offsets):
        print(f"\n--- 测试 {i+1}/{len(test_offsets)} ---")
        
        if test_offset_with_known_control(test_control, offset):
            print(f"✅ 找到正确偏移: {offset}")
            correct_offset = offset
            break
        else:
            print(f"❌ 偏移 {offset} 不正确")
    
    if correct_offset:
        print(f"\n🎉 找到正确的坐标偏移: {correct_offset}")
        
        # 测试其他控件验证
        print(f"\n🔬 验证其他控件...")
        for ctrl in controls[1:3]:  # 测试接下来的2个控件
            print(f"\n验证控件: {ctrl['name']}")
            if test_offset_with_known_control(ctrl, correct_offset):
                print(f"✅ {ctrl['name']} 验证成功")
            else:
                print(f"❌ {ctrl['name']} 验证失败")
        
        return correct_offset
    else:
        print(f"\n❌ 未找到正确的偏移")
        
        # 手动调整模式
        print(f"\n🔧 进入手动调整模式...")
        manual_offset = manual_offset_adjustment(test_control, wlc_offset)
        return manual_offset


def manual_offset_adjustment(control, base_offset):
    """手动调整偏移"""
    print(f"\n🔧 手动调整偏移模式")
    print(f"基础偏移: {base_offset}")
    print(f"测试控件: {control['name']}")
    print(f"输入格式: x_offset,y_offset (例如: 564,155)")
    print(f"输入 'q' 退出")
    
    while True:
        try:
            user_input = input("\n请输入偏移值: ").strip()
            
            if user_input.lower() == 'q':
                break
            
            if ',' in user_input:
                x_str, y_str = user_input.split(',')
                test_offset = (int(x_str.strip()), int(y_str.strip()))
                
                print(f"测试偏移: {test_offset}")
                
                if test_offset_with_known_control(control, test_offset):
                    print(f"✅ 偏移 {test_offset} 正确!")
                    return test_offset
                else:
                    print(f"❌ 偏移 {test_offset} 不正确，请继续调整")
            else:
                print("❌ 格式错误，请使用 x,y 格式")
                
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            break
    
    return None


def main():
    """主函数"""
    print("🔍 寻找真正的坐标偏移")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    try:
        correct_offset = find_correct_offset()
        
        if correct_offset:
            print(f"\n🎯 最终结果:")
            print(f"正确的坐标偏移: {correct_offset}")
            print(f"使用方法: 屏幕坐标 = AT-SPI坐标 + {correct_offset}")
        else:
            print(f"\n❌ 未能确定正确的偏移")
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
