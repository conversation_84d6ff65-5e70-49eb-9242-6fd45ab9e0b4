#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
专门测试hellobig树形控件的table cell选择问题
"""

import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_tree_cell_coordinates():
    """测试树形控件各个cell的坐标检测"""
    try:
        from UNI_new import UNI
        
        # 从分析结果获得的各个节点坐标
        tree_cells = [
            {'name': '根项目1', 'coords': (421, 303, 234, 18)},
            {'name': '子项1-1', 'coords': (441, 321, 214, 18)},
            {'name': '孙项1-1-1', 'coords': (461, 339, 194, 18)},
            {'name': '子项1-2', 'coords': (441, 357, 214, 18)},
            {'name': '根项目2', 'coords': (421, 375, 234, 18)},
            {'name': '子项2-1', 'coords': (441, 393, 214, 18)},
        ]
        
        print("🧪 测试各个树形节点的坐标检测:")
        print("=" * 60)
        
        uni = UNI()
        
        for i, cell in enumerate(tree_cells):
            x, y, w, h = cell['coords']
            center_x = x + w // 2
            center_y = y + h // 2
            
            print(f"\n--- 测试节点 {i+1}/{len(tree_cells)} ---")
            print(f"预期节点: '{cell['name']}'")
            print(f"坐标区域: ({x}, {y}) 尺寸: {w}×{h} 面积: {w*h}")
            print(f"中心点: ({center_x}, {center_y})")
            
            # 测试中心点
            try:
                detected, info = uni.kdk_getElement_Uni(center_x, center_y, quick=False, highlight=False)
                
                if detected:
                    detected_name = detected.get('Name', 'N/A')
                    detected_role = detected.get('Rolename', 'N/A')
                    detected_coords = detected.get('Coords', {})
                    
                    print(f"检测结果: {detected_role} '{detected_name}'")
                    if detected_coords:
                        dx, dy, dw, dh = detected_coords.get('x', 0), detected_coords.get('y', 0), detected_coords.get('width', 0), detected_coords.get('height', 0)
                        print(f"检测坐标: ({dx}, {dy}) 尺寸: {dw}×{dh} 面积: {dw*dh}")
                    
                    # 判断是否匹配
                    if detected_name == cell['name']:
                        print("✅ 检测完全正确!")
                    else:
                        print("❌ 检测到了错误的节点")
                        print(f"   期望: '{cell['name']}' 实际: '{detected_name}'")
                        
                        # 分析为什么选择了错误的节点
                        if detected_coords:
                            expected_area = w * h
                            actual_area = dw * dh
                            print(f"   面积对比: 期望={expected_area} 实际={actual_area}")
                            if actual_area > expected_area:
                                print("   ⚠️  检测到了更大面积的控件")
                else:
                    print(f"❌ 未检测到控件: {info}")
                    
            except Exception as e:
                print(f"❌ 检测出错: {e}")
            
            # 额外测试：测试节点的不同位置点
            test_points = [
                (x + 5, y + h//2, "左边缘"),
                (x + w - 5, y + h//2, "右边缘"),
                (center_x, y + 5, "上边缘"),
                (center_x, y + h - 5, "下边缘"),
            ]
            
            print("   📍 测试节点边界点:")
            for px, py, desc in test_points:
                try:
                    detected, _ = uni.kdk_getElement_Uni(px, py, quick=False, highlight=False)
                    if detected:
                        detected_name = detected.get('Name', 'N/A')
                        match_status = "✅" if detected_name == cell['name'] else "❌"
                        print(f"     {desc}({px},{py}): {match_status} '{detected_name}'")
                    else:
                        print(f"     {desc}({px},{py}): ❌ 无控件")
                except:
                    print(f"     {desc}({px},{py}): ❌ 错误")
        
        print(f"\n" + "=" * 60)
        print("🎯 测试结论:")
        print("   1. 如果所有节点都检测为'根项目2'，说明控件选择算法有问题")
        print("   2. 如果部分节点检测正确，说明是坐标重叠或优先级问题")
        print("   3. 需要查看控件选择算法的面积和层次判断逻辑")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_control_selection_logic():
    """分析控件选择逻辑"""
    print(f"\n🔍 分析控件选择逻辑问题:")
    print("=" * 60)
    
    # 从代码分析来理解问题
    tree_cells = [
        {'name': '根项目1', 'coords': (421, 303, 234, 18), 'area': 234*18},
        {'name': '子项1-1', 'coords': (441, 321, 214, 18), 'area': 214*18},
        {'name': '孙项1-1-1', 'coords': (461, 339, 194, 18), 'area': 194*18},
        {'name': '子项1-2', 'coords': (441, 357, 214, 18), 'area': 214*18},
        {'name': '根项目2', 'coords': (421, 375, 234, 18), 'area': 234*18},
        {'name': '子项2-1', 'coords': (441, 393, 214, 18), 'area': 214*18},
    ]
    
    print("📊 各节点的坐标和面积分析:")
    for cell in tree_cells:
        x, y, w, h = cell['coords']
        print(f"  {cell['name']}: ({x},{y}) {w}×{h} 面积:{cell['area']}")
    
    print(f"\n🎯 问题分析:")
    print("  1. 所有节点都是'table cell'类型，优先级相同")
    print("  2. '根项目1'和'根项目2'面积最大(4212)")
    print("  3. 子节点面积较小，但位置可能重叠")
    print("  4. 控件选择算法可能偏向选择面积大的控件")
    
    print(f"\n💡 推测的选择逻辑问题:")
    print("  - 当多个控件包含同一坐标时")
    print("  - 算法选择了面积最大或最后遍历到的控件")
    print("  - '根项目2'在列表中位置靠后，且面积大")
    print("  - 导致其他节点的坐标都被'根项目2'覆盖")

def main():
    """主函数"""
    print("🌳 Hellobig树形控件table cell选择问题分析")
    print("=" * 80)
    
    # 分析控件选择逻辑
    analyze_control_selection_logic()
    
    # 测试各个节点的检测
    test_tree_cell_coordinates()
    
    print("\n" + "=" * 80)
    print("🎉 分析完成!")
    print("\n💡 结论:")
    print("   问题不在于控件识别，而在于控件选择算法")
    print("   当多个table cell控件重叠时，算法选择了错误的控件")
    print("   需要优化控件选择逻辑，优先选择面积更小、更精确的控件")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())