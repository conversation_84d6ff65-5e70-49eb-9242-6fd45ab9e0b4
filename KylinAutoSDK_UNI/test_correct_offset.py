#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
使用正确的窗口偏移测试hellobig控件
直接使用wlcctrl获取的窗口位置作为偏移
"""

import sys
import os
import subprocess
import time
from offset_corrected_detector import OffsetCorrectedDetector


def get_hellobig_window_offset():
    """获取hellobig窗口的真实偏移"""
    try:
        print("🔍 获取hellobig窗口偏移...")
        
        # 获取窗口列表
        result = subprocess.run(['wlcctrl', '--list'], capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 查找hellobig窗口ID
        window_id = None
        lines = result.stdout.strip().split('\n')
        for i, line in enumerate(lines):
            if ('AT-SPI测试界面' in line or 'Qt控件集合' in line) and 'Qt Creator' not in line:
                if i > 0:
                    prev_line = lines[i-1]
                    if 'toplevel' in prev_line:
                        window_id = prev_line.split('"')[1]
                        print(f"   找到窗口ID: {window_id}")
                        break
        
        if not window_id:
            return None
        
        # 获取窗口几何信息
        result = subprocess.run(['wlcctrl', '--getwindowgeometry', window_id], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        # 解析几何信息
        import re
        for line in result.stdout.strip().split('\n'):
            if 'geometry:' in line:
                match = re.search(r'geometry:\s*\((\d+),\s*(\d+)\)', line)
                if match:
                    x, y = int(match.group(1)), int(match.group(2))
                    print(f"   窗口偏移: ({x}, {y})")
                    return (x, y)
        
        return None
        
    except Exception as e:
        print(f"❌ 获取窗口偏移失败: {e}")
        return None


def test_control_at_atspi_position(detector, name, atspi_x, atspi_y, width, height, expected_type):
    """直接使用AT-SPI坐标测试控件"""
    center_x = atspi_x + width // 2
    center_y = atspi_y + height // 2
    
    print(f"\n🎯 测试控件: {name}")
    print(f"   AT-SPI位置: ({atspi_x}, {atspi_y}) 尺寸: {width}×{height}")
    print(f"   AT-SPI中心: ({center_x}, {center_y})")
    print("-" * 40)
    
    # 直接使用AT-SPI坐标进行检测（不进行坐标转换）
    result = detector.get_control_at_point_corrected(center_x, center_y, auto_detect_offset=False)
    
    if result['success']:
        detected_info = result['control_info']
        print(f"✅ 检测成功!")
        print(f"   检测到名称: {detected_info['name']}")
        print(f"   检测到类型: {detected_info['role']}")
        
        # 验证是否匹配
        name_match = name in detected_info['name'] or detected_info['name'] in name
        type_match = expected_type == detected_info['role']
        
        if name_match and type_match:
            print(f"   🎉 完全匹配!")
            return True
        elif name_match:
            print(f"   ⚠️  名称匹配，但类型不匹配 (期望: {expected_type}, 实际: {detected_info['role']})")
            return True
        elif type_match:
            print(f"   ⚠️  类型匹配，但名称不匹配 (期望: {name}, 实际: {detected_info['name']})")
            return True
        else:
            print(f"   ❌ 不匹配 (期望: {name}/{expected_type}, 实际: {detected_info['name']}/{detected_info['role']})")
            return False
    else:
        print(f"❌ 检测失败: {result['message']}")
        return False


def test_control_at_screen_position(detector, name, atspi_x, atspi_y, width, height, expected_type, window_offset):
    """使用屏幕坐标测试控件"""
    # 计算屏幕坐标
    screen_x = window_offset[0] + atspi_x + width // 2
    screen_y = window_offset[1] + atspi_y + height // 2
    
    print(f"\n🎯 测试控件: {name} (屏幕坐标)")
    print(f"   AT-SPI位置: ({atspi_x}, {atspi_y}) 尺寸: {width}×{height}")
    print(f"   屏幕坐标: ({screen_x}, {screen_y})")
    print("-" * 40)
    
    # 使用屏幕坐标进行检测
    result = detector.get_control_at_point_corrected(screen_x, screen_y)
    
    if result['success']:
        detected_info = result['control_info']
        print(f"✅ 检测成功!")
        print(f"   检测到名称: {detected_info['name']}")
        print(f"   检测到类型: {detected_info['role']}")
        
        # 验证是否匹配
        name_match = name in detected_info['name'] or detected_info['name'] in name
        type_match = expected_type == detected_info['role']
        
        if name_match and type_match:
            print(f"   🎉 完全匹配!")
            return True
        elif name_match:
            print(f"   ⚠️  名称匹配，但类型不匹配 (期望: {expected_type}, 实际: {detected_info['role']})")
            return True
        elif type_match:
            print(f"   ⚠️  类型匹配，但名称不匹配 (期望: {name}, 实际: {detected_info['name']})")
            return True
        else:
            print(f"   ❌ 不匹配 (期望: {name}/{expected_type}, 实际: {detected_info['name']}/{detected_info['role']})")
            return False
    else:
        print(f"❌ 检测失败: {result['message']}")
        return False


def main():
    """主函数"""
    print("🔧 正确偏移测试器")
    print("=" * 60)
    
    if not os.getenv('DISPLAY'):
        print("❌ 未检测到DISPLAY环境变量")
        sys.exit(1)
    
    # 获取窗口偏移
    window_offset = get_hellobig_window_offset()
    if not window_offset:
        print("❌ 无法获取窗口偏移")
        sys.exit(1)
    
    # 初始化检测器
    detector = OffsetCorrectedDetector(target_app_name="hellobig")
    
    # 测试控件列表
    test_controls = [
        ('普通按钮', 20, 20, 100, 30, 'push button'),
        ('切换按钮', 140, 20, 100, 30, 'check box'),
        ('复选框', 20, 71, 58, 22, 'check box'),
        ('单选按钮1', 160, 71, 54, 22, 'radio button'),
        ('单选按钮2', 280, 71, 54, 22, 'radio button'),
        ('用户名输入框', 110, 110, 200, 25, 'text'),
        ('密码输入框', 420, 110, 200, 25, 'password text'),
        ('音量滑块', 110, 160, 200, 20, 'slider'),
        ('数字输入框', 330, 160, 80, 25, 'spin button'),
        ('城市下拉框', 110, 210, 150, 25, 'combo box'),
    ]
    
    print(f"\n📋 测试方案对比:")
    print("1. 直接使用AT-SPI坐标")
    print("2. 使用屏幕坐标（加上窗口偏移）")
    print("=" * 60)
    
    # 方案1：直接使用AT-SPI坐标
    print(f"\n🔬 方案1：直接使用AT-SPI坐标测试")
    print("-" * 40)
    atspi_success = 0
    for name, x, y, w, h, expected_type in test_controls:
        if test_control_at_atspi_position(detector, name, x, y, w, h, expected_type):
            atspi_success += 1
    
    print(f"\n📊 AT-SPI坐标测试结果: {atspi_success}/{len(test_controls)} ({atspi_success/len(test_controls)*100:.1f}%)")
    
    # 方案2：使用屏幕坐标
    print(f"\n🔬 方案2：使用屏幕坐标测试（窗口偏移: {window_offset}）")
    print("-" * 40)
    screen_success = 0
    for name, x, y, w, h, expected_type in test_controls:
        if test_control_at_screen_position(detector, name, x, y, w, h, expected_type, window_offset):
            screen_success += 1
    
    print(f"\n📊 屏幕坐标测试结果: {screen_success}/{len(test_controls)} ({screen_success/len(test_controls)*100:.1f}%)")
    
    # 总结
    print(f"\n🎯 测试总结:")
    print("=" * 40)
    print(f"AT-SPI坐标方案: {atspi_success}/{len(test_controls)} 成功")
    print(f"屏幕坐标方案: {screen_success}/{len(test_controls)} 成功")
    
    if atspi_success > screen_success:
        print("✅ AT-SPI坐标方案更准确")
        print("💡 建议：直接使用AT-SPI坐标，不需要额外的窗口偏移修正")
    elif screen_success > atspi_success:
        print("✅ 屏幕坐标方案更准确")
        print(f"💡 建议：使用窗口偏移 {window_offset} 进行坐标修正")
    else:
        print("⚖️  两种方案效果相同")


if __name__ == "__main__":
    main()
