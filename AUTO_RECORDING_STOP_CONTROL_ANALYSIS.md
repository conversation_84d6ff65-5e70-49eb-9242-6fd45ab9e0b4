# auto_recording_manager_v11.py 停止监听控制处理梳理

## 🎯 概述

auto_recording_manager_v11.py中的停止监听控制处理涉及多个组件的协调停止，包括事件捕获、悬停检测、菜单监听、线程管理等。

## 🏗️ 架构层次

```
AutoRecordingManager (主管理器)
├── EventCapture (事件捕获器)
├── HoverDetector (悬停检测器)
├── MenuListenerManager (菜单监听管理器)
├── 事件处理线程
├── 命令监听线程
└── 控件分析线程池
```

## 🔧 停止控制机制详解

### 1. 主管理器 (AutoRecordingManager)

#### 1.1 核心停止方法
```python
def stop_recording(self) -> Optional[str]:
    """停止录制 - 主要停止入口"""
    if not self.is_recording:
        return None
    
    try:
        self.is_recording = False
        
        # 1. 停止菜单监听器
        self.menu_listener.stop_listener()
        
        # 2. 停止事件捕获
        self.event_capture.stop()
        
        # 3. 停止事件处理线程
        self.stop_event.set()
        if self.event_processor_thread:
            self.event_processor_thread.join(timeout=5)
        
        # 4. 关闭控件分析线程池
        if self.widget_analysis_executor:
            self.widget_analysis_executor.shutdown(wait=True)
            self.widget_analysis_executor = None
        
        # 5. 保存会话数据
        # 6. 清理资源
        
    except Exception as e:
        print(f"[ERROR] 停止录制失败: {e}")
```

#### 1.2 暂停录制
```python
def pause_recording(self) -> bool:
    """暂停录制 - 临时停止"""
    try:
        # 1. 暂停事件捕获
        if self.event_capture:
            self.event_capture.stop()
        
        # 2. 暂停悬停检测
        if self.hover_detector:
            self.hover_detector.stop()
        
        # 3. 强制清除所有高亮显示
        self._force_clear_all_highlights()
        
        # 4. 设置暂停状态
        self.is_paused = True
        
    except Exception as e:
        print(f"[ERROR] 暂停录制失败: {e}")
```

#### 1.3 资源清理
```python
def cleanup(self):
    """清理所有资源"""
    if self.is_recording:
        self.stop_recording()
    
    if self.widget_analysis_executor:
        self.widget_analysis_executor.shutdown(wait=True)
    
    # 清理悬停检测器
    if hasattr(self, 'hover_detector') and self.hover_detector:
        self.hover_detector.cleanup()
```

### 2. 事件捕获器 (EventCapture)

#### 2.1 停止机制
```python
def stop(self):
    """停止事件捕获"""
    self.running = False  # 设置运行标志为False
    
    if self.input_listener:
        self.input_listener.stop_listen()  # 停止底层输入监听
        self.input_listener = None
    
    if self.debug:
        print("[DEBUG] 事件捕获器已停止")
```

#### 2.2 运行状态控制
- **启动**: `self.running = True`
- **停止**: `self.running = False`
- **状态检查**: 在事件处理方法中检查`self.running`状态

### 3. 悬停检测器 (HoverDetector)

#### 3.1 停止机制
```python
def stop(self):
    """停止悬停检测"""
    with self.lock:
        self.running = False
        
        # 取消当前的悬停计时器
        if self.hover_timer:
            self.hover_timer.cancel()
            self.hover_timer = None
        
        # 取消录制计时器
        if self.hover_record_timer:
            self.hover_record_timer.cancel()
            self.hover_record_timer = None
        
        # 清除高亮显示
        self._clear_highlight()
```

#### 3.2 资源清理
```python
def cleanup(self):
    """清理资源"""
    with self.lock:
        if self.hover_timer:
            self.hover_timer.cancel()
            self.hover_timer = None
        
        self._clear_highlight()
        
        if self.highlight_renderer:
            try:
                if hasattr(self.highlight_renderer, 'cleanup'):
                    self.highlight_renderer.cleanup()
            except Exception as e:
                if self.debug:
                    print(f"[DEBUG] 清理高亮渲染器时发生错误: {e}")
```

### 4. 菜单监听管理器 (MenuListenerManager)

#### 4.1 停止机制
```python
def stop_listener(self):
    """停止菜单监听进程"""
    if not self.running or not self.listener_process:
        return
    
    process_pid = self.listener_process.pid
    try:
        # 1. 检查进程是否还存在
        if self.listener_process.poll() is not None:
            print(f"[INFO] 菜单监听进程 {process_pid} 已经结束")
            return
        
        # 2. 尝试获取进程组ID并发送SIGTERM
        try:
            pgid = os.getpgid(process_pid)
            os.killpg(pgid, signal.SIGTERM)
            
            # 等待进程结束
            try:
                self.listener_process.wait(timeout=3)
                print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已正常结束")
                return
            except subprocess.TimeoutExpired:
                # 超时则强制终止
                os.killpg(pgid, signal.SIGKILL)
                self.listener_process.wait()
                print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已强制结束")
                
        except OSError:
            # 如果无法获取进程组，直接终止主进程
            self.listener_process.terminate()
            try:
                self.listener_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.listener_process.kill()
                self.listener_process.wait()
                
    except Exception as e:
        print(f"[ERROR] 停止菜单监听进程时发生错误: {e}")
        # 最后的清理操作
        try:
            if self.listener_process:
                self.listener_process.kill()
                self.listener_process.wait()
        except:
            pass
    finally:
        self.listener_process = None
        self.running = False
```

### 5. 线程管理

#### 5.1 事件处理线程
```python
def _process_events(self):
    """事件处理线程主循环"""
    while not self.stop_event.is_set():  # 检查停止事件
        try:
            # 从队列中获取事件，设置超时避免阻塞
            try:
                event_type, event = self.event_queue.get(timeout=0.1)
            except queue.Empty:
                continue
            
            # 处理事件...
            
        except Exception as e:
            print(f"[ERROR] 处理事件时发生错误: {e}")
```

#### 5.2 命令监听线程
```python
def _listen_for_commands(self):
    """命令监听线程"""
    while not self.stop_event.is_set():  # 检查停止事件
        try:
            # 使用select检查stdin是否有数据，超时0.1秒
            ready, _, _ = select.select([sys.stdin], [], [], 0.1)
            if ready:
                command = sys.stdin.readline().strip()
                if command:
                    self._handle_command(command)
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 命令监听异常: {e}")
```

#### 5.3 控件分析线程池
```python
# 创建线程池
self.widget_analysis_executor = concurrent.futures.ThreadPoolExecutor(
    max_workers=2, 
    thread_name_prefix="WidgetAnalysis"
)

# 停止线程池
if self.widget_analysis_executor:
    self.widget_analysis_executor.shutdown(wait=True)
    self.widget_analysis_executor = None
```

## 🎛️ 停止控制流程

### 1. 正常停止流程
```
用户调用stop_recording()
    ↓
设置is_recording = False
    ↓
停止菜单监听器 (MenuListenerManager.stop_listener())
    ↓
停止事件捕获 (EventCapture.stop())
    ↓
设置停止事件 (stop_event.set())
    ↓
等待事件处理线程结束 (join(timeout=5))
    ↓
关闭控件分析线程池 (shutdown(wait=True))
    ↓
保存会话数据
    ↓
清理资源
```

### 2. 暂停流程
```
用户调用pause_recording()
    ↓
停止事件捕获 (EventCapture.stop())
    ↓
停止悬停检测 (HoverDetector.stop())
    ↓
强制清除所有高亮显示
    ↓
设置is_paused = True
```

### 3. 异常停止流程
```
捕获异常
    ↓
记录错误日志
    ↓
尝试正常停止流程
    ↓
如果失败，强制清理资源
    ↓
重置所有状态标志
```

## 🔒 线程安全机制

### 1. 锁机制
- **HoverDetector**: 使用`threading.Lock()`保护共享状态
- **事件队列**: 使用`queue.Queue()`线程安全队列

### 2. 状态标志
- **running**: 各组件的运行状态标志
- **is_recording**: 主录制状态
- **is_paused**: 暂停状态
- **stop_event**: 线程停止事件

### 3. 超时机制
- **线程join**: `join(timeout=5)`避免无限等待
- **进程等待**: `wait(timeout=3)`避免进程僵死
- **队列获取**: `get(timeout=0.1)`避免阻塞

## ⚠️ 注意事项

### 1. 停止顺序很重要
1. 先停止数据生产者（事件捕获、菜单监听）
2. 再停止数据消费者（事件处理线程）
3. 最后清理资源和保存数据

### 2. 异常处理
- 每个停止操作都有try-catch保护
- 即使某个组件停止失败，也要继续停止其他组件
- 使用finally确保状态重置

### 3. 资源清理
- 计时器需要显式取消
- 线程池需要shutdown
- 进程需要正确终止
- 文件句柄需要关闭

### 4. 状态一致性
- 停止操作要原子性地更新状态
- 避免竞态条件
- 使用锁保护关键状态

## 🎯 总结

auto_recording_manager_v11.py的停止监听控制处理采用了分层、协调的方式：

1. **主管理器**负责整体协调和顺序控制
2. **各组件**负责自己的资源清理
3. **线程管理**使用事件和超时机制
4. **异常处理**确保即使部分失败也能正常停止
5. **状态管理**保证系统状态的一致性

这种设计确保了系统能够优雅地停止，避免资源泄漏和僵死进程。
