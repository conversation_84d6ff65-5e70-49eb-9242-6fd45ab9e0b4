#!/usr/bin/env python3
from libinput import LibInput, Event
from libinput.constant import Event as EventType
from libinput.constant import ButtonState,PointerAxis
from libinput.evcodes import Button
import time,os
import socket
import json



#write log to file
def write_log(data):
    if not debug:
        return
    import datetime
    with open('input_log_server.txt', 'a') as f:
        # add time
        f.write(f"[{str(datetime.datetime.now())}]:{data}\n")

def print_event(event:Event):
    """打印输入事件的详细信息"""
    data = None

    try:
        event_type = event.type
        # print(f"监听到事件类型: {event_type}")

        if event_type == EventType.KEYBOARD_KEY:
            key_event = event.get_keyboard_event()
            print(f"键盘事件: 按键={key_event.get_key()} 状态={key_event.get_key_state()}")
            # print(type(key_event.get_key()), type(key_event.get_key_state()))
            #将监听到的事件，打包并格式化为一个json，然后发送到上层应用
            data = {
                "event_type": "KEYBOARD_KEY",
                "time": int(time.time()*1000),
                "key": key_event.get_key().value,
                "key_state": key_event.get_key_state().value
            }
        elif event_type == EventType.POINTER_MOTION:
            pointer_event = event.get_pointer_event()
            print(f"鼠标移动: dx={pointer_event.get_dx():.2f} dy={pointer_event.get_dy():.2f}")
            #将监听到的事件，打包并格式化为一个json，然后发送到上层应用
            data = {
                "event_type": "POINTER_MOTION",
                "time": int(time.time()*1000),
                "x":pointer_event.get_dx(),
                "y":pointer_event.get_dy(),
            }
        elif event_type == EventType.POINTER_BUTTON:
            pointer_event = event.get_pointer_event()
            print(f"鼠标点击: 按钮={pointer_event.get_button()} 状态={pointer_event.get_button_state()}")
            # print(type(pointer_event.get_button()), type(pointer_event.get_button_state()))
            #将监听到的事件，打包并格式化为一个json，然后发送到上层应用
            data = {
                "event_type": "POINTER_BUTTON",
                "time": int(time.time()*1000),
                "button": pointer_event.get_button().value,
                "button_state": pointer_event.get_button_state().value,
            }
        elif event_type == EventType.POINTER_AXIS:
            pointer_event = event.get_pointer_event()
            print(f"鼠标滚轮: 垂直滚动={pointer_event.get_axis_value(PointerAxis.SCROLL_VERTICAL)}")
            # print(type(pointer_event.get_axis_value(PointerAxis.SCROLL_VERTICAL)), \
            #     type(pointer_event.get_axis_source()), \
            #     type(pointer_event.get_axis_value_discrete(PointerAxis.SCROLL_VERTICAL)))
            #将监听到的事件，打包并格式化为一个json，然后发送到上层应用
            data = {
                "event_type": "POINTER_AXIS",
                "time": int(time.time()*1000),
                "axis_value": pointer_event.get_axis_value(PointerAxis.SCROLL_VERTICAL),
                "axis_source": pointer_event.get_axis_source().value,
                "axis_value_discrete": pointer_event.get_axis_value_discrete(PointerAxis.SCROLL_VERTICAL)
            }
        else:
            # print(f"未处理事件类型: {event_type}")
            pass

    except StopIteration:
        print("事件队列已空")
        pass
    except Exception as e:
        if str(e) == "generator raised StopIteration":
            print("事件队列已空")
        else:
            print(f"处理事件时出错: {str(e)}")

    if data:
        print(data)
        write_log(f"捕获到事件：{data}")
    return data

if __name__ == "__main__":
    #获取命令行参数
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--debug":
        debug = True
    else:
        debug = False
    if not debug:
        socket_path = "/tmp/input-listener.sock"
        if os.path.exists(socket_path):
            os.remove(socket_path)
        sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        sock.bind(socket_path)
        os.chmod(socket_path, 0o777)
        sock.listen(1)
        #set the timeout to 3 second
        sock.settimeout(3)

        try:
            conn, addr = sock.accept()
            conn.setblocking(False)
        except Exception as e:
            print("没有客户端连接")
            os.unlink(socket_path)
            sock.close()
            exit()

    # 初始化libinput上下文(使用udev)
    li = LibInput(udev=True)
    li.udev_assign_seat("seat0")  # 分配默认seat
    print("开始监听输入事件(按Ctrl+C退出)...")
    write_log("开始监听输入事件")

    try:
        while True:
            if not debug:
                try:
                    #receive data from client non-blocking
                    data = conn.recv(1024)
                    if not data:
                        pass
                    elif data.decode() == "exit":
                        #exit the program
                        os.unlink(socket_path)
                        sock.close()
                        exit()
                except BlockingIOError:
                    pass
                except Exception as e:
                    print(f"处理客户端请求时出错: {str(e)}")
                    write_log(f"处理客户端请求时出错: {str(e)}")
                    os.unlink(socket_path)
                    sock.close()
                    exit()


            try:
                # 获取事件(非阻塞)
                events = li.get_event(timeout=0.1)
                for event in events:
                    data=print_event(event)
                    #if client connected, send data to client
                    if not debug and data:
                        conn.send(json.dumps(data).encode()+b'\n')
                        write_log(f"send data: {data}")
            except StopIteration:
                time.sleep(0.01)  # 无事件时短暂休眠
                pass
            except Exception as e:
                if str(e) == "generator raised StopIteration":
                    # print("事件队列已空")
                    pass
                else:
                    print(f"处理事件时出错: {str(e)}")
                    # time.sleep(1)


    except KeyboardInterrupt:
        write_log("退出程序")
        print("退出程序")
    except Exception as e:
        write_log(f"异常退出: {str(e)}")
        print(f"异常退出: {str(e)}")
