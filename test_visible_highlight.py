#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可见高亮测试脚本
增强高亮效果，确保用户能看到高亮显示
"""

import sys
import time
import os

def test_enhanced_highlight(x: int, y: int):
    """测试增强的高亮效果"""
    print("=" * 80)
    print(f"🎯 增强高亮效果测试")
    print(f"📍 测试坐标: ({x}, {y})")
    print("=" * 80)
    
    # 1. 导入必要模块
    try:
        from UNI import UNI
        from wayland_highlight_renderer import UnifiedHighlightRenderer
        print("✅ 模块导入成功")
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    
    # 2. 识别控件
    try:
        uni = UNI()
        widget_info, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        
        if not widget_info:
            print(f"❌ 未找到控件: {info_text}")
            return False
            
        coords = widget_info.get('Coords')
        if not coords:
            print("❌ 控件坐标信息缺失")
            return False
            
        print(f"✅ 找到控件: {widget_info.get('Name', 'Unknown')}")
        print(f"📍 位置: ({coords['x']}, {coords['y']})")
        print(f"📏 大小: {coords['width']} x {coords['height']}")
        
    except Exception as e:
        print(f"❌ 控件识别失败: {e}")
        return False
    
    # 3. 多种高亮方法测试
    print("\n" + "=" * 80)
    print("🎨 测试多种高亮方法")
    print("=" * 80)
    
    # 方法1: 统一高亮渲染器 - 长时间高亮
    try:
        print("📍 方法1: 统一高亮渲染器 (10秒)")
        renderer = UnifiedHighlightRenderer(debug=True)
        
        result = renderer.highlight_widget(
            coords['x'], coords['y'], 
            coords['width'], coords['height'], 
            widget_info
        )
        
        if result:
            print("✅ 统一高亮渲染器启动成功")
            print("⏰ 高亮将持续10秒，请观察屏幕...")
            time.sleep(10)
            renderer.clear_highlight()
            print("✅ 高亮已清除")
        else:
            print("❌ 统一高亮渲染器失败")
            
        renderer.cleanup()
        
    except Exception as e:
        print(f"❌ 统一高亮渲染器测试失败: {e}")
    
    # 方法2: KylinAutoSDK_UNI的ultimate_highlight
    try:
        print("\n📍 方法2: KylinAutoSDK_UNI ultimate_highlight (5秒)")
        sys.path.insert(0, 'KylinAutoSDK_UNI')
        from ultimate_highlight import ultimate_highlight
        
        result = ultimate_highlight(
            coords['x'], coords['y'], 
            coords['width'], coords['height'],
            duration=5, color='red', border_width=4
        )
        
        if result:
            print("✅ KylinAutoSDK_UNI高亮成功")
        else:
            print("❌ KylinAutoSDK_UNI高亮失败")
            
    except Exception as e:
        print(f"❌ KylinAutoSDK_UNI高亮测试失败: {e}")
    
    # 方法3: 直接X11脚本 - 粗边框
    try:
        print("\n📍 方法3: 直接X11脚本 - 粗边框 (5秒)")
        
        import subprocess
        
        x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    colormap = screen.default_colormap
    red_pixel = colormap.alloc_named_color('red').pixel
    
    # 创建粗边框高亮窗口
    win = root.create_window(
        {coords['x']}, {coords['y']}, {coords['width']}, {coords['height']}, 
        border_width=5,  # 粗边框
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,
        border_pixel=red_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    
    # 设置窗口属性
    win.set_wm_name("Thick Border Highlight")
    
    # 显示窗口
    win.map()
    disp.sync()
    
    print("✅ 粗边框高亮窗口已显示")
    
    # 保持5秒
    time.sleep(5)
    
    # 清理
    win.unmap()
    win.destroy()
    disp.sync()
    disp.close()
    
    print("✅ 粗边框高亮窗口已清除")
    
except Exception as e:
    print(f"❌ 粗边框高亮失败: {{e}}")
    exit(1)
'''
        
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ 直接X11脚本高亮成功")
            if result.stdout:
                print(result.stdout)
        else:
            print("❌ 直接X11脚本高亮失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
                
    except Exception as e:
        print(f"❌ 直接X11脚本测试失败: {e}")
    
    # 方法4: 闪烁高亮
    try:
        print("\n📍 方法4: 闪烁高亮效果 (10秒)")
        
        import subprocess
        
        flash_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    colormap = screen.default_colormap
    red_pixel = colormap.alloc_named_color('red').pixel
    blue_pixel = colormap.alloc_named_color('blue').pixel
    
    # 创建闪烁高亮窗口
    win = root.create_window(
        {coords['x']}, {coords['y']}, {coords['width']}, {coords['height']}, 
        border_width=6,  # 更粗边框
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,
        border_pixel=red_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    
    win.map()
    disp.sync()
    
    print("✅ 开始闪烁高亮效果")
    
    # 闪烁10次
    for i in range(10):
        # 红色
        win.change_attributes(border_pixel=red_pixel)
        disp.sync()
        time.sleep(0.5)
        
        # 蓝色
        win.change_attributes(border_pixel=blue_pixel)
        disp.sync()
        time.sleep(0.5)
    
    # 清理
    win.unmap()
    win.destroy()
    disp.sync()
    disp.close()
    
    print("✅ 闪烁高亮效果完成")
    
except Exception as e:
    print(f"❌ 闪烁高亮失败: {{e}}")
    exit(1)
'''
        
        result = subprocess.run(['python3', '-c', flash_script], 
                              capture_output=True, text=True, timeout=25)
        
        if result.returncode == 0:
            print("✅ 闪烁高亮效果成功")
            if result.stdout:
                print(result.stdout)
        else:
            print("❌ 闪烁高亮效果失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
                
    except Exception as e:
        print(f"❌ 闪烁高亮测试失败: {e}")
    
    return True

def main():
    """主函数"""
    print("🎯 可见高亮效果测试")
    print("=" * 80)
    print("💡 本测试将使用多种方法进行高亮显示")
    print("📺 请注意观察屏幕上的红色边框高亮效果")
    print("⏰ 每种方法都会持续较长时间以便观察")
    print("=" * 80)
    
    # 检查环境
    print(f"DISPLAY: {os.environ.get('DISPLAY', 'None')}")
    print(f"WAYLAND_DISPLAY: {os.environ.get('WAYLAND_DISPLAY', 'None')}")
    print(f"XDG_SESSION_TYPE: {os.environ.get('XDG_SESSION_TYPE', 'None')}")
    
    # 测试坐标
    test_x, test_y = 1739, 320
    
    print(f"\n🎯 开始测试坐标 ({test_x}, {test_y}) 的高亮效果...")
    print("=" * 80)
    
    success = test_enhanced_highlight(test_x, test_y)
    
    print("\n" + "=" * 80)
    print("📋 测试完成")
    print("=" * 80)
    
    if success:
        print("🎉 增强高亮测试完成!")
        print("💡 如果您仍然看不到高亮效果，可能的原因:")
        print("   1. 当前不在图形界面环境中")
        print("   2. X11显示服务器不可用")
        print("   3. 控件可能被其他窗口遮挡")
        print("   4. 需要在桌面环境中运行此脚本")
    else:
        print("⚠️ 测试过程中遇到问题")
        print("💡 建议在图形桌面环境中重新运行此脚本")

if __name__ == "__main__":
    main()
