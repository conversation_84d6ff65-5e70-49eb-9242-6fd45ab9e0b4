#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wayland高亮功能演示脚本
演示新的统一高亮渲染器在不同环境下的工作情况
"""

import sys
import time
import os
import threading
from typing import Dict, Any

def detect_display_server():
    """检测当前显示服务器类型"""
    if os.environ.get('WAYLAND_DISPLAY'):
        return 'wayland'
    elif os.environ.get('DISPLAY'):
        return 'x11'
    
    session_type = os.environ.get('XDG_SESSION_TYPE', '').lower()
    if session_type == 'wayland':
        return 'wayland'
    elif session_type == 'x11':
        return 'x11'
    
    return 'unknown'

def demo_highlight_renderer():
    """演示高亮渲染器功能"""
    print("=" * 60)
    print("Wayland兼容高亮渲染器演示")
    print("=" * 60)
    
    display_server = detect_display_server()
    print(f"当前显示服务器: {display_server}")
    print(f"DISPLAY: {os.environ.get('DISPLAY', 'None')}")
    print(f"WAYLAND_DISPLAY: {os.environ.get('WAYLAND_DISPLAY', 'None')}")
    print(f"XDG_SESSION_TYPE: {os.environ.get('XDG_SESSION_TYPE', 'None')}")
    
    try:
        from wayland_highlight_renderer import UnifiedHighlightRenderer
        print("✅ 统一高亮渲染器导入成功")
    except ImportError as e:
        print(f"❌ 统一高亮渲染器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 统一高亮渲染器导入异常: {e}")
        return False
    
    # 创建渲染器实例
    try:
        renderer = UnifiedHighlightRenderer(debug=True)
        print(f"✅ 统一高亮渲染器创建成功")
        print(f"   渲染器类型: {renderer.renderer_type}")
        print(f"   支持的环境: {'Wayland + X11' if renderer.renderer_type == 'GTK' else 'X11 only'}")
    except Exception as e:
        print(f"❌ 统一高亮渲染器创建失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("开始高亮演示")
    print("=" * 60)
    
    # 演示不同位置和大小的高亮
    demo_widgets = [
        {
            'name': '按钮控件',
            'x': 100, 'y': 100, 'width': 120, 'height': 30,
            'info': {'Name': 'Demo Button', 'Role': 'push button', 'ProcessName': 'demo_app'}
        },
        {
            'name': '文本输入框',
            'x': 300, 'y': 150, 'width': 200, 'height': 25,
            'info': {'Name': 'Text Input', 'Role': 'entry', 'ProcessName': 'demo_app'}
        },
        {
            'name': '标签控件',
            'x': 150, 'y': 250, 'width': 100, 'height': 20,
            'info': {'Name': 'Label Text', 'Role': 'label', 'ProcessName': 'demo_app'}
        },
        {
            'name': '菜单项',
            'x': 400, 'y': 300, 'width': 80, 'height': 22,
            'info': {'Name': 'Menu Item', 'Role': 'menu item', 'ProcessName': 'demo_app'}
        },
        {
            'name': '大窗口',
            'x': 200, 'y': 400, 'width': 400, 'height': 200,
            'info': {'Name': 'Main Window', 'Role': 'window', 'ProcessName': 'demo_app'}
        }
    ]
    
    try:
        for i, widget in enumerate(demo_widgets):
            print(f"\n{i+1}. 高亮显示: {widget['name']}")
            print(f"   位置: ({widget['x']}, {widget['y']})")
            print(f"   大小: {widget['width']} x {widget['height']}")
            
            # 显示高亮
            result = renderer.highlight_widget(
                widget['x'], widget['y'], 
                widget['width'], widget['height'], 
                widget['info']
            )
            
            if result:
                print(f"   ✅ 高亮显示成功")
                print(f"   等待2秒...")
                time.sleep(2)
            else:
                print(f"   ❌ 高亮显示失败")
            
            # 清除高亮
            renderer.clear_highlight()
            print(f"   ✅ 高亮已清除")
            
            if i < len(demo_widgets) - 1:
                time.sleep(0.5)  # 短暂间隔
        
        print("\n" + "=" * 60)
        print("演示完成")
        print("=" * 60)
        
        # 清理资源
        renderer.cleanup()
        print("✅ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_widget_capture():
    """演示控件捕获功能"""
    print("\n" + "=" * 60)
    print("控件捕获功能演示")
    print("=" * 60)
    print("注意：此演示需要图形界面环境")
    
    try:
        from widget_capture_module import WidgetCaptureManager
        print("✅ 控件捕获模块导入成功")
    except ImportError as e:
        print(f"❌ 控件捕获模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 控件捕获模块导入异常: {e}")
        return False
    
    try:
        # 创建捕获管理器
        manager = WidgetCaptureManager(debug=True)
        print("✅ 控件捕获管理器创建成功")
        
        # 检查高亮渲染器类型
        if hasattr(manager.highlight_renderer, 'renderer_type'):
            print(f"✅ 高亮渲染器类型: {manager.highlight_renderer.renderer_type}")
        else:
            print("✅ 使用传统Xlib高亮渲染器")
        
        print("\n说明：")
        print("- 在图形界面中，按住Ctrl键并移动鼠标可以高亮显示控件")
        print("- 点击控件可以捕获控件信息")
        print("- 按Ctrl+C退出捕获模式")
        print("\n由于这是演示脚本，不会实际启动捕获功能")
        
        # 清理资源
        manager.cleanup()
        print("✅ 控件捕获管理器清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 控件捕获演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主演示函数"""
    print("🎯 Wayland兼容高亮功能演示")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {os.name}")
    
    # 检查环境
    display_server = detect_display_server()
    if display_server == 'unknown':
        print("⚠️ 警告：未检测到图形环境，高亮功能可能无法正常工作")
    
    # 演示高亮渲染器
    highlight_demo_success = demo_highlight_renderer()
    
    # 演示控件捕获
    capture_demo_success = demo_widget_capture()
    
    # 总结
    print("\n" + "=" * 60)
    print("演示总结")
    print("=" * 60)
    print(f"高亮渲染器演示: {'✅ 成功' if highlight_demo_success else '❌ 失败'}")
    print(f"控件捕获演示: {'✅ 成功' if capture_demo_success else '❌ 失败'}")
    
    if highlight_demo_success and capture_demo_success:
        print("\n🎉 所有演示成功！")
        print("\n✨ 新功能特点：")
        print("   • 支持Wayland和X11环境")
        print("   • 使用GTK实现跨平台兼容")
        print("   • 自动回退到Xlib（X11环境）")
        print("   • 保持向后兼容性")
        print("   • 优化的性能和稳定性")
        
        print("\n📖 使用方法：")
        print("   1. 直接使用现有的控件捕获功能")
        print("   2. 在Wayland环境中会自动使用GTK高亮")
        print("   3. 在X11环境中优先使用GTK，备用Xlib")
        print("   4. 无需修改现有代码")
        
        return True
    else:
        print("\n⚠️ 部分演示失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
