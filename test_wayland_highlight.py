#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wayland高亮功能测试脚本
测试新的统一高亮渲染器在不同环境下的工作情况
"""

import sys
import time
import os
from typing import Dict, Any

def detect_display_server():
    """检测当前显示服务器类型"""
    if os.environ.get('WAYLAND_DISPLAY'):
        return 'wayland'
    elif os.environ.get('DISPLAY'):
        return 'x11'
    
    session_type = os.environ.get('XDG_SESSION_TYPE', '').lower()
    if session_type == 'wayland':
        return 'wayland'
    elif session_type == 'x11':
        return 'x11'
    
    return 'unknown'

def test_unified_highlight_renderer():
    """测试统一高亮渲染器"""
    print("=" * 60)
    print("测试统一高亮渲染器")
    print("=" * 60)
    
    display_server = detect_display_server()
    print(f"检测到的显示服务器: {display_server}")
    
    try:
        from wayland_highlight_renderer import UnifiedHighlightRenderer
        print("✅ 统一高亮渲染器导入成功")
    except ImportError as e:
        print(f"❌ 统一高亮渲染器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 统一高亮渲染器导入异常: {e}")
        return False
    
    # 创建渲染器实例
    try:
        renderer = UnifiedHighlightRenderer(debug=True)
        print(f"✅ 统一高亮渲染器创建成功，类型: {renderer.renderer_type}")
    except Exception as e:
        print(f"❌ 统一高亮渲染器创建失败: {e}")
        return False
    
    # 测试高亮功能
    try:
        print("\n测试高亮显示...")
        
        # 模拟控件信息
        widget_info = {
            'Name': 'Test Button',
            'Role': 'push button',
            'ProcessName': 'test_app',
            'Coords': {
                'x': 100,
                'y': 100,
                'width': 200,
                'height': 50
            }
        }
        
        # 显示高亮
        result = renderer.highlight_widget(100, 100, 200, 50, widget_info)
        if result:
            print("✅ 高亮显示成功")
            print("高亮将显示3秒...")
            time.sleep(3)
        else:
            print("❌ 高亮显示失败")
        
        # 清除高亮
        renderer.clear_highlight()
        print("✅ 高亮清除成功")
        
        # 清理资源
        renderer.cleanup()
        print("✅ 资源清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 高亮功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_widget_capture_integration():
    """测试控件捕获模块集成"""
    print("\n" + "=" * 60)
    print("测试控件捕获模块集成")
    print("=" * 60)
    
    try:
        from widget_capture_module import WidgetCaptureManager
        print("✅ 控件捕获模块导入成功")
    except ImportError as e:
        print(f"❌ 控件捕获模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 控件捕获模块导入异常: {e}")
        return False
    
    # 创建捕获管理器
    try:
        manager = WidgetCaptureManager(debug=True)
        print("✅ 控件捕获管理器创建成功")
        
        # 检查高亮渲染器类型
        if hasattr(manager.highlight_renderer, 'renderer_type'):
            print(f"✅ 使用的高亮渲染器类型: {manager.highlight_renderer.renderer_type}")
        else:
            print("✅ 使用传统Xlib高亮渲染器")
        
        # 清理资源
        manager.cleanup()
        print("✅ 控件捕获管理器清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 控件捕获模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_recording_integration():
    """测试自动录制管理器集成"""
    print("\n" + "=" * 60)
    print("测试自动录制管理器集成")
    print("=" * 60)
    
    try:
        from auto_recording_manager_v11 import AutoRecordingManager
        print("✅ 自动录制管理器导入成功")
    except ImportError as e:
        print(f"❌ 自动录制管理器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 自动录制管理器导入异常: {e}")
        return False
    
    # 创建录制管理器
    try:
        manager = AutoRecordingManager(debug=True)
        print("✅ 自动录制管理器创建成功")
        
        # 检查高亮渲染器（在widget_analyzer中）
        if hasattr(manager, 'widget_analyzer') and manager.widget_analyzer:
            if hasattr(manager.widget_analyzer, 'highlight_renderer') and manager.widget_analyzer.highlight_renderer:
                if hasattr(manager.widget_analyzer.highlight_renderer, 'renderer_type'):
                    print(f"✅ 使用的高亮渲染器类型: {manager.widget_analyzer.highlight_renderer.renderer_type}")
                else:
                    print("✅ 使用传统高亮渲染器")
            else:
                print("⚠️ 高亮渲染器未初始化")
        else:
            print("⚠️ widget_analyzer未初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ 自动录制管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dependencies():
    """测试依赖库"""
    print("\n" + "=" * 60)
    print("测试依赖库")
    print("=" * 60)
    
    # 测试GTK
    try:
        import gi
        gi.require_version('Gtk', '3.0')
        gi.require_version('Gdk', '3.0')
        from gi.repository import Gtk, Gdk, GLib, GObject
        print("✅ GTK库可用")
        gtk_available = True
    except ImportError as e:
        print(f"❌ GTK库不可用: {e}")
        gtk_available = False
    except Exception as e:
        print(f"❌ GTK库异常: {e}")
        gtk_available = False
    
    # 测试Xlib
    try:
        from Xlib import X, display, Xutil, Xatom
        print("✅ Xlib库可用")
        xlib_available = True
    except ImportError as e:
        print(f"❌ Xlib库不可用: {e}")
        xlib_available = False
    except Exception as e:
        print(f"❌ Xlib库异常: {e}")
        xlib_available = False
    
    # 测试UNI模块
    try:
        from UNI import UNI
        print("✅ UNI模块可用")
        uni_available = True
    except ImportError as e:
        print(f"❌ UNI模块不可用: {e}")
        uni_available = False
    except Exception as e:
        print(f"❌ UNI模块异常: {e}")
        uni_available = False
    
    return gtk_available, xlib_available, uni_available

def main():
    """主测试函数"""
    print("Wayland高亮功能测试")
    print(f"Python版本: {sys.version}")
    print(f"显示服务器: {detect_display_server()}")
    print(f"DISPLAY: {os.environ.get('DISPLAY', 'None')}")
    print(f"WAYLAND_DISPLAY: {os.environ.get('WAYLAND_DISPLAY', 'None')}")
    print(f"XDG_SESSION_TYPE: {os.environ.get('XDG_SESSION_TYPE', 'None')}")
    
    # 测试依赖库
    gtk_available, xlib_available, uni_available = test_dependencies()
    
    # 测试统一高亮渲染器
    unified_test_passed = test_unified_highlight_renderer()
    
    # 测试控件捕获模块集成
    widget_capture_test_passed = test_widget_capture_integration()
    
    # 测试自动录制管理器集成
    auto_recording_test_passed = test_auto_recording_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"GTK库: {'✅' if gtk_available else '❌'}")
    print(f"Xlib库: {'✅' if xlib_available else '❌'}")
    print(f"UNI模块: {'✅' if uni_available else '❌'}")
    print(f"统一高亮渲染器: {'✅' if unified_test_passed else '❌'}")
    print(f"控件捕获模块集成: {'✅' if widget_capture_test_passed else '❌'}")
    print(f"自动录制管理器集成: {'✅' if auto_recording_test_passed else '❌'}")
    
    if unified_test_passed and widget_capture_test_passed:
        print("\n🎉 所有核心测试通过！Wayland高亮功能已成功实现。")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
