#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际控件识别和高亮测试脚本
使用UNI.py识别指定坐标的控件，并进行高亮显示
"""

import sys
import time
import os
from typing import Dict, Any, Optional

def test_widget_detection_and_highlight(x: int, y: int):
    """测试控件识别和高亮"""
    print("=" * 80)
    print(f"🎯 实际控件识别和高亮测试")
    print(f"📍 测试坐标: ({x}, {y})")
    print("=" * 80)
    
    # 1. 导入UNI模块
    try:
        from UNI import UNI
        print("✅ UNI模块导入成功")
    except ImportError as e:
        print(f"❌ UNI模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ UNI模块导入异常: {e}")
        return False
    
    # 2. 导入高亮渲染器
    try:
        from wayland_highlight_renderer import UnifiedHighlightRenderer
        print("✅ 统一高亮渲染器导入成功")
    except ImportError as e:
        print(f"❌ 统一高亮渲染器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 统一高亮渲染器导入异常: {e}")
        return False
    
    # 3. 创建UNI实例
    try:
        uni = UNI()
        print("✅ UNI实例创建成功")
        print(f"   显示服务器: {uni.display_server}")
        print(f"   X11可用: {uni.x11_available}")
        print(f"   Wnck可用: {uni.wnck_available}")
    except Exception as e:
        print(f"❌ UNI实例创建失败: {e}")
        return False
    
    # 4. 创建高亮渲染器
    try:
        renderer = UnifiedHighlightRenderer(debug=True)
        print("✅ 高亮渲染器创建成功")
        print(f"   渲染器类型: {renderer.renderer_type}")
    except Exception as e:
        print(f"❌ 高亮渲染器创建失败: {e}")
        return False
    
    print("\n" + "=" * 80)
    print("🔍 开始控件识别")
    print("=" * 80)
    
    # 5. 使用UNI识别控件
    try:
        print(f"📍 正在识别坐标 ({x}, {y}) 处的控件...")
        
        # 调用UNI的控件识别方法
        widget_info, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        
        if widget_info:
            print("✅ 控件识别成功!")
            print(f"📋 控件名称: {widget_info.get('Name', 'Unknown')}")
            print(f"🏷️  控件角色: {widget_info.get('Role', 'Unknown')}")
            print(f"📱 进程名称: {widget_info.get('ProcessName', 'Unknown')}")
            
            # 获取坐标信息
            coords = widget_info.get('Coords')
            if coords:
                print(f"📍 控件坐标: x={coords['x']}, y={coords['y']}")
                print(f"📏 控件大小: width={coords['width']}, height={coords['height']}")
                
                print("\n" + "=" * 80)
                print("✨ 开始高亮显示")
                print("=" * 80)
                
                # 6. 使用高亮渲染器进行高亮
                print(f"🎨 正在高亮控件...")
                print(f"   位置: ({coords['x']}, {coords['y']})")
                print(f"   大小: {coords['width']} x {coords['height']}")
                
                # 执行高亮
                highlight_result = renderer.highlight_widget(
                    coords['x'], 
                    coords['y'], 
                    coords['width'], 
                    coords['height'], 
                    widget_info
                )
                
                if highlight_result:
                    print("✅ 高亮显示成功!")
                    print("⏰ 高亮将持续5秒...")
                    
                    # 保持高亮5秒
                    time.sleep(5)
                    
                    # 清除高亮
                    renderer.clear_highlight()
                    print("✅ 高亮已清除")
                else:
                    print("❌ 高亮显示失败")
                    return False
            else:
                print("❌ 控件坐标信息缺失")
                return False
        else:
            print(f"❌ 控件识别失败: {info_text}")
            return False
            
    except Exception as e:
        print(f"❌ 控件识别过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 7. 清理资源
        try:
            renderer.cleanup()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理时发生错误: {e}")
    
    return True

def test_alternative_highlight_methods(x: int, y: int, width: int, height: int):
    """测试备用高亮方法"""
    print("\n" + "=" * 80)
    print("🔧 测试备用高亮方法")
    print("=" * 80)
    
    # 1. 测试KylinAutoSDK_UNI的ultimate_highlight
    try:
        print("📍 尝试使用KylinAutoSDK_UNI的ultimate_highlight...")
        
        # 检查是否可以导入
        sys.path.insert(0, 'KylinAutoSDK_UNI')
        from ultimate_highlight import ultimate_highlight
        
        print("✅ ultimate_highlight导入成功")
        print(f"🎨 正在高亮控件: ({x}, {y}) {width}x{height}")
        
        result = ultimate_highlight(x, y, width, height, duration=3, color='red', border_width=3)
        
        if result:
            print("✅ KylinAutoSDK_UNI高亮成功!")
        else:
            print("❌ KylinAutoSDK_UNI高亮失败")
            
    except ImportError as e:
        print(f"⚠️ 无法导入ultimate_highlight: {e}")
    except Exception as e:
        print(f"❌ KylinAutoSDK_UNI高亮测试失败: {e}")
    
    # 2. 测试直接X11脚本
    try:
        print("\n📍 尝试直接执行X11高亮脚本...")
        
        import subprocess
        
        x11_script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time
    
    # 连接到X11
    disp = display.Display()
    screen = disp.screen()
    root = screen.root
    
    # 获取颜色
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('red').pixel
    
    # 创建高亮窗口
    win = root.create_window(
        {x}, {y}, {width}, {height}, 
        border_width=3,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=0,
        border_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    
    # 显示窗口
    win.map()
    disp.sync()
    
    print("✅ X11高亮窗口已显示")
    
    # 保持3秒
    time.sleep(3)
    
    # 清理
    win.unmap()
    win.destroy()
    disp.sync()
    disp.close()
    
    print("✅ X11高亮窗口已清除")
    
except Exception as e:
    print(f"❌ X11高亮失败: {{e}}")
    exit(1)
'''
        
        result = subprocess.run(['python3', '-c', x11_script], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 直接X11脚本高亮成功!")
            print("📄 脚本输出:")
            if result.stdout:
                print(result.stdout)
        else:
            print("❌ 直接X11脚本高亮失败")
            print("📄 错误输出:")
            if result.stderr:
                print(result.stderr)
                
    except subprocess.TimeoutExpired:
        print("⚠️ X11脚本执行超时")
    except Exception as e:
        print(f"❌ 直接X11脚本测试失败: {e}")

def check_display_environment():
    """检查显示环境"""
    print("=" * 80)
    print("🖥️ 显示环境检查")
    print("=" * 80)
    
    print(f"DISPLAY: {os.environ.get('DISPLAY', 'None')}")
    print(f"WAYLAND_DISPLAY: {os.environ.get('WAYLAND_DISPLAY', 'None')}")
    print(f"XDG_SESSION_TYPE: {os.environ.get('XDG_SESSION_TYPE', 'None')}")
    print(f"DESKTOP_SESSION: {os.environ.get('DESKTOP_SESSION', 'None')}")
    
    # 检查X11连接
    try:
        import subprocess
        result = subprocess.run(['xdpyinfo'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ X11显示服务器可访问")
        else:
            print("❌ X11显示服务器不可访问")
    except:
        print("⚠️ 无法检查X11显示服务器")

def main():
    """主函数"""
    print("🎯 实际控件识别和高亮测试")
    print(f"Python版本: {sys.version}")
    
    # 检查显示环境
    check_display_environment()
    
    # 测试指定坐标
    test_x, test_y = 1739, 320
    
    # 执行控件识别和高亮测试
    success = test_widget_detection_and_highlight(test_x, test_y)
    
    if not success:
        print("\n⚠️ 主要测试失败，尝试备用方法...")
        # 如果主要测试失败，使用默认尺寸测试备用方法
        test_alternative_highlight_methods(test_x, test_y, 100, 30)
    
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    if success:
        print("🎉 控件识别和高亮测试成功!")
        print("✅ 您的高亮模块在当前环境中工作正常")
    else:
        print("⚠️ 主要测试未完全成功")
        print("💡 建议检查:")
        print("   1. 确保在图形环境中运行")
        print("   2. 检查指定坐标处是否有控件")
        print("   3. 确认X11环境可用")

if __name__ == "__main__":
    main()
