#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试auto_recording_manager_v11.py中集成的高亮功能
"""

import sys
import time

def test_widget_analyzer_highlight():
    """测试WidgetAnalyzer的高亮功能"""
    print("=" * 80)
    print("🎯 测试auto_recording_manager_v11.py中的高亮功能")
    print("=" * 80)
    
    try:
        # 导入WidgetAnalyzer
        from auto_recording_manager_v11 import WidgetAnalyzer
        print("✅ WidgetAnalyzer导入成功")
    except ImportError as e:
        print(f"❌ WidgetAnalyzer导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ WidgetAnalyzer导入异常: {e}")
        return False
    
    try:
        # 创建WidgetAnalyzer实例
        analyzer = WidgetAnalyzer(debug=True)
        print("✅ WidgetAnalyzer实例创建成功")
        print(f"   高亮功能启用: {analyzer.highlight_enabled}")
    except Exception as e:
        print(f"❌ WidgetAnalyzer实例创建失败: {e}")
        return False
    
    # 测试坐标
    test_x, test_y = 1739, 320
    
    print(f"\n📍 开始测试坐标 ({test_x}, {test_y}) 的控件识别和高亮...")
    print("=" * 80)
    
    try:
        # 执行控件识别（会自动触发高亮）
        result, info_text = analyzer.analyze_widget_at(test_x, test_y)
        
        if result:
            print("✅ 控件识别成功!")
            print(f"📋 控件名称: {result.get('Name', 'Unknown')}")
            print(f"🏷️  控件角色: {result.get('Rolename', 'Unknown')}")
            print(f"📱 进程名称: {result.get('ProcessName', 'Unknown')}")
            
            coords = result.get('Coords')
            if coords:
                print(f"📍 控件位置: ({coords['x']}, {coords['y']})")
                print(f"📏 控件大小: {coords['width']} x {coords['height']}")
                print("✨ 高亮应该已经自动显示（红色边框，持续2秒）")
            else:
                print("⚠️ 控件坐标信息缺失")
            
            return True
        else:
            print(f"❌ 控件识别失败: {info_text}")
            return False
            
    except Exception as e:
        print(f"❌ 控件识别过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_highlight():
    """测试手动调用高亮功能"""
    print("\n" + "=" * 80)
    print("🎨 测试手动高亮功能")
    print("=" * 80)
    
    try:
        from auto_recording_manager_v11 import WidgetAnalyzer
        analyzer = WidgetAnalyzer(debug=True)
        
        # 模拟控件信息
        mock_widget_info = {
            'Name': '测试按钮',
            'Rolename': 'push button',
            'ProcessName': 'test_app',
            'Coords': {
                'x': 100,
                'y': 100,
                'width': 200,
                'height': 50
            }
        }
        
        print("📍 测试手动高亮功能...")
        print(f"   控件: {mock_widget_info['Name']}")
        print(f"   位置: ({mock_widget_info['Coords']['x']}, {mock_widget_info['Coords']['y']})")
        print(f"   大小: {mock_widget_info['Coords']['width']} x {mock_widget_info['Coords']['height']}")
        
        # 调用高亮方法
        result = analyzer.highlight_widget(mock_widget_info)
        
        if result:
            print("✅ 手动高亮成功!")
            print("✨ 应该能看到红色边框高亮效果（持续2秒）")
            return True
        else:
            print("❌ 手动高亮失败")
            return False
            
    except Exception as e:
        print(f"❌ 手动高亮测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_recording_manager():
    """测试完整的AutoRecordingManager"""
    print("\n" + "=" * 80)
    print("🎯 测试完整的AutoRecordingManager")
    print("=" * 80)
    
    try:
        from auto_recording_manager_v11 import AutoRecordingManager
        print("✅ AutoRecordingManager导入成功")
    except ImportError as e:
        print(f"❌ AutoRecordingManager导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ AutoRecordingManager导入异常: {e}")
        return False
    
    try:
        # 创建AutoRecordingManager实例
        manager = AutoRecordingManager(debug=True)
        print("✅ AutoRecordingManager实例创建成功")
        
        # 检查WidgetAnalyzer
        if hasattr(manager, 'widget_analyzer') and manager.widget_analyzer:
            print("✅ WidgetAnalyzer已集成到AutoRecordingManager")
            print(f"   高亮功能启用: {manager.widget_analyzer.highlight_enabled}")
        else:
            print("⚠️ WidgetAnalyzer未正确集成")
            
        return True
        
    except Exception as e:
        print(f"❌ AutoRecordingManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖项"""
    print("=" * 80)
    print("🔍 检查依赖项")
    print("=" * 80)
    
    # 检查UNI模块
    try:
        from UNI import UNI
        print("✅ UNI模块可用")
        uni_available = True
    except ImportError:
        print("❌ UNI模块不可用")
        uni_available = False
    except Exception as e:
        print(f"❌ UNI模块异常: {e}")
        uni_available = False
    
    # 检查ultimate_highlight
    try:
        sys.path.insert(0, 'KylinAutoSDK_UNI')
        from ultimate_highlight import ultimate_highlight
        print("✅ KylinAutoSDK_UNI ultimate_highlight可用")
        ultimate_available = True
    except ImportError:
        print("❌ KylinAutoSDK_UNI ultimate_highlight不可用")
        ultimate_available = False
    except Exception as e:
        print(f"❌ KylinAutoSDK_UNI ultimate_highlight异常: {e}")
        ultimate_available = False
    
    # 检查统一高亮渲染器
    try:
        from wayland_highlight_renderer import UnifiedHighlightRenderer
        print("✅ 统一高亮渲染器可用")
        unified_available = True
    except ImportError:
        print("❌ 统一高亮渲染器不可用")
        unified_available = False
    except Exception as e:
        print(f"❌ 统一高亮渲染器异常: {e}")
        unified_available = False
    
    return uni_available, ultimate_available, unified_available

def main():
    """主函数"""
    print("🎯 auto_recording_manager_v11.py 高亮功能集成测试")
    print("=" * 80)
    
    # 检查依赖项
    uni_available, ultimate_available, unified_available = check_dependencies()
    
    if not uni_available:
        print("⚠️ UNI模块不可用，控件识别功能将受限")
    
    if not (ultimate_available or unified_available):
        print("⚠️ 没有可用的高亮渲染器")
    
    # 测试WidgetAnalyzer高亮功能
    widget_test_success = test_widget_analyzer_highlight()
    
    # 测试手动高亮功能
    manual_test_success = test_manual_highlight()
    
    # 测试完整的AutoRecordingManager
    manager_test_success = test_auto_recording_manager()
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    print(f"UNI模块: {'✅' if uni_available else '❌'}")
    print(f"KylinAutoSDK_UNI ultimate_highlight: {'✅' if ultimate_available else '❌'}")
    print(f"统一高亮渲染器: {'✅' if unified_available else '❌'}")
    print(f"WidgetAnalyzer高亮测试: {'✅' if widget_test_success else '❌'}")
    print(f"手动高亮测试: {'✅' if manual_test_success else '❌'}")
    print(f"AutoRecordingManager测试: {'✅' if manager_test_success else '❌'}")
    
    if widget_test_success and manual_test_success and manager_test_success:
        print("\n🎉 所有测试通过！高亮功能已成功集成到auto_recording_manager_v11.py中")
        print("\n✨ 功能说明:")
        print("   • 当识别到控件信息后，会自动绘制红色边框高亮")
        print("   • 优先使用KylinAutoSDK_UNI的ultimate_highlight")
        print("   • 高亮持续时间为2秒")
        print("   • 支持Wayland和X11环境")
        
        print("\n📖 使用方法:")
        print("   1. 创建AutoRecordingManager实例")
        print("   2. 使用widget_analyzer.analyze_widget_at(x, y)识别控件")
        print("   3. 控件识别成功后会自动显示高亮效果")
        
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
