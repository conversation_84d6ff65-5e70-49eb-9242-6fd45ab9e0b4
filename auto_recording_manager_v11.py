#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全自动鼠标键盘事件录制管理器
负责协调各个组件，实现完整的自动录制功能
"""

import sys
import time
import json
import threading
import queue
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import traceback
import signal
import math
import subprocess
import os
from pathlib import Path
import importlib.util
from input_listener_client import InputListener
from libinput.evcodes import Button

password = ""

# 导入事件监听库
try:
    from input_listener_client import InputListener
    from libinput.evcodes import Button,Key
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("[ERROR] pynput库不可用，无法进行全局事件监听", file=sys.stderr)

# 导入UNI模块
try:
    # 尝试多个可能的路径
    UNI_PATHS = [
        "scripts",  # 主要路径
        "extensions/KylinRobot-v2/common",  # 备用路径1
        ".",  # 当前目录
        "kylinrobot-ide-x64-remote-display-enhanced/resources/app/scripts",  # 备用路径2
        "kylinrobot-ide-arm64-remote-display-enhanced/resources/app/scripts"  # 备用路径3
    ]

    UNI_AVAILABLE = False
    for path in UNI_PATHS:
        try:
            if path not in sys.path:
                sys.path.insert(0, path)
            from UNI import UNI
            UNI_AVAILABLE = True
            print(f"[INFO] UNI模块从路径加载成功: {path}", file=sys.stderr)
            break
        except ImportError:
            continue

    if not UNI_AVAILABLE:
        print("[ERROR] UNI模块导入失败，尝试的路径:", file=sys.stderr)
        for path in UNI_PATHS:
            print(f"  - {path}", file=sys.stderr)

except Exception as e:
    UNI_AVAILABLE = False
    print(f"[ERROR] UNI模块导入异常: {e}", file=sys.stderr)

# 导入高亮显示模块
try:
    # 优先尝试导入统一高亮渲染器
    from wayland_highlight_renderer import UnifiedHighlightRenderer
    UNIFIED_HIGHLIGHT_AVAILABLE = True
    print("[INFO] 统一高亮显示模块加载成功", file=sys.stderr)
except ImportError:
    UNIFIED_HIGHLIGHT_AVAILABLE = False
    print("[WARNING] 统一高亮显示模块不可用", file=sys.stderr)
except Exception as e:
    UNIFIED_HIGHLIGHT_AVAILABLE = False
    print(f"[ERROR] 统一高亮显示模块导入异常: {e}", file=sys.stderr)

# 备用方案：导入传统高亮渲染器
try:
    from widget_capture_module import HighlightRenderer
    HIGHLIGHT_AVAILABLE = True
    print("[INFO] 传统高亮显示模块加载成功", file=sys.stderr)
except ImportError:
    HIGHLIGHT_AVAILABLE = False
    print("[WARNING] 传统高亮显示模块不可用，将跳过高亮功能", file=sys.stderr)
except Exception as e:
    HIGHLIGHT_AVAILABLE = False
    print(f"[ERROR] 传统高亮显示模块导入异常: {e}", file=sys.stderr)

# 导入KylinAutoSDK_UNI的ultimate_highlight
ULTIMATE_HIGHLIGHT_AVAILABLE = False
try:
    sys.path.insert(0, 'KylinAutoSDK_UNI')
    from ultimate_highlight import ultimate_highlight
    ULTIMATE_HIGHLIGHT_AVAILABLE = True
    print("[INFO] KylinAutoSDK_UNI ultimate_highlight加载成功", file=sys.stderr)
except ImportError:
    print("[WARNING] KylinAutoSDK_UNI ultimate_highlight不可用", file=sys.stderr)
except Exception as e:
    print(f"[ERROR] KylinAutoSDK_UNI ultimate_highlight导入异常: {e}", file=sys.stderr)

# 导入locator生成器
try:
    from locator_generator import LocatorGenerator
    LOCATOR_GENERATOR_AVAILABLE = True
    print("[INFO] Locator生成器加载成功", file=sys.stderr)
except ImportError as e:
    LOCATOR_GENERATOR_AVAILABLE = False
    print(f"[WARNING] Locator生成器不可用: {e}", file=sys.stderr)
except Exception as e:
    LOCATOR_GENERATOR_AVAILABLE = False
    print(f"[ERROR] Locator生成器导入异常: {e}", file=sys.stderr)


@dataclass
class MouseEvent:
    """鼠标事件数据结构"""
    timestamp: float
    event_type: str  # 'click', 'right_click', 'double_click', 'move', 'scroll', 'drag_start', 'drag_move', 'drag_end'
    x: int
    y: int
    button: Optional[str] = None  # 'left', 'right', 'middle'
    pressed: Optional[bool] = None
    scroll_dx: Optional[int] = None
    scroll_dy: Optional[int] = None
    widget_info: Optional[Dict[str, Any]] = None

    # 拖动相关字段
    drag_start_x: Optional[int] = None
    drag_start_y: Optional[int] = None
    drag_distance: Optional[float] = None
    drag_duration: Optional[float] = None
    drag_path: Optional[List[Tuple[int, int]]] = None  # 拖动路径点

    # 双击相关字段
    click_count: Optional[int] = None  # 点击次数
    time_since_last_click: Optional[float] = None  # 距离上次点击的时间


@dataclass
class KeyboardEvent:
    """键盘事件数据结构"""
    timestamp: float
    event_type: str  # 'press', 'release'
    key: str
    key_code: Optional[int] = None
    modifiers: Optional[List[str]] = None


@dataclass
class RecordingSession:
    """录制会话数据结构"""
    session_id: str
    start_time: float
    end_time: Optional[float] = None
    test_case_id: Optional[str] = None
    mouse_events: List[MouseEvent] = None
    keyboard_events: List[KeyboardEvent] = None

    def __post_init__(self):
        if self.mouse_events is None:
            self.mouse_events = []
        if self.keyboard_events is None:
            self.keyboard_events = []


class HoverDetector:
    """
    鼠标悬停检测器
    负责检测鼠标悬停状态，并在悬停时触发控件识别和高亮显示
    """

    def __init__(self, widget_analyzer=None, debug: bool = False, event_queue=None):
        self.debug = debug
        self.widget_analyzer = widget_analyzer
        self.event_queue = event_queue  # 用于发送悬停事件到录制队列

        # 悬停检测参数
        self.hover_threshold = 0.5  # 悬停阈值时间（秒）- 用于高亮显示
        self.hover_record_threshold = 3.0  # 悬停录制阈值时间（秒）- 用于录制事件
        self.movement_threshold = 5  # 鼠标移动阈值（像素）

        # 当前状态
        self.current_position = (0, 0)
        self.last_move_time = 0
        self.hover_timer = None
        self.hover_record_timer = None  # 用于录制的悬停计时器
        self.is_hovering = False
        self.hover_recorded = False  # 标记是否已经录制了悬停事件

        # 控件信息缓存
        self.cached_widget_info = None
        self.cached_position = None
        self.cached_timestamp = None
        self.cache_timeout = 5.0  # 缓存超时时间（秒）

        # 高亮显示器
        self.highlight_renderer = None

        # 优先使用统一高亮渲染器
        if UNIFIED_HIGHLIGHT_AVAILABLE:
            try:
                self.highlight_renderer = UnifiedHighlightRenderer(debug=debug)
                if self.debug:
                    print("[DEBUG] 统一高亮显示器初始化成功", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] 统一高亮显示器初始化失败: {e}", file=sys.stderr)
                self.highlight_renderer = None

        # 备用方案：使用传统高亮渲染器
        if not self.highlight_renderer and HIGHLIGHT_AVAILABLE:
            try:
                self.highlight_renderer = HighlightRenderer(debug=debug)
                if self.debug:
                    print("[DEBUG] 传统高亮显示器初始化成功", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] 传统高亮显示器初始化失败: {e}", file=sys.stderr)
                self.highlight_renderer = None

        # 线程锁
        self.lock = threading.Lock()

        # 运行状态
        self.running = True

    def start(self):
        """启动悬停检测"""
        with self.lock:
            self.running = True
            if self.debug:
                print("[DEBUG] 悬停检测器已启动", file=sys.stderr)

    def stop(self):
        """停止悬停检测"""
        with self.lock:
            self.running = False

            # 取消当前的悬停计时器
            if self.hover_timer:
                self.hover_timer.cancel()
                self.hover_timer = None

            # 取消录制计时器
            if self.hover_record_timer:
                self.hover_record_timer.cancel()
                self.hover_record_timer = None

            # 清除高亮显示
            self._clear_highlight()

            # 清除缓存
            self._clear_cache()

            if self.debug:
                print("[DEBUG] 悬停检测器已停止", file=sys.stderr)

    def on_mouse_move(self, x: int, y: int):
        """处理鼠标移动事件"""
        with self.lock:
            # 如果悬停检测器已停止，不处理事件
            if not self.running:
                return

            current_time = time.time()

            # 检查鼠标是否移动了足够的距离
            distance = math.sqrt((x - self.current_position[0])**2 + (y - self.current_position[1])**2)

            if distance > self.movement_threshold:
                # 鼠标移动距离超过阈值，取消之前的悬停计时器
                if self.hover_timer:
                    self.hover_timer.cancel()
                    self.hover_timer = None

                # 取消录制计时器
                if self.hover_record_timer:
                    self.hover_record_timer.cancel()
                    self.hover_record_timer = None

                # 清除高亮显示和缓存
                self._clear_highlight()
                self._clear_cache()

                # 更新位置和时间
                self.current_position = (x, y)
                self.last_move_time = current_time
                self.is_hovering = False
                self.hover_recorded = False

                # 启动新的悬停计时器（用于高亮显示）
                self.hover_timer = threading.Timer(
                    self.hover_threshold,
                    self._on_hover_timeout,
                    args=(x, y)
                )
                self.hover_timer.start()

                # 启动新的录制计时器（用于录制悬停事件）
                if self.event_queue:
                    self.hover_record_timer = threading.Timer(
                        self.hover_record_threshold,
                        self._on_hover_record_timeout,
                        args=(x, y)
                    )
                    self.hover_record_timer.start()

                # 移除鼠标移动的调试信息，避免日志过多
                pass

    def _on_hover_timeout(self, x: int, y: int):
        """悬停超时回调，触发控件识别和高亮"""
        try:
            print(f"[INFO] 悬停超时触发: 坐标=({x}, {y})", file=sys.stderr)

            # 先设置悬停状态（在锁内快速完成）
            with self.lock:
                self.is_hovering = True

            # 进行控件识别（在锁外进行，避免阻塞其他事件）
            widget_info = None
            info_text = ""

            if self.widget_analyzer:
                start_time = time.time()
                print(f"[INFO] 开始控件识别...", file=sys.stderr)

                # 直接进行控件分析，不进行桌面位置预判
                widget_info, info_text = self.widget_analyzer.analyze_widget_at_with_new_app_detection(x, y)

                recognition_time = time.time() - start_time
                print(f"[INFO] 控件识别完成，耗时: {recognition_time:.3f}秒", file=sys.stderr)

                # 验证控件信息
                if widget_info and not widget_info.get('error'):
                    # 在锁内快速更新缓存
                    with self.lock:
                        self.cached_widget_info = widget_info
                        self.cached_position = (x, y)
                        self.cached_timestamp = time.time()

                    # 高亮显示控件（异步进行，避免阻塞）
                    self._highlight_widget_async(widget_info)

                    widget_name = widget_info.get('Name', 'Unknown')
                    process_name = widget_info.get('ProcessName', 'Unknown')
                    print(f"[INFO] 🎯 悬停识别成功并缓存: {widget_name} (进程: {process_name})", file=sys.stderr)
                else:
                    print(f"[INFO] 悬停识别失败: {info_text}", file=sys.stderr)
                    with self.lock:
                        self._clear_cache()

        except Exception as e:
            print(f"[ERROR] 悬停处理时发生错误: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)

    def _on_hover_record_timeout(self, x: int, y: int):
        """悬停录制超时回调，生成悬停事件"""
        try:
            with self.lock:
                # 检查是否已经录制过悬停事件
                if self.hover_recorded or not self.running:
                    return

                self.hover_recorded = True

            print(f"[INFO] 🎯 悬停录制触发: 坐标=({x}, {y})", file=sys.stderr)

            # 创建悬停事件
            hover_event = MouseEvent(
                timestamp=time.time(),
                event_type='hover',
                x=x,
                y=y,
                button='none'
            )

            # 尝试获取缓存的控件信息
            cached_widget_info = self.get_cached_widget_info(x, y)
            if cached_widget_info:
                hover_event.widget_info = cached_widget_info
                widget_name = cached_widget_info.get('Name', 'Unknown')
                print(f"[INFO] 悬停事件使用缓存控件信息: {widget_name}", file=sys.stderr)
            else:
                # 如果没有缓存信息，进行控件识别
                if self.widget_analyzer:
                    widget_info, info_text = self.widget_analyzer.analyze_widget_at(x, y)
                    if widget_info and not widget_info.get('error'):
                        hover_event.widget_info = widget_info
                        widget_name = widget_info.get('Name', 'Unknown')
                        print(f"[INFO] 悬停事件识别控件: {widget_name}", file=sys.stderr)
                    else:
                        hover_event.widget_info = None
                        print(f"[INFO] 悬停事件无法识别控件: {info_text}", file=sys.stderr)

            # 将悬停事件加入录制队列
            if self.event_queue:
                try:
                    self.event_queue.put(('mouse', hover_event), block=False)
                    print(f"[INFO] ✅ 悬停事件已加入录制队列", file=sys.stderr)
                except queue.Full:
                    print("[ERROR] 事件队列已满，丢弃悬停事件", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 悬停录制处理时发生错误: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)

    def _highlight_widget(self, widget_info: Dict[str, Any]):
        """高亮显示控件"""
        if not self.highlight_renderer:
            return

        try:
            # 获取控件坐标信息
            coords = widget_info.get('Coords')
            if coords:
                self.highlight_renderer.highlight_widget(
                    coords['x'],
                    coords['y'],
                    coords['width'],
                    coords['height']
                )
                if self.debug:
                    print(f"[DEBUG] 高亮控件: x={coords['x']}, y={coords['y']}, w={coords['width']}, h={coords['height']}", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 高亮显示控件时发生错误: {e}", file=sys.stderr)

    def _highlight_widget_async(self, widget_info: Dict[str, Any]):
        """异步高亮显示控件，避免阻塞事件处理"""
        def highlight_worker():
            try:
                print(f"[INFO] 开始异步高亮显示...", file=sys.stderr)
                self._highlight_widget(widget_info)
                print(f"[INFO] 异步高亮显示完成", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] 异步高亮显示失败: {e}", file=sys.stderr)

        # 在新线程中执行高亮显示
        highlight_thread = threading.Thread(target=highlight_worker, daemon=True)
        highlight_thread.start()

    def _clear_highlight(self):
        """清除高亮显示"""
        if self.highlight_renderer:
            try:
                self.highlight_renderer.clear_highlight()
            except Exception as e:
                if self.debug:
                    print(f"[DEBUG] 清除高亮时发生错误: {e}", file=sys.stderr)



    def get_cached_widget_info(self, x: int, y: int, tolerance: int = 15) -> Optional[Dict[str, Any]]:
        """
        获取缓存的控件信息（智能匹配悬停控件与点击位置）

        匹配策略：
        1. 优先检查点击位置是否在缓存控件的边界内
        2. 如果不在边界内，检查是否在容差范围内
        3. 考虑红色边框的视觉影响，适当扩大匹配范围
        """
        with self.lock:
            # 检查缓存是否存在和有效
            if not self.cached_widget_info or not self.cached_position or not self.cached_timestamp:
                if self.debug:
                    print(f"[DEBUG] 无缓存控件信息可用", file=sys.stderr)
                return None

            # 检查缓存是否过期
            if time.time() - self.cached_timestamp > self.cache_timeout:
                if self.debug:
                    print(f"[DEBUG] 缓存已过期，清理缓存", file=sys.stderr)
                self._clear_cache()
                return None

            # 获取缓存控件的坐标信息
            coords = self.cached_widget_info.get('Coords', {})
            cached_x, cached_y = self.cached_position

            # 策略1: 检查点击位置是否在控件边界内
            if coords and isinstance(coords, dict):
                widget_x = coords.get('x', 0)
                widget_y = coords.get('y', 0)
                widget_width = coords.get('width', 0)
                widget_height = coords.get('height', 0)

                if (widget_x <= x <= widget_x + widget_width and
                    widget_y <= y <= widget_y + widget_height):
                    if self.debug:
                        widget_name = self.cached_widget_info.get('Name', 'Unknown')
                        print(f"[DEBUG] ✅ 点击位置在缓存控件边界内，使用悬停控件: {widget_name}", file=sys.stderr)
                    return self.cached_widget_info

            # 策略2: 检查点击位置是否在悬停位置的容差范围内
            distance = math.sqrt((x - cached_x)**2 + (y - cached_y)**2)

            if distance <= tolerance:
                if self.debug:
                    widget_name = self.cached_widget_info.get('Name', 'Unknown')
                    print(f"[DEBUG] ✅ 点击位置在容差范围内({distance:.1f}像素)，使用悬停控件: {widget_name}", file=sys.stderr)
                return self.cached_widget_info
            else:
                if self.debug:
                    print(f"[DEBUG] ❌ 点击位置距离悬停位置太远({distance:.1f}像素 > {tolerance})，重新识别控件", file=sys.stderr)
                return None

    def _clear_cache(self):
        """清理控件信息缓存"""
        self.cached_widget_info = None
        self.cached_position = None
        self.cached_timestamp = None

    def cleanup(self):
        """清理资源"""
        with self.lock:
            if self.hover_timer:
                self.hover_timer.cancel()
                self.hover_timer = None

            self._clear_highlight()

            if self.highlight_renderer:
                try:
                    # 如果高亮渲染器有cleanup方法，调用它
                    if hasattr(self.highlight_renderer, 'cleanup'):
                        self.highlight_renderer.cleanup()
                except Exception as e:
                    if self.debug:
                        print(f"[DEBUG] 清理高亮渲染器时发生错误: {e}", file=sys.stderr)


class DragDetector:
    """
    鼠标拖动检测器
    负责检测和跟踪鼠标拖动操作
    """

    def __init__(self, debug: bool = False):
        self.debug = debug

        # 拖动检测参数
        self.drag_threshold = 10  # 拖动阈值距离（像素）
        self.path_sample_interval = 50  # 路径采样间隔（毫秒）

        # 拖动状态
        self.is_dragging = False
        self.drag_button = None
        self.drag_start_pos = None
        self.drag_start_time = None
        self.drag_path = []
        self.last_path_sample_time = 0

        # 线程锁
        self.lock = threading.Lock()

    def on_mouse_press(self, x: int, y: int, button: str) -> None:
        """处理鼠标按下事件"""
        with self.lock:
            # 记录可能的拖动起始点
            self.drag_start_pos = (x, y)
            self.drag_start_time = time.time()
            self.drag_button = button
            self.drag_path = [(x, y)]
            self.last_path_sample_time = time.time()

            print(f"[INFO] 记录拖动起始点: ({x}, {y}) 按钮={button}", file=sys.stderr)

    def on_mouse_move(self, x: int, y: int) -> Optional[MouseEvent]:
        """处理鼠标移动事件，检测拖动"""
        with self.lock:
            if not self.drag_start_pos or not self.drag_button:
                return None

            current_time = time.time()
            start_x, start_y = self.drag_start_pos

            # 计算移动距离
            distance = math.sqrt((x - start_x)**2 + (y - start_y)**2)

            if not self.is_dragging and distance > self.drag_threshold:
                # 开始拖动
                self.is_dragging = True

                print(f"[INFO] 🎯 检测到拖动开始: 起始({start_x}, {start_y}) 当前({x}, {y}) 距离={distance:.1f}px", file=sys.stderr)

                # 创建拖动开始事件
                return MouseEvent(
                    timestamp=self.drag_start_time,
                    event_type='drag_start',
                    x=start_x,
                    y=start_y,
                    button=self.drag_button,
                    pressed=True
                )

            elif self.is_dragging:
                # 拖动进行中

                # 采样路径点
                if current_time - self.last_path_sample_time >= self.path_sample_interval / 1000:
                    self.drag_path.append((x, y))
                    self.last_path_sample_time = current_time

                # 创建拖动移动事件（可选，避免事件过多）
                return MouseEvent(
                    timestamp=current_time,
                    event_type='drag_move',
                    x=x,
                    y=y,
                    button=self.drag_button,
                    pressed=True,
                    drag_start_x=start_x,
                    drag_start_y=start_y,
                    drag_distance=distance
                )

            return None

    def on_mouse_release(self, x: int, y: int, button: str) -> Optional[MouseEvent]:
        """处理鼠标释放事件，结束拖动"""
        with self.lock:
            # 检查是否有拖动起始点且按钮匹配
            if self.drag_start_pos and button == self.drag_button:
                start_x, start_y = self.drag_start_pos
                drag_duration = time.time() - self.drag_start_time
                final_distance = math.sqrt((x - start_x)**2 + (y - start_y)**2)

                print(f"[INFO] 鼠标释放检查: 起始({start_x}, {start_y}) 结束({x}, {y}) 距离={final_distance:.1f}px 阈值={self.drag_threshold}px", file=sys.stderr)

                # 检查是否超过拖动阈值
                if final_distance > self.drag_threshold:
                    # 是拖动事件
                    # 添加最终位置到路径
                    self.drag_path.append((x, y))

                    print(f"[INFO] 🏁 拖动结束: 起始({start_x}, {start_y}) 结束({x}, {y}) 距离={final_distance:.1f}px 时长={drag_duration:.2f}s", file=sys.stderr)

                    # 创建拖动结束事件
                    drag_end_event = MouseEvent(
                        timestamp=time.time(),
                        event_type='drag_end',
                        x=x,
                        y=y,
                        button=button,
                        pressed=False,
                        drag_start_x=start_x,
                        drag_start_y=start_y,
                        drag_distance=final_distance,
                        drag_duration=drag_duration,
                        drag_path=self.drag_path.copy()
                    )

                    # 重置拖动状态
                    self._reset_drag_state()

                    return drag_end_event
                else:
                    # 距离不够，不是拖动，重置状态
                    print(f"[INFO] 距离不足，不是拖动: {final_distance:.1f}px < {self.drag_threshold}px", file=sys.stderr)
                    self._reset_drag_state()

            return None

    def _reset_drag_state(self):
        """重置拖动状态"""
        self.is_dragging = False
        self.drag_button = None
        self.drag_start_pos = None
        self.drag_start_time = None
        self.drag_path = []
        self.last_path_sample_time = 0


class DoubleClickDetector:
    """
    双击检测器
    负责检测鼠标双击操作
    """

    def __init__(self, debug: bool = False):
        self.debug = debug

        # 双击检测参数
        self.double_click_threshold = 500  # 双击时间阈值（毫秒）
        self.double_click_distance = 5     # 双击位置阈值（像素）

        # 双击状态
        self.last_click_time = 0
        self.last_click_pos = None
        self.last_click_button = None
        self.click_count = 0

        # 线程锁
        self.lock = threading.Lock()

    def on_mouse_click(self, x: int, y: int, button: str, timestamp: float) -> Optional[MouseEvent]:
        """处理鼠标点击事件，检测双击"""
        with self.lock:
            current_time = timestamp
            time_since_last = (current_time - self.last_click_time) * 1000  # 转换为毫秒

            # 检查是否为连续点击
            is_consecutive = (
                self.last_click_pos and
                button == self.last_click_button and
                time_since_last <= self.double_click_threshold and
                self._is_within_distance(x, y, self.last_click_pos[0], self.last_click_pos[1])
            )

            if is_consecutive:
                # 连续点击
                self.click_count += 1

                if self.click_count == 2:
                    # 双击检测成功
                    print(f"[INFO] 🖱️ 检测到双击: 位置=({x}, {y}) 按钮={button} 间隔={time_since_last:.1f}ms", file=sys.stderr)

                    # 重置状态
                    self._reset_click_state()

                    # 创建双击事件
                    return MouseEvent(
                        timestamp=current_time,
                        event_type='double_click',
                        x=x,
                        y=y,
                        button=button,
                        pressed=True,
                        click_count=2,
                        time_since_last_click=time_since_last
                    )
                else:
                    # 更新状态，等待可能的下一次点击
                    self.last_click_time = current_time
                    self.last_click_pos = (x, y)
                    self.last_click_button = button

            else:
                # 新的点击序列开始
                self.click_count = 1
                self.last_click_time = current_time
                self.last_click_pos = (x, y)
                self.last_click_button = button

                if self.debug:
                    print(f"[DEBUG] 记录单击: 位置=({x}, {y}) 按钮={button}", file=sys.stderr)

            return None

    def _is_within_distance(self, x1: int, y1: int, x2: int, y2: int) -> bool:
        """检查两个点是否在双击距离阈值内"""
        distance = math.sqrt((x1 - x2)**2 + (y1 - y2)**2)
        return distance <= self.double_click_distance

    def _reset_click_state(self):
        """重置点击状态"""
        self.last_click_time = 0
        self.last_click_pos = None
        self.last_click_button = None
        self.click_count = 0


class EventCapture:
    """
    事件捕获器
    负责监听全局鼠标和键盘事件
    """

    def __init__(self, event_queue: queue.Queue, debug: bool = False, widget_analyzer=None, hover_detector=None):
        self.event_queue = event_queue
        self.debug = debug
        self.input_listener = None
        self.running = False
        self.widget_analyzer = widget_analyzer  # 添加widget_analyzer引用
        self.hover_detector = hover_detector  # 添加悬停检测器引用

        # 添加拖动检测器
        self.drag_detector = DragDetector(debug)

        # 添加双击检测器
        self.double_click_detector = DoubleClickDetector(debug)

        # 当前按下的修饰键
        self.pressed_modifiers = set()

        # 键盘事件去重 - 增加阈值以更好地过滤重复事件
        self.last_keyboard_event = None
        self.keyboard_event_threshold = 0.2  # 200ms内的重复事件将被忽略

        # 鼠标事件去重 - 增加阈值以更好地过滤重复事件
        self.last_mouse_event = None
        self.mouse_event_threshold = 0.1  # 100ms内的重复事件将被忽略

        # 窗口过滤配置 - 排除录制控制器窗口的事件
        self.excluded_window_titles = [
            '记录器',  # GAT录制控制器窗口
            'operationRecord',  # 窗口名称
            '录制控制器',  # 可能的中文标题
            'Recording Controller',  # 可能的英文标题
        ]
        self.excluded_window_classes = [
            'operationRecord',  # 窗口类名
        ]

        # 事件统计信息
        self.event_stats = {
            'mouse_events': 0,
            'keyboard_events': 0,
            'duplicate_mouse_events': 0,
            'duplicate_keyboard_events': 0
        }

    def _should_exclude_window(self, x: int, y: int) -> bool:
        """
        检查指定坐标的窗口是否应该被排除录制

        Args:
            x: 鼠标X坐标
            y: 鼠标Y坐标

        Returns:
            bool: True表示应该排除，False表示应该录制
        """
        try:
            import pyatspi

            # 使用更高效的方法：直接获取指定坐标的可访问对象
            try:
                # 获取指定坐标的顶层窗口
                accessible = pyatspi.Registry.getDesktop(0).getAccessibleAtPoint(x, y, pyatspi.DESKTOP_COORDS)

                # 向上遍历到窗口级别
                current = accessible
                window_found = None
                max_depth = 10  # 限制遍历深度，避免无限循环

                for _ in range(max_depth):
                    if not current:
                        break

                    # 检查是否是窗口或框架
                    role = current.getRole()
                    if role in [pyatspi.ROLE_WINDOW, pyatspi.ROLE_FRAME, pyatspi.ROLE_DIALOG]:
                        window_found = current
                        break

                    # 向上查找父对象
                    try:
                        current = current.parent
                    except:
                        break

                if window_found:
                    # 检查窗口标题
                    window_title = getattr(window_found, 'name', '') or ''
                    if window_title:
                        for excluded_title in self.excluded_window_titles:
                            if excluded_title in window_title:
                                if self.debug:
                                    print(f"[DEBUG] 🚫 排除录制控制器窗口事件: 标题='{window_title}' 坐标=({x}, {y})", file=sys.stderr)
                                return True

                    # 检查应用程序名称（窗口的父应用）
                    try:
                        app = window_found.getApplication()
                        if app:
                            app_name = getattr(app, 'name', '') or ''
                            if app_name:
                                for excluded_title in self.excluded_window_titles:
                                    if excluded_title in app_name:
                                        if self.debug:
                                            print(f"[DEBUG] 🚫 排除录制控制器应用事件: 应用='{app_name}' 坐标=({x}, {y})", file=sys.stderr)
                                        return True
                    except:
                        pass

            except Exception as e:
                if self.debug:
                    print(f"[DEBUG] 快速窗口检查失败，使用备用方法: {e}", file=sys.stderr)

                # 备用方法：遍历所有窗口（性能较低但更可靠）
                return self._should_exclude_window_fallback(x, y)

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 窗口过滤检查失败: {e}", file=sys.stderr)

        return False

    def _should_exclude_window_fallback(self, x: int, y: int) -> bool:
        """
        备用的窗口排除检查方法
        """
        try:
            import pyatspi

            desktop = pyatspi.Registry.getDesktop(0)
            if not desktop:
                return False

            # 只检查前几个应用程序，提高性能
            max_apps = min(desktop.childCount, 5)

            for i in range(max_apps):
                try:
                    app = desktop.getChildAtIndex(i)
                    if not app:
                        continue

                    # 检查应用程序名称
                    app_name = getattr(app, 'name', '') or ''
                    if any(excluded_title in app_name for excluded_title in self.excluded_window_titles):
                        # 检查这个应用的窗口是否包含指定坐标
                        for j in range(min(app.childCount, 3)):  # 只检查前3个窗口
                            try:
                                window = app.getChildAtIndex(j)
                                if window and hasattr(window, 'queryComponent'):
                                    component = window.queryComponent()
                                    if component:
                                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                        if (extents.x <= x <= extents.x + extents.width and
                                            extents.y <= y <= extents.y + extents.height):
                                            if self.debug:
                                                print(f"[DEBUG] 🚫 排除录制控制器窗口事件(备用): 应用='{app_name}' 坐标=({x}, {y})", file=sys.stderr)
                                            return True
                            except:
                                continue

                except Exception:
                    continue

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 备用窗口过滤检查失败: {e}", file=sys.stderr)

        return False

    def _simple_validate_widget_info(self, widget_info):
        """简单验证控件信息的有效性"""
        if not widget_info:
            return False

        # 检查是否有错误标记
        if widget_info.get('error'):
            return False

        # 检查得分是否有效
        score = widget_info.get('score')
        if isinstance(score, (int, float)) and score < 0:
            return False

        return True

    def start(self) -> bool:
        """开始事件捕获"""
        if self.running:
            return True

        try:
            self.input_listener =InputListener(on_move =self._on_mouse_move,on_click=self._on_mouse_click, \
                on_scroll=self._on_mouse_scroll,on_key_press=self._on_key_press,on_key_release=self._on_key_release,sudo_password=password)
            self.input_listener.start_listen()

            # # 启动鼠标监听
            # self.mouse_listener = mouse.Listener(
            #     on_move=self._on_mouse_move,
            #     on_click=self._on_mouse_click,
            #     on_scroll=self._on_mouse_scroll
            # )
            # self.mouse_listener.daemon = True
            # self.mouse_listener.start()

            # # 启动键盘监听
            # self.keyboard_listener = keyboard.Listener(
            #     on_press=self._on_key_press,
            #     on_release=self._on_key_release
            # )
            # self.keyboard_listener.daemon = True
            # self.keyboard_listener.start()

            self.running = True

            if self.debug:
                print("[DEBUG] 事件捕获器已启动", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 启动事件捕获器失败: {e}", file=sys.stderr)
            return False

    def stop(self):
        """停止事件捕获"""
        self.running = False

        if self.input_listener:
            self.input_listener.stop_listen()
            self.input_listener = None

        if self.debug:
            print("[DEBUG] 事件捕获器已停止", file=sys.stderr)

    def _on_mouse_move(self, x: int, y: int):
        """处理鼠标移动事件"""
        if not self.running:
            return

        # 对于移动事件，我们不进行窗口过滤，因为：
        # 1. 移动事件通常不会被录制为动作
        # 2. 悬停检测需要在所有窗口上工作
        # 3. 过滤移动事件可能会影响拖动检测

        # 通知悬停检测器鼠标移动
        if self.hover_detector:
            self.hover_detector.on_mouse_move(x, y)

        # 检测拖动移动
        drag_event = self.drag_detector.on_mouse_move(x, y)
        if drag_event:
            try:
                self.event_queue.put(('mouse', drag_event), block=False)
                if drag_event.event_type == 'drag_start':
                    print(f"[INFO] 拖动开始事件已加入队列: {drag_event.event_type}", file=sys.stderr)
                # 不记录drag_move事件，避免日志过多
            except queue.Full:
                print("[ERROR] 事件队列已满，丢弃拖动事件", file=sys.stderr)

        # 只记录关键的移动事件，避免数据过多
        # 可以根据需要调整采样频率
        event = MouseEvent(
            timestamp=time.time(),
            event_type='move',
            x=x,
            y=y
        )

        try:
            self.event_queue.put(('mouse', event), block=False)
        except queue.Full:
            if self.debug:
                print("[DEBUG] 事件队列已满，丢弃鼠标移动事件", file=sys.stderr)

    def _on_mouse_click(self, x: int, y: int, button, pressed: bool):
        """处理鼠标点击事件"""
        if not self.running:
            return

        # 检查是否应该排除此窗口的事件
        if self._should_exclude_window(x, y):
            if self.debug:
                print(f"[DEBUG] 🚫 跳过录制控制器窗口的鼠标事件: 位置=({x}, {y})", file=sys.stderr)
            return

        button_name = self._get_button_name(button)

        if pressed:
            # 鼠标按下：通知拖动检测器，但不立即处理点击事件
            self.drag_detector.on_mouse_press(x, y, button_name)
            print(f"[INFO] 鼠标按下: 位置=({x}, {y}) 按钮={button_name} - 等待拖动检测", file=sys.stderr)
            return  # 不立即处理，等待拖动检测结果
        else:
            # 鼠标释放：检查是否结束拖动
            drag_event = self.drag_detector.on_mouse_release(x, y, button_name)
            if drag_event:
                # 不进行控件识别，只记录拖动事件
                print(f"[INFO] 检测到拖动事件: 起始({drag_event.drag_start_x}, {drag_event.drag_start_y}) -> 结束({drag_event.x}, {drag_event.y}) 距离={drag_event.drag_distance:.1f}px", file=sys.stderr)

                try:
                    self.event_queue.put(('mouse', drag_event), block=False)
                    print(f"[INFO] 拖动结束事件已加入队列", file=sys.stderr)
                except queue.Full:
                    print("[ERROR] 事件队列已满，丢弃拖动结束事件", file=sys.stderr)
                return  # 拖动结束，不处理普通点击
            else:
                # 不是拖动，处理为点击事件
                print(f"[INFO] 鼠标释放: 位置=({x}, {y}) 按钮={button_name} - 处理为点击事件", file=sys.stderr)

                # 检查双击
                current_timestamp = time.time()
                double_click_event = self.double_click_detector.on_mouse_click(x, y, button_name, current_timestamp)
                if double_click_event:
                    # 是双击事件
                    print(f"[INFO] 处理双击事件: 位置=({x}, {y}) 按钮={button_name}", file=sys.stderr)
                    self._process_click_event(double_click_event, x, y)
                    return

                # 检查右键点击
                if button_name == 'right':
                    # 右键点击事件
                    event = MouseEvent(
                        timestamp=current_timestamp,
                        event_type='right_click',
                        x=x,
                        y=y,
                        button=button_name,
                        pressed=True
                    )
                    print(f"[INFO] 处理右键点击事件: 位置=({x}, {y})", file=sys.stderr)
                else:
                    # 普通左键点击事件
                    event = MouseEvent(
                        timestamp=current_timestamp,
                        event_type='click',
                        x=x,
                        y=y,
                        button=button_name,
                        pressed=True
                    )
                    print(f"[INFO] 处理左键点击事件: 位置=({x}, {y})", file=sys.stderr)

                # 处理点击事件
                self._process_click_event(event, x, y)
                return

        # 如果到这里，说明是按下事件，已经在前面返回了
        return

    def _process_click_event(self, event: MouseEvent, x: int, y: int):
        """处理点击事件的通用逻辑"""
        # 统计鼠标事件
        self.event_stats['mouse_events'] += 1

        # 检查是否是重复的鼠标事件
        if self._is_duplicate_mouse_event(x, y, event.event_type, event.timestamp):
            self.event_stats['duplicate_mouse_events'] += 1
            if self.debug:
                print(f"[DEBUG] 忽略重复鼠标事件: {event.event_type} 位置=({x}, {y}) (第{self.event_stats['duplicate_mouse_events']}次重复)", file=sys.stderr)
            return

        try:
            # 优先使用悬停检测器缓存的控件信息
            cached_widget_info = None
            if self.hover_detector:
                cached_widget_info = self.hover_detector.get_cached_widget_info(x, y)

            if cached_widget_info:
                event.widget_info = cached_widget_info
                widget_name = cached_widget_info.get('Name', 'Unknown')
                process_name = cached_widget_info.get('ProcessName', 'Unknown')
                print(f"[INFO] 🎯 使用悬停缓存控件: {widget_name} (进程: {process_name})", file=sys.stderr)
                print(f"[INFO] 📌 悬停-点击绑定成功", file=sys.stderr)

            # 如果没有缓存信息，进行控件识别
            elif self.widget_analyzer:
                print(f"[INFO] 🔍 无悬停缓存，重新识别控件", file=sys.stderr)

                # 首先尝试正常识别
                widget_info, info_text = self.widget_analyzer.analyze_widget_at(x, y)

                # 如果正常识别失败，使用新应用检测
                if not widget_info or widget_info.get('error'):
                    if self.debug:
                        print(f"[DEBUG] 🔄 正常识别失败，启用新应用检测", file=sys.stderr)
                    widget_info, info_text = self.widget_analyzer.analyze_widget_at_with_new_app_detection(x, y)

                # 简单验证控件信息的有效性
                is_valid_widget = self._simple_validate_widget_info(widget_info)

                # 将控件信息添加到事件中
                event.widget_info = widget_info if is_valid_widget else None

                if self.debug:
                    if widget_info and is_valid_widget:
                        widget_name = widget_info.get('Name', 'Unknown')
                        process_name = widget_info.get('ProcessName', 'Unknown')
                        print(f"[DEBUG] ⚡ 重新识别成功: {widget_name} (进程: {process_name})", file=sys.stderr)
                    else:
                        print(f"[DEBUG] ❌ 重新识别失败: {info_text}", file=sys.stderr)
            else:
                if self.debug:
                    print(f"[DEBUG] 没有widget_analyzer，跳过控件识别", file=sys.stderr)

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 控件识别失败: {e}", file=sys.stderr)
            event.widget_info = None

        # 更新最后一次鼠标事件记录
        self.last_mouse_event = {
            'x': x,
            'y': y,
            'event_type': event.event_type,
            'timestamp': event.timestamp
        }

        try:
            self.event_queue.put(('mouse', event), block=False)
            print(f"[INFO] ✅ {event.event_type}事件已加入队列: {event.button}键 位置=({x}, {y})", file=sys.stderr)

        except queue.Full:
            print("[ERROR] 事件队列已满，丢弃点击事件", file=sys.stderr)

    def _on_mouse_scroll(self, x: int, y: int, dx: int, dy: int):
        """处理鼠标滚轮事件"""
        if not self.running:
            return

        event = MouseEvent(
            timestamp=time.time(),
            event_type='scroll',
            x=x,
            y=y,
            scroll_dx=dx,
            scroll_dy=dy
        )

        try:
            self.event_queue.put(('mouse', event), block=False)

            if self.debug:
                print(f"[DEBUG] 鼠标滚轮: 位置=({x}, {y}) 滚动=({dx}, {dy})", file=sys.stderr)

        except queue.Full:
            if self.debug:
                print("[DEBUG] 事件队列已满，丢弃鼠标滚轮事件", file=sys.stderr)

    def _on_key_press(self, key):
        """处理键盘按下事件"""
        if not self.running:
            return

        key_name = self._get_key_name(key)

        # 更新修饰键状态
        if self._is_modifier_key(key):
            self.pressed_modifiers.add(key_name)

        current_time = time.time()

        # 统计键盘事件
        self.event_stats['keyboard_events'] += 1

        # 检查是否是重复事件
        if self._is_duplicate_keyboard_event(key_name, current_time):
            self.event_stats['duplicate_keyboard_events'] += 1
            if self.debug:
                print(f"[DEBUG] 忽略重复键盘事件: {key_name} (第{self.event_stats['duplicate_keyboard_events']}次重复)", file=sys.stderr)
            return

        # 检查是否是修饰键，如果是修饰键则不输出事件
        if self._is_modifier_key(key):
            if self.debug:
                print(f"[DEBUG] 修饰键按下: {key_name}, 当前修饰键状态: {list(self.pressed_modifiers)}", file=sys.stderr)
            return

        event = KeyboardEvent(
            timestamp=current_time,
            event_type='press',
            key=key_name,
            modifiers=list(self.pressed_modifiers)
        )

        # 更新最后一次键盘事件记录
        self.last_keyboard_event = {
            'key': key_name,
            'timestamp': current_time,
            'modifiers': list(self.pressed_modifiers)
        }

        try:
            self.event_queue.put(('keyboard', event), block=False)

            if self.debug:
                modifiers_str = '+'.join(self.pressed_modifiers) if self.pressed_modifiers else ''
                key_combo = f"{modifiers_str}+{key_name}" if modifiers_str else key_name
                print(f"[DEBUG] 键盘按下: {key_combo} (按键={key_name}, 修饰键={list(self.pressed_modifiers)})", file=sys.stderr)

        except queue.Full:
            if self.debug:
                print("[DEBUG] 事件队列已满，丢弃键盘按下事件", file=sys.stderr)

    def _is_duplicate_keyboard_event(self, key_name: str, current_time: float) -> bool:
        """检查是否是重复的键盘事件"""
        if not self.last_keyboard_event:
            return False

        # 检查时间间隔
        time_diff = current_time - self.last_keyboard_event['timestamp']
        if time_diff > self.keyboard_event_threshold:
            return False

        # 检查按键是否相同
        if key_name != self.last_keyboard_event['key']:
            return False

        # 检查修饰键是否相同
        current_modifiers = list(self.pressed_modifiers)
        if current_modifiers != self.last_keyboard_event['modifiers']:
            return False

        return True

    def _is_duplicate_mouse_event(self, x: int, y: int, event_type: str, timestamp: float) -> bool:
        """检查是否是重复的鼠标事件"""
        if not self.last_mouse_event:
            return False

        # 检查时间间隔
        time_diff = timestamp - self.last_mouse_event['timestamp']
        if time_diff > self.mouse_event_threshold:
            return False

        # 检查位置是否相同（允许小范围误差）
        x_diff = abs(x - self.last_mouse_event['x'])
        y_diff = abs(y - self.last_mouse_event['y'])
        if x_diff > 2 or y_diff > 2:  # 允许2像素的误差
            return False

        # 检查事件类型是否相同
        if event_type != self.last_mouse_event['event_type']:
            return False

        return True

    def print_event_stats(self):
        """打印事件统计信息，用于诊断重复事件问题"""
        total_mouse = self.event_stats['mouse_events']
        duplicate_mouse = self.event_stats['duplicate_mouse_events']
        total_keyboard = self.event_stats['keyboard_events']
        duplicate_keyboard = self.event_stats['duplicate_keyboard_events']

        mouse_duplicate_rate = (duplicate_mouse / total_mouse * 100) if total_mouse > 0 else 0
        keyboard_duplicate_rate = (duplicate_keyboard / total_keyboard * 100) if total_keyboard > 0 else 0

        print(f"\n=== 事件统计信息 ===", file=sys.stderr)
        print(f"鼠标事件: 总计{total_mouse}, 重复{duplicate_mouse}, 重复率{mouse_duplicate_rate:.1f}%", file=sys.stderr)
        print(f"键盘事件: 总计{total_keyboard}, 重复{duplicate_keyboard}, 重复率{keyboard_duplicate_rate:.1f}%", file=sys.stderr)

        if mouse_duplicate_rate > 20 or keyboard_duplicate_rate > 20:
            print(f"⚠️  检测到高重复率事件，可能存在多个监听器冲突！", file=sys.stderr)

    def _on_key_release(self, key):
        """处理键盘释放事件"""
        if not self.running:
            return

        key_name = self._get_key_name(key)

        # 更新修饰键状态
        if self._is_modifier_key(key):
            self.pressed_modifiers.discard(key_name)

        event = KeyboardEvent(
            timestamp=time.time(),
            event_type='release',
            key=key_name,
            modifiers=list(self.pressed_modifiers)
        )

        try:
            self.event_queue.put(('keyboard', event), block=False)

            if self.debug:
                print(f"[DEBUG] 键盘释放: {key_name}", file=sys.stderr)

        except queue.Full:
            if self.debug:
                print("[DEBUG] 事件队列已满，丢弃键盘释放事件", file=sys.stderr)

    def _get_button_name(self, button) -> str:
        """获取鼠标按钮名称"""
        if button == Button.BTN_LEFT:
            return 'left'
        elif button == Button.BTN_RIGHT:
            return 'right'
        elif button == Button.BTN_MIDDLE:
            return 'middle'
        else:
            return str(button)

    def _get_key_name(self, key) -> str:
        """获取键盘按键名称"""
        try:
            if hasattr(key, 'char') and key.char:
                return key.char
            elif hasattr(key, 'name'):
                return key.name
            else:
                return str(key)
        except AttributeError:
            return str(key)

    def _is_modifier_key(self, key) -> bool:
        """判断是否为修饰键"""
        # modifier_keys = {
        #     keyboard.Key.ctrl, keyboard.Key.ctrl_l, keyboard.Key.ctrl_r,
        #     keyboard.Key.alt, keyboard.Key.alt_l, keyboard.Key.alt_r,
        #     keyboard.Key.shift, keyboard.Key.shift_l, keyboard.Key.shift_r,
        #     keyboard.Key.cmd, keyboard.Key.cmd_l, keyboard.Key.cmd_r
        # }
        modifier_keys = {
            Key.KEY_LEFTCTRL, Key.KEY_RIGHTCTRL,
            Key.KEY_LEFTALT, Key.KEY_RIGHTALT,
            Key.KEY_LEFTSHIFT, Key.KEY_RIGHTSHIFT,
            Key.KEY_LEFTMETA, Key.KEY_RIGHTMETA
        }
        return key in modifier_keys


class MenuListenerManager:
    """
    菜单监听管理器
    负责启动和管理listenHF.py进程，用于监听弹出菜单事件
    """

    def __init__(self, debug: bool = False):
        self.debug = debug
        self.listener_process = None
        self.running = False
        self.menu_file = "/tmp/.recordmenu.txt"

        # 获取listenHF.py的路径
        current_dir = Path(__file__).parent
        self.listener_script = current_dir / "listenHF.py"

        if not self.listener_script.exists():
            print(f"[ERROR] 菜单监听脚本不存在: {self.listener_script}", file=sys.stderr)

    def start_listener(self) -> bool:
        """启动菜单监听进程"""
        if self.running:
            if self.debug:
                print("[DEBUG] 菜单监听器已经在运行", file=sys.stderr)
            return True

        if not self.listener_script.exists():
            print(f"[ERROR] 菜单监听脚本不存在: {self.listener_script}", file=sys.stderr)
            return False

        try:
            if self.debug:
                print(f"[DEBUG] 启动菜单监听进程: {self.listener_script}", file=sys.stderr)

            # 清空菜单记录文件
            try:
                with open(self.menu_file, 'w') as f:
                    f.write('')
                if self.debug:
                    print(f"[DEBUG] 已清空菜单记录文件: {self.menu_file}", file=sys.stderr)
            except Exception as e:
                print(f"[WARNING] 清空菜单记录文件失败: {e}", file=sys.stderr)

            # 启动监听进程
            self.listener_process = subprocess.Popen(
                [sys.executable, str(self.listener_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(self.listener_script.parent.parent),  # 设置工作目录为项目根目录
                preexec_fn=os.setsid  # 创建新的进程组
            )

            # 等待一下让进程启动
            time.sleep(1)

            # 检查进程是否正常启动
            if self.listener_process.poll() is None:
                self.running = True
                if self.debug:
                    print(f"[DEBUG] ✅ 菜单监听进程启动成功，PID: {self.listener_process.pid}", file=sys.stderr)
                return True
            else:
                # 进程启动失败，获取错误信息
                stdout, stderr = self.listener_process.communicate()
                print(f"[ERROR] 菜单监听进程启动失败:", file=sys.stderr)
                if stdout:
                    print(f"stdout: {stdout.decode()}", file=sys.stderr)
                if stderr:
                    print(f"stderr: {stderr.decode()}", file=sys.stderr)
                self.listener_process = None
                return False

        except Exception as e:
            print(f"[ERROR] 启动菜单监听进程时发生错误: {e}", file=sys.stderr)
            self.listener_process = None
            return False

    def stop_listener(self):
        """停止菜单监听进程"""
        if not self.running or not self.listener_process:
            if self.debug:
                print("[DEBUG] 菜单监听器未运行或进程不存在，跳过停止操作", file=sys.stderr)
            return

        process_pid = self.listener_process.pid
        try:
            print(f"[INFO] 正在停止菜单监听进程，PID: {process_pid}", file=sys.stderr)

            # 检查进程是否还存在
            if self.listener_process.poll() is not None:
                print(f"[INFO] 菜单监听进程 {process_pid} 已经结束", file=sys.stderr)
                return

            # 尝试获取进程组ID
            try:
                pgid = os.getpgid(process_pid)
                print(f"[DEBUG] 进程组ID: {pgid}", file=sys.stderr)
            except OSError as e:
                print(f"[WARNING] 无法获取进程组ID: {e}，尝试直接终止进程", file=sys.stderr)
                # 如果无法获取进程组，直接终止主进程
                self.listener_process.terminate()
                try:
                    self.listener_process.wait(timeout=5)
                    print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已通过terminate()结束", file=sys.stderr)
                    return
                except subprocess.TimeoutExpired:
                    print(f"[WARNING] 进程 {process_pid} terminate()超时，尝试kill()", file=sys.stderr)
                    self.listener_process.kill()
                    self.listener_process.wait()
                    print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已通过kill()结束", file=sys.stderr)
                    return

            # 发送SIGTERM信号给整个进程组
            print(f"[DEBUG] 发送SIGTERM信号给进程组 {pgid}", file=sys.stderr)
            os.killpg(pgid, signal.SIGTERM)

            # 等待进程结束
            try:
                self.listener_process.wait(timeout=5)
                print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已正常结束", file=sys.stderr)
            except subprocess.TimeoutExpired:
                print(f"[WARNING] 菜单监听进程 {process_pid} 未响应SIGTERM，强制终止", file=sys.stderr)
                # 强制终止
                try:
                    os.killpg(pgid, signal.SIGKILL)
                    self.listener_process.wait(timeout=3)
                    print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已强制结束", file=sys.stderr)
                except Exception as kill_error:
                    print(f"[ERROR] 强制终止进程组失败: {kill_error}", file=sys.stderr)
                    # 最后尝试直接kill主进程
                    try:
                        self.listener_process.kill()
                        self.listener_process.wait()
                        print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已通过直接kill结束", file=sys.stderr)
                    except Exception as final_error:
                        print(f"[ERROR] 最终终止进程失败: {final_error}", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 停止菜单监听进程时发生错误: {e}", file=sys.stderr)
            print(f"[ERROR] 错误详情: {traceback.format_exc()}", file=sys.stderr)
            # 尝试最后的清理操作
            try:
                if self.listener_process:
                    self.listener_process.kill()
                    self.listener_process.wait()
                    print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已在异常处理中结束", file=sys.stderr)
            except:
                print(f"[ERROR] 最终清理操作也失败", file=sys.stderr)
        finally:
            self.listener_process = None
            self.running = False
            print(f"[INFO] 菜单监听器状态已重置", file=sys.stderr)

    def is_running(self) -> bool:
        """检查菜单监听器是否正在运行"""
        if not self.listener_process:
            return False

        # 检查进程是否还活着
        if self.listener_process.poll() is not None:
            # 进程已结束
            self.running = False
            self.listener_process = None
            return False

        return self.running

    def get_menu_data(self) -> Optional[Dict]:
        """获取菜单数据"""
        if not os.path.exists(self.menu_file):
            return None

        try:
            with open(self.menu_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return None

                import json
                return json.loads(content)
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 读取菜单数据失败: {e}", file=sys.stderr)
            return None


class WidgetAnalyzer:
    """
    改进的控件分析器
    负责使用UNI模块获取鼠标点击位置的控件信息
    解决坐标与窗口不匹配的问题
    """

    def __init__(self, debug: bool = False):
        self.debug = debug
        self.uni = None
        self.highlight_enabled = True  # 默认启用高亮功能

        # 新增应用缓存相关属性
        self._app_cache = {}
        self._last_cache_refresh = 0
        self._cache_timeout = 5.0  # 5秒缓存过期时间

        if UNI_AVAILABLE:
            try:
                self.uni = UNI()
                if self.debug:
                    print("[DEBUG] 改进的UNI控件分析器初始化成功", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] UNI控件分析器初始化失败: {e}", file=sys.stderr)
        else:
            print("[ERROR] UNI模块不可用，控件分析功能将被禁用", file=sys.stderr)

        # 检查高亮功能可用性
        if ULTIMATE_HIGHLIGHT_AVAILABLE:
            if self.debug:
                print("[DEBUG] 使用KylinAutoSDK_UNI ultimate_highlight进行高亮显示", file=sys.stderr)
        elif UNIFIED_HIGHLIGHT_AVAILABLE:
            if self.debug:
                print("[DEBUG] 使用统一高亮渲染器进行高亮显示", file=sys.stderr)
        elif HIGHLIGHT_AVAILABLE:
            if self.debug:
                print("[DEBUG] 使用传统高亮渲染器进行高亮显示", file=sys.stderr)
        else:
            self.highlight_enabled = False
            if self.debug:
                print("[DEBUG] 高亮功能不可用", file=sys.stderr)

    def highlight_widget(self, widget_info: Dict[str, Any]) -> bool:
        """
        高亮显示控件

        Args:
            widget_info: 控件信息字典，包含Coords等信息

        Returns:
            bool: 是否成功高亮
        """
        if not self.highlight_enabled:
            return False

        try:
            coords = widget_info.get('Coords')
            if not coords:
                if self.debug:
                    print("[DEBUG] 控件信息中缺少坐标信息，跳过高亮", file=sys.stderr)
                return False

            x = coords.get('x', 0)
            y = coords.get('y', 0)
            width = coords.get('width', 0)
            height = coords.get('height', 0)

            if width <= 0 or height <= 0:
                if self.debug:
                    print(f"[DEBUG] 控件尺寸无效 ({width}x{height})，跳过高亮", file=sys.stderr)
                return False

            if self.debug:
                control_name = widget_info.get('Name', 'Unknown')
                print(f"[DEBUG] 开始高亮控件: {control_name} at ({x}, {y}) {width}x{height}", file=sys.stderr)

            # 优先使用KylinAutoSDK_UNI的ultimate_highlight
            if ULTIMATE_HIGHLIGHT_AVAILABLE:
                try:
                    result = ultimate_highlight(
                        x, y, width, height,
                        duration=2,  # 2秒高亮
                        color='red',
                        border_width=3
                    )
                    if result:
                        if self.debug:
                            print("[DEBUG] ✅ KylinAutoSDK_UNI高亮成功", file=sys.stderr)
                        return True
                    else:
                        if self.debug:
                            print("[DEBUG] ❌ KylinAutoSDK_UNI高亮失败", file=sys.stderr)
                except Exception as e:
                    if self.debug:
                        print(f"[DEBUG] KylinAutoSDK_UNI高亮异常: {e}", file=sys.stderr)

            # 备用方案：使用统一高亮渲染器
            if UNIFIED_HIGHLIGHT_AVAILABLE:
                try:
                    from wayland_highlight_renderer import UnifiedHighlightRenderer
                    renderer = UnifiedHighlightRenderer(debug=self.debug)
                    result = renderer.highlight_widget(x, y, width, height, widget_info)
                    if result:
                        if self.debug:
                            print("[DEBUG] ✅ 统一高亮渲染器成功", file=sys.stderr)
                        # 延迟清除高亮
                        import threading
                        def clear_highlight():
                            time.sleep(2)
                            renderer.clear_highlight()
                            renderer.cleanup()
                        threading.Thread(target=clear_highlight, daemon=True).start()
                        return True
                except Exception as e:
                    if self.debug:
                        print(f"[DEBUG] 统一高亮渲染器异常: {e}", file=sys.stderr)

            # 最后备用方案：传统高亮渲染器
            if HIGHLIGHT_AVAILABLE:
                try:
                    renderer = HighlightRenderer(debug=self.debug)
                    result = renderer.highlight_widget(x, y, width, height, widget_info)
                    if result:
                        if self.debug:
                            print("[DEBUG] ✅ 传统高亮渲染器成功", file=sys.stderr)
                        # 延迟清除高亮
                        import threading
                        def clear_highlight():
                            time.sleep(2)
                            renderer.clear_highlight()
                        threading.Thread(target=clear_highlight, daemon=True).start()
                        return True
                except Exception as e:
                    if self.debug:
                        print(f"[DEBUG] 传统高亮渲染器异常: {e}", file=sys.stderr)

            if self.debug:
                print("[DEBUG] ❌ 所有高亮方法都失败", file=sys.stderr)
            return False

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 高亮控件时发生错误: {e}", file=sys.stderr)
            return False

    def force_refresh_atspi_registry(self):
        """
        强制刷新AT-SPI注册表，确保新打开的应用能被检测到
        这是一个增强版的刷新功能，专门解决新应用控件识别问题
        """
        if not self.uni:
            return False

        try:
            if self.debug:
                print("[DEBUG] 🔄 开始强制刷新AT-SPI注册表...", file=sys.stderr)

            # 导入pyatspi
            import pyatspi

            # 1. 清除所有相关缓存
            if hasattr(self.uni, 'desktop_cache'):
                self.uni.desktop_cache.clear()
                if self.debug:
                    print("[DEBUG] 已清除桌面缓存", file=sys.stderr)

            if hasattr(self.uni, 'window_cache'):
                self.uni.window_cache.clear()
                if self.debug:
                    print("[DEBUG] 已清除窗口缓存", file=sys.stderr)

            if hasattr(self.uni, 'process_cache'):
                self.uni.process_cache.clear()
                if self.debug:
                    print("[DEBUG] 已清除进程缓存", file=sys.stderr)

            # 2. 重置桌面刷新时间戳
            if hasattr(self.uni, '_last_desktop_refresh'):
                self.uni._last_desktop_refresh = 0

            # 3. 多次获取桌面对象，强制AT-SPI重新扫描
            desktop_attempts = []
            app_names_before = set()
            app_names_after = set()

            for i in range(3):
                try:
                    desktop = pyatspi.Registry.getDesktop(0)
                    app_count = desktop.childCount if desktop else 0
                    desktop_attempts.append(app_count)

                    # 记录应用名称用于调试
                    if i == 0:
                        for j in range(app_count):
                            try:
                                app = desktop.getChildAtIndex(j)
                                if app and app.name:
                                    app_names_before.add(app.name)
                            except:
                                continue
                    elif i == 2:
                        for j in range(app_count):
                            try:
                                app = desktop.getChildAtIndex(j)
                                if app and app.name:
                                    app_names_after.add(app.name)
                            except:
                                continue

                    if self.debug:
                        print(f"[DEBUG] 第{i+1}次获取桌面: {app_count}个应用", file=sys.stderr)

                    # 短暂等待，让AT-SPI有时间更新
                    time.sleep(0.05)
                except Exception as e:
                    if self.debug:
                        print(f"[DEBUG] 第{i+1}次获取桌面失败: {e}", file=sys.stderr)

            # 4. 检查应用数量是否有变化（表明刷新生效）
            if len(desktop_attempts) > 1:
                if desktop_attempts[-1] > desktop_attempts[0]:
                    if self.debug:
                        print(f"[DEBUG] ✅ 检测到新应用: {desktop_attempts[0]} -> {desktop_attempts[-1]}", file=sys.stderr)
                        # 显示新增的应用
                        new_apps = app_names_after - app_names_before
                        if new_apps:
                            print(f"[DEBUG] 新增应用: {', '.join(new_apps)}", file=sys.stderr)
                elif desktop_attempts[-1] == desktop_attempts[0] and desktop_attempts[-1] > 0:
                    if self.debug:
                        print(f"[DEBUG] ✅ 应用数量稳定: {desktop_attempts[-1]}个", file=sys.stderr)

            # 5. 最终等待，确保AT-SPI完全更新
            time.sleep(0.1)

            if self.debug:
                print("[DEBUG] ✅ AT-SPI注册表刷新完成", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 强制刷新AT-SPI注册表失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return False

    def analyze_widget_at(self, x: int, y: int) -> Tuple[Optional[Dict[str, Any]], str]:
        """
        使用优化后的UNI模块进行控件识别
        直接使用UNI模块的X11层级检测和增强选择策略

        Args:
            x: 横坐标
            y: 纵坐标

        Returns:
            Tuple[控件信息字典, 状态描述]
        """
        if not self.uni:
            return None, "UNI模块不可用"

        try:
            if self.debug:
                print(f"[DEBUG] 使用优化后的UNI模块进行控件识别: 坐标({x}, {y})", file=sys.stderr)

            # 添加超时机制，防止长时间阻塞
            start_time = time.time()
            timeout = 3.0  # 3秒超时

            # 轻量级刷新：清除桌面缓存，确保能检测到新应用
            if hasattr(self.uni, 'desktop_cache') and 'desktop_object' in self.uni.desktop_cache:
                # 检查缓存是否过期（使用更短的超时时间）
                cache_entry = self.uni.desktop_cache['desktop_object']
                if time.time() - cache_entry['time'] > 0.05:  # 50ms超时
                    del self.uni.desktop_cache['desktop_object']
                    if self.debug:
                        print(f"[DEBUG] 已清除过期的桌面缓存", file=sys.stderr)

            # 如果UNI模块有_get_fresh_desktop方法，先触发桌面刷新
            if hasattr(self.uni, '_get_fresh_desktop'):
                # 检查是否超时
                if time.time() - start_time > timeout:
                    print(f"[WARNING] 控件识别超时，跳过桌面刷新", file=sys.stderr)
                    return None, "控件识别超时"

                # 强制刷新桌面以检测新打开的应用
                # 每次控件识别前都强制刷新，确保能检测到新应用
                self.uni._last_desktop_refresh = 0
                desktop = self.uni._get_fresh_desktop()
                if self.debug:
                    app_count = desktop.childCount if desktop else 0
                    print(f"[DEBUG] 已触发桌面刷新，应用数: {app_count}", file=sys.stderr)

            # 再次检查超时
            if time.time() - start_time > timeout:
                print(f"[WARNING] 控件识别超时，跳过UNI识别", file=sys.stderr)
                return None, "控件识别超时"

            # 直接使用优化后的UNI模块，启用菜单控件识别
            result, info_text = self.uni.kdk_getElement_Uni(x, y, False, True)

            if result and not result.get('error'):
                if self.debug:
                    process_name = result.get('ProcessName', 'Unknown')
                    control_name = result.get('Name', 'N/A')
                    print(f"[DEBUG] UNI模块识别成功: {control_name} (进程: {process_name})", file=sys.stderr)

                # 识别成功后立即进行高亮显示
                try:
                    self.highlight_widget(result)
                except Exception as e:
                    if self.debug:
                        print(f"[DEBUG] 高亮显示失败: {e}", file=sys.stderr)

                return result, "成功"
            else:
                error_msg = result.get('error', '未知错误') if result else '无返回结果'
                if self.debug:
                    print(f"[DEBUG] UNI模块识别失败: {error_msg}", file=sys.stderr)
                return None, error_msg

        except Exception as e:
            error_msg = f"UNI模块控件识别时发生错误: {e}"
            print(f"[ERROR] {error_msg}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return None, error_msg

    def analyze_widget_at_with_new_app_detection(self, x: int, y: int) -> Tuple[Optional[Dict[str, Any]], str]:
        """
        带新应用检测的控件识别(优化版)
        1. 使用应用缓存减少重复遍历
        2. 限制最大递归深度
        3. 并行处理多个应用
        """
        if not self.uni:
            return None, "UNI模块不可用"

        # 检查缓存是否过期
        current_time = time.time()
        if current_time - self._last_cache_refresh > self._cache_timeout:
            self._app_cache.clear()
            self._last_cache_refresh = current_time
            if self.debug:
                print("[DEBUG] 🔄 应用缓存已过期，清空缓存", file=sys.stderr)

        # 第一次尝试使用缓存识别
        for app_info in self._app_cache.values():
            result, info_text = self._analyze_in_cached_app(app_info, x, y)
            if result:
                return result, "缓存识别成功"

        # 缓存未命中，执行标准识别流程
        result, info_text = self.analyze_widget_at(x, y)
        if result:
            # 更新缓存
            process_name = result.get('ProcessName')
            if process_name:
                self._app_cache[process_name] = {
                    'process': process_name,
                    'last_used': current_time,
                    'window': result.get('WindowName')
                }
            return result, info_text

        # 强化检测流程
        if self.debug:
            print(f"[DEBUG] 🔍 启动新应用强化检测: 坐标({x}, {y})", file=sys.stderr)

        self.force_refresh_atspi_registry()
        time.sleep(0.1)  # 减少等待时间

        # 优化后的识别尝试
        result, info_text = self.uni.kdk_getElement_Uni(x, y, False, True)
        if result:
            # 更新缓存
            process_name = result.get('ProcessName')
            if process_name:
                self._app_cache[process_name] = {
                    'process': process_name,
                    'last_used': current_time,
                    'window': result.get('WindowName')
                }
            return result, "新应用检测成功"

        return None, info_text or "识别失败"

    def _analyze_in_cached_app(self, app_info, x, y):
        """在缓存应用中尝试识别控件"""
        try:
            import pyatspi
            desktop = pyatspi.Registry.getDesktop(0)

            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                if app.name == app_info['process']:
                    result, _ = self.uni.kdk_getElement_Uni(x, y, False, True)
                    if result:
                        return result, "缓存应用识别成功"
        #except Exception:
        #    pass
        #return None, ""

        except Exception as e:
            error_msg = f"新应用检测时发生错误: {e}"
            print(f"[ERROR] {error_msg}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return None, error_msg

    def _find_windows_at_point(self, x: int, y: int) -> List[Tuple[Any, Dict[str, Any]]]:
        """
        找到包含指定坐标的所有窗口

        Args:
            x: 横坐标
            y: 纵坐标

        Returns:
            包含坐标的窗口列表，每个元素为(窗口对象, 窗口信息字典)
        """
        candidate_windows = []

        try:
            # 导入pyatspi
            import pyatspi
            desktop = pyatspi.Registry.getDesktop(0)

            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)

                    for j in range(app.childCount):
                        try:
                            window = app.getChildAtIndex(j)
                            component = window.queryComponent()

                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                                # 检查坐标是否在窗口范围内
                                if (extents.x <= x <= extents.x + extents.width and
                                    extents.y <= y <= extents.y + extents.height and
                                    extents.width > 0 and extents.height > 0):

                                    # 收集窗口信息
                                    window_info = {
                                        'name': window.name if window.name else f'({app.name})',
                                        'process': app.name,
                                        'area': extents.width * extents.height,
                                        'bounds': (extents.x, extents.y, extents.width, extents.height),
                                        'active': window.getState().contains(pyatspi.STATE_ACTIVE),
                                        'visible': window.getState().contains(pyatspi.STATE_VISIBLE),
                                        'showing': window.getState().contains(pyatspi.STATE_SHOWING)
                                    }

                                    candidate_windows.append((window, window_info))

                        except Exception as e:
                            # 忽略无法访问的窗口
                            continue

                except Exception as e:
                    # 忽略无法访问的应用
                    continue

        except Exception as e:
            print(f"[ERROR] 查找窗口时发生错误: {e}", file=sys.stderr)

        return candidate_windows

    def _find_widget_in_window(self, window, x: int, y: int, window_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        在指定窗口中查找控件

        Args:
            window: 窗口对象
            x: 横坐标
            y: 纵坐标
            window_info: 窗口信息

        Returns:
            控件信息字典或None
        """
        try:
            # 导入pyatspi
            import pyatspi

            # 使用UNI的控件查找逻辑，但限制在指定窗口内
            self.uni.childEle = None

            # 获取窗口区域
            component = window.queryComponent()
            if not component:
                return None

            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
            window_region = [extents.x, extents.y, extents.x + extents.width, extents.y + extents.height]

            # 调用UNI的控件查找方法
            self.uni._find_accessible_at_point(window, x, y, window_region)

            if self.uni.childEle:
                # 提取控件信息
                data = self.uni._extract_element_info(self.uni.childEle)

                # 补充窗口信息
                data["RecordPosition"] = (x, y)
                data["WindowRoleName"] = window.getRoleName()
                data["WindowChildCount"] = window.childCount

                # 验证控件信息
                if self._validate_widget_info(data, x, y):
                    return data

            return None

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 在窗口中查找控件时发生错误: {e}", file=sys.stderr)
            return None

    def _validate_widget_info(self, widget_info: Dict[str, Any], x: int, y: int) -> bool:
        """
        验证控件信息的有效性

        Args:
            widget_info: 控件信息字典
            x: 点击的X坐标
            y: 点击的Y坐标

        Returns:
            bool: 控件信息是否有效
        """
        if not widget_info:
            return False

        # 检查是否有错误标记
        if widget_info.get('error'):
            return False

        # 检查坐标是否在控件范围内
        coords = widget_info.get('Coords', {})
        if coords:
            widget_x = coords.get('x', 0)
            widget_y = coords.get('y', 0)
            widget_w = coords.get('width', 0)
            widget_h = coords.get('height', 0)

            if not (widget_x <= x <= widget_x + widget_w and
                    widget_y <= y <= widget_y + widget_h):
                if self.debug:
                    print(f"[DEBUG] 控件坐标不匹配: 点击({x}, {y}) 不在控件范围({widget_x}, {widget_y}, {widget_w}, {widget_h})内", file=sys.stderr)
                return False

        return True


class ActionRecorder:
    """
    动作记录器
    负责将事件转换为可执行的动作记录
    """

    def __init__(self, debug: bool = False):
        self.debug = debug
        self.recorded_actions = []

    def record_mouse_action(self, event: MouseEvent) -> Optional[Dict[str, Any]]:
        """
        记录鼠标动作

        Args:
            event: 鼠标事件

        Returns:
            动作记录字典
        """
        if event.event_type == 'click' and event.pressed:
            # 记录左键点击事件
            action = {
                'type': 'mouse_click',
                'timestamp': event.timestamp,
                'x': event.x,
                'y': event.y,
                'button': event.button,
                'widget_info': event.widget_info
            }

            self.recorded_actions.append(action)
            print(f"[INFO] 记录鼠标点击动作: {event.button}键 位置=({event.x}, {event.y})", file=sys.stderr)

            return action

        elif event.event_type == 'right_click' and event.pressed:
            # 记录右键点击事件
            action = {
                'type': 'mouse_right_click',
                'timestamp': event.timestamp,
                'x': event.x,
                'y': event.y,
                'button': event.button,
                'widget_info': event.widget_info
            }

            self.recorded_actions.append(action)
            print(f"[INFO] 记录鼠标右键点击动作: 位置=({event.x}, {event.y})", file=sys.stderr)

            return action

        elif event.event_type == 'double_click' and event.pressed:
            # 记录双击事件
            action = {
                'type': 'mouse_double_click',
                'timestamp': event.timestamp,
                'x': event.x,
                'y': event.y,
                'button': event.button,
                'click_count': event.click_count,
                'time_since_last_click': event.time_since_last_click,
                'widget_info': event.widget_info
            }

            self.recorded_actions.append(action)
            print(f"[INFO] 记录鼠标双击动作: {event.button}键 位置=({event.x}, {event.y}) 间隔={event.time_since_last_click:.1f}ms", file=sys.stderr)

            return action

        elif event.event_type == 'scroll':
            action = {
                'type': 'mouse_scroll',
                'timestamp': event.timestamp,
                'x': event.x,
                'y': event.y,
                'scroll_dx': event.scroll_dx,
                'scroll_dy': event.scroll_dy
            }

            self.recorded_actions.append(action)

            if self.debug:
                print(f"[DEBUG] 记录鼠标滚轮动作: 位置=({event.x}, {event.y}) 滚动=({event.scroll_dx}, {event.scroll_dy})", file=sys.stderr)

            return action

        elif event.event_type == 'drag_end':
            # 记录拖动动作（简化版，不包含控件信息）
            action = {
                'type': 'mouse_drag',
                'timestamp': event.timestamp,
                'start_x': event.drag_start_x,
                'start_y': event.drag_start_y,
                'end_x': event.x,
                'end_y': event.y,
                'button': event.button,
                'distance': event.drag_distance,
                'duration': event.drag_duration,
                'path_points': len(event.drag_path) if event.drag_path else 0
            }

            self.recorded_actions.append(action)

            print(f"[INFO] 记录拖动动作: 起始({event.drag_start_x}, {event.drag_start_y}) 结束({event.x}, {event.y}) 距离={event.drag_distance:.1f}px", file=sys.stderr)

            return action

        elif event.event_type == 'drag_start':
            # 记录拖动开始（用于调试）
            print(f"[INFO] 拖动开始: 位置=({event.x}, {event.y}) 按钮={event.button}", file=sys.stderr)

        elif event.event_type == 'hover':
            # 记录悬停事件
            action = {
                'type': 'mouse_hover',
                'timestamp': event.timestamp,
                'x': event.x,
                'y': event.y,
                'widget_info': event.widget_info
            }

            self.recorded_actions.append(action)

            widget_name = 'Unknown'
            if event.widget_info:
                widget_name = event.widget_info.get('Name', 'Unknown')

            print(f"[INFO] 🎯 记录悬停动作: 位置=({event.x}, {event.y}) 控件={widget_name}", file=sys.stderr)

            return action

        return None

    def record_keyboard_action(self, event: KeyboardEvent) -> Optional[Dict[str, Any]]:
        """
        记录键盘动作

        Args:
            event: 键盘事件

        Returns:
            动作记录字典
        """
        if event.event_type == 'press':
            # 只记录按键按下事件
            action = {
                'type': 'key_press',
                'timestamp': event.timestamp,
                'key': event.key,
                'modifiers': event.modifiers or []
            }

            self.recorded_actions.append(action)

            if self.debug:
                modifiers_str = '+'.join(event.modifiers) if event.modifiers else ''
                key_combo = f"{modifiers_str}+{event.key}" if modifiers_str else event.key
                print(f"[DEBUG] 记录键盘按键动作: {key_combo}", file=sys.stderr)

            return action

        return None

    def get_recorded_actions(self) -> List[Dict[str, Any]]:
        """获取所有记录的动作"""
        return self.recorded_actions.copy()

    def clear_actions(self):
        """清空动作记录"""
        self.recorded_actions.clear()
        if self.debug:
            print("[DEBUG] 已清空动作记录", file=sys.stderr)


class RecordingStorage:
    """
    录制数据存储器
    负责保存和加载录制会话数据
    """

    def __init__(self, storage_path: str = "recordings", debug: bool = False):
        self.storage_path = storage_path
        self.debug = debug

        # 确保存储目录存在
        import os
        os.makedirs(storage_path, exist_ok=True)

    def save_session(self, session: RecordingSession) -> bool:
        """
        保存录制会话

        Args:
            session: 录制会话对象

        Returns:
            是否保存成功
        """
        try:
            import os
            filename = f"{session.session_id}.json"
            filepath = os.path.join(self.storage_path, filename)

            # 转换为可序列化的字典
            session_data = asdict(session)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)

            if self.debug:
                print(f"[DEBUG] 录制会话已保存: {filepath}", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 保存录制会话失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return False

    def load_session(self, session_id: str) -> Optional[RecordingSession]:
        """
        加载录制会话

        Args:
            session_id: 会话ID

        Returns:
            录制会话对象或None
        """
        try:
            import os
            filename = f"{session_id}.json"
            filepath = os.path.join(self.storage_path, filename)

            if not os.path.exists(filepath):
                if self.debug:
                    print(f"[DEBUG] 录制会话文件不存在: {filepath}", file=sys.stderr)
                return None

            with open(filepath, 'r', encoding='utf-8') as f:
                session_data = json.load(f)

            # 重建对象
            session = RecordingSession(**session_data)

            if self.debug:
                print(f"[DEBUG] 录制会话已加载: {filepath}", file=sys.stderr)

            return session

        except Exception as e:
            print(f"[ERROR] 加载录制会话失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return None


class AutoRecordingManager:
    """
    全自动录制管理器
    协调各个组件，实现完整的自动录制功能
    """

    def __init__(self, storage_path: str = "recordings", debug: bool = False, json_output: bool = False):
        self.debug = debug
        self.storage_path = storage_path
        self.json_output = json_output  # 新增JSON输出模式

        # 在JSON输出模式下，减少stderr的调试信息
        if self.json_output:
            self.debug = False  # 强制关闭调试模式以减少stderr输出

        # 录制状态
        self.is_recording = False
        self.is_paused = False
        self.current_session = None

        # 组件初始化
        self.event_queue = queue.Queue(maxsize=1000)
        self.widget_analyzer = WidgetAnalyzer(debug)

        # 初始化菜单监听管理器
        self.menu_listener = MenuListenerManager(debug)

        # 初始化locator生成器
        self.locator_generator = None
        if LOCATOR_GENERATOR_AVAILABLE:
            try:
                self.locator_generator = LocatorGenerator(debug=debug)
                if self.debug:
                    print("[DEBUG] Locator生成器初始化成功", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] Locator生成器初始化失败: {e}", file=sys.stderr)

        # 初始化悬停检测器，传递event_queue用于录制悬停事件
        self.hover_detector = HoverDetector(self.widget_analyzer, debug, self.event_queue)

        # 初始化事件捕获器，传入悬停检测器
        self.event_capture = EventCapture(self.event_queue, debug, self.widget_analyzer, self.hover_detector)
        self.action_recorder = ActionRecorder(debug)
        self.storage = RecordingStorage(storage_path, debug)

        # 事件处理线程
        self.event_processor_thread = None
        self.stop_event = threading.Event()

        # 命令监听线程
        self.command_listener_thread = None

        # 控件分析线程池（用于异步分析控件信息）
        self.widget_analysis_executor = None

        # 加载app_menu模块用于driver匹配
        self.app_menu_module = None
        self.current_testcase_path = None  # 当前测试用例路径
        self._load_app_menu_module()

        if self.debug:
            print("[DEBUG] 全自动录制管理器初始化完成", file=sys.stderr)

    def _load_app_menu_module(self, testcase_path: str = None):
        """
        加载app_menu模块

        Args:
            testcase_path: 测试用例路径，如果提供则优先从该路径加载app_menu.py
        """
        try:
            app_menu_paths = []

            # 如果提供了测试用例路径，优先从该路径查找
            if testcase_path:
                # 测试用例路径通常指向testcase目录，app_menu.py在其父目录
                testcase_dir = os.path.dirname(testcase_path) if os.path.isfile(testcase_path) else testcase_path

                # 构建可能的app_menu.py路径
                possible_paths = [
                    os.path.join(testcase_dir, "app_menu.py"),  # 同级目录
                    os.path.join(os.path.dirname(testcase_dir), "app_menu.py"),  # 父目录
                    os.path.join(testcase_dir, "..", "app_menu.py"),  # 相对父目录
                ]

                for path in possible_paths:
                    normalized_path = os.path.normpath(path)
                    if os.path.exists(normalized_path):
                        app_menu_paths.append(normalized_path)
                        if self.debug:
                            print(f"[DEBUG] 从测试用例路径找到app_menu.py: {normalized_path}", file=sys.stderr)

            # 添加默认的备用路径
            app_menu_paths.extend([
                "KylinRobot-v2/app_menu.py",
                "../KylinRobot-v2/app_menu.py",
                "../../KylinRobot-v2/app_menu.py",
                "extensions/KylinRobot-v2/app_menu.py",
                "kylinrobot-ide-arm64-remote-display-enhanced/resources/app/KylinRobot-v2/app_menu.py"
            ])

            for app_menu_path in app_menu_paths:
                if os.path.exists(app_menu_path):
                    spec = importlib.util.spec_from_file_location("app_menu", app_menu_path)
                    if spec and spec.loader:
                        self.app_menu_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(self.app_menu_module)
                        if self.debug:
                            print(f"[DEBUG] app_menu模块加载成功: {app_menu_path}", file=sys.stderr)
                        return

            if self.debug:
                print("[DEBUG] 未找到app_menu.py文件，driver匹配功能将不可用", file=sys.stderr)

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 加载app_menu模块失败: {e}", file=sys.stderr)

    def get_driver_from_process_name(self, process_name: str) -> str:
        """
        根据ProcessName从app_menu.py中获取对应的driver值

        Args:
            process_name: 控件信息中的ProcessName字段

        Returns:
            str: 匹配的driver值，如果未找到则返回process_name本身
        """
        if not self.app_menu_module or not process_name:
            return process_name or 'unknown'

        try:
            # 获取app_menu模块中的所有类
            for attr_name in dir(self.app_menu_module):
                attr = getattr(self.app_menu_module, attr_name)
                # 检查是否是dataclass类
                if hasattr(attr, '__dataclass_fields__') and hasattr(attr, 'exec'):
                    try:
                        # 创建类实例以获取exec值
                        instance = attr()
                        exec_value = instance.exec

                        # 直接匹配
                        if exec_value == process_name:
                            if self.debug:
                                print(f"[DEBUG] 精确匹配找到driver: {process_name} -> {exec_value}", file=sys.stderr)
                            return exec_value

                        # 处理截断情况：如果process_name是exec_value的前缀
                        if process_name and exec_value.startswith(process_name):
                            if self.debug:
                                print(f"[DEBUG] 前缀匹配找到driver: {process_name} -> {exec_value}", file=sys.stderr)
                            return exec_value

                        # 处理截断情况：如果exec_value是process_name的前缀
                        if exec_value and process_name.startswith(exec_value):
                            if self.debug:
                                print(f"[DEBUG] 反向前缀匹配找到driver: {process_name} -> {exec_value}", file=sys.stderr)
                            return exec_value

                    except Exception as e:
                        # 某些类可能无法实例化，跳过
                        continue

            # 如果没有找到匹配，尝试自动创建新的driver类
            if self.debug:
                print(f"[DEBUG] 未找到匹配的driver，尝试自动创建: {process_name}", file=sys.stderr)

            # 自动创建新的driver类
            if self._auto_create_driver_class(process_name):
                if self.debug:
                    print(f"[DEBUG] 成功创建新driver类: {process_name}", file=sys.stderr)
                return process_name
            else:
                if self.debug:
                    print(f"[DEBUG] 创建driver类失败，使用原始ProcessName: {process_name}", file=sys.stderr)
                return process_name

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] driver匹配过程出错: {e}", file=sys.stderr)
            return process_name or 'unknown'

    def _auto_create_driver_class(self, process_name: str) -> bool:
        """
        自动在app_menu.py中创建新的driver类

        Args:
            process_name: 未匹配的进程名

        Returns:
            bool: 是否创建成功
        """
        if not process_name or not self.app_menu_module:
            return False

        try:
            # 查找app_menu.py文件路径
            app_menu_paths = [
                "KylinRobot-v2/app_menu.py",
                "../KylinRobot-v2/app_menu.py",
                "../../KylinRobot-v2/app_menu.py",
                "extensions/KylinRobot-v2/app_menu.py",
                "kylinrobot-ide-arm64-remote-display-enhanced/resources/app/KylinRobot-v2/app_menu.py"
            ]

            app_menu_file_path = None
            for path in app_menu_paths:
                if os.path.exists(path):
                    app_menu_file_path = path
                    break

            if not app_menu_file_path:
                if self.debug:
                    print("[DEBUG] 未找到app_menu.py文件，无法自动创建driver类", file=sys.stderr)
                return False

            # 生成类名（将process_name转换为合适的类名）
            class_name = self._generate_class_name(process_name)

            # 检查类名是否已存在
            if hasattr(self.app_menu_module, class_name):
                if self.debug:
                    print(f"[DEBUG] 类名 {class_name} 已存在，跳过创建", file=sys.stderr)
                return True

            # 生成新的类定义
            new_class_definition = self._generate_class_definition(class_name, process_name)

            # 将新类追加到app_menu.py文件
            with open(app_menu_file_path, 'a', encoding='utf-8') as f:
                f.write('\n\n' + new_class_definition)

            if self.debug:
                print(f"[DEBUG] 成功在 {app_menu_file_path} 中创建新类: {class_name}", file=sys.stderr)

            # 重新加载app_menu模块以包含新类
            self._reload_app_menu_module()

            return True

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 自动创建driver类失败: {e}", file=sys.stderr)
            return False

    def _generate_class_name(self, process_name: str) -> str:
        """
        根据进程名生成合适的类名

        Args:
            process_name: 进程名

        Returns:
            str: 生成的类名
        """
        # 移除特殊字符，转换为驼峰命名
        clean_name = ''.join(c for c in process_name if c.isalnum() or c in '-_')

        # 按照连字符和下划线分割，然后转换为驼峰命名
        parts = clean_name.replace('-', '_').split('_')
        class_name = ''.join(word.capitalize() for word in parts if word)

        # 确保类名不为空且以字母开头
        if not class_name or not class_name[0].isalpha():
            class_name = 'App' + class_name

        return class_name

    def _generate_class_definition(self, class_name: str, process_name: str) -> str:
        """
        生成新的dataclass类定义

        Args:
            class_name: 类名
            process_name: 进程名

        Returns:
            str: 类定义字符串
        """
        return f'''@dataclass(frozen=True)
class {class_name}:
    """
    自动生成的应用类 - {process_name}
    """
    exec: str = '{process_name}' '''

    def _reload_app_menu_module(self):
        """重新加载app_menu模块"""
        try:
            # 重新加载模块，使用当前的测试用例路径
            self._load_app_menu_module(self.current_testcase_path)
            if self.debug:
                print("[DEBUG] app_menu模块重新加载成功", file=sys.stderr)
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 重新加载app_menu模块失败: {e}", file=sys.stderr)

    def setup_locator_generator(self, testcase_path: str):
        """设置locator生成器的测试用例路径"""
        if self.locator_generator:
            self.locator_generator.setup_locator_directory(testcase_path)
            if self.debug:
                print(f"[DEBUG] Locator生成器路径已设置: {testcase_path}", file=sys.stderr)

        # 如果测试用例路径发生变化，重新加载app_menu模块
        if self.current_testcase_path != testcase_path:
            self.current_testcase_path = testcase_path
            self._load_app_menu_module(testcase_path)
            if self.debug:
                print(f"[DEBUG] 已根据新的测试用例路径重新加载app_menu模块: {testcase_path}", file=sys.stderr)

    def generate_locator_from_widget_info(self, widget_info: Dict[str, Any], driver: str = None) -> bool:
        """
        从控件信息生成locator文件

        Args:
            widget_info: 控件信息
            driver: 驱动名称，如果为None则尝试从控件信息中提取

        Returns:
            bool: 是否生成成功
        """
        if not self.locator_generator:
            if self.debug:
                print("[DEBUG] Locator生成器不可用，跳过locator生成", file=sys.stderr)
            return False

        try:
            # 如果没有提供driver，尝试从控件信息中提取并匹配
            if not driver:
                process_name = widget_info.get('ProcessName', 'unknown')
                driver = self.get_driver_from_process_name(process_name)

            # 判断控件类型并生成相应的locator
            if widget_info.get('MenuElement') == '1':
                # 菜单控件，使用UNI类型
                return self.locator_generator.generate_uni_locator(widget_info, driver)
            elif 'Coords' in widget_info and 'Name' in widget_info:
                # 有坐标和名称的控件，使用UNI类型
                return self.locator_generator.generate_uni_locator(widget_info, driver)
            elif 'Coords' in widget_info:
                # 只有坐标的控件，使用OCR类型
                coords = widget_info['Coords']
                key = f"录制坐标{coords['x']}{coords['y']}"
                return self.locator_generator.generate_ocr_locator(key, coords, driver)
            else:
                if self.debug:
                    print(f"[DEBUG] 控件信息不足，无法生成locator: {widget_info}", file=sys.stderr)
                return False

        except Exception as e:
            print(f"[ERROR] 生成locator时发生错误: {e}", file=sys.stderr)
            return False

    def _force_clear_all_highlights(self):
        """强制清除所有高亮显示"""
        try:
            # 清除悬停检测器的高亮
            if hasattr(self, 'hover_detector') and self.hover_detector and hasattr(self.hover_detector, 'highlight_renderer'):
                if self.hover_detector.highlight_renderer:
                    self.hover_detector.highlight_renderer.clear_highlight()
                    if self.debug:
                        print("[DEBUG] 已清除悬停检测器高亮", file=sys.stderr)

            # 清除事件捕获器中可能的高亮状态
            if hasattr(self, 'event_capture') and self.event_capture:
                # 如果事件捕获器有高亮相关的状态，也清除
                if hasattr(self.event_capture, 'hover_detector') and self.event_capture.hover_detector:
                    if hasattr(self.event_capture.hover_detector, '_clear_highlight'):
                        self.event_capture.hover_detector._clear_highlight()
                        if self.debug:
                            print("[DEBUG] 已清除事件捕获器中的高亮", file=sys.stderr)

            # 发送JSON事件通知前端清除高亮
            if self.json_output:
                output_json_event('highlight', {'action': 'clear', 'message': '已清除所有高亮显示'})

            if self.debug:
                print("[DEBUG] 强制清除所有高亮显示完成", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 强制清除高亮时发生错误: {e}", file=sys.stderr)
            if self.debug:
                import traceback
                traceback.print_exc(file=sys.stderr)

    def start_recording(self, test_case_id: Optional[str] = None, testcase_path: Optional[str] = None) -> bool:
        """
        开始录制

        Args:
            test_case_id: 测试用例ID（可选）
            testcase_path: 测试用例路径（可选，用于设置locator生成器）

        Returns:
            是否成功开始录制
        """
        if self.is_recording:
            print("[WARNING] 录制已在进行中", file=sys.stderr)
            return False

        try:
            # 生成会话ID
            session_id = f"session_{int(time.time() * 1000)}"

            # 创建录制会话
            self.current_session = RecordingSession(
                session_id=session_id,
                start_time=time.time(),
                test_case_id=test_case_id
            )

            # 清空之前的动作记录
            self.action_recorder.clear_actions()

            # 如果提供了测试用例路径，设置locator生成器
            if testcase_path:
                self.setup_locator_generator(testcase_path)

            # 启动菜单监听器
            if not self.menu_listener.start_listener():
                print("[WARNING] 菜单监听器启动失败，菜单控件识别可能受影响", file=sys.stderr)
            else:
                if self.debug:
                    print("[DEBUG] ✅ 菜单监听器启动成功", file=sys.stderr)

            # 启动事件捕获
            if not self.event_capture.start():
                print("[ERROR] 启动事件捕获失败", file=sys.stderr)
                # 如果事件捕获失败，也要停止菜单监听器
                self.menu_listener.stop_listener()
                return False

            # 启动事件处理线程
            self.stop_event.clear()
            self.event_processor_thread = threading.Thread(
                target=self._process_events,
                daemon=True
            )
            self.event_processor_thread.start()

            # 启动命令监听线程
            if self.debug:
                print("[DEBUG] 准备启动命令监听线程", file=sys.stderr)

            self.command_listener_thread = threading.Thread(
                target=self._listen_for_commands,
                daemon=True
            )
            self.command_listener_thread.start()

            if self.debug:
                print(f"[DEBUG] 命令监听线程已启动，线程ID: {self.command_listener_thread.ident}", file=sys.stderr)

            # 启动控件分析线程池
            from concurrent.futures import ThreadPoolExecutor
            self.widget_analysis_executor = ThreadPoolExecutor(max_workers=2)

            self.is_recording = True

            print(f"[INFO] 开始录制会话: {session_id}", file=sys.stderr)
            if test_case_id:
                print(f"[INFO] 关联测试用例: {test_case_id}", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 开始录制失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return False

    def stop_recording(self) -> Optional[str]:
        """
        停止录制

        Returns:
            录制会话ID，如果失败则返回None
        """
        if not self.is_recording:
            print("[WARNING] 当前没有进行录制", file=sys.stderr)
            return None

        try:
            self.is_recording = False

            # 停止菜单监听器
            self.menu_listener.stop_listener()
            if self.debug:
                print("[DEBUG] 菜单监听器已停止", file=sys.stderr)

            # 停止事件捕获
            self.event_capture.stop()

            # 停止事件处理线程
            self.stop_event.set()
            if self.event_processor_thread:
                self.event_processor_thread.join(timeout=5)

            # 关闭控件分析线程池
            if self.widget_analysis_executor:
                self.widget_analysis_executor.shutdown(wait=True)
                self.widget_analysis_executor = None

            # 完成当前会话
            if self.current_session:
                self.current_session.end_time = time.time()

                # 获取记录的动作
                recorded_actions = self.action_recorder.get_recorded_actions()

                # 将动作分类存储到会话中
                for action in recorded_actions:
                    if action['type'] in ['mouse_click', 'mouse_scroll', 'mouse_drag']:
                        # 创建鼠标事件对象
                        if action['type'] == 'mouse_drag':
                            mouse_event = MouseEvent(
                                timestamp=action['timestamp'],
                                event_type='drag_end',
                                x=action['end_x'],
                                y=action['end_y'],
                                button=action.get('button'),
                                pressed=False,
                                drag_start_x=action.get('start_x'),
                                drag_start_y=action.get('start_y'),
                                drag_distance=action.get('distance'),
                                drag_duration=action.get('duration'),
                                widget_info=action.get('widget_info')
                            )
                        else:
                            mouse_event = MouseEvent(
                                timestamp=action['timestamp'],
                                event_type='click' if action['type'] == 'mouse_click' else 'scroll',
                                x=action['x'],
                                y=action['y'],
                                button=action.get('button'),
                                pressed=True if action['type'] == 'mouse_click' else None,
                                scroll_dx=action.get('scroll_dx'),
                                scroll_dy=action.get('scroll_dy'),
                                widget_info=action.get('widget_info')
                            )
                        self.current_session.mouse_events.append(mouse_event)

                    elif action['type'] == 'key_press':
                        # 创建键盘事件对象
                        keyboard_event = KeyboardEvent(
                            timestamp=action['timestamp'],
                            event_type='press',
                            key=action['key'],
                            modifiers=action.get('modifiers')
                        )
                        self.current_session.keyboard_events.append(keyboard_event)

                # 保存会话
                session_id = self.current_session.session_id
                if self.storage.save_session(self.current_session):
                    print(f"[INFO] 录制会话已保存: {session_id}", file=sys.stderr)
                    print(f"[INFO] 录制时长: {self.current_session.end_time - self.current_session.start_time:.2f}秒", file=sys.stderr)
                    print(f"[INFO] 鼠标事件数: {len(self.current_session.mouse_events)}", file=sys.stderr)
                    print(f"[INFO] 键盘事件数: {len(self.current_session.keyboard_events)}", file=sys.stderr)

                    # 打印事件统计信息，用于诊断重复事件问题
                    if self.event_capture:
                        self.event_capture.print_event_stats()
                else:
                    print("[ERROR] 保存录制会话失败", file=sys.stderr)

                self.current_session = None
                return session_id

            return None

        except Exception as e:
            print(f"[ERROR] 停止录制失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return None

    def pause_recording(self) -> bool:
        """
        暂停录制

        Returns:
            是否成功暂停录制
        """
        if not self.is_recording:
            print("[WARNING] 录制未在进行中，无法暂停", file=sys.stderr)
            return False

        if self.is_paused:
            print("[WARNING] 录制已处于暂停状态", file=sys.stderr)
            return False

        try:
            if self.debug:
                print("[DEBUG] 开始暂停录制...", file=sys.stderr)

            # 暂停事件捕获（停止监听全局事件）
            if self.event_capture:
                self.event_capture.stop()
                if self.debug:
                    print("[DEBUG] 事件捕获已暂停", file=sys.stderr)

            # 暂停悬停检测
            if self.hover_detector:
                self.hover_detector.stop()
                if self.debug:
                    print("[DEBUG] 悬停检测已暂停", file=sys.stderr)

            # 强制清除所有高亮显示
            self._force_clear_all_highlights()
            if self.debug:
                print("[DEBUG] 强制清除所有高亮显示", file=sys.stderr)

            # 设置暂停状态（这个要在最后设置，确保上面的操作都完成了）
            self.is_paused = True

            if self.debug:
                print("[DEBUG] 录制已暂停", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 暂停录制失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return False

    def resume_recording(self) -> bool:
        """
        恢复录制

        Returns:
            是否成功恢复录制
        """
        if not self.is_recording:
            print("[WARNING] 录制未在进行中，无法恢复", file=sys.stderr)
            return False

        if not self.is_paused:
            print("[WARNING] 录制未处于暂停状态", file=sys.stderr)
            return False

        try:
            if self.debug:
                print("[DEBUG] 开始恢复录制...", file=sys.stderr)

            # 恢复事件捕获（重新开始监听全局事件）
            if self.event_capture:
                if not self.event_capture.start():
                    print("[WARNING] 恢复事件捕获失败，可能是input不可用", file=sys.stderr)
                    # 不返回False，继续执行其他恢复操作
                else:
                    if self.debug:
                        print("[DEBUG] 事件捕获已恢复", file=sys.stderr)

            # 恢复悬停检测
            if self.hover_detector:
                self.hover_detector.start()
                if self.debug:
                    print("[DEBUG] 悬停检测已恢复", file=sys.stderr)

            # 发送JSON事件通知前端恢复状态
            if self.json_output:
                output_json_event('highlight', {'action': 'resume', 'message': '高亮功能已恢复'})
                if self.debug:
                    print("[DEBUG] 已发送高亮恢复通知", file=sys.stderr)

            # 清除暂停状态（这个要在最后设置，确保上面的操作都完成了）
            self.is_paused = False

            if self.debug:
                print("[DEBUG] 录制已恢复", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 恢复录制失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)
            return False

    def _process_events(self):
        """
        事件处理线程主函数
        处理事件队列中的事件
        """
        if self.debug:
            print("[DEBUG] 事件处理线程已启动", file=sys.stderr)

        while not self.stop_event.is_set():
            try:
                # 从队列中获取事件，设置超时避免阻塞
                try:
                    event_type, event = self.event_queue.get(timeout=0.1)
                except queue.Empty:
                    continue

                # 如果录制已暂停，跳过事件处理但仍需标记任务完成
                if self.is_paused:
                    self.event_queue.task_done()
                    continue

                if event_type == 'mouse':
                    self._handle_mouse_event(event)
                elif event_type == 'keyboard':
                    self._handle_keyboard_event(event)

                # 标记任务完成
                self.event_queue.task_done()

            except Exception as e:
                print(f"[ERROR] 处理事件时发生错误: {e}", file=sys.stderr)
                if self.debug:
                    traceback.print_exc(file=sys.stderr)

        if self.debug:
            print("[DEBUG] 事件处理线程已停止", file=sys.stderr)

    def _listen_for_commands(self):
        """
        监听stdin命令的线程函数
        """
        if self.debug:
            print("[DEBUG] 命令监听线程已启动", file=sys.stderr)

        import select
        import sys as stdin_sys

        while not self.stop_event.is_set():
            try:
                # 使用select检查stdin是否有数据，超时0.1秒
                ready, _, _ = select.select([stdin_sys.stdin], [], [], 0.1)
                if ready:
                    command = stdin_sys.stdin.readline().strip()
                    if command:
                        if self.debug:
                            print(f"[DEBUG] stdin接收到数据: '{command}'", file=sys.stderr)
                        self._handle_command(command)
                    else:
                        if self.debug:
                            print("[DEBUG] stdin有数据但为空行", file=sys.stderr)

            except Exception as e:
                if self.debug:
                    print(f"[DEBUG] 命令监听异常: {e}", file=sys.stderr)
                # 在异常情况下稍作等待
                time.sleep(0.1)

        if self.debug:
            print("[DEBUG] 命令监听线程已停止", file=sys.stderr)

    def _handle_command(self, command: str):
        """
        处理接收到的命令

        Args:
            command: 接收到的命令字符串
        """
        try:
            if self.debug:
                print(f"[DEBUG] 收到命令: {command}", file=sys.stderr)

            if command == 'pause':
                if self.debug:
                    print(f"[DEBUG] 处理暂停命令，当前状态: is_recording={self.is_recording}, is_paused={self.is_paused}", file=sys.stderr)

                if self.pause_recording():
                    if self.json_output:
                        output_json_event('status', {'message': '录制已暂停', 'is_paused': True})
                    else:
                        print("[INFO] 录制已暂停", file=sys.stderr)

                    if self.debug:
                        print(f"[DEBUG] 暂停成功，新状态: is_recording={self.is_recording}, is_paused={self.is_paused}", file=sys.stderr)
                else:
                    if self.debug:
                        print(f"[DEBUG] 暂停失败，状态: is_recording={self.is_recording}, is_paused={self.is_paused}", file=sys.stderr)

            elif command == 'resume':
                if self.debug:
                    print(f"[DEBUG] 处理恢复命令，当前状态: is_recording={self.is_recording}, is_paused={self.is_paused}", file=sys.stderr)

                if self.resume_recording():
                    if self.json_output:
                        output_json_event('status', {'message': '录制已恢复', 'is_paused': False})
                    else:
                        print("[INFO] 录制已恢复", file=sys.stderr)

                    if self.debug:
                        print(f"[DEBUG] 恢复成功，新状态: is_recording={self.is_recording}, is_paused={self.is_paused}", file=sys.stderr)
                else:
                    if self.debug:
                        print(f"[DEBUG] 恢复失败，状态: is_recording={self.is_recording}, is_paused={self.is_paused}", file=sys.stderr)

            elif command == 'stop':
                if self.debug:
                    print("[DEBUG] 收到停止命令", file=sys.stderr)
                self.stop_event.set()

            elif command == 'status':
                status = self.get_recording_status()
                if self.json_output:
                    output_json_event('status', status)
                else:
                    print(f"[INFO] 录制状态: {status}", file=sys.stderr)

            else:
                if self.debug:
                    print(f"[DEBUG] 未知命令: {command}", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 处理命令失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)

    def _handle_mouse_event(self, event: MouseEvent):
        """
        处理鼠标事件

        Args:
            event: 鼠标事件对象
        """
        try:
            # 处理各种鼠标事件
            if event.event_type in ['click', 'right_click', 'double_click'] and event.pressed:
                # 点击事件：记录动作
                self.action_recorder.record_mouse_action(event)

                # 生成locator文件
                widget_info = getattr(event, 'widget_info', None)
                if widget_info and not widget_info.get('error'):
                    self.generate_locator_from_widget_info(widget_info)

                # 如果启用JSON输出模式，也要输出JSON事件
                if self.json_output:
                    is_valid = widget_info is not None and not widget_info.get('error')
                    self._output_mouse_click_json(event, widget_info, is_valid)
            elif event.event_type == 'scroll':
                # 处理滚轮事件
                self.action_recorder.record_mouse_action(event)
            elif event.event_type == 'drag_end':
                # 处理拖动结束事件
                print(f"[INFO] 处理拖动结束事件: 起始({event.drag_start_x}, {event.drag_start_y}) 结束({event.x}, {event.y})", file=sys.stderr)
                self.action_recorder.record_mouse_action(event)

                # 如果启用JSON输出模式，输出拖动事件
                if self.json_output:
                    self._output_mouse_drag_json(event)

            elif event.event_type == 'drag_start':
                # 处理拖动开始事件
                print(f"[INFO] 处理拖动开始事件: 位置=({event.x}, {event.y})", file=sys.stderr)
            elif event.event_type == 'hover':
                # 处理悬停事件
                print(f"[INFO] 处理悬停事件: 位置=({event.x}, {event.y})", file=sys.stderr)
                self.action_recorder.record_mouse_action(event)

                # 如果启用JSON输出模式，输出悬停事件
                if self.json_output:
                    widget_info = getattr(event, 'widget_info', None)
                    is_valid = widget_info is not None and not widget_info.get('error')
                    self._output_mouse_hover_json(event, widget_info, is_valid)
            else:
                # 忽略其他鼠标事件（如释放事件）
                if self.debug and event.event_type == 'click' and not event.pressed:
                    print(f"[DEBUG] 🚫 忽略鼠标释放事件: 位置=({event.x}, {event.y})", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 处理鼠标事件时发生错误: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)

    def _analyze_widget_with_timeout(self, x: int, y: int, timeout: float = 5.0) -> Tuple[Optional[Dict[str, Any]], str]:
        """
        带超时机制的控件分析

        Args:
            x: 横坐标
            y: 纵坐标
            timeout: 超时时间（秒）

        Returns:
            控件信息和描述文本的元组
        """
        import concurrent.futures

        def analyze_task():
            return self.widget_analyzer.analyze_widget_at(x, y)

        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(analyze_task)
                try:
                    widget_info, info_text = future.result(timeout=timeout)
                    return widget_info, info_text
                except concurrent.futures.TimeoutError:
                    error_msg = f"控件分析超时（{timeout}秒）"
                    if self.debug:
                        print(f"[WARNING] {error_msg}: 坐标({x}, {y})", file=sys.stderr)
                    return None, error_msg
        except Exception as e:
            error_msg = f"控件分析异常: {e}"
            if self.debug:
                print(f"[ERROR] {error_msg}", file=sys.stderr)
            return None, error_msg

    def _analyze_widget_with_timeout_new_app(self, x: int, y: int, timeout: float = 3.0) -> Tuple[Optional[Dict[str, Any]], str]:
        """
        带超时机制的新应用检测控件分析

        Args:
            x: 横坐标
            y: 纵坐标
            timeout: 超时时间（秒）

        Returns:
            控件信息和描述文本的元组
        """
        import concurrent.futures

        def analyze_task():
            return self.widget_analyzer.analyze_widget_at_with_new_app_detection(x, y)

        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(analyze_task)
                try:
                    widget_info, info_text = future.result(timeout=timeout)
                    return widget_info, info_text
                except concurrent.futures.TimeoutError:
                    error_msg = f"新应用检测控件分析超时（{timeout}秒）"
                    if self.debug:
                        print(f"[WARNING] {error_msg}: 坐标({x}, {y})", file=sys.stderr)
                    return None, error_msg
        except Exception as e:
            error_msg = f"新应用检测控件分析异常: {e}"
            if self.debug:
                print(f"[ERROR] {error_msg}", file=sys.stderr)
            return None, error_msg



    def _analyze_and_record_mouse_click(self, event: MouseEvent):
        """
        分析鼠标点击并记录（可能在后台线程中执行）

        Args:
            event: 鼠标事件对象
        """
        try:
            # 获取控件信息（添加超时机制）
            widget_info, info_text = self._analyze_widget_with_timeout(event.x, event.y, timeout=5.0)

            # 如果正常识别失败，尝试新应用检测（也添加超时）
            if not widget_info or widget_info.get('error'):
                if self.debug:
                    print(f"[DEBUG] 🔄 点击控件识别失败，启用新应用检测", file=sys.stderr)
                widget_info, info_text = self._analyze_widget_with_timeout_new_app(event.x, event.y, timeout=3.0)

            # 验证控件信息的有效性
            is_valid_widget = self._validate_widget_info(widget_info, event.x, event.y)

            # 将控件信息添加到事件中
            event.widget_info = widget_info if is_valid_widget else None

            # 记录动作
            self.action_recorder.record_mouse_action(event)

            # 如果启用JSON输出模式，立即输出事件
            if self.json_output:
                self._output_mouse_click_json(event, widget_info, is_valid_widget)

            if self.debug:
                if widget_info and not widget_info.get('error'):
                    widget_name = widget_info.get('Name', 'Unknown')
                    score = widget_info.get('score', 'N/A')
                    valid_status = "有效" if is_valid_widget else "无效"
                    print(f"[DEBUG] 鼠标点击控件: {widget_name} (得分: {score}, {valid_status}) 位置=({event.x}, {event.y})", file=sys.stderr)

                    if not is_valid_widget:
                        print(f"[DEBUG] 控件被标记为无效，可能是负分或其他问题", file=sys.stderr)
                else:
                    print(f"[DEBUG] 鼠标点击未识别控件: 位置=({event.x}, {event.y}) 原因={info_text}", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 分析鼠标点击时发生错误: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)

            # 即使分析失败，也要记录基本的点击事件
            self.action_recorder.record_mouse_action(event)

    def _output_mouse_click_json(self, event: MouseEvent, widget_info: Optional[Dict[str, Any]], is_valid: bool):
        """输出鼠标点击事件的JSON格式数据"""
        try:
            # 调试信息：确认方法被调用
            print(f"[DEBUG] 准备输出{event.event_type}JSON: 位置({event.x}, {event.y}) 按钮={event.button}", file=sys.stderr)

            # 构建事件数据
            event_data = {
                'event_type': event.event_type,  # 支持 'click', 'right_click', 'double_click'
                'timestamp': event.timestamp,
                'x': event.x,
                'y': event.y,
                'button': event.button,
                'pressed': event.pressed,
                'widget_info': None
            }

            # 如果是双击事件，添加双击相关信息
            if event.event_type == 'double_click':
                event_data['click_count'] = event.click_count
                event_data['time_since_last_click'] = event.time_since_last_click

            # 如果有控件信息，添加完整的控件信息到事件中
            if widget_info and isinstance(widget_info, dict):
                # 构建完整的控件信息 - 保持正确的字段格式（首字母大写）
                complete_widget_info = {
                    # 基本信息 - 保持UNI标准字段格式
                    'Name': widget_info.get('Name', ''),
                    'Rolename': widget_info.get('Rolename', ''),
                    'Description': widget_info.get('Description', ''),
                    'Key': widget_info.get('Key', ''),

                    # 位置和大小信息
                    'Coords': widget_info.get('Coords', {}),
                    'RecordPosition': widget_info.get('RecordPosition', []),

                    # 进程和窗口信息
                    'ProcessName': widget_info.get('ProcessName', ''),
                    'ProcessID': widget_info.get('ProcessID', ''),
                    'WindowName': widget_info.get('WindowName', ''),
                    'WindowRoleName': widget_info.get('WindowRoleName', ''),
                    'WindowChildCount': widget_info.get('WindowChildCount', 0),

                    # 状态和操作信息
                    'States': widget_info.get('States', []),
                    'Actions': widget_info.get('Actions', []),

                    # 捕获状态和评分
                    'capture_status': widget_info.get('capture_status', 'unknown'),
                    'score': widget_info.get('score', 0),
                    'note': widget_info.get('note', ''),

                    # 层级信息 - 保持UNI标准字段格式
                    'ParentPath': widget_info.get('ParentPath', []),
                    'ParentCount': widget_info.get('ParentCount', 0),
                    'Index_in_parent': widget_info.get('Index_in_parent', 'N/A'),
                    'ChildrenCount': widget_info.get('ChildrenCount', 0),

                    # 文本信息
                    'Text': widget_info.get('Text', ''),

                    # 其他UNI字段
                    'ID': widget_info.get('ID', -1),
                    'MenuElement': widget_info.get('MenuElement', ''),

                    # 是否有效
                    'is_valid': is_valid,

                    # 错误信息（如果有）
                    'error': widget_info.get('error', None)
                }

                # 添加driver字段，根据ProcessName匹配app_menu.py中的exec值
                process_name = widget_info.get('ProcessName', '')
                driver = self.get_driver_from_process_name(process_name)
                complete_widget_info['driver'] = driver

                # 只有在没有错误时才标记为有效
                if not widget_info.get('error') and is_valid:
                    complete_widget_info['is_valid'] = True
                else:
                    complete_widget_info['is_valid'] = False
                    if widget_info.get('error'):
                        complete_widget_info['error'] = widget_info.get('error')

                event_data['widget_info'] = complete_widget_info

            # 根据事件类型输出不同的JSON事件
            json_event_type = 'mouse_click'
            if event.event_type == 'right_click':
                json_event_type = 'mouse_right_click'
            elif event.event_type == 'double_click':
                json_event_type = 'mouse_double_click'

            # 输出JSON事件
            output_json_event(json_event_type, event_data)

            # 调试信息：确认JSON输出成功
            print(f"[DEBUG] 成功输出{json_event_type}JSON: 位置({event.x}, {event.y}) 控件={event_data['widget_info']['name'] if event_data['widget_info'] and event_data['widget_info'].get('name') else '无'}", file=sys.stderr)

        except Exception as e:
            if self.debug:
                print(f"[ERROR] 输出鼠标点击JSON失败: {e}", file=sys.stderr)
            # 即使出错也要输出基本的点击事件
            try:
                # 确定事件类型
                json_event_type = 'mouse_click'
                if event.event_type == 'right_click':
                    json_event_type = 'mouse_right_click'
                elif event.event_type == 'double_click':
                    json_event_type = 'mouse_double_click'

                basic_event_data = {
                    'event_type': event.event_type,
                    'timestamp': event.timestamp,
                    'x': event.x,
                    'y': event.y,
                    'button': event.button,
                    'pressed': event.pressed,
                    'widget_info': {'error': f'JSON输出失败: {e}', 'is_valid': False}
                }
                output_json_event(json_event_type, basic_event_data)
            except:
                pass

    def _output_mouse_drag_json(self, event: MouseEvent):
        """输出鼠标拖动事件的JSON格式数据"""
        try:
            event_data = {
                'event_type': 'mouse_drag',
                'timestamp': event.timestamp,
                'start_x': event.drag_start_x,
                'start_y': event.drag_start_y,
                'end_x': event.x,
                'end_y': event.y,
                'button': event.button,
                'distance': event.drag_distance,
                'duration': event.drag_duration,
                'path_points': len(event.drag_path) if event.drag_path else 0
            }

            # 添加控件信息 - 保持UNI标准字段格式
            if event.widget_info:
                # 添加driver字段，根据ProcessName匹配app_menu.py中的exec值
                process_name = event.widget_info.get('ProcessName', '')
                driver = self.get_driver_from_process_name(process_name)

                event_data['widget_info'] = {
                    'Name': event.widget_info.get('Name', ''),
                    'Rolename': event.widget_info.get('Rolename', ''),
                    'ProcessName': process_name,
                    'WindowName': event.widget_info.get('WindowName', ''),
                    'Coords': event.widget_info.get('Coords', {}),
                    'driver': driver,
                    'is_valid': not event.widget_info.get('error', False)
                }

            # 输出JSON事件
            output_json_event('mouse_drag', event_data)

            if self.debug:
                print(f"[DEBUG] 成功输出拖动JSON: 起始({event.drag_start_x}, {event.drag_start_y}) 结束({event.x}, {event.y})", file=sys.stderr)

        except Exception as e:
            if self.debug:
                print(f"[ERROR] 输出拖动JSON失败: {e}", file=sys.stderr)

    def _output_mouse_hover_json(self, event: MouseEvent, widget_info: Optional[Dict[str, Any]], is_valid: bool):
        """输出鼠标悬停事件的JSON格式数据"""
        try:
            # 调试信息：确认方法被调用
            print(f"[DEBUG] 准备输出悬停JSON: 位置({event.x}, {event.y})", file=sys.stderr)

            # 构建事件数据
            event_data = {
                'event_type': 'hover',
                'timestamp': event.timestamp,
                'x': event.x,
                'y': event.y,
                'button': getattr(event, 'button', 'none'),
                'widget_info': None
            }

            # 添加控件信息
            if widget_info and is_valid:
                # 构建完整的控件信息 - 保持UNI标准字段格式
                complete_widget_info = {
                    'Name': widget_info.get('Name', ''),
                    'Rolename': widget_info.get('Rolename', ''),  # 修复：使用Rolename而不是Role
                    'Description': widget_info.get('Description', ''),
                    'ClassName': widget_info.get('ClassName', ''),
                    'AutomationId': widget_info.get('AutomationId', ''),
                    'ControlType': widget_info.get('ControlType', ''),
                    'is_valid': True,
                    'capture_status': widget_info.get('capture_status', ''),
                    'score': widget_info.get('score', 0),
                    'ProcessName': widget_info.get('ProcessName', ''),
                    'ProcessID': widget_info.get('ProcessID', 0),  # 修复：使用ProcessID
                    'WindowName': widget_info.get('WindowName', ''),
                    'WindowRoleName': widget_info.get('WindowRoleName', ''),  # 修复：使用WindowRoleName
                    'WindowChildCount': widget_info.get('WindowChildCount', 0),
                    'States': widget_info.get('States', []),
                    'Actions': widget_info.get('Actions', []),
                    'Coords': widget_info.get('Coords', {}),
                    'note': widget_info.get('note', ''),

                    # 添加其他UNI标准字段
                    'Key': widget_info.get('Key', ''),
                    'ID': widget_info.get('ID', -1),
                    'Text': widget_info.get('Text', ''),
                    'ParentPath': widget_info.get('ParentPath', []),
                    'ParentCount': widget_info.get('ParentCount', 0),
                    'Index_in_parent': widget_info.get('Index_in_parent', 'N/A'),
                    'ChildrenCount': widget_info.get('ChildrenCount', 0),
                    'RecordPosition': widget_info.get('RecordPosition', []),
                    'MenuElement': widget_info.get('MenuElement', '')
                }

                # 添加driver字段，根据ProcessName匹配app_menu.py中的exec值
                process_name = widget_info.get('ProcessName', '')
                driver = self.get_driver_from_process_name(process_name)
                complete_widget_info['driver'] = driver

                # 如果有错误信息，也要包含
                if widget_info.get('error'):
                    complete_widget_info['error'] = widget_info.get('error')

                event_data['widget_info'] = complete_widget_info
            else:
                # 无效或无控件信息 - 保持UNI标准字段格式
                event_data['widget_info'] = {
                    'Name': '',
                    'Rolename': '',
                    'Description': '',
                    'WindowName': '',
                    'is_valid': False,
                    'error': widget_info.get('error', '无法识别控件') if widget_info else '无控件信息'
                }

            # 输出JSON事件
            output_json_event('mouse_hover', event_data)

            # 调试信息：确认JSON输出成功
            widget_name = event_data['widget_info']['Name'] if event_data['widget_info'] and event_data['widget_info'].get('Name') else '无'
            print(f"[DEBUG] 成功输出悬停JSON: 位置({event.x}, {event.y}) 控件={widget_name}", file=sys.stderr)

        except Exception as e:
            if self.debug:
                print(f"[ERROR] 输出悬停JSON失败: {e}", file=sys.stderr)
            # 即使出错也要输出基本的悬停事件
            try:
                basic_event_data = {
                    'event_type': 'hover',
                    'timestamp': event.timestamp,
                    'x': event.x,
                    'y': event.y,
                    'button': 'none',
                    'widget_info': {'error': f'JSON输出失败: {e}', 'is_valid': False}
                }
                output_json_event('mouse_hover', basic_event_data)
            except:
                pass

    def _output_keyboard_json(self, event: KeyboardEvent, action: Dict[str, Any]):
        """输出键盘事件的JSON格式数据"""
        try:
            # 调试信息：确认方法被调用
            print(f"[DEBUG] 准备输出键盘JSON: 按键={event.key} 修饰键={event.modifiers}", file=sys.stderr)

            # 构建键盘事件数据
            event_data = {
                'event_type': 'key_press',
                'timestamp': event.timestamp,
                'key': event.key,
                'modifiers': event.modifiers or [],
                'key_combination': self._format_key_combination(event.key, event.modifiers)
            }

            # 输出JSON事件
            output_json_event('keyboard', event_data)

            # 调试信息：确认JSON输出成功
            key_combo = event_data['key_combination']
            print(f"[DEBUG] 成功输出键盘JSON: 组合键={key_combo}", file=sys.stderr)

        except Exception as e:
            if self.debug:
                print(f"[ERROR] 输出键盘JSON失败: {e}", file=sys.stderr)
            # 即使出错也要输出基本的键盘事件
            try:
                basic_event_data = {
                    'event_type': 'key_press',
                    'timestamp': event.timestamp,
                    'key': event.key,
                    'modifiers': [],
                    'key_combination': event.key,
                    'error': f'JSON输出失败: {e}'
                }
                output_json_event('keyboard', basic_event_data)
            except:
                pass

    def _format_key_combination(self, key: str, modifiers: Optional[List[str]]) -> str:
        """格式化按键组合为字符串"""
        if not modifiers:
            return key

        # 按标准顺序排列修饰键
        modifier_order = ['ctrl', 'alt', 'shift', 'cmd', 'super']
        sorted_modifiers = []

        for modifier in modifier_order:
            for mod in modifiers:
                if mod.lower() == modifier:
                    sorted_modifiers.append(mod)
                    break

        # 添加其他修饰键
        for mod in modifiers:
            if mod.lower() not in [m.lower() for m in sorted_modifiers]:
                sorted_modifiers.append(mod)

        return '+'.join(sorted_modifiers + [key])

    def _validate_widget_info(self, widget_info: Optional[Dict[str, Any]], x: int, y: int) -> bool:
        """
        验证控件信息的有效性

        Args:
            widget_info: 控件信息字典
            x: 点击的X坐标
            y: 点击的Y坐标

        Returns:
            bool: 控件信息是否有效
        """
        if not widget_info:
            return False

        # 检查是否有错误标记
        if widget_info.get('error'):
            if self.debug:
                print(f"[DEBUG] 控件信息包含错误标记: {widget_info.get('error')}", file=sys.stderr)
            return False

        # 检查得分是否有效
        score = widget_info.get('score')
        if isinstance(score, (int, float)):
            if score < 0:
                if self.debug:
                    print(f"[DEBUG] 控件得分为负数: {score}，坐标=({x}, {y})", file=sys.stderr)
                return False
            elif score == 0:
                if self.debug:
                    print(f"[DEBUG] 控件得分为0: {score}，坐标=({x}, {y})", file=sys.stderr)
                # 得分为0可能是有效的，取决于具体情况
                # 这里可以根据实际需求调整
                pass

        # 检查控件名称是否有效
        name = widget_info.get('Name', '')
        if not name or name.strip() == '':
            if self.debug:
                print(f"[DEBUG] 控件名称为空，坐标=({x}, {y})", file=sys.stderr)
            # 名称为空不一定无效，取决于具体情况
            pass

        # 可以添加更多验证规则
        # 例如：检查控件类型、位置、大小等

        return True

    def _handle_keyboard_event(self, event: KeyboardEvent):
        """
        处理键盘事件

        Args:
            event: 键盘事件对象
        """
        try:
            # 记录键盘动作
            action = self.action_recorder.record_keyboard_action(event)

            # 输出键盘事件给前端（无论是否启用JSON输出模式）
            if action:
                self._output_keyboard_json(event, action)

        except Exception as e:
            print(f"[ERROR] 处理键盘事件时发生错误: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)

    def get_recording_status(self) -> Dict[str, Any]:
        """
        获取录制状态信息

        Returns:
            状态信息字典
        """
        status = {
            'is_recording': self.is_recording,
            'is_paused': self.is_paused,
            'current_session_id': self.current_session.session_id if self.current_session else None,
            'start_time': self.current_session.start_time if self.current_session else None,
            'test_case_id': self.current_session.test_case_id if self.current_session else None,
            'recorded_actions_count': len(self.action_recorder.get_recorded_actions()),
            'input_available': True,
            'uni_available': UNI_AVAILABLE,
            'highlight_available': HIGHLIGHT_AVAILABLE,
            'hover_detection_enabled': hasattr(self, 'hover_detector') and self.hover_detector is not None
        }

        if self.current_session:
            status['recording_duration'] = time.time() - self.current_session.start_time

        return status

    def load_session(self, session_id: str) -> Optional[RecordingSession]:
        """
        加载指定的录制会话

        Args:
            session_id: 会话ID

        Returns:
            录制会话对象或None
        """
        return self.storage.load_session(session_id)

    def cleanup(self):
        """清理资源"""
        if self.is_recording:
            self.stop_recording()

        if self.widget_analysis_executor:
            self.widget_analysis_executor.shutdown(wait=True)

        # 清理悬停检测器
        if hasattr(self, 'hover_detector') and self.hover_detector:
            self.hover_detector.cleanup()


def output_json_event(event_type: str, data: Dict[str, Any]):
    """输出JSON格式的事件到stdout，供VSCode接收"""
    try:
        # 清理数据中的特殊字符，避免JSON序列化问题
        cleaned_data = _clean_data_for_json(data)

        event = {
            'type': event_type,
            'timestamp': time.time(),
            'data': cleaned_data
        }

        # 使用更安全的JSON序列化
        json_str = json.dumps(event, ensure_ascii=False, separators=(',', ':'))
        print(json_str)
        sys.stdout.flush()

    except Exception as e:
        print(f"[ERROR] 输出JSON事件失败: {e}", file=sys.stderr)
        # 尝试输出一个简化的错误事件
        try:
            error_event = {
                'type': 'error',
                'timestamp': time.time(),
                'data': {'error': f'JSON序列化失败: {str(e)}', 'original_type': event_type}
            }
            print(json.dumps(error_event, ensure_ascii=True))
            sys.stdout.flush()
        except:
            pass  # 如果连错误事件都无法输出，就放弃


def _clean_data_for_json(data: Any) -> Any:
    """清理数据中可能导致JSON序列化问题的内容"""
    if isinstance(data, dict):
        cleaned = {}
        for key, value in data.items():
            # 清理键名
            clean_key = _clean_string_for_json(str(key))
            cleaned[clean_key] = _clean_data_for_json(value)
        return cleaned
    elif isinstance(data, list):
        return [_clean_data_for_json(item) for item in data]
    elif isinstance(data, str):
        return _clean_string_for_json(data)
    else:
        return data


def _clean_string_for_json(text: str) -> str:
    """清理字符串中可能导致JSON问题的字符"""
    if not isinstance(text, str):
        return str(text)

    # 移除或替换可能有问题的字符
    # 保留基本的中文字符，但移除一些特殊的控制字符
    cleaned = ""
    for char in text:
        # 保留正常的可打印字符和中文字符
        if (ord(char) >= 32 and ord(char) <= 126) or (ord(char) >= 0x4e00 and ord(char) <= 0x9fff):
            cleaned += char
        elif char in ['\n', '\t', '\r']:
            # 保留基本的换行符等，但转义
            cleaned += char
        else:
            # 替换其他特殊字符为安全字符
            cleaned += '?'

    return cleaned


# 使用示例和测试代码
def main():
    """主函数 - 演示如何使用全自动录制管理器"""
    import argparse
    global password

    parser = argparse.ArgumentParser(description='全自动鼠标键盘事件录制器')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--test-case-id', type=str, help='测试用例ID')
    parser.add_argument('--app-name', type=str, help='录制的应用名称')
    parser.add_argument('--storage-path', type=str, default='recordings', help='录制数据存储路径')
    parser.add_argument('--duration', type=int, default=300, help='录制时长（秒）')
    parser.add_argument('--json-output', action='store_true', help='启用JSON事件输出模式')
    parser.add_argument('--testcase-path', type=str, help='测试用例路径（用于加载对应的app_menu.py）')
    parser.add_argument('--password', type=str, help='用户密码）')

    args = parser.parse_args()

    if args.password:
        password = args.password
        if args.debug:
            print(f"[DEBUG] 设置密码: {password}", file=sys.stderr)
    else:
        print("[ERROR] 未提供密码", file=sys.stderr)
        exit(0)

    # 创建录制管理器
    manager = AutoRecordingManager(
        storage_path=args.storage_path,
        debug=args.debug,
        json_output=args.json_output
    )

    # 如果提供了testcase路径，在初始化后立即设置
    if args.testcase_path:
        if args.debug:
            print(f"[DEBUG] 设置testcase路径: {args.testcase_path}", file=sys.stderr)
        manager.setup_locator_generator(args.testcase_path)

    try:
        # 检查依赖
        status = manager.get_recording_status()

        # 如果启用JSON输出模式，输出状态信息
        if args.json_output:
            output_json_event('status', {
                'message': '录制器初始化完成',
                'storage_path': args.storage_path,
                'debug': args.debug,
                'duration': args.duration,
                'test_case_id': args.test_case_id,
                'input_available': status['input_available'],
                'uni_available': status['uni_available'],
                'highlight_available': status['highlight_available'],
                'hover_detection_enabled': status['hover_detection_enabled']
            })
        else:
            print("=== 全自动鼠标键盘事件录制器 ===")
            print(f"存储路径: {args.storage_path}")
            print(f"调试模式: {args.debug}")
            print(f"录制时长: {args.duration}秒")
            if args.test_case_id:
                print(f"测试用例ID: {args.test_case_id}")
            print()

            print("依赖检查:")
            print(f"  input可用: {status['input_available']}")
            print(f"  UNI模块可用: {status['uni_available']}")
            print(f"  高亮显示可用: {status['highlight_available']}")
            print(f"  悬停检测启用: {status['hover_detection_enabled']}")
            print()

        if not status['input_available']:
            error_msg = "input库不可用，无法进行录制"
            if args.json_output:
                output_json_event('error', {'message': error_msg})
            else:
                print(f"[ERROR] {error_msg}")

            # 即使input不可用，也启动命令监听以支持暂停/恢复控制
            if args.debug:
                print("[DEBUG] 启动命令监听模式（仅支持控制命令）", file=sys.stderr)

            # 设置录制状态为True，以支持暂停/恢复命令
            manager.is_recording = True
            if args.debug:
                print("[DEBUG] 设置录制状态为True以支持暂停/恢复", file=sys.stderr)

            # 启动命令监听线程
            if args.debug:
                print("[DEBUG] 准备启动命令监听线程", file=sys.stderr)

            manager.command_listener_thread = threading.Thread(
                target=manager._listen_for_commands,
                daemon=True
            )
            manager.command_listener_thread.start()

            if args.debug:
                print(f"[DEBUG] 命令监听线程已启动，线程ID: {manager.command_listener_thread.ident}", file=sys.stderr)

            # 等待命令或超时
            try:
                start_time = time.time()
                while time.time() - start_time < args.duration:
                    time.sleep(1)
                    if manager.stop_event.is_set():
                        break
            except KeyboardInterrupt:
                if args.debug:
                    print("[DEBUG] 收到键盘中断信号", file=sys.stderr)

            # 清理资源
            manager.stop_event.set()
            if args.debug:
                print("[DEBUG] 程序结束", file=sys.stderr)
            return

        # 开始录制
        if args.json_output:
            output_json_event('status', {'message': '开始录制'})
        else:
            print("开始录制...")
            print("请在桌面上进行鼠标点击和键盘操作")
            print("录制过程中会自动获取点击位置的控件信息")
            if status['hover_detection_enabled']:
                print("✨ 悬停检测已启用：鼠标悬停0.5秒后会自动识别控件并高亮显示")
            print()

        if manager.start_recording(args.test_case_id, args.testcase_path):
            # 录制指定时长
            start_time = time.time()
            if args.json_output:
                output_json_event('recording_started', {
                    'duration': args.duration,
                    'start_time': start_time,
                    'expected_end_time': start_time + args.duration
                })
            else:
                print(f"[INFO] 开始录制，预计持续 {args.duration} 秒")
                print(f"[INFO] 开始时间: {datetime.fromtimestamp(start_time).strftime('%H:%M:%S')}")
                print(f"[INFO] 预计结束时间: {datetime.fromtimestamp(start_time + args.duration).strftime('%H:%M:%S')}")

            for i in range(args.duration):
                time.sleep(1)
                current_time = time.time()
                elapsed = current_time - start_time
                remaining = args.duration - i - 1

                if args.debug and i % 5 == 0:
                    status = manager.get_recording_status()
                    if args.json_output:
                        output_json_event('recording_progress', {
                            'elapsed_seconds': int(elapsed),
                            'remaining_seconds': remaining,
                            'progress_percent': round((i + 1) / args.duration * 100, 1),
                            'recorded_actions_count': status['recorded_actions_count']
                        })
                    else:
                        print(f"[DEBUG] 录制进行中... 已录制 {int(elapsed)} 秒，剩余 {remaining} 秒，已录制动作数: {status['recorded_actions_count']}")
                elif not args.debug and i % 30 == 0:  # 非调试模式每30秒输出一次进度
                    if args.json_output:
                        output_json_event('recording_progress', {
                            'elapsed_seconds': int(elapsed),
                            'remaining_seconds': remaining,
                            'progress_percent': round((i + 1) / args.duration * 100, 1)
                        })
                    else:
                        print(f"[INFO] 录制进度: {int(elapsed)}/{args.duration} 秒 ({round((i + 1) / args.duration * 100, 1)}%)")

            # 停止录制
            if args.json_output:
                output_json_event('status', {'message': '停止录制'})
            else:
                print("\n停止录制...")

            session_id = manager.stop_recording()

            if session_id:
                if args.json_output:
                    output_json_event('recording_complete', {
                        'session_id': session_id,
                        'message': '录制完成'
                    })
                else:
                    print(f"录制完成！会话ID: {session_id}")

                # 加载并显示录制结果
                session = manager.load_session(session_id)
                if session:
                    if args.json_output:
                        output_json_event('recording_result', {
                            'session_id': session.session_id,
                            'start_time': session.start_time,
                            'end_time': session.end_time,
                            'test_case_id': session.test_case_id,
                            'mouse_events_count': len(session.mouse_events),
                            'keyboard_events_count': len(session.keyboard_events)
                        })
                    else:
                        print(f"\n录制结果:")
                        print(f"  会话ID: {session.session_id}")
                        print(f"  开始时间: {datetime.fromtimestamp(session.start_time)}")
                        print(f"  结束时间: {datetime.fromtimestamp(session.end_time) if session.end_time else 'N/A'}")
                        print(f"  测试用例ID: {session.test_case_id or 'N/A'}")
                        print(f"  鼠标事件数: {len(session.mouse_events)}")
                        print(f"  键盘事件数: {len(session.keyboard_events)}")

                        # 显示前几个事件的详细信息
                        if session.mouse_events:
                            print(f"\n前3个鼠标事件:")
                            for i, event in enumerate(session.mouse_events[:3]):
                                widget_name = "未知"
                                if event.widget_info and not event.widget_info.get('error'):
                                    widget_name = event.widget_info.get('Name', '未知')
                                print(f"  {i+1}. {event.event_type} 位置=({event.x}, {event.y}) 控件={widget_name}")

                        if session.keyboard_events:
                            print(f"\n前3个键盘事件:")
                            for i, event in enumerate(session.keyboard_events[:3]):
                                modifiers = '+'.join(event.modifiers) if event.modifiers else ''
                                key_combo = f"{modifiers}+{event.key}" if modifiers else event.key
                                print(f"  {i+1}. {event.event_type} 按键={key_combo}")
            else:
                if args.json_output:
                    output_json_event('error', {'message': '录制失败'})
                else:
                    print("录制失败！")
        else:
            if args.json_output:
                output_json_event('error', {'message': '启动录制失败'})
            else:
                print("启动录制失败！")

    except KeyboardInterrupt:
        print("\n用户中断录制")
        if manager.is_recording:
            session_id = manager.stop_recording()
            if session_id:
                print(f"录制已保存，会话ID: {session_id}")

    except Exception as e:
        print(f"发生错误: {e}")
        if args.debug:
            traceback.print_exc()

    finally:
        # 清理资源
        manager.cleanup()
        print("程序结束")


if __name__ == "__main__":
    main()
