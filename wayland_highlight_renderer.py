#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wayland兼容的高亮渲染器
使用GTK实现跨平台的控件高亮显示功能，支持X11和Wayland环境
"""

import sys
import time
import threading
from typing import Dict, Any, Optional

# 检测显示服务器类型
import os

def detect_display_server():
    """检测当前显示服务器类型"""
    if os.environ.get('WAYLAND_DISPLAY'):
        return 'wayland'
    elif os.environ.get('DISPLAY'):
        return 'x11'
    
    session_type = os.environ.get('XDG_SESSION_TYPE', '').lower()
    if session_type == 'wayland':
        return 'wayland'
    elif session_type == 'x11':
        return 'x11'
    
    return 'x11'  # 默认假设X11

DISPLAY_SERVER = detect_display_server()

# 检查是否有Xlib可用（用于高亮脚本）
XLIB_AVAILABLE = False
try:
    import subprocess
    # 测试是否可以导入Xlib
    result = subprocess.run(['python3', '-c', 'from Xlib import X, display'],
                          capture_output=True, text=True)
    if result.returncode == 0:
        XLIB_AVAILABLE = True
        print(f"[INFO] Xlib库可用，显示服务器: {DISPLAY_SERVER}", file=sys.stderr)
    else:
        print(f"[WARNING] Xlib库不可用", file=sys.stderr)
except Exception as e:
    print(f"[ERROR] 检查Xlib时发生错误: {e}", file=sys.stderr)

# 备用方案：尝试导入Xlib（仅用于X11环境）
XLIB_AVAILABLE = False
if DISPLAY_SERVER == 'x11':
    try:
        from Xlib import X, display, Xutil, Xatom
        XLIB_AVAILABLE = True
        print("[INFO] Xlib库可用作为X11环境的备用方案", file=sys.stderr)
    except ImportError:
        print("[WARNING] Xlib库不可用，将仅使用GTK方案", file=sys.stderr)


class WaylandHighlightRenderer:
    """
    Wayland兼容的高亮渲染器
    基于KylinAutoSDK_UNI的ultimate_highlight实现，使用subprocess调用独立脚本
    """

    def __init__(self, debug=False):
        self.debug = debug
        self.highlight_visible = False
        self.border_width = 3

        # 节流控制
        self.last_update_time = 0
        self.update_throttle = 0.05  # 50ms

        # 当前高亮信息
        self.current_highlight_info = None
        self.highlight_x = 0
        self.highlight_y = 0
        self.highlight_width = 100
        self.highlight_height = 100

        # 当前高亮进程
        self.current_highlight_process = None

        if self.debug:
            print("[DEBUG] Wayland高亮渲染器初始化完成", file=sys.stderr)
    
    def _create_highlight_script(self, x, y, width, height, duration=2, color='red', border_width=2):
        """创建高亮脚本，基于KylinAutoSDK_UNI的ultimate_highlight实现"""
        script = f'''
import os
os.environ["DISPLAY"] = ":0"

try:
    from Xlib import X, display
    import time

    # 连接到X11
    disp = display.Display()
    screen = disp.screen()
    root = screen.root

    # 获取颜色
    colormap = screen.default_colormap
    color_pixel = colormap.alloc_named_color('{color}').pixel

    # 计算边框参数
    border_w = {border_width}
    target_x = {x}
    target_y = {y}
    target_width = {width}
    target_height = {height}

    # 创建四个独立的边框窗口
    windows = []

    # 上边框线
    top_border = root.create_window(
        target_x, target_y, target_width, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,  # 关键：绕过窗口管理器
        colormap=X.CopyFromParent
    )
    windows.append(top_border)

    # 下边框线
    bottom_border = root.create_window(
        target_x, target_y + target_height - border_w, target_width, border_w,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(bottom_border)

    # 左边框线
    left_border = root.create_window(
        target_x, target_y, border_w, target_height,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(left_border)

    # 右边框线
    right_border = root.create_window(
        target_x + target_width - border_w, target_y, border_w, target_height,
        border_width=0,
        depth=screen.root_depth,
        window_class=X.InputOutput,
        visual=X.CopyFromParent,
        background_pixel=color_pixel,
        override_redirect=True,
        colormap=X.CopyFromParent
    )
    windows.append(right_border)

    # 为所有边框窗口设置属性
    for i, win in enumerate(windows):
        win.set_wm_name(f"Ultimate Border {{i+1}}")
        win.set_wm_class("ultimate_border", "UltimateBorder")

        # 设置窗口类型为dock（避免窗口管理器干预）
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_WINDOW_TYPE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_WINDOW_TYPE_DOCK")]
            )
        except:
            pass

        # 设置窗口状态
        try:
            win.change_property(
                disp.intern_atom("_NET_WM_STATE"),
                disp.intern_atom("ATOM"),
                32,
                [disp.intern_atom("_NET_WM_STATE_SKIP_TASKBAR"),
                 disp.intern_atom("_NET_WM_STATE_SKIP_PAGER"),
                 disp.intern_atom("_NET_WM_STATE_ABOVE")]
            )
        except:
            pass

    # 同时显示所有边框
    for win in windows:
        win.map()
    disp.sync()

    # 保持显示指定时间
    time.sleep({duration})

    # 清理所有边框窗口
    for win in windows:
        win.unmap()
        win.destroy()
    disp.sync()
    disp.close()

    exit(0)

except Exception as e:
    exit(1)
'''
        return script
    
    def _execute_highlight_script(self, script):
        """执行高亮脚本"""
        try:
            import subprocess
            # 静默执行脚本
            result = subprocess.run(['python3', '-c', script],
                                  capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 执行高亮脚本失败: {e}", file=sys.stderr)
            return False
    
    def _stop_current_highlight(self):
        """停止当前的高亮进程"""
        if self.current_highlight_process:
            try:
                self.current_highlight_process.terminate()
                self.current_highlight_process.wait(timeout=1)
            except:
                try:
                    self.current_highlight_process.kill()
                except:
                    pass
            finally:
                self.current_highlight_process = None
    
    def highlight_widget(self, x: int, y: int, width: int, height: int, widget_info: dict = None):
        """高亮显示控件"""
        # 节流处理
        current_time = time.time()
        if current_time - self.last_update_time < self.update_throttle:
            return True
        self.last_update_time = current_time

        # 检查是否与当前高亮区域相同
        if (self.highlight_visible and
            self.highlight_x == x and
            self.highlight_y == y and
            self.highlight_width == width and
            self.highlight_height == height):
            return True

        try:
            # 停止当前高亮
            self._stop_current_highlight()

            # 更新高亮信息
            self.highlight_x = x
            self.highlight_y = y
            self.highlight_width = width
            self.highlight_height = height
            self.current_highlight_info = widget_info

            # 创建高亮脚本
            script = self._create_highlight_script(x, y, width, height,
                                                 duration=0.5,  # 短时间高亮
                                                 color='red',
                                                 border_width=self.border_width)

            # 在后台执行高亮脚本
            import subprocess
            self.current_highlight_process = subprocess.Popen(['python3', '-c', script])

            self.highlight_visible = True

            if self.debug:
                print(f"[DEBUG] ✅ 高亮控件: x={x}, y={y}, width={width}, height={height}", file=sys.stderr)

            return True
        except Exception as e:
            print(f"[ERROR] 高亮控件时发生错误: {e}", file=sys.stderr)
            return False
    
    def clear_highlight(self):
        """清除高亮"""
        if not self.highlight_visible:
            return

        try:
            # 停止当前高亮进程
            self._stop_current_highlight()
            self.highlight_visible = False

            if self.debug:
                print("[DEBUG] 高亮已清除", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 清除高亮时发生错误: {e}", file=sys.stderr)
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清除高亮
            self.clear_highlight()

            if self.debug:
                print("[DEBUG] 高亮渲染器资源已清理", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 清理高亮渲染器时发生错误: {e}", file=sys.stderr)


class UnifiedHighlightRenderer:
    """
    统一的高亮渲染器
    根据环境自动选择合适的高亮实现（GTK优先，Xlib备用）
    """

    def __init__(self, debug=False):
        self.debug = debug
        self.renderer = None
        self.renderer_type = None

        # 优先尝试基于subprocess的高亮渲染器（支持Wayland和X11）
        try:
            self.renderer = WaylandHighlightRenderer(debug=debug)
            self.renderer_type = "Subprocess"
            if self.debug:
                print(f"[DEBUG] 使用Subprocess高亮渲染器 (显示服务器: {DISPLAY_SERVER})", file=sys.stderr)
        except Exception as e:
            print(f"[WARNING] Subprocess高亮渲染器初始化失败: {e}", file=sys.stderr)

        # 如果Subprocess渲染器不可用且在X11环境下，尝试使用传统Xlib渲染器
        if not self.renderer and DISPLAY_SERVER == 'x11':
            try:
                # 导入原有的Xlib高亮渲染器
                from widget_capture_module import HighlightRenderer as XlibHighlightRenderer
                self.renderer = XlibHighlightRenderer(debug=debug)
                self.renderer_type = "Xlib"
                if self.debug:
                    print("[DEBUG] 使用传统Xlib高亮渲染器 (X11环境)", file=sys.stderr)
            except Exception as e:
                print(f"[WARNING] 传统Xlib高亮渲染器初始化失败: {e}", file=sys.stderr)

        if not self.renderer:
            print("[ERROR] 无法初始化任何高亮渲染器", file=sys.stderr)

    def highlight_widget(self, x: int, y: int, width: int, height: int, widget_info: dict = None):
        """高亮显示控件"""
        if self.renderer:
            return self.renderer.highlight_widget(x, y, width, height, widget_info)
        return False

    def clear_highlight(self):
        """清除高亮"""
        if self.renderer:
            self.renderer.clear_highlight()

    def cleanup(self):
        """清理资源"""
        if self.renderer:
            self.renderer.cleanup()

    @property
    def highlight_visible(self):
        """获取高亮可见状态"""
        if self.renderer:
            return getattr(self.renderer, 'highlight_visible', False)
        return False
