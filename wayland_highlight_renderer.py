#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wayland兼容的高亮渲染器
使用GTK实现跨平台的控件高亮显示功能，支持X11和Wayland环境
"""

import sys
import time
import threading
from typing import Dict, Any, Optional

# 检测显示服务器类型
import os

def detect_display_server():
    """检测当前显示服务器类型"""
    if os.environ.get('WAYLAND_DISPLAY'):
        return 'wayland'
    elif os.environ.get('DISPLAY'):
        return 'x11'
    
    session_type = os.environ.get('XDG_SESSION_TYPE', '').lower()
    if session_type == 'wayland':
        return 'wayland'
    elif session_type == 'x11':
        return 'x11'
    
    return 'x11'  # 默认假设X11

DISPLAY_SERVER = detect_display_server()

# 尝试导入GTK
GTK_AVAILABLE = False
try:
    import gi
    gi.require_version('Gtk', '3.0')
    gi.require_version('Gdk', '3.0')
    from gi.repository import Gtk, Gdk, GLib, GObject
    GTK_AVAILABLE = True
    print(f"[INFO] GTK库加载成功，显示服务器: {DISPLAY_SERVER}", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] GTK库不可用: {e}", file=sys.stderr)
except Exception as e:
    print(f"[ERROR] GTK初始化失败: {e}", file=sys.stderr)

# 备用方案：尝试导入Xlib（仅用于X11环境）
XLIB_AVAILABLE = False
if DISPLAY_SERVER == 'x11':
    try:
        from Xlib import X, display, Xutil, Xatom
        XLIB_AVAILABLE = True
        print("[INFO] Xlib库可用作为X11环境的备用方案", file=sys.stderr)
    except ImportError:
        print("[WARNING] Xlib库不可用，将仅使用GTK方案", file=sys.stderr)


class WaylandHighlightRenderer:
    """
    Wayland兼容的高亮渲染器
    使用GTK实现跨平台的控件高亮显示功能
    """
    
    def __init__(self, debug=False):
        self.debug = debug
        self.highlight_visible = False
        self.highlight_windows = []
        self.text_window = None
        self.border_width = 3
        self.border_color = (1.0, 0.0, 0.0, 0.8)  # 红色，80%透明度
        self.text_color = (1.0, 1.0, 1.0, 1.0)    # 白色文字
        self.bg_color = (0.0, 0.0, 0.0, 0.7)      # 黑色背景，70%透明度
        
        # 节流控制
        self.last_update_time = 0
        self.update_throttle = 0.05  # 50ms
        
        # 当前高亮信息
        self.current_highlight_info = None
        self.highlight_x = 0
        self.highlight_y = 0
        self.highlight_width = 100
        self.highlight_height = 100
        
        if not GTK_AVAILABLE:
            print("[ERROR] GTK库不可用，无法创建Wayland兼容的高亮窗口", file=sys.stderr)
            return
        
        # 初始化GTK
        self._init_gtk()
        
        if self.debug:
            print("[DEBUG] Wayland高亮渲染器初始化完成", file=sys.stderr)
    
    def _init_gtk(self):
        """初始化GTK环境"""
        try:
            # 确保GTK已初始化
            if not Gtk.init_check():
                print("[ERROR] GTK初始化失败", file=sys.stderr)
                return False
            
            # 创建高亮窗口（四个边框）
            self._create_highlight_windows()
            
            if self.debug:
                print("[DEBUG] GTK环境初始化成功", file=sys.stderr)
            return True
        except Exception as e:
            print(f"[ERROR] GTK初始化时发生错误: {e}", file=sys.stderr)
            return False
    
    def _create_highlight_windows(self):
        """创建高亮边框窗口"""
        try:
            # 创建四个边框窗口：上、下、左、右
            for i in range(4):
                window = Gtk.Window(type=Gtk.WindowType.POPUP)
                window.set_app_paintable(True)
                window.set_visual(window.get_screen().get_rgba_visual())
                
                # 设置窗口属性
                window.set_decorated(False)
                window.set_resizable(False)
                window.set_keep_above(True)
                window.set_accept_focus(False)
                window.set_can_focus(False)
                
                # 设置窗口类型提示
                window.set_type_hint(Gdk.WindowTypeHint.NOTIFICATION)
                window.set_skip_taskbar_hint(True)
                window.set_skip_pager_hint(True)
                
                # 连接绘制事件
                window.connect('draw', self._on_border_draw)
                
                self.highlight_windows.append(window)
            
            # 创建文字标签窗口
            self.text_window = Gtk.Window(type=Gtk.WindowType.POPUP)
            self.text_window.set_app_paintable(True)
            self.text_window.set_visual(self.text_window.get_screen().get_rgba_visual())
            
            # 设置文字窗口属性
            self.text_window.set_decorated(False)
            self.text_window.set_resizable(False)
            self.text_window.set_keep_above(True)
            self.text_window.set_accept_focus(False)
            self.text_window.set_can_focus(False)
            self.text_window.set_type_hint(Gdk.WindowTypeHint.NOTIFICATION)
            self.text_window.set_skip_taskbar_hint(True)
            self.text_window.set_skip_pager_hint(True)
            
            # 创建标签控件
            self.text_label = Gtk.Label()
            self.text_label.set_markup('<span foreground="white" weight="bold">Widget</span>')
            self.text_window.add(self.text_label)
            
            # 连接文字窗口绘制事件
            self.text_window.connect('draw', self._on_text_draw)
            
            if self.debug:
                print("[DEBUG] GTK高亮窗口创建成功", file=sys.stderr)
            
        except Exception as e:
            print(f"[ERROR] 创建GTK高亮窗口时发生错误: {e}", file=sys.stderr)
    
    def _on_border_draw(self, widget, cr):
        """绘制边框"""
        try:
            # 设置颜色和透明度
            cr.set_source_rgba(*self.border_color)
            
            # 获取窗口大小
            allocation = widget.get_allocation()
            width = allocation.width
            height = allocation.height
            
            # 填充整个窗口（作为边框）
            cr.rectangle(0, 0, width, height)
            cr.fill()
            
            return False
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 绘制边框时发生错误: {e}", file=sys.stderr)
            return False
    
    def _on_text_draw(self, widget, cr):
        """绘制文字背景"""
        try:
            # 设置背景颜色
            cr.set_source_rgba(*self.bg_color)
            
            # 获取窗口大小
            allocation = widget.get_allocation()
            width = allocation.width
            height = allocation.height
            
            # 绘制圆角矩形背景
            radius = 5
            cr.new_sub_path()
            cr.arc(width - radius, radius, radius, -90 * (3.14159 / 180), 0 * (3.14159 / 180))
            cr.arc(width - radius, height - radius, radius, 0 * (3.14159 / 180), 90 * (3.14159 / 180))
            cr.arc(radius, height - radius, radius, 90 * (3.14159 / 180), 180 * (3.14159 / 180))
            cr.arc(radius, radius, radius, 180 * (3.14159 / 180), 270 * (3.14159 / 180))
            cr.close_path()
            cr.fill()
            
            return False
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 绘制文字背景时发生错误: {e}", file=sys.stderr)
            return False
    
    def highlight_widget(self, x: int, y: int, width: int, height: int, widget_info: dict = None):
        """高亮显示控件"""
        if not GTK_AVAILABLE or not self.highlight_windows:
            if self.debug:
                print("[DEBUG] GTK不可用或高亮窗口未创建，跳过高亮显示", file=sys.stderr)
            return False
        
        # 节流处理
        current_time = time.time()
        if current_time - self.last_update_time < self.update_throttle:
            return True
        self.last_update_time = current_time
        
        # 检查是否与当前高亮区域相同
        if (self.highlight_visible and
            self.highlight_x == x and
            self.highlight_y == y and
            self.highlight_width == width and
            self.highlight_height == height):
            return True
        
        try:
            # 更新高亮信息
            self.highlight_x = x
            self.highlight_y = y
            self.highlight_width = width
            self.highlight_height = height
            self.current_highlight_info = widget_info
            
            # 使用GLib.idle_add确保在主线程中执行GTK操作
            GLib.idle_add(self._update_highlight_windows)
            
            if self.debug:
                print(f"[DEBUG] ✅ 请求高亮控件: x={x}, y={y}, width={width}, height={height}", file=sys.stderr)
            
            return True
        except Exception as e:
            print(f"[ERROR] 高亮控件时发生错误: {e}", file=sys.stderr)
            return False
    
    def _update_highlight_windows(self):
        """在主线程中更新高亮窗口位置和大小"""
        try:
            x, y, width, height = self.highlight_x, self.highlight_y, self.highlight_width, self.highlight_height
            
            # 确保尺寸有效
            if width <= 0 or height <= 0:
                return False
            
            # 更新四个边框窗口的位置和大小
            # 上边框
            self.highlight_windows[0].move(x, y)
            self.highlight_windows[0].resize(width, self.border_width)
            self.highlight_windows[0].show_all()
            
            # 下边框
            self.highlight_windows[1].move(x, y + height - self.border_width)
            self.highlight_windows[1].resize(width, self.border_width)
            self.highlight_windows[1].show_all()
            
            # 左边框
            self.highlight_windows[2].move(x, y)
            self.highlight_windows[2].resize(self.border_width, height)
            self.highlight_windows[2].show_all()
            
            # 右边框
            self.highlight_windows[3].move(x + width - self.border_width, y)
            self.highlight_windows[3].resize(self.border_width, height)
            self.highlight_windows[3].show_all()
            
            # 显示文字标签
            self._show_widget_label(x, y, width, height)
            
            self.highlight_visible = True
            
            if self.debug:
                print(f"[DEBUG] ✅ GTK高亮窗口已更新", file=sys.stderr)
            
            return False  # 不重复执行
        except Exception as e:
            print(f"[ERROR] 更新GTK高亮窗口时发生错误: {e}", file=sys.stderr)
            return False
    
    def _show_widget_label(self, x: int, y: int, width: int, height: int):
        """显示控件类别文字标签"""
        try:
            if not self.text_window or not self.text_label:
                return
            
            # 生成标签文字
            label_text = self._generate_label_text(self.current_highlight_info)
            
            # 设置标签文字
            markup = f'<span foreground="white" weight="bold" size="small">{label_text}</span>'
            self.text_label.set_markup(markup)
            
            # 计算标签位置（在控件上方）
            label_x = x
            label_y = max(0, y - 30)  # 在控件上方30像素
            
            # 调整标签大小
            self.text_window.resize(len(label_text) * 8 + 20, 25)
            self.text_window.move(label_x, label_y)
            self.text_window.show_all()
            
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 显示控件标签时发生错误: {e}", file=sys.stderr)
    
    def _generate_label_text(self, widget_info: dict) -> str:
        """生成控件标签文字"""
        try:
            if not widget_info:
                return "Widget"
            
            # 获取控件角色
            role = widget_info.get('Role', '').lower()
            
            # 角色翻译映射
            role_translations = {
                'push button': 'Button',
                'button': 'Button',
                'text': 'Text',
                'entry': 'Input',
                'label': 'Label',
                'window': 'Window',
                'frame': 'Frame',
                'panel': 'Panel',
                'menu': 'Menu',
                'menu item': 'MenuItem',
                'list': 'List',
                'list item': 'ListItem',
                'table': 'Table',
                'cell': 'Cell',
                'check box': 'CheckBox',
                'radio button': 'RadioButton',
                'combo box': 'ComboBox',
                'scroll bar': 'ScrollBar',
                'slider': 'Slider',
                'progress bar': 'ProgressBar',
                'tab': 'Tab',
                'tab list': 'TabList',
                'tree': 'Tree',
                'tree item': 'TreeItem',
                'unknown': 'Unknown'
            }
            
            return role_translations.get(role.lower(), role.title() if role else 'Widget')
            
        except Exception as e:
            if self.debug:
                print(f"[DEBUG] 生成标签文字失败: {e}", file=sys.stderr)
            return "Widget"
    
    def clear_highlight(self):
        """清除高亮"""
        if not GTK_AVAILABLE:
            return
        
        if not self.highlight_visible:
            return
        
        try:
            # 使用GLib.idle_add确保在主线程中执行GTK操作
            GLib.idle_add(self._clear_highlight_windows)
            
            if self.debug:
                print("[DEBUG] 请求清除GTK高亮", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 清除高亮时发生错误: {e}", file=sys.stderr)
    
    def _clear_highlight_windows(self):
        """在主线程中清除高亮窗口"""
        try:
            # 隐藏所有边框窗口
            for window in self.highlight_windows:
                if window:
                    window.hide()
            
            # 隐藏文字标签
            if self.text_window:
                self.text_window.hide()
            
            self.highlight_visible = False
            
            if self.debug:
                print("[DEBUG] ✅ GTK高亮已清除", file=sys.stderr)
            
            return False  # 不重复执行
        except Exception as e:
            print(f"[ERROR] 清除GTK高亮窗口时发生错误: {e}", file=sys.stderr)
            return False
    
    def cleanup(self):
        """清理资源"""
        if not GTK_AVAILABLE:
            return
        
        try:
            # 销毁所有窗口
            for window in self.highlight_windows:
                if window:
                    window.destroy()
            
            if self.text_window:
                self.text_window.destroy()
            
            self.highlight_windows.clear()
            self.text_window = None
            
            if self.debug:
                print("[DEBUG] GTK高亮渲染器资源已清理", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] 清理GTK高亮渲染器时发生错误: {e}", file=sys.stderr)


class UnifiedHighlightRenderer:
    """
    统一的高亮渲染器
    根据环境自动选择合适的高亮实现（GTK优先，Xlib备用）
    """

    def __init__(self, debug=False):
        self.debug = debug
        self.renderer = None
        self.renderer_type = None

        # 优先尝试GTK渲染器（支持Wayland和X11）
        if GTK_AVAILABLE:
            try:
                self.renderer = WaylandHighlightRenderer(debug=debug)
                self.renderer_type = "GTK"
                if self.debug:
                    print(f"[DEBUG] 使用GTK高亮渲染器 (显示服务器: {DISPLAY_SERVER})", file=sys.stderr)
            except Exception as e:
                print(f"[WARNING] GTK高亮渲染器初始化失败: {e}", file=sys.stderr)

        # 如果GTK不可用且在X11环境下，尝试使用Xlib渲染器
        if not self.renderer and XLIB_AVAILABLE and DISPLAY_SERVER == 'x11':
            try:
                # 导入原有的Xlib高亮渲染器
                from widget_capture_module import HighlightRenderer as XlibHighlightRenderer
                self.renderer = XlibHighlightRenderer(debug=debug)
                self.renderer_type = "Xlib"
                if self.debug:
                    print("[DEBUG] 使用Xlib高亮渲染器 (X11环境)", file=sys.stderr)
            except Exception as e:
                print(f"[WARNING] Xlib高亮渲染器初始化失败: {e}", file=sys.stderr)

        if not self.renderer:
            print("[ERROR] 无法初始化任何高亮渲染器", file=sys.stderr)

    def highlight_widget(self, x: int, y: int, width: int, height: int, widget_info: dict = None):
        """高亮显示控件"""
        if self.renderer:
            return self.renderer.highlight_widget(x, y, width, height, widget_info)
        return False

    def clear_highlight(self):
        """清除高亮"""
        if self.renderer:
            self.renderer.clear_highlight()

    def cleanup(self):
        """清理资源"""
        if self.renderer:
            self.renderer.cleanup()

    @property
    def highlight_visible(self):
        """获取高亮可见状态"""
        if self.renderer:
            return getattr(self.renderer, 'highlight_visible', False)
        return False
