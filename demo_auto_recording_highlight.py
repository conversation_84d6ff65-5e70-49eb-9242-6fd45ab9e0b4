#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示auto_recording_manager_v11.py中集成的高亮功能
"""

import sys
import time

def demo_highlight_integration():
    """演示高亮功能集成"""
    print("=" * 80)
    print("🎯 auto_recording_manager_v11.py 高亮功能演示")
    print("=" * 80)
    print("💡 本演示将展示集成后的控件识别和自动高亮功能")
    print("📺 请观察屏幕上的红色边框高亮效果")
    print("=" * 80)
    
    try:
        # 导入AutoRecordingManager
        from auto_recording_manager_v11 import AutoRecordingManager
        print("✅ AutoRecordingManager导入成功")
        
        # 创建实例
        manager = AutoRecordingManager(debug=True)
        print("✅ AutoRecordingManager实例创建成功")
        print(f"   高亮功能启用: {manager.widget_analyzer.highlight_enabled}")
        
        # 测试坐标（您提供的坐标）
        test_x, test_y = 1739, 320
        
        print(f"\n📍 开始识别坐标 ({test_x}, {test_y}) 处的控件...")
        print("⏰ 识别成功后将自动显示红色边框高亮（持续2秒）")
        print("=" * 80)
        
        # 执行控件识别（会自动触发高亮）
        result, info_text = manager.widget_analyzer.analyze_widget_at(test_x, test_y)
        
        if result:
            print("🎉 控件识别和高亮成功!")
            print(f"📋 控件名称: {result.get('Name', 'Unknown')}")
            print(f"🏷️  控件角色: {result.get('Rolename', 'Unknown')}")
            print(f"📱 进程名称: {result.get('ProcessName', 'Unknown')}")
            
            coords = result.get('Coords')
            if coords:
                print(f"📍 控件位置: ({coords['x']}, {coords['y']})")
                print(f"📏 控件大小: {coords['width']} x {coords['height']}")
                print("✨ 红色边框高亮已自动显示!")
            
            print("\n" + "=" * 80)
            print("✅ 演示完成!")
            print("=" * 80)
            print("🎯 功能特点:")
            print("   • 控件识别成功后自动显示高亮")
            print("   • 使用KylinAutoSDK_UNI的ultimate_highlight")
            print("   • 红色边框，持续2秒")
            print("   • 支持Wayland和X11环境")
            print("   • 完全透明内部，不遮挡控件内容")
            
            return True
        else:
            print(f"❌ 控件识别失败: {info_text}")
            return False
            
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_multiple_coordinates():
    """演示多个坐标的高亮效果"""
    print("\n" + "=" * 80)
    print("🎨 多坐标高亮演示")
    print("=" * 80)
    
    try:
        from auto_recording_manager_v11 import WidgetAnalyzer
        analyzer = WidgetAnalyzer(debug=True)
        
        # 测试多个坐标
        test_coordinates = [
            (1739, 320, "您提供的坐标"),
            (100, 100, "左上角测试"),
            (500, 300, "中间区域测试"),
            (800, 200, "右侧区域测试")
        ]
        
        for i, (x, y, description) in enumerate(test_coordinates):
            print(f"\n{i+1}. 测试 {description}: ({x}, {y})")
            
            try:
                result, info_text = analyzer.analyze_widget_at(x, y)
                
                if result:
                    coords = result.get('Coords', {})
                    print(f"   ✅ 识别成功: {result.get('Name', 'Unknown')}")
                    print(f"   📍 实际位置: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})")
                    print(f"   📏 大小: {coords.get('width', 'N/A')} x {coords.get('height', 'N/A')}")
                    print("   ✨ 高亮已显示")
                else:
                    print(f"   ❌ 识别失败: {info_text}")
                
                # 等待高亮完成
                time.sleep(2.5)
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
        
        print("\n✅ 多坐标演示完成!")
        return True
        
    except Exception as e:
        print(f"❌ 多坐标演示失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 auto_recording_manager_v11.py 高亮功能演示")
    print("=" * 80)
    print("📖 本演示展示了集成到auto_recording_manager_v11.py中的高亮功能")
    print("🎨 当识别到控件信息后，会自动绘制红色边框高亮")
    print("⏰ 每次高亮持续2秒")
    print("=" * 80)
    
    # 主要演示
    main_demo_success = demo_highlight_integration()
    
    if main_demo_success:
        print("\n💡 是否继续多坐标演示？(y/n): ", end="")
        try:
            choice = input().strip().lower()
            if choice in ['y', 'yes', '是']:
                demo_multiple_coordinates()
        except (KeyboardInterrupt, EOFError):
            print("\n演示结束")
    
    print("\n" + "=" * 80)
    print("📋 总结")
    print("=" * 80)
    
    if main_demo_success:
        print("🎉 高亮功能集成成功!")
        print("\n✨ 已实现的功能:")
        print("   ✅ 控件识别后自动高亮显示")
        print("   ✅ 使用KylinAutoSDK_UNI的ultimate_highlight")
        print("   ✅ 红色边框，2秒持续时间")
        print("   ✅ 支持Wayland和X11环境")
        print("   ✅ 完全透明内部，不遮挡内容")
        
        print("\n📖 使用方法:")
        print("   1. 导入: from auto_recording_manager_v11 import AutoRecordingManager")
        print("   2. 创建: manager = AutoRecordingManager(debug=True)")
        print("   3. 识别: result, info = manager.widget_analyzer.analyze_widget_at(x, y)")
        print("   4. 高亮会在识别成功后自动显示")
        
        print("\n🔧 技术细节:")
        print("   • 优先使用KylinAutoSDK_UNI的ultimate_highlight")
        print("   • 备用统一高亮渲染器和传统高亮渲染器")
        print("   • 四个独立边框窗口，完全透明内部")
        print("   • 使用override_redirect绕过窗口管理器")
        
        return True
    else:
        print("⚠️ 演示未完全成功，请检查环境配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
