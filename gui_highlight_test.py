#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图形界面高亮测试
创建一个简单的GUI窗口来测试高亮效果
"""

import sys
import time
import threading

def create_test_gui():
    """创建测试GUI"""
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 创建主窗口
        root = tk.Tk()
        root.title("高亮测试窗口")
        root.geometry("800x600+100+100")
        
        # 创建标签
        label = tk.Label(root, text="高亮测试窗口", font=("Arial", 16))
        label.pack(pady=20)
        
        # 创建按钮
        test_button = tk.Button(root, text="测试按钮", font=("Arial", 12))
        test_button.pack(pady=10)
        
        # 创建文本框
        entry = tk.Entry(root, font=("Arial", 12))
        entry.pack(pady=10)
        entry.insert(0, "测试文本框")
        
        # 创建列表框
        listbox = tk.Listbox(root)
        listbox.pack(pady=10)
        for i in range(5):
            listbox.insert(tk.END, f"列表项 {i+1}")
        
        # 创建控制面板
        control_frame = tk.Frame(root)
        control_frame.pack(pady=20)
        
        # 状态标签
        status_label = tk.Label(control_frame, text="点击下面的按钮开始高亮测试", fg="blue")
        status_label.pack(pady=5)
        
        def start_highlight_test():
            """开始高亮测试"""
            status_label.config(text="正在进行高亮测试...", fg="orange")
            root.update()
            
            # 在新线程中执行高亮测试
            def highlight_thread():
                try:
                    # 获取按钮的位置
                    test_button.update_idletasks()
                    x = test_button.winfo_rootx()
                    y = test_button.winfo_rooty()
                    width = test_button.winfo_width()
                    height = test_button.winfo_height()
                    
                    print(f"测试按钮位置: ({x}, {y}), 大小: {width}x{height}")
                    
                    # 导入高亮模块
                    from wayland_highlight_renderer import UnifiedHighlightRenderer
                    
                    # 创建高亮渲染器
                    renderer = UnifiedHighlightRenderer(debug=True)
                    
                    # 高亮测试按钮
                    result = renderer.highlight_widget(x, y, width, height)
                    
                    if result:
                        # 更新状态
                        root.after(0, lambda: status_label.config(text="高亮显示中... (5秒)", fg="green"))
                        time.sleep(5)
                        
                        # 清除高亮
                        renderer.clear_highlight()
                        renderer.cleanup()
                        
                        # 更新状态
                        root.after(0, lambda: status_label.config(text="高亮测试完成!", fg="blue"))
                    else:
                        root.after(0, lambda: status_label.config(text="高亮测试失败!", fg="red"))
                        
                except Exception as e:
                    print(f"高亮测试错误: {e}")
                    root.after(0, lambda: status_label.config(text=f"高亮测试错误: {e}", fg="red"))
            
            # 启动高亮线程
            thread = threading.Thread(target=highlight_thread, daemon=True)
            thread.start()
        
        # 高亮测试按钮
        highlight_btn = tk.Button(control_frame, text="开始高亮测试", 
                                command=start_highlight_test, bg="lightgreen")
        highlight_btn.pack(pady=5)
        
        # 退出按钮
        quit_btn = tk.Button(control_frame, text="退出", command=root.quit, bg="lightcoral")
        quit_btn.pack(pady=5)
        
        # 说明文字
        info_text = tk.Text(root, height=8, width=80)
        info_text.pack(pady=10, fill=tk.BOTH, expand=True)
        
        info_content = """
高亮测试说明：

1. 这个窗口包含了多个可测试的控件
2. 点击"开始高亮测试"按钮，将会高亮显示"测试按钮"
3. 高亮效果是红色边框，持续5秒
4. 您也可以手动测试其他控件的坐标

如果您看到红色边框围绕"测试按钮"，说明高亮功能正常工作！

窗口位置信息：
- 窗口起始位置: (100, 100)
- 窗口大小: 800x600
- 测试按钮大约在窗口中上部分
        """
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        print("=" * 60)
        print("🎯 图形界面高亮测试")
        print("=" * 60)
        print("✅ 测试窗口已创建")
        print("📍 窗口位置: (100, 100)")
        print("📏 窗口大小: 800x600")
        print("💡 请在窗口中点击'开始高亮测试'按钮")
        print("👀 观察'测试按钮'周围是否出现红色边框")
        print("=" * 60)
        
        # 运行GUI
        root.mainloop()
        
    except ImportError:
        print("❌ tkinter库不可用，无法创建图形界面")
        return False
    except Exception as e:
        print(f"❌ 创建图形界面失败: {e}")
        return False
    
    return True

def test_coordinate_highlight():
    """测试指定坐标的高亮"""
    print("\n" + "=" * 60)
    print("🎯 指定坐标高亮测试")
    print("=" * 60)
    
    # 测试您提供的坐标
    test_x, test_y = 1739, 320
    
    try:
        from UNI import UNI
        from wayland_highlight_renderer import UnifiedHighlightRenderer
        
        # 识别控件
        uni = UNI()
        widget_info, info_text = uni.kdk_getElement_Uni(test_x, test_y, False, True)
        
        if widget_info and widget_info.get('Coords'):
            coords = widget_info['Coords']
            print(f"✅ 找到控件: {widget_info.get('Name', 'Unknown')}")
            print(f"📍 实际位置: ({coords['x']}, {coords['y']})")
            print(f"📏 大小: {coords['width']}x{coords['height']}")
            
            # 高亮显示
            renderer = UnifiedHighlightRenderer(debug=True)
            result = renderer.highlight_widget(
                coords['x'], coords['y'], 
                coords['width'], coords['height'], 
                widget_info
            )
            
            if result:
                print("✅ 高亮显示成功，持续10秒...")
                time.sleep(10)
                renderer.clear_highlight()
                print("✅ 高亮已清除")
            else:
                print("❌ 高亮显示失败")
                
            renderer.cleanup()
            
        else:
            print(f"❌ 在坐标({test_x}, {test_y})未找到控件")
            
    except Exception as e:
        print(f"❌ 坐标高亮测试失败: {e}")

def main():
    """主函数"""
    print("🎯 图形界面高亮测试程序")
    print("=" * 60)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--coordinate":
        # 仅测试坐标高亮
        test_coordinate_highlight()
    else:
        # 创建GUI测试
        print("💡 选择测试模式:")
        print("1. 创建图形界面进行交互测试")
        print("2. 直接测试指定坐标 (1739, 320)")
        
        try:
            choice = input("请选择 (1/2): ").strip()
            
            if choice == "1":
                create_test_gui()
            elif choice == "2":
                test_coordinate_highlight()
            else:
                print("无效选择，默认创建图形界面")
                create_test_gui()
                
        except KeyboardInterrupt:
            print("\n程序已中断")
        except EOFError:
            print("自动选择图形界面模式")
            create_test_gui()

if __name__ == "__main__":
    main()
